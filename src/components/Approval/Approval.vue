<template>
  <div>
    <a-form-model
      layout="horizontal"
      ref="ruleForm"
      :model="form"
    >
      <a-row>
        <div v-if="headList.length>0">
          <a-col
            span='12'
            v-for="(i,index) in headList"
            :key="index"
          >
            <a-form-model-item
              v-bind="formItemLayout"
              :label="i.name"
            >
              <a-input
                :read-only="i.edit"
                v-model="form[i.id]"
              />
            </a-form-model-item>
          </a-col>
        </div>
        <a-col
          :span='span'
          v-for="(i,index) in list"
          :key="index"
        >
          <a-form-model-item
            v-bind="formItemLayout"
            :label="i.name"
          >
            <div v-if="i.type === 'input'">
              <a-input
                :read-only="i.edit"
                v-model="form[i.id]"
              />
            </div>
            <div v-if="i.type === 'select'">
              <el-select
                style="width:100%"
                :disabled="i.edit"
                v-model="form[i.id]"
              >
                <el-option
                  v-for="item in i.list"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
            <div v-if="i.type === 'date'">
              <a-date-picker
                :disabled="i.edit"
                :getPopupContainer=" triggerNode => {
                    return triggerNode.parentNode || document.body;
                  }"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                v-model="form[i.id]"
              />
            </div>
            <div v-if="i.type ==='number'">
              <a-input-number
                :disabled="i.edit"
                style="width:100%"
                :min="0"
                :max="99999999"
                v-model="form[i.id]"
              />
            </div>
            <div v-if="i.type ==='selectList'">
              <my-selectList
                :url="i.url"
                :tableColumn="i.Column"
                :form="i.form"
                :read-only="true"
                :data="dataList[i.id]"
                :name="i.id"
                :disabled="i.edit"
                @choose="choose($event)"
                v-model="form[i.id]"
              ></my-selectList>
            </div>
            <div v-if="i.type ==='radio'">
              <a-radio-group
                :disabled="i.edit"
                v-model="form[i.id]"
              >
                <a-radio-button
                  value="T"
                  style="margin-right:20px"
                >{{ $t('public.T') }}</a-radio-button>
                <a-radio-button value="F">{{ $t('public.F') }}</a-radio-button>
              </a-radio-group>
            </div>
            <div v-if="i.type ==='textarea'">
              <a-input
                type='textarea'
                :read-only="i.edit"
                v-model="form[i.id]"
              />
            </div>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:right"
        >
          <a-button
            :loading="loading"
            @click="handleOK()"
          >{{ $t('public.update') }}</a-button>
        </a-col>
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:left"
        >
          <!-- <a-button @click="onClose">{{ $t('public.cancel') }}</a-button> -->
        </a-col>
      </a-row>
    </a-form-model>
    <vxe-pager
      v-if="headList.length>0"
      :loading="loading"
      :current-page="tablePage.currentPage"
      :page-size="tablePage.pageSize"
      :total="tablePage.total"
      :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
      @page-change="handlePageChange"
    >
    </vxe-pager>
  </div>
</template>
<script>
import axios from '@/router/axios'
import MySelectList from '@/components/MySelectList'
import { formData } from './formData.js'

export default {
  components: {
    MySelectList
  },
  data () {
    return {
      loading: false,
      list: [],
      headList: [],
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        }
      },
      dataList: {},
      tablePage: {
        currentPage: 1,
        pageSize: 1,
        total: 0,
        pageSizes: [1],
        layouts: ['Sizes', 'PrevPage', 'Number', 'NextPage', 'Total'],
      },
      form: {},
      span: '12',
      jsp: ''
    }
  },
  props: {
    sessionsId: {
      type: String,
      required: true
    },
    data: {
      type: Object,
      required: true
    }
  },
  created () {
    this.jsp = this.data.currentTaskVos[0].fromJsp //获取配置表单路径
    // this.jsp = '/identifier'
    this.getList()
  },
  methods: {
    // 列表组件获取传值
    choose (obj) {
      let sr = obj.obj
      // 列表组件选择时根据name对form对象赋值
      this.form[sr.name] = sr.data[sr.name]
    },
    getList () {
      this.list = formData[this.jsp].itm //获取配置项数组
      if (formData[this.jsp].head) {
        this.headList = formData[this.jsp].head //获取配置表头项数组
      }
      this.span = formData[this.jsp].span//获取配置表单分布
      axios({
        url: formData[this.jsp].query,
        method: 'get',
        params: Object.assign({
          current: this.tablePage.currentPage,
          size: this.tablePage.pageSize,
          sessionsId: this.sessionsId
        })
      }).then(res => {
        this.tablePage.total = res.data.total
        this.tablePage.currentPage = res.data.current
        if (res.data.records.length > 0) {
          this.form = res.data.records[0]
          this.list.forEach(i => {
            if (i.type === 'selectList') {
              //判断如果是列表组件输入框则获取data属性中的dataList对象根据配置项id命名赋值空对象
              let a = i.id
              this.dataList = Object.assign(this.dataList, { [a]: '' })
              //根据i.id取得对象属性进行赋值 回显在列表组件输入框中
              let name = res.data.records[0][i.data]
              if (name && typeof (name) === 'string') {
                this.dataList[i.id] = name
              } else if (name && typeof (name) === 'object') {
                this.dataList[i.id] = res.data.records[0][i.data].name
              }

            }
          })
        }
      })
    },
    onClose () {
    },
    handleOK () {
      let obj = {
        sessionsId: this.sessionsId,
        id: this.form.id,
        ...this.form
      }
      axios({
        url: formData[this.jsp].edit,
        method: 'post',
        data: obj
      }).then(res => {
        this.loading = false
        this.$message.success(this.$t('public.success'))
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
  }
}
</script>
