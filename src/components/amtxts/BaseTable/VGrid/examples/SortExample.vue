<template>
  <div class="sort-example">
    <h3>表格排序示例</h3>
    
    <div class="controls">
      <label>
        排序配置：
        <input 
          v-model="sortContent" 
          placeholder="例如：box_no desc, update_by asc"
          style="width: 300px; margin-left: 10px;"
        />
      </label>
      <button @click="updateSort" style="margin-left: 10px;">应用排序</button>
    </div>

    <vGrid
      ref="gridRef"
      :FUNID="'test.sort'"
      :tableColumn="tableColumn"
      :showToolbar="false"
      :showForm="false"
      :sortContent="sortContent"
      @ready="handleGridReady"
    />
  </div>
</template>

<script>
import vGrid from '../index.vue'

export default {
  name: 'SortExample',
  
  components: {
    vGrid
  },

  data() {
    return {
      sortContent: 'box_no desc, update_by asc',
      tableColumn: [
        {
          field: 'box_no',
          title: '箱号',
          sortable: true
        },
        {
          field: 'update_by',
          title: '更新人',
          sortable: true
        },
        {
          field: 'create_time',
          title: '创建时间',
          sortable: true
        },
        {
          field: 'status',
          title: '状态'
        }
      ],
      // 模拟列配置数据
      columnContent: [
        {
          field: 'box_no',
          title: '箱号',
          rem_gb: '箱号',
          is_show: 'T',
          size: '120'
        },
        {
          field: 'update_by',
          title: '更新人',
          rem_gb: '更新人',
          is_show: 'T',
          size: '100'
        },
        {
          field: 'create_time',
          title: '创建时间',
          rem_gb: '创建时间',
          is_show: 'T',
          size: '150'
        },
        {
          field: 'status',
          title: '状态',
          rem_gb: '状态',
          is_show: 'T',
          size: '80'
        }
      ],
      // 模拟表格数据
      tableData: [
        {
          box_no: 'BOX001',
          update_by: '张三',
          create_time: '2024-01-15 10:30:00',
          status: '正常'
        },
        {
          box_no: 'BOX003',
          update_by: '李四',
          create_time: '2024-01-14 09:20:00',
          status: '异常'
        },
        {
          box_no: 'BOX002',
          update_by: '王五',
          create_time: '2024-01-16 14:15:00',
          status: '正常'
        }
      ]
    }
  },

  methods: {
    handleGridReady() {
      // 模拟设置列配置和数据
      this.$refs.gridRef.columnContent = this.columnContent
      this.$refs.gridRef.sortContent = this.sortContent
      this.$refs.gridRef.updateGridColumns()
      
      // 设置表格数据
      this.$refs.gridRef.gridOptions.data = this.tableData
    },

    updateSort() {
      // 更新排序配置
      this.$refs.gridRef.sortContent = this.sortContent
      this.$refs.gridRef.updateGridColumns()
      
      console.log('当前排序配置:', this.sortContent)
    }
  }
}
</script>

<style scoped>
.sort-example {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.controls {
  margin-bottom: 20px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.controls label {
  display: flex;
  align-items: center;
}

.controls input {
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.controls button {
  padding: 5px 15px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.controls button:hover {
  background: #40a9ff;
}
</style>
