# VGrid 表格排序功能说明

## 功能概述

VGrid 组件现在支持通过 `sortContent` 字符串自动解析并设置表格的默认排序。

## sortContent 格式

### 基本格式
```
"字段名 排序方向, 字段名 排序方向"
```

### 示例
```javascript
// 单个字段排序
this.sortContent = "box_no desc"

// 多个字段排序
this.sortContent = "box_no desc, update_by asc"

// 更复杂的示例
this.sortContent = "priority desc, create_time asc, update_time desc"
```

### 排序方向
- `asc`: 升序排列
- `desc`: 降序排列

## 使用方法

### 1. 在组件中设置 sortContent

```vue
<template>
  <vGrid
    :FUNID="'your.function.id'"
    :tableColumn="tableColumn"
    :sortContent="sortContent"
  />
</template>

<script>
export default {
  data() {
    return {
      sortContent: "box_no desc, update_by asc",
      tableColumn: [
        {
          field: 'box_no',
          title: '箱号',
          sortable: true  // 必须设置为 true 才能排序
        },
        {
          field: 'update_by', 
          title: '更新人',
          sortable: true
        }
      ]
    }
  }
}
</script>
```

### 2. 动态更新排序

```javascript
// 更新排序配置
this.sortContent = "new_field asc"
this.$refs.gridRef.updateGridColumns()
```

### 3. 在方案切换中使用

当使用 SearchForm 的方案功能时，`sortContent` 会自动从方案数据中获取并应用：

```javascript
// 在 handleSchemeChange 中自动处理
handleSchemeChange(schemeData) {
  this.columnContent = schemeData.columnContent
  this.sortContent = schemeData.sortContent  // 自动设置排序
  this.updateGridColumns()  // 应用新的排序配置
}
```

## 实现原理

### 解析过程

1. **字符串分割**: 按逗号分割多个排序条件
2. **条件解析**: 每个条件按空格分割为字段名和排序方向
3. **验证**: 确保排序方向为 'asc' 或 'desc'
4. **配置生成**: 生成 VXE 表格的 defaultSort 配置

### 核心代码

```javascript
// 解析排序内容字符串
parseSortContent(sortContent) {
  if (!sortContent || typeof sortContent !== 'string') {
    return []
  }

  const sortItems = sortContent.split(',').map(item => item.trim())
  const defaultSort = []

  sortItems.forEach(item => {
    const parts = item.split(/\s+/)
    if (parts.length >= 2) {
      const field = parts[0]
      const order = parts[1].toLowerCase()
      
      if (order === 'asc' || order === 'desc') {
        defaultSort.push({
          field: field,
          order: order
        })
      }
    }
  })

  return defaultSort
}
```

## 注意事项

1. **字段必须存在**: sortContent 中的字段名必须在 tableColumn 或 columnContent 中存在
2. **启用排序**: 对应的列必须设置 `sortable: true`
3. **格式严格**: 字段名和排序方向之间必须有空格分隔
4. **大小写**: 排序方向不区分大小写，会自动转换为小写
5. **容错处理**: 无效的排序配置会被忽略，不会影响其他有效配置

## 调试信息

组件会在控制台输出解析结果，方便调试：

```
解析排序配置: [
  { field: 'box_no', order: 'desc' },
  { field: 'update_by', order: 'asc' }
]
```

## 兼容性

- 支持 VXE Table 的多列排序功能
- 与现有的 SearchForm 方案功能完全兼容
- 不影响手动点击列头进行排序的功能
