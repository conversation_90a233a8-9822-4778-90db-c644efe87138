/**
 * 保存状态管理器
 * 用于管理表单和表格的保存状态，包括：
 * 1. 监听数据变化
 * 2. 更新保存按钮状态
 * 3. 管理标签页关闭确认状态
 */
export default class SaveStateManager {
  constructor(options = {}) {
    this.store = options.store;
    this.bus = options.bus;
    this.route = options.route;
    this.toolbarRef = options.toolbarRef;
    this.checkInterval = null;
    this.hasChanges = false;
    this.dataCache = null;
    this.type = options.type || 'form'; // 'form' 或 'grid'
  }

  /**
   * 初始化状态管理器
   */
  init() {
    this.startWatcher();
    this.updateTabCloseState();
    // 初始化数据缓存
    this.dataCache = this.cloneData(this.getCurrentData());
  }

  /**
   * 销毁状态管理器
   */
  destroy() {
    this.bus.$off("updateTabCloseState");
    clearInterval(this.checkInterval);
  }

  /**
   * 更新标签页关闭状态
   */
  updateTabCloseState() {
    this.bus.$on("updateTabCloseState", async ({param, callback}) => {
      const path = this.route.path;
      if(param === path) {
        try {
          await this.handleSave();
          await this.store.dispatch('setTabCloseConfirmTip', { tabKey: path, flag: false });
          callback(true);
        } catch (err) {
          console.error('保存失败:', err);
          callback(false);
        }
      }
    });
  }

  /**
   * 开始监听数据变化
   */
  startWatcher() {
    this.checkInterval = setInterval(() => {
      try {
        const currentData = this.getCurrentData();
        if (!currentData) return;

        const hasChanges = this.checkHasChanges(currentData);
        this.updateState(hasChanges);
        
      } catch (err) {
        console.error('数据监听错误:', err);
      }
    }, 100);
  }

  /**
   * 获取当前数据
   */
  getCurrentData() {
    const _$refs = this.toolbarRef.$parent.$refs
    if (this.type === 'grid') {
        const gridData = _$refs.gridRef?.getRecordset();
        const formData = _$refs.formRef?.value;
        return {
            grid: gridData,
            form: formData
        };
    } else {
        return _$refs.formRef?.value;
    }
  }

  /**
   * 检查是否有变更
   */
  checkHasChanges(data) {
    if (this.type === 'grid') {
      const { grid, form } = data;
      if (!grid) return false;
      
      const { insertRecords, removeRecords, updateRecords } = grid;
      const hasGridChanges = insertRecords.length > 0 || removeRecords.length > 0 || updateRecords.length > 0;
      
      // 检查表单数据是否有变化
      const formCache = this.dataCache?.form || {};
      const hasFormChanges = !this.deepEqual(formCache, form);
      
      // 如果表单数据恢复到原始状态，重置状态
      if (!hasGridChanges && !hasFormChanges) {
        this.resetState();
        return false;
      }
      
      return hasGridChanges || hasFormChanges;
    } else {
      const formCache = this.dataCache || {};
      const hasChanges = !this.deepEqual(formCache, data);
      
      // 如果表单数据恢复到原始状态，重置状态
      if (!hasChanges) {
        this.resetState();
        return false;
      }
      
      return hasChanges;
    }
  }

  /**
   * 更新状态
   */
  updateState(hasChanges) {
    // 当状态从有变化变为无变化时，重置状态
    if (this.hasChanges && !hasChanges) {
      this.resetState();
      return;
    }
    
    if (hasChanges !== this.hasChanges) {
      this.hasChanges = hasChanges;
      const tabKey = this.route.path;
      this.store.dispatch('setTabCloseConfirmTip', { tabKey, flag: hasChanges });
      
      // 更新保存按钮图标
      if (this.toolbarRef && this.toolbarRef.toolbarItems) {
        this.toolbarRef.toolbarItems.forEach(item => {
          if(item.value === 'save') {
            this.toolbarRef.$set(item, 'icon', hasChanges ? 'icon-save-change' : 'icon-save');
          }
        });
      }
    }
  }

  /**
   * 重置状态
   */
  resetState() {
    this.hasChanges = false;
    const tabKey = this.route.path;
    this.store.dispatch('setTabCloseConfirmTip', { tabKey, flag: false });
    
    if (this.toolbarRef && this.toolbarRef.toolbarItems) {
      this.toolbarRef.toolbarItems.forEach(item => {
        if(item.value === 'save') {
          this.toolbarRef.$set(item, 'icon', 'icon-save');
        }
      });
    }

    // 重置数据缓存
    const currentData = this.getCurrentData();
    if (currentData) {
      this.dataCache = this.cloneData(currentData);
    }
  }

  /**
   * 深度克隆数据
   */
  cloneData(data) {
    return JSON.parse(JSON.stringify(data));
  }

  /**
   * 深度比较两个对象
   */
  deepEqual(obj1, obj2) {
    try {
      // 处理基本类型
      if (obj1 === obj2) return true;
      if (obj1 == null || obj2 == null) return obj1 === obj2;
      
      // 处理数组
      if (Array.isArray(obj1) && Array.isArray(obj2)) {
        if (obj1.length !== obj2.length) return false;
        return obj1.every((item, index) => this.deepEqual(item, obj2[index]));
      }
      
      // 处理对象
      if (typeof obj1 === 'object' && typeof obj2 === 'object') {
        const keys1 = Object.keys(obj1);
        const keys2 = Object.keys(obj2);
        
        if (keys1.length !== keys2.length) return false;
        
        return keys1.every(key => {
          const val1 = obj1[key];
          const val2 = obj2[key];
          
          // 处理数字和字符串的转换比较
          if (typeof val1 !== typeof val2) {
            // 如果一个是数字，一个是字符串，尝试转换后比较
            if ((typeof val1 === 'number' && typeof val2 === 'string') ||
                (typeof val1 === 'string' && typeof val2 === 'number')) {
              const num1 = Number(val1);
              const num2 = Number(val2);
              if (!isNaN(num1) && !isNaN(num2)) {
                return num1 === num2;
              }
            }
            // 如果一个是布尔值，一个是字符串，尝试转换后比较
            if ((typeof val1 === 'boolean' && typeof val2 === 'string') ||
                (typeof val1 === 'string' && typeof val2 === 'boolean')) {
              return String(val1) === String(val2);
            }
            return false;
          }
          
          return this.deepEqual(val1, val2);
        });
      }
      
      // 处理其他类型
      return String(obj1) === String(obj2);
    } catch (err) {
      console.error('对象比较错误:', err);
      return false;
    }
  }

  /**
   * 保存处理函数
   * 需要在使用时实现
   */
  async handleSave() {
    throw new Error('handleSave 方法需要在使用时实现');
  }
} 