<template>
  <div>
    <a-drawer :title="title" placement="right" :closable="false" @close="onClose" :visible="visible"
      :destroyOnClose="true" width="70%">
      <a-form :form="form" ref="form">
        <a-row :gutter="16">
          <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
            <a-form-item label="模具启用" v-bind="formItemLayout">
              <a-input :disabled="formIndex" v-decorator="['sortNo', { rules: [{ required: true, message:'模具启用' }
                                                 
                ] }]" placeholder="请输入模具启用" />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
            <a-form-item label="名称" v-bind="formItemLayout">
              <a-input :disabled="formIndex" v-decorator="['sortNm', { rules: [{ required: false, message:'名称' }
                                                 
                ] }]" placeholder="请输入名称" />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
            <a-form-item :label="$t('部门')" v-bind="formItemLayout">
              <!-- url="mes/basicData/depPage"   :tableColumn="$Column.salmDep"   :form="$Form.salmDep"-->
              <my-selectListwo url="mms/dept/getDeptPage" :read-only="true" :tableColumn="$Column.devicesalmDep"
                :form="$Form.devicesalmDep" :data="data" name="dep" :disabled="formStatus" @choose="choose($event)"
                ref="selectList"
                v-decorator="['dep', { rules: [{ required: false, message:$t('salm.placeholder.depName') } ] }]"
                :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
            <a-form-item :label="$t('停用日期')" v-bind="formItemLayout">
              <a-date-picker :disabled="formStatus" format="YYYY-MM-DD" style="width:100%"
                v-decorator="['stopDd', { rules: [{ required: false, message:'请输入停用日期' }] }]" placeholder="请输入停用日期" />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
            <a-form-item label="备注" v-bind="formItemLayout">
              <a-input :disabled="formStatus" v-decorator="['rem', { rules: [{ required: false, message:'备注' }
                                                   
                ] }]" placeholder="请输入备注" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <a-row :gutter="16">
        <a-col class="gutter-row" :span="12" style="text-align:right">
          <a-button type="primary" v-if="modeType==='0'" :loading="loading" @click="handleOK()">{{ $t('public.save')
            }}</a-button>
          <a-button type="primary" v-if="modeType==='1'" @click="handleMenuClick()">{{ $t('public.edit') }}</a-button>
          <a-button type="primary" v-if="modeType==='2'" :loading="loading" @click="handleEdit()">{{ $t('public.save')
            }}</a-button>
        </a-col>
        <a-col class="gutter-row" :span="12" style="text-align:left">
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script>

  import { sortsaveOrUpdate } from '@/api/muju/index'
  import { add, edit, getTogto } from '@/api/salm'
  import { addSebMang, updateSebMang } from '@/api/barcode/basicdata/management'
  import MySelectList from '@/components/MySelectList'
  import MySelectListwo from '@/components/MySelectListwo'
  import moment from 'moment'
  import { mapGetters } from 'vuex'
  export default {
    name: 'SalmModal',
    components: {
      MySelectList,
      MySelectListwo
    },
    data() {
      return {
        bywxId: '1',
        title: '',
        loading: false,
        visible: false,
        formStatus: false,
        formIndex: false,
        UpStatus: false,
        data: '',
        Updata: '',
        row: {},
        modeType: '',
        form: this.$form.createForm(this),
        formItemLayout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 7 },
            md: { span: 8 },
            lg: { span: 7 }
          },
          wrapperCol: {
            xs: { span: 5 },
            sm: { span: 16 },
            md: { span: 17 },
            lg: { span: 16 }
          }
        },
        depName: null,
        salName: null,
        dName: null,
        flag: false,
        typeList: [
          { label: '保养/维修', value: 1 },
          { label: '保养', value: 2 },
          { label: '维修', value: 3 },
        ],
        dataygNo: '',
      }
    },
    computed: {
      ...mapGetters(['permissions'])
    },
    created() {

    },
    methods: {
      dsTypeChange(e) {
        this.dsScope = []
        this.form.dsScopeM = []
      },
      disabledDate(current) {
        return current && current < moment().endOf('day')
      },
      // 渲染组件
      // choose (obj) {
      //   var map = {}
      //   if (obj.obj.name === 'upSalNo') {
      //     map[obj.obj.name] = obj.obj.data.salNo
      //   }
      //   if (obj.obj.name === 'dep') {
      //     map[obj.obj.name] = obj.obj.data.dep
      //   }
      //   this.form.setFieldsValue(map)
      // },

      // 渲染组件
      choose(obj) {
        var map = {}
        if (obj.obj.name === 'upSalNo') {
          this.salName = obj.obj.data.name
          map[obj.obj.name] = obj.obj.data.salNo
        }
        // if (obj.obj.name === 'salNo') {
        //   this.salName = obj.obj.data.name
        //   map[obj.obj.name] = obj.obj.data.salNo
        // }
        if (obj.obj.name === 'dep') {
          this.flag = true
          this.depName = obj.obj.data.depName
          map[obj.obj.name] = obj.obj.data.dep
        }
        this.form.setFieldsValue(map)
      },
      ygNochoose(obj) {
        var map = {}
        // if (obj.obj.name === 'salNo') {
        //   this.salName = obj.obj.data.name
        //   map[obj.obj.name] = obj.obj.data.salNo
        // }
        if (obj.obj.name === 'ygNo') {
          this.flag = true
          this.ygName = obj.obj.data.salName
          // this.dep = obj.obj.data.dep
          this.form.setFieldsValue({
            'dep': obj.obj.data.dep,
          })
          this.data = obj.obj.data.dep
          // map[dep] = obj.obj.data.dep
          map[obj.obj.name] = obj.obj.data.salNo
        }
        this.form.setFieldsValue(map)
      },
      // 取消关闭
      onClose() {
        this.form.resetFields()
        this.loading = false
        this.visible = false
        this.keyStatus = ''
        this.data = ''
        this.Updata = ''
        this.dataygNo = ''
        this.modeType = '0'
        this.row = {}
      },
      create(model, obj) {
        this.formIndex = false
        this.title = model.title
        this.modeType = '0'
        this.visible = true
        this.formStatus = false
        this.UpStatus = false

        setTimeout(() => {
          // if (obj.name && obj.id !== '') {
          //   this.data = obj.name

          //   this.form.setFieldsValue({
          //     bywxId:obj.bywxId,
          //     'dep': obj.id,

          //   })
          // }else{
          this.form.setFieldsValue({
            bywxId: 1,
          })
          // }
          // this.form.setFieldsValue({
          //   'logon': 'F'
          // })
        }, 1)
      },
      // 点击编辑按钮
      handleMenuClick() {
        this.modeType = '2'
        this.title = this.$t('public.edit')
        this.formIndex = true
        this.UpStatus = true
        this.formStatus = false
      },
      // 双击弹出框
      edit(model, row) {
        if (row.dep) {
          this.data = row.dep + '-' + row.depName
        }
        this.title = model.title
        this.modeType = '1'
        this.row = row
        this.formStatus = true
        this.UpStatus = true
        this.formIndex = true
        this.visible = true
        if (this.flag) {
        } else {
          this.dName = row.depName
        }
        this.salName = row.salName
        //  localStorage.setItem('keyrecords', JSON.stringify(row.id));
        localStorage.setItem('rowId', row.id);
        setTimeout(() => {
          this.form.setFieldsValue({
            'stopDd': row.stopDd == null ? null : moment(row.stopDd),
            sortNo: row.sortNo,
            'dep': row.dep,
            sortNm: row.sortNm,
            // 'name':row.name,
            // 'upSalNo': row.salNo,
            rem: row.rem,
          })
        }, 1)
      },

      // 添加确认
      handleOK() {
        this.form.validateFields((err, values) => {

          console.log(values, 'ggggggggggggg')
          const Params = {
            ...values,
            stopDd: values.stopDd == null ? null : moment(values.stopDd).format('YYYY-MM-DD HH:mm:ss'),
            depName: this.depName,
            // salNo: values.upSalNo,
            // dep: "030101"
          }
          // if (Params.salNo === Params.upSalNo) {
          //   this.$notification.error({
          //     message: this.$t('public.errorInfo'),
          //     description: this.$t('public.upLeavel')
          //   })
          //   err = true
          // }
          if (!err) {
            this.loading = true
            sortsaveOrUpdate(Params)
              .then((res) => {
                if (res !== null) {
                  if (res.msg == 'fail') {
                    this.$message.error(this.$t(res.data))
                  }
                  if (res.msg == 'success') {
                    this.$emit('onOk')
                    this.visible = false
                    this.$message.success(this.$t('public.success'))
                  }
                  this.loading = false
                }
              })
              .catch(err => this.requestFailed(err))
              .finally(() => {
                this.loading = false
              })
          }
        })
      },
      // 确认编辑
      handleEdit() {
        if (this.flag) {
          this.dName = this.depName
        }
        this.form.validateFields((err, values) => {


          const Params = {
            ...values,
            stopDd: values.stopDd == null ? null : moment(values.stopDd).format('YYYY-MM-DD HH:mm:ss'),
            depName: this.dName,
            // salNo: values.upSalNo,
            id: localStorage.getItem('rowId')
          }
          // if (Params.salNo === Params.upSalNo) {
          //   this.$notification.error({
          //     message: this.$t('public.errorInfo'),
          //     description: this.$t('public.upLeavel')
          //   })
          //   err = true
          // }
          if (!err) {
            this.loading = true
            sortsaveOrUpdate(Params)
              .then((res) => {
                if (res !== null) {
                  if (res.msg == 'fail') {
                    this.$message.error(this.$t(res.data))
                  }
                  if (res.msg == 'success') {
                    this.$emit('onOk')
                    this.visible = false
                    this.$message.success(this.$t('public.success'))
                  }
                  this.loading = false
                  this.data = ''
                  this.Updata = ''
                  this.dataygNo = ''
                }
              })
              .catch(err => this.requestFailed(err))
              .finally(() => {
                this.loading = false
              })
          }
        })
      }

    }
  }
</script>