<template>
  <div>
    <a-drawer
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :destroyOnClose="true"
      width="70%"
    >
      <template slot="title">
        <span class="title-name">{{ title }}</span>
        <span
          v-if="modeType!=='0'"
          class="title-age"
        >
          <a-dropdown>
            <a-button class="ant-dropdown-link">
              {{ $t('public.action') }}
              <a-icon type="down" />
            </a-button>
            <a-menu slot="overlay">
              <a-menu-item v-permission="barcode_propertysettings_set">
                <a @click="set_salm()">{{ $t('propertySettings.set') }}</a>
              </a-menu-item>
              <a-menu-item v-permission="barcode_property_attribute">
                <a @click="attribute()">{{ $t('propertySettings.attribute') }}</a>
              </a-menu-item>
              <a-menu-item v-permission="barcode_propertysettings_del">
                <a @click="del()">{{ $t('public.delete') }}</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </template>
      <a-form
        :form="form"
        ref="form"
      >
        <a-row :gutter="16">
          <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="12"
            :xl="12"
          >
            <a-form-item
              :label="$t('propertySettings.compno')"
              v-bind="formItemLayout"
            >
              <a-input
                disabled
                v-decorator="['compno', { rules: [{ required: false, message:$t('propertySettings.placeholder.compno') }] }]"
                :placeholder="$t('propertySettings.placeholder.compno')"
              />
            </a-form-item>
          </a-col>
          <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="12"
            :xl="12"
          >
            <a-form-item
              :label="$t('propertySettings.roleno')"
              v-bind="formItemLayout"
            >
              <a-input
                :disabled="formIndex"
                v-decorator="['roleno', { rules: [{ required: true, message:$t('propertySettings.placeholder.roleno') }] }]"
                :placeholder="$t('propertySettings.placeholder.roleno')"
              />
            </a-form-item>
          </a-col>
          <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="12"
            :xl="12"
          >
            <a-form-item
              :label="$t('propertySettings.name')"
              v-bind="formItemLayout"
            >
              <a-input
                :disabled="formStatus"
                v-decorator="['name', { rules: [{ required: true, message:$t('propertySettings.placeholder.name') }] }]"
                :placeholder="$t('propertySettings.placeholder.name')"
              />
            </a-form-item>
          </a-col>
          <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="12"
            :xl="12"
          >
            <a-form-item
              :label="$t('propertySettings.dep')"
              v-bind="formItemLayout"
            >
              <!-- url="/sunlikecode/dept/getDept" -->
              <my-selectList
                url="/admin/dept/page"
                :read-only="true"
                :tableColumn="$Column.fmDep"
                :form="$Form.fmDep"
                :data="data"
                name="dep"
                :disabled="formStatus"
                @choose="choose($event)"
                ref="selectList"
                v-decorator="['dep', { rules: [{ required: true, message:$t('propertySettings.placeholder.dep') }] }]"
                v-model="form.depNo"
                :placeholder="$t('salm.placeholder.depName')"
              ></my-selectList>
            </a-form-item>
          </a-col>
          <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="12"
            :xl="12"
          >
            <a-form-item
              :label="$t('propertySettings.publicId')"
              v-bind="formItemLayout"
            >
              <a-radio-group
                :disabled="formStatus"
                v-decorator="['publicId', { rules: [{ required: true, message:$t('propertySettings.placeholder.publicId') }] }]"
              >
                <a-radio-button
                  value="T"
                  style="margin-right:20px"
                >{{ $t('public.T') }} </a-radio-button>
                <a-radio-button value="F">{{ $t('public.F') }}</a-radio-button>
              </a-radio-group>
            </a-form-item>
          </a-col>

          <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="12"
            :xl="12"
          >
            <a-form-item
              :label="$t('propertySettings.rem')"
              v-bind="formItemLayout"
            >
              <a-input
                :disabled="formStatus"
                v-decorator="['rem', { rules: [{ required: true, message:$t('propertySettings.placeholder.rem') }] }]"
                :placeholder="$t('propertySettings.placeholder.rem')"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <a-row :gutter="16">
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:right"
        >
          <a-button
            type="primary"
            v-if="modeType==='0'"
            :loading="loading"
            @click="handleOK()"
            v-permission="barcode_propertysettings_save"
          >{{ $t('public.save') }}</a-button>
          <a-button
            type="primary"
            v-if="modeType==='1'"
            @click="handleMenuClick()"
            v-permission="barcode_propertysettings_save"
          >{{ $t('public.edit') }}</a-button>
          <a-button
            type="primary"
            v-if="modeType==='2'"
            :loading="loading"
            @click="handleEdit()"
            v-permission="barcode_propertysettings_save"
          >{{ $t('public.save') }}</a-button>
        </a-col>
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:left"
        >
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
      <set-modal ref="set" />
      <propertySet ref="pro" />

    </a-drawer>
  </div>
</template>
<script>
import { addObj, putObj, delObj, getListAll } from '@/api/barcode/propertySettings'
import { mapGetters } from 'vuex'
import MySelectList from '@/components/MySelectList'
import setModal from './setModal'
import propertySet from './propertySet'

export default {
  name: 'PropertySettingsModel',
  components: {
    MySelectList, setModal, propertySet
  },
  data () {
    return {
      barcode_propertysettings_set: 'barcode_propertysettings_set',
      barcode_property_attribute: 'barcode_property_attribute',
      barcode_propertysettings_del: 'barcode_propertysettings_del',
      barcode_propertysettings_save: 'barcode_propertysettings_save',
      title: '',
      visible: false,
      loading: false,
      formStatus: false,
      formIndex: false,
      modeType: '', // 添加为0，编辑为1
      data: '',
      row: {},
      confirmLoading: false,
      form: this.$form.createForm(this),
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 7 },
          md: { span: 8 },
          lg: { span: 7 }
        },
        wrapperCol: {
          xs: { span: 5 },
          sm: { span: 16 },
          md: { span: 17 },
          lg: { span: 16 }
        }
      }
    }
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  methods: {
    choose (obj) {
      this.form.depNo = obj.obj.data.deptCode
      this.data = obj.obj.value
      setTimeout(() => {
          this.form.setFieldsValue({ 'dep': obj.obj.data.deptCode })
        }, 0)
    },
    // 设置人员
    set_salm () {
      const row = this.row
      this.$refs.set.open({ title: this.$t('propertySettings.set') }, row)
    },
    // 设置属性
    attribute () {
      const row = this.row
      this.$refs.pro.open({ title: this.$t('propertySettings.attribute') }, row)
      // getListAll().then(res => {
      //   sessionStorage.setItem('allWh', JSON.stringify(res.data))
      // })
    },
    onClose () {
      this.loading = false
      this.visible = false
      this.form.resetFields()
    },
    // 点击添加按钮弹出框
    create (model) {
      this.formIndex = false
      this.title = model.title
      this.formStatus = false
      this.data = ''
      this.form.depNo = ''
      this.modeType = '0'
      this.visible = true
    },
    // 删除按钮
    del () {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('public.del.content'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk () {
          that.loading = true
          delObj(that.row)
            .then(() => {
              that.loading = false
              that.$emit('getList')
              that.onClose()
              that.$message.success(that.$t('public.success'))
            })
            .catch(err => {
              that.requestFailed(err)
              that.loading = false
            }).finally(() => {
                that.loading = false
            })
        },
        onCancel () {
          that.loading = false
        }
      })
    },
    // 点击编辑按钮
    handleMenuClick () {
      this.modeType = '2'
      this.title = this.$t('public.edit')
      this.formIndex = true
      this.UpStatus = false
      this.formStatus = false
    },
    // 点击编辑按钮弹出框
    edit (model, row) {
      this.title = model.title
      // this.openId = row.publicId
      this.formStatus = true
      this.formIndex = true
      this.modeType = '1'
      this.row = row
      this.visible = true
      this.$nextTick(() => {
        setTimeout(() => {
          this.data = row.depName
          this.form.depNo = row.dep
          this.form.setFieldsValue({
            'compno': row.compno,
            'roleno': row.roleno,
            'name': row.name,
            'publicId': row.publicId,
            'rem': row.rem
         })
        })
      })
    },
    // 添加确认
    handleOK () {

      this.form.validateFields((err, values) => {

        const propertySettingss = {
          $dep: this.data,
          $publicId: values.publicId == 'T'? '是' : '否',
          dep: this.form.depNo,
          ...values
        }
        if (!err) {
          addObj(propertySettingss)
            .then(() => {
              this.$emit('getList')
              this.visible = false
              this.$message.success(this.$t('public.success'))
            })
            .catch((err) => {
               this.requestFailed(err)
              //this.$message.error(this.$t('public.error'))
            })
        }
      })
    },
    // 确认编辑
    handleEdit () {
      this.form.validateFields((err, values) => {

        const propertySettingss = {
          ...values,
          $dep: this.data,
          dep: this.form.depNo,
          $publicId: values.publicId == 'T'? '是' : '否',
        }
        if (!err) {
          putObj(propertySettingss)
            .then(() => {
              this.$emit('getList')
              this.visible = false
              this.$message.success(this.$t('public.success'))
            })
            .catch((err) => {
              this.requestFailed(err)
              //this.$message.error(this.$t('public.error'))
            })
        }
      })
    }
  }
}
</script>
