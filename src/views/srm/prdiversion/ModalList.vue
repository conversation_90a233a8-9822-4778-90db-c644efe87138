<template>
  <div id="box">
    <a-modal
      :title="title"
      destroyOnClose
      :visible.sync="visible"
      @cancel="onClose"
      :footer="null"
      width="75%"
      :maskClosable="false"
    >
      <vxe-toolbar>
        <template #buttons>
          <!-- <vxe-button @click="$refs.xTable4.toggleCheckboxRow(tableData4[1])">切换第二行选中</vxe-button> -->
          <vxe-button @click="$refs.xTable2.setCheckboxRow([tableData2[2], tableData2[1]], true)">设置第三、四行选中</vxe-button>
          <!-- <vxe-button @click="$refs.xTable4.setAllCheckboxRow(true)">设置所有行选中</vxe-button>
            <vxe-button @click="$refs.xTable4.clearCheckboxRow()">清除所有行选中</vxe-button> -->
        </template>
      </vxe-toolbar>
      <vxe-table
        border
        resizable
        stripe
        size="small"
        max-height="450px"
        highlight-current-row
        show-overflow
        highlight-hover-row
        ref="xTable2"
        :checkbox-config="{reserve:true}"
        :loading="loading"
        :data="tableData2"
        :keyboard-config="{ isArrow: true }"
        @checkbox-change="selectChangeEvent"
        @checkbox-all="selectChangeAll"
      >
        <!-- <vxe-table-column
          type="checkbox"
          title="指定询价"
          align="center"
          :width="150"
        ></vxe-table-column> -->
        <vxe-table-column
          field="cusNo"
          fixed="left"
          title="供应商代号"
          align="center"
          :width="150"
        ></vxe-table-column>
        <vxe-table-column
          field="cusName"
          title="供应商名称"
          align="center"
          :width="150"
        ></vxe-table-column>

      </vxe-table>

      <a-spin :spinning="spinning">
        <div v-if="titleD === '熔炼炉号'">
          <a-descriptions
            style="height: 100%; overflow-y: auto"
            :title="titleD"
            bordered
          >
            <a-descriptions-item
              v-for="(i, index) in list"
              :key="index"
              :label="i.name"
            >
              <div v-if="showVal == '3' && obj.bs != 'T'">
                <div
                  v-if="i.name != '熔炼炉号' && i.name != '制令单号'"
                  style="color: black; font-weight: 700"
                  class="input_value"
                >
                  <div
                    v-show="!!obj[i.value]"
                    style="float: left; padding-top: 6px"
                  >{{ obj[i.value] }}</div>
                  <div
                    v-show="i.value != 'createTime' && !obj[i.value]"
                    style="display:inline-block;width:50%;color:#ccc"
                  >

                    无
                    <!-- <a-input v-model="obj[i.value]"> </a-input> -->
                  </div>
                  <div
                    v-if="i.value == 'createTime'"
                    style="float: left;width:50%;"
                  >
                    <a-date-picker
                      style="width:50%;"
                      v-model="obj[i.value + '2']"
                      format="YYYY-MM-DD HH:mm:ss"
                      valueFormat="YYYY-MM-DD HH:mm:ss"
                      :show-time="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"
                    > </a-date-picker>
                  </div>
                  <div
                    v-else
                    style="float: right;width:50%;"
                  >
                    <a-input
                      v-model="obj[i.value + '2']"
                      onkeyup="value=value.replace(/[^\a-\z\A-\Z0-9\u4E00-\u9FA5\-]/g,'')"
                    > </a-input>
                  </div>
                </div>
                <div
                  v-else
                  style="color: black; font-weight: 700"
                  class="input_value"
                >
                  {{ obj[i.value] }}
                </div>
              </div>
              <div v-else>
                {{ obj[i.value] }}
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="正常图片">
              <viewer :images="images2">
                <div
                  v-for="src in images2"
                  :key="src"
                  style="width: 100px; height: 100px"
                >
                  <img
                    style="width: 100%; height: 100%; padding: 5px; cursor: pointer"
                    :src="src"
                  />
                </div>
              </viewer>
            </a-descriptions-item>
            <a-descriptions-item label="异常图片">
              <viewer :images="images3">
                <div
                  v-for="src in images3"
                  :key="src"
                  style="width: 100px; height: 100px"
                >
                  <img
                    style="width: 100%; height: 100%; padding: 5px; cursor: pointer"
                    :src="src"
                  />
                </div>
              </viewer>
            </a-descriptions-item>
            <a-descriptions-item
              label="标识"
              v-if="showVal == '3'"
            >
              {{ obj.bs }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <div
          v-else
          class="box2"
        >
          <a-descriptions
            style="height: 100%; overflow-y: auto"
            :title="titleD"
            bordered
          >
            <a-descriptions-item
              v-for="(i, index) in list1"
              :key="index"
              :label="i.name"
            >
              <div v-if="showVal == '3' && obj.bs != 'T'">
                <div
                  v-if="i.name != '热处理炉号' && i.name != '制令单号' && i.name != '作业人员'"
                  style="color: black; font-weight: 700"
                  class="input_value"
                >
                  <div style="float: left; padding-top: 6px">{{ obj[i.value] }}</div>
                  <div
                    v-if="i.value == 'createTime'"
                    style="float: left;width:50%;"
                  >
                    <a-date-picker
                      v-model="obj[i.value + '2']"
                      format="YYYY-MM-DD"
                      valueFormat="YYYY-MM-DD"
                    > </a-date-picker>
                  </div>
                  <div
                    v-else
                    style="float: left;float: right;width:50%;"
                  >
                    <a-input v-model="obj[i.value + '2']"> </a-input>
                  </div>
                </div>
                <div
                  v-else
                  style="color: black; font-weight: 700"
                  class="input_value"
                >
                  {{ obj[i.value] }}
                </div>
              </div>
              <div v-else>
                {{ obj[i.value] }}
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="正常图片">
              <viewer :images="images">
                <div
                  v-for="src in images"
                  :key="src"
                  style="width: 100px; height: 100px"
                >
                  <img
                    style="width: 100%; height: 100%; padding: 5px; cursor: pointer"
                    :src="src"
                  />
                </div>
              </viewer>
            </a-descriptions-item>
            <a-descriptions-item label="异常图片">
              <viewer :images="images2">
                <div
                  v-for="src in images2"
                  :key="src"
                  style="width: 100px; height: 100px"
                >
                  <img
                    style="width: 100%; height: 100%; padding: 5px; cursor: pointer"
                    :src="src"
                  />
                </div>
              </viewer>
            </a-descriptions-item>
            <a-descriptions-item
              label="标识"
              v-if="showVal == '3'"
            >
              {{ obj.bs }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <a-row :gutter="16">
          <a-col style="float: right">
            <a-button
              style="margin-top: 20px;"
              type="primary"
              v-if="showVal == '3'"
              :disabled="obj.bs === 'T'"
              @click="submit"
            >{{ $t('public.save') }}</a-button>
            <a-button
              style="margin-top: 20px;margin-left: 20px;margin-right: 20px"
              @click="onClose"
            >{{ $t('public.cancel') }}</a-button>
          </a-col>
        </a-row>
      </a-spin>
    </a-modal>
  </div>
</template>
<script>
import { getSpc, getInfo, getInfo2, getInfo3, rlnoadd, rclnoadd } from '@/api/report/information'
import moment from 'moment'
export default {
  data () {
    return {

      tableData2: [
        { id: 0, cusNo: '1', suppliername: 'Develop' },
        { id: 1, cusNo: '2', suppliername: 'Test' },
        { id: 2, cusNo: '3', suppliername: 'PM', },
      ],
      list: [],
      list1: [],
      dataOne: [
        { name: '熔炼炉号', value: 'rlRllNo' },
        { name: '创建时间', value: 'createTime' },
        { name: '炉况', value: 'rlRllLk' },
        { name: '钢包型号', value: 'rlGbxh' },
        { name: '脱氧前测氧', value: 'rlTyqcy' },
        { name: '脱氧后测氧', value: 'rlTyhcy' },
        { name: '开炉时间', value: 'rlKlsj' },
        { name: '钢包温度', value: 'rlGbwd' },
        { name: '钢包内钢水温度', value: 'rlGbngswd' },
        { name: '出钢时间', value: 'rlCksj' },
        { name: '出钢温度', value: 'rlCkwd' },
        { name: '镇定时间/秒s', value: 'rlZdsj' },
        { name: '脱氧材料重量/公斤', value: 'rlTyclzl' },
        { name: '新钢材重量/公斤', value: 'rlXglzl' },
        { name: '回炉钢料重量/公斤', value: 'rlHlglzl' },
        { name: '吹氩时间/秒', value: 'rlCysj' },
        { name: '上炉余水', value: 'rlSlys' },
        { name: 'Si', value: 'rlSi' },
        { name: '高Mn', value: 'rlGmn' },
        { name: '电解Mn', value: 'rlDjmn' },
        { name: '高Cr', value: 'rlGcr' },
        { name: '钙矽锰', value: 'rlGxm' },
        { name: '铝', value: 'rlAl' },
        { name: '微Cr', value: 'rlWcr' },
        { name: '镍', value: 'rlNi' },
        { name: '钼', value: 'rlMu' },
        { name: '钒', value: 'rlV' },
        { name: '其他', value: 'rlQtys' },
        { name: '制令单号', value: 'moNoRem' },
        { name: '化学成分-C', value: 'hxcfC' },
        { name: '化学成分-Si', value: 'hxcfSi' },
        { name: '化学成分-Mn', value: 'hxcfMn' },
        { name: '化学成分-P', value: 'hxcfP' },
        { name: '化学成分-S', value: 'hxcfS' },
        { name: '化学成分-Cr', value: 'hxcfCr' },
        { name: '化学成分-AL', value: 'hxcfAl' },
        { name: '化学成分-Ni', value: 'hxcfNi' },
        { name: '化学成分-Mo', value: 'hxcfMo' },
        { name: '化学成分-V', value: 'hxcfV' },
        { name: '化学成分-Cu', value: 'hxcfCu' },
        { name: '化学成分-其他', value: 'hxcfQt' },
        { name: '异常说明', value: 'tlRem' },
        { name: '作业人员', value: 'man' },
        { name: '脱氧材料批次', value: 'rlTyclzlpc' },
        { name: '新钢料批次', value: 'rlXglzlpc' },
        { name: '口水直径', value: 'rlKszj' },
      ],
      dataTwo: [
        { name: '热处理炉号', value: 'rclNo' },
        { name: '创建时间', value: 'createTime' },
        { name: '热处理方式', value: 'rclFs' },
        { name: '热处理炉型号', value: 'rclLxh' },
        { name: '热处理温度', value: 'rclWd' },
        { name: '保温时间(小时)', value: 'rclBwHour' },
        { name: '制令单号', value: 'moNoRem' },
        { name: '作业人员', value: 'man' },
      ],
      spinning: false,
      previewVisible: false,
      title: '',
      titleD: '',
      previewImage: '',
      spcDd: '',
      showVal: '',
      tableData: [],
      images2: [],
      images3: [],
      images: [],
      images2: [],
      visible: false,
      loading: false,
      row: {},
      tablePage: {
        currentPage: 1,
        pageSize: 5,
        total: 0,
      },
      obj: {},
      recordsdata: [],
      crrowid: '',
      recordsdataok: [],

    }
  },
  methods: {
    moment,

    // 勾选事件
    selectChangeEvent ({ checked, records, rowIndex }) {
      let that = this
      // this.recordsdata = records
      that['recordsdata' + that.crrowid] = records
      that.recordsdataok = records
      // localStorage.setItem('keyrecords', JSON.stringify(this.recordsdata));
      if (checked) {
        // this.$refs.xTable.setActiveRow(this.tableData[rowIndex])
        // this.tableData[rowIndex].paraValue3 = this.tableData[rowIndex].paraValue3
      } else {
        // this.tableData[rowIndex].paraValue3 = ''
      }
    },
    selectChangeAll ({ records, checked }) {
      // this.recordsdata = records
      that['recordsdata' + that.crrowid] = records
      that.recordsdataok = records
      // localStorage.setItem('keyrecords', JSON.stringify(this.recordsdata));
      if (checked) {
      } else {
        // this.tableData.forEach((i) => {
        //   i.paraValue3 = ''
        // })
      }
    },
    submit () {
      this.submitRlnoadd()
      if (this.titleD === '熔炼炉号') {
        this.submitRlnoadd()
        return
      }
      if (this.titleD === '热处理炉号信息') {
        this.submitRclnoadd()
        return
      }
    },
    // 保存熔炼炉号
    submitRlnoadd () {
      this.spinning = true
      const selectRecords = this.$refs.xTable2.getCheckboxRecords()
      // if (selectRecords == [] || selectRecords.length == 0) return this.$message.error('请至少选择一条数据！')
      // const arr = JSON.parse(JSON.stringify(this.tableData))
      const arr = JSON.parse(JSON.stringify(selectRecords))
  
      // arr.forEach((rowid) => {
   
      //   this.$refs.xTable2.setCheckboxRow([this.tableData2[2], this.tableData2[1]], true);
      // })

      let datarecord
      datarecord = []
      datarecord = JSON.parse(localStorage.getItem("keyrecords"))
      // this.$emit('child-say', datarecord);
      // this.$emit('child-say', this['recordsdata' + this.crrowid], this.crrowid);
      this.$emit('child-say', this.recordsdataok, this.crrowid);

      // location.reload();
      this.spinning = false
      this.visible = false
    },
    submitRclnoadd () {
      this.spinning = true
      rclnoadd(this.obj)
        .then(res => {
          this.$message.success(res.data)
          this.spinning = false
          this.visible = false
        })
        .catch(err => {
          this.spinning = false
          this.requestFailed(err)
        })

    },
    handleCancel () {
      this.previewVisible = false
    },
    async handlePreview (file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj)
      }
      this.previewImage = file.url || file.preview
      this.previewVisible = true
    },
    onClose () {
      this.list = []
      this.list1 = []
      this.images2 = []
      this.images3 = []
      this.images = []
      this.images2 = []
      this.spinning = false
      this.loading = false
      this.visible = false
    },
    create (model, row) {
   
      this.tableData2 = row.cusNameList
      this.crrowid = row.id
      this.obj = {}
      this.title = model.title
      this.showVal = model.val
      this.row = row
      this.visible = true
      let that = this
      setTimeout(() => {
        this.$nextTick(() => {

          if (!!that['recordsdata' + row.id]) {
            //  that['imageUrlbanner' + row.id ] =
            let itemdata = []
            that['recordsdata' + row.id].forEach((item, index) => {
              itemdata.push(item.id)
            })
            // let isyou = itemdata.indexOf(rowIndex)
            // if (isyou != -1) {
            //   this.nameDisabled = false
            // } else {
            //   this.nameDisabled = true
            // }

            // if (this.selectedPapers.has(paper.id))
            let recordscon = []
            that['recordsdata' + row.id].forEach((item) => {
              recordscon.push(item)
            })

         
            that.tableData2.forEach((rowid) => {
              if (itemdata.indexOf(rowid.id) != -1) {
                that.$refs.xTable2.setCheckboxRow(recordscon, true)
              }
            })


          }
          // that.$refs.xTable2.setCheckboxRow(this.$refs.xTable2.getRowById(1), true);
        })
      }, 0)
    },
    // 添加弹框
    // create (model, row) {
    //   this.obj = {}
    //   this.title = model.title
    //   this.showVal = model.val
    //   this.row = row
    //   this.visible = true
    //   let that = this
    //   // table.getRowById(paper.id)
    //   // table.setCheckboxRow(table.getRowById(rowid), true);
    //   setTimeout(() => {
    //     this.$nextTick(() => {
    //       // that.recordsdata.forEach((rowid) => {
   
    //       that.$refs.xTable2.setCheckboxRow(this.$refs.xTable2.getRowById(1), true);
    //     })
    //     // that.$refs.xTable2.setCheckboxRow([this.recordsdata[0], this.recordsdata[1]], true)
    //     // })
    //   }, 0)


    //   // this.recordsdata.forEach((rowid) => {


    //   // })
    // },
    get0301RllNo (row) {
      let that = this
      this.list = this.dataOne
      this.titleD = '熔炼炉号'
      getSpc(
        Object.assign({
          zcNo2: row.zcNo2,
          rllNo: row.value,
        })
      ).then((res) => {
        if (res) {
          this.obj = res.data
          if (res.data.rlTp !== '' && res.data.rlTp !== null) {
            let arr = res.data.rlTp.split(',')
            if (arr && arr.length > 0) {
              arr.forEach((e) => {
                that.images2.push(e)
              })
            }
          }
          if (res.data.ycTp !== '' && res.data.ycTp !== null) {
            let arr = res.data.ycTp.split(',')
            if (arr && arr.length > 0) {
              arr.forEach((e) => {
                that.images3.push(e)
              })
            }
          }
        }
      }).catch(err => that.requestFailed(err))
    },
    get08010101Lh (row) {
      let that = this
      this.titleD = '热处理炉号信息'
      this.list1 = this.dataTwo
      getSpc(
        Object.assign({
          zcNo2: row.zcNo2,
          rllNo: row.value,
        })
      ).then((res) => {
        if (res) {
          that.obj = res.data
          if (res.data.tp1 !== '' && res.data.tp1 !== null) {
            let arr = res.data.tp1.split(',')
            if (arr && arr.length > 0) {
              arr.forEach((e, index) => {
                that.images.push(e)
              })
            }
          }
          if (res.data.tp2 !== '' && res.data.tp2 !== null) {
            let arr = res.data.tp2.split(',')
            if (arr && arr.length > 0) {
              arr.forEach((e, index) => {
                that.images2.push(e)
              })
            }
          }
        }
      }).catch(err => that.requestFailed(err))
    },

    get0301ZcNo (row) {
      let that = this
      this.list = this.dataOne
      this.titleD = '熔炼炉号'
      getInfo({
        rlNo: row.paraValue3,
      }).then((res) => {
        if (res) {
          this.obj = res.data
          if (res.data.rlTp !== '' && res.data.rlTp !== null) {
            let arr = res.data.rlTp.split(',')
            if (arr && arr.length > 0) {
              arr.forEach((e) => {
                that.images2.push(e)
              })
            }
          }
          if (res.data.ycTp !== '' && res.data.ycTp !== null) {
            let arr = res.data.ycTp.split(',')
            if (arr && arr.length > 0) {
              arr.forEach((e) => {
                that.images3.push(e)
              })
            }
          }
        }
      }).catch(err => that.requestFailed(err))
    },

    get0801ZcNo (row) {
      let that = this
      this.titleD = '热处理炉号信息'
      this.list1 = this.dataTwo
      getInfo2({
        rlNo: row.paraValue3,
      }).then((res) => {
        if (res) {
          that.obj = res.data
          if (res.data.tp1 !== '' && res.data.tp1 !== null) {
            let arr = res.data.tp1.split(',')
            if (arr && arr.length > 0) {
              arr.forEach((e, index) => {
                that.images.push(e)
              })
            }
          }
          if (res.data.tp2 !== '' && res.data.tp2 !== null) {
            let arr = res.data.tp2.split(',')
            if (arr && arr.length > 0) {
              arr.forEach((e, index) => {
                that.images2.push(e)
              })
            }
          }
        }
      }).catch(err => that.requestFailed(err))
    },
    get1701ZcNo (row) {
      let that = this
      this.titleD = '热处理炉号信息'
      this.list1 = this.dataTwo
      getInfo3({
        rlNo: row.paraValue3,
      }).then((res) => {
        if (res) {
          that.obj = res.data
          if (res.data.tp1 !== '' && res.data.tp1 !== null) {
            let arr = res.data.tp1.split(',')
            if (arr && arr.length > 0) {
              arr.forEach((e, index) => {
                that.images.push(e)
              })
            }
          }
          if (res.data.tp2 !== '' && res.data.tp2 !== null) {
            let arr = res.data.tp2.split(',')
            if (arr && arr.length > 0) {
              arr.forEach((e, index) => {
                that.images2.push(e)
              })
            }
          }
        }
      }).catch(err => that.requestFailed(err))
    },
    inputRange () {

    }
  },
}
</script>
<style lang="less" scoped >
// ::v-deep .ant-descriptions-bordered .ant-descriptions-item-label {
//   color: black !important;
//   font-weight: 700 !important;
// }
// .input_value .ant-input {
//   // border: none;
// }
// .input_value /deep/ .ant-calendar-picker-input {
//   border: none;
// }
// .box2 /deep/ .ant-descriptions-bordered .ant-descriptions-item-label {
//   width: 150px;
// }
</style>

