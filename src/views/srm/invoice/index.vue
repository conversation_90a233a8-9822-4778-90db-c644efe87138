<template>
  <a-card :bordered="false">

    <!-- 打印区域 -->
    <div v-show="false">
      <div
        id="showPrint"
        style="width: 1000px"
      >
        <div
          :key="i"
          v-for="i in Len"
        >
          <div style="position: relative;">
            <a-row>
              <a-col
                :span="24"
                style="text-align: center"
              >
                <h3>三力士股份有限公司</h3>
              </a-col>
            </a-row>
            <a-row>
              <a-col
                :span="24"
                style="text-align: center"
              >
                <h4>进货开票明细表</h4>
              </a-col>
            </a-row>
            <a-row style="margin-bottom: 5px">
              <a-col
                :span="6"
                style="font-size: 10px; font-weight: 700"
              > 开票单号： {{ form.lzNo }}</a-col>
              <a-col
                :span="5"
                style="font-size: 10px; font-weight: 700"
              > 备注发票号码： {{ form.rem }}</a-col>
              <a-col
                :span="6"
                style="font-size: 10px; font-weight: 700"
              > 合计未税金额： {{ totalNoTax }}</a-col>
              <a-col
                :span="5"
                style="font-size: 10px; font-weight: 700"
              > 供应商： {{ cusName }}</a-col>
            </a-row>
            <img
              src="../../../../public/shenlin.png"
              style="position: absolute;top:280px;left:43%;z-index:-1;"
            >
          </div>
          <!-- 表格 -->
          <div>
            <table
              id="table"
              border="1"
              align="center"
              style="width: 100%; border-collapse: collapse"
            >
              <thead id="table2">
                <tr>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >序号</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >进货单号</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >采购单号</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >采购货品</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >采购货品名</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >数量</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >单位</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >采购单价</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >金额</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >库位</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >仓库收货</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >质量检验</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(item, index) in 23"
                  :key="item"
                >
                  <td>{{ index + 23 * (i - 1) + 1 }}</td>
                  <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].lzBilNo }}</td>
                  <td v-else></td>
                  <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].bilNo }}</td>
                  <td v-else></td>
                  <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].prdNo.slice(0, 28) }}</td>
                  <td v-else></td>
                  <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].prdName.slice(0, 10) }}</td>
                  <td v-else></td>
                  <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].qty }}</td>
                  <td v-else></td>
                  <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].unitName }}</td>
                  <td v-else></td>
                  <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].up }}</td>
                  <td v-else></td>
                  <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].amt }}</td>
                  <td v-else></td>
                  <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].whName }}</td>
                  <td v-else></td>
                  <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].storageName }}</td>
                  <td v-else></td>
                  <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].checkName }}</td>
                  <td v-else></td>
                </tr>
              </tbody>
            </table>
            <div style="height: 50px; text-align: center; line-height: 50px">
              <span>第 {{ i }} 页</span><span style="margin-left: 10px;">共 {{ printData.length }} 条</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <a-card :bordered="true">
      <div
        class="table-page-search-wrapper"
        id="ant_form"
      >
        <div class="header_invoice">
          <a-form-model
            layout="horizontal"
            ref="ruleForm"
            :rules="rules"
            :model="form"
          >
            <a-row :gutter="48">
              <a-col
                :md="6"
                :sm="24"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.date')"
                  prop="lzDd"
                >
                  <a-date-picker
                    style="width: 100%"
                    v-model="form.lzDd"
                    :disabled="disabled"
                    :placeholder="$t('purchase.placeholder.day')"
                  />
                </a-form-model-item>
              </a-col>
              <a-col
                :md="6"
                :sm="24"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.invoiiceNo')"
                  prop="lzNo"
                >
                  <a-input
                    :disabled="true"
                    v-model="form.lzNo"
                    :placeholder="$t('invoice.placeholder.invoiiceNo')"
                  />
                </a-form-model-item>
              </a-col>
              <a-col
                :md="6"
                :sm="24"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.customer')"
                  prop="cusNo"
                >
                  <my-selectList
                    url="/srm/cust/page"
                    :tableColumn="$Column.srmcus"
                    :form="$Form.srmcus"
                    :data="data"
                    :value="cusNameone"
                    :disabled="disabled"
                    name="cusNo"
                    @choose="choose($event)"
                    ref="selectList"
                    :placeholder="$t('invoice.placeholder.customer')"
                  >
                  </my-selectList>
                </a-form-model-item>
              </a-col>
              <a-col
                :md="6"
                :sm="24"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.rem')"
                >
                  <a-input
                    v-model="form.rem"
                    :placeholder="$t('invoice.placeholder.rem')"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="48">
              <a-col
                :span="6"
                :md="6"
                :sm="24"
                class="last_col"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.rem1')"
                >
                  <a-input
                    v-model="form.invNo"
                    :placeholder="$t('invoice.rem1')"
                    :readOnly="true"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-item
                  v-bind="formItemLayout"
                  :label="$t('prdiversion.bilType')"
                >
                  <a-select
                    style="width: 100%"
                    v-model="form.bilType"
                    :placeholder="$t('prdiversion.placeholder.bilType')"
                  >
                    <a-select-option
                      v-for="(val, index) in documentCate"
                      :key="index"
                      :value="val.value"
                    ><span style="margin-right:8px;">{{val.value}}</span><span>{{ val.label}}</span>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <!-- <a-col
                :span="6"
                style="text-align: right;float:right;"
              >

                <a-button
                  type="primary"
                  @click="uploadpic()"
                >上传</a-button>
                <a-button
                  style="margin-left:10px;margin-right:10px;"
                  type="primary"
                  @click="seepic()"
                >查看</a-button>
                <a-button
                  type="primary"
                  @click="downInvoice()"
                >下载</a-button>
              </a-col> -->
              <a-col :span="6">
                <a-form-item
                  v-bind="formItemLayout"
                  :label="$t('inquiryoffer.paytype')"
                >
                  <a-select
                    style="width: 100%"
                    v-model="form.payType"
                  >
                    <a-select-option
                      v-for="(val, index) in sexList"
                      :key="index"
                      :value="val.value"
                    >{{
                        val.label
                      }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item
                  v-bind="formItemLayout"
                  :label="$t('发票附件')"
                >
                  <div style="position: relative;">
                    <a-input
                      :readOnly="true"
                      v-model="invoice"
                      :placeholder="$t('发票附件')"
                    />
                    <div style="position:absolute;top:0px;right:65px;">
                      <a-button
                        :loading="loading"
                        type="primary"
                        size='small'
                        @click="seepic()"
                      >查看
                      </a-button>
                    </div>
                    <div style="position:absolute;top:0px;right:2px;">
                      <a-button
                        :loading="loading"
                        type="primary"
                        size='small'
                        @click="uploadpic()"
                      >上传
                      </a-button>
                    </div>

                  </div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <!-- <a-col
                :span="6"
                :md="6"
                :sm="24"
                class="last_col"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('付款附件')"
                >
                  <a-input
                    v-model="form.payAttach"
                    :placeholder="$t('付款附件')"
                  />
                </a-form-model-item>
              </a-col> -->
              <!-- <a-col
                :span="6"
                :md="6"
                :sm="24"
                class="last_col"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('付款金额')"
                >
                  <a-input
                    :disabled="true"
                    v-model="form.payAttach"
                    :placeholder="$t('付款金额')"
                  />
                </a-form-model-item>
              </a-col> -->
              <a-col
                :span="8"
                :md="6"
                :sm="24"
                class="last_col"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t(wenzi)"
                >
                  <a-input
                    :disabled="true"
                    v-model="form.amtTotal"
                    :placeholder="$t('付款金额')"
                  />
                </a-form-model-item>
              </a-col>

              <a-col
                :md="18"
                :sm="24"
                style="text-align: right;float:right;"
              >
                <div>
                  <a-button
                    style="text-align:right;margin-right:10px;"
                    type="primary"
                    @click="monthpay()"
                  
                  >月付款明细</a-button>
                  <a-button
                    style="text-align:right;"
                    type="primary"
                    @click="handReset()"
                    v-permission="srm_invoice_reset"
                  >{{ $t('public.add') }}</a-button>
                  <a-button
                    style="margin-left: 10px"
                    type="danger"
                    :disabled="save"
                    @click="handDFile($event)"
                    v-permission="srm_invoice_save"
                  >{{$t('public.save')}}</a-button>
                  <a-button
                    style="margin-left: 10px"
                    type="primary"
                    @click="handleFind()"
                    v-permission="srm_invoice_search"
                  >{{ $t('public.query') }}</a-button>
                  <a-button
                    style="margin-left: 10px"
                    type="primary"
                    @click="handleDel()"
                    v-permission="srm_invoice_del"
                  >{{ $t('public.delete') }}</a-button>
                  <a-button
                    style="margin-left: 10px"
                    type="primary"
                    v-print="'showPrint'"
                    v-permission="srm_invoice_print"
                  >{{ $t('public.print') }}</a-button>
                  <a-button
                    style="margin-left: 10px"
                    type="primary"
                    @click="handleExport()"
                    v-permission="srm_invoice_export"
                  >{{ $t('public.export') }}</a-button>
                </div>
              </a-col>
            </a-row>
          </a-form-model>
        </div>
      </div>
    </a-card>

    <vxe-toolbar>
      <template v-slot:buttons>
        <a-button
          type="primary"
          :disabled="save"
          icon="plus"
          @click="handleAdd()"
          v-permission="srm_invoice_add"
        >{{ $t('public.addTo') }}</a-button>
      </template>
    </vxe-toolbar>
    <vxe-table
      border
      resizable
      stripe
      highlight-current-row
      show-overflow
      highlight-hover-row
      export-config
      ref="xTable"
      size="mini"
      :loading="loading"
      :data="tableData"
      :height="tableHeight"
      :keyboard-config="{ isArrow: true }"
      :seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
      :edit-config="{ trigger: 'click', mode: 'row' }"
      v-if="isRefresh"
    >
      <!-- height="580" -->
      <div>
        <vxe-table-column
          fixed="left"
          type="seq"
          title="delivery.bilItm"
          align="center"
          :width="50"
        ></vxe-table-column>
        <vxe-table-column
          field="lzBilNo"
          title="invoice.lzBilNo"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="bilNo"
          title="invoice.source1"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="bilTypeName"
          title="prdiversion.bilType"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="prdNo"
          title="invoice.prdNo"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="prdName"
          title="invoice.prdName"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="unitName"
          title="invoice.unit"
          align="center"
          :width="100"
        > </vxe-table-column>
        <vxe-table-column
          field="qty"
          title="invoice.qty"
          align="center"
          :width="120"
        > </vxe-table-column>
          <vxe-table-column
          field="upInTax"
          title="含税单价"
          align="center"
          :width="120"
        > </vxe-table-column>
          <vxe-table-column
          field="amtInTax"
          title="含税金额"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="up"
          title="invoice.up"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="taxId"
          title="扣税类别"
          align="center"
          :width="120"
        > 
        </vxe-table-column>
        <vxe-table-column
          field="amtnNet"
          title="invoice.amtnSnet"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="tax"
          title="invoice.taxs"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="amt"
          title="invoice.amt"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="taxRto"
          title="invoice.taxRto"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="amtFp"
          title="invoice.amtFp"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="amtUn"
          title="invoice.AmtUn"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="payTypeName"
          title="inquiryoffer.paytype"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          title="操作"
          align="center"
          fixed="right"
          :width="100"
          v-if="isVisible"
        >
          <template v-slot="scope">
            <a-button
              size="small"
              @click="delTableColwn(scope.row)"
            >{{ $t('public.delete') }}</a-button>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="curName"
          title="prdiversion.curId"
          align="center"
          :width="120"
        > </vxe-table-column>
        <!-- <vxe-table-column
          field="contractNo"
          title="entrustment.contractNo"
          align="center"
          :width="120"
        > </vxe-table-column> -->
        <vxe-table-column
          field="contractNo"
          align="center"
          title="entrustment.contractNo"
          :width="150"
        >
          <template slot-scope="scope">
            <el-select
              size="small"
              value-key="fileId"
              v-model="scope.row.flag"
              @focus="getDatalist(scope.row)"
              @change="downloadtwo(scope.row)"
              placeholder="请选择"
              filterable
              allow-create
            >
              <el-option
                v-for="val in scope.row.projectJbrUserlist"
                :key="val.fileId"
                :label="val.fileName"
                :value="val"
              >
                <!-- <div>{{ val.fileName }}</div> -->
              </el-option>
            </el-select>
          </template>
        </vxe-table-column>
        <!-- <vxe-table-column
          field="contractNo"
          title="entrustment.contractNo"
          align="center"
          :width="120"
        >
          <template slot-scope="scope">
            <vxe-button
              type="text"
              @click="contQuery(scope.row)"
            >
              合同
           
            </vxe-button>
          </template>
        </vxe-table-column> -->
        <!-- {{ scope.row.contractNo }} -->
        <!-- <vxe-table-column
          field="enclosure"
          title="inquiryoffer.enclosure"
          align="center"
          :width="110"
        >
          <template slot-scope="scope">
            <vxe-button
              type="text"
              @click="uploadpic(scope.row)"
            >
              上传
            </vxe-button>
          </template>
        </vxe-table-column> -->
      </div>
    </vxe-table>
    <div class="footer_invoice">
      <a-card :bordered="true">
        <div class="table-page-search-wrapper">
          <a-form-model
            layout="horizontal"
            ref="ruleForm"
            :rules="rules"
            :model="form"
          >
            <a-row :gutter="48">
              <a-col
                :md="6"
                :sm="24"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.totalQty')"
                >
                  <a-input
                    :disabled="true"
                    v-model="totalForm.totalQty"
                    :placeholder="$t('invoice.totalQty')"
                  />
                </a-form-model-item>
              </a-col>
              <a-col
                :md="6"
                :sm="24"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.totalNoTax')"
                >
                  <a-input
                    :disabled="true"
                    v-model="totalForm.totalNoTax"
                    :placeholder="$t('invoice.totalNoTax')"
                  />
                </a-form-model-item>
              </a-col>
              <a-col
                :md="6"
                :sm="24"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.totalTax')"
                >
                  <a-input
                    :disabled="true"
                    v-model="totalForm.totalTax"
                    :placeholder="$t('invoice.totalTax')"
                  />
                </a-form-model-item>
              </a-col>
              <a-col
                :md="6"
                :sm="24"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.totalAmt')"
                >
                  <a-input
                    :disabled="true"
                    v-model="totalForm.totalAmt"
                    :placeholder="$t('invoice.totalAmt')"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </div>
      </a-card>
    </div>
    <a-modal
      title="月付款明细"
      destroyOnClose
      width="50%"
      height="50%"
      :visible.sync="visibleBargain"
      :confirmLoading="confirmLoading"
      @cancel="handleCancelBargain"
    >
    <div class="table-page-search-wrapper">
    <a-form layout="inline">
          <a-row>
        <a-col :span="7">
          <a-form-model-item
            v-bind="formItemLayout"
            label="日期"
            prop="cusNo"
          >
          <a-month-picker
            v-model="monthlast"
            @change="onChange"
             format="YYYY-MM"
            placeholder="请选择日期"
            style="width:95%;"
          />
          </a-form-model-item>
        </a-col>
        <a-col
          :md="9"
          :sm="24"
        >
          <a-form-model-item
            v-bind="formItemLayout"
            :label="$t('invoice.customer')"
            prop="cusNo"
          >
            <my-selectList
              url="/srm/cust/page"
              :tableColumn="$Column.srmcus"
              :form="$Form.srmcus"
              allowClear
              :value="cusNametwo"
              :data="datatwo"
              :disabled="disabled"
              name="cusNo"
              @choose="choosetwo($event)"
              ref="selectList"
              :placeholder="$t('invoice.placeholder.customer')"
              style="width:95%;"
            >
            </my-selectList>
          </a-form-model-item>
        </a-col>
          <!-- <a-col :span="6">
                    <a-form-item label="部门名称">
                      <a-input v-model="dutyDepName" placeholder="请输入部门名称" style="width: 95%"  />
                    </a-form-item>
          </a-col> -->
     <a-col :span="6">
     <a-button
            style="display:inline-block"
            type="primary"
            @click="xunJiapagequery"
          >查询</a-button>
     </a-col>
    </a-row>
    </a-form>
    </div>
        
   

      <vxe-table
        border
        resizable
        stripe
        size="mini"
        highlight-current-row
        show-overflow
        highlight-hover-row
        ref="xTableBargain"
        :checkbox-config="{ strict: true}"
        :loading="loading"
        :data="tableDatazeren"
        :keyboard-config="{ isArrow: true }"
       
        :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true}"
      >
       <!-- @radio-change="zerenradio" -->
        <!-- <vxe-table-column
          type="radio"
          align="center"
          :width="50"
        ></vxe-table-column> -->
        <vxe-table-column
          field="rpDd"
          title="付款日期"
          align="center"
        ></vxe-table-column>
        <vxe-table-column
          field="amtnChk"
          title="付款金额"
          align="center"
        ></vxe-table-column>
      </vxe-table>
      <!-- <vxe-pager
        :loading="loading"
        :current-page="tablePagedep.currentPage"
        :page-size="tablePagedep.pageSize"
        :total="tablePagedep.total"
        :page-sizes="[5, 10, 20, 100, 200, 500]"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChangedep"
      > </vxe-pager> -->
      <div style="height:10px;"></div>

      <template slot="footer">
        <!-- <a-button
          key="ok"
          type="primary"
          @click="saveBargain()"
        >确定</a-button> -->
          <a-button
            style="display:inline-block"
            key="cancel"
            @click="handleCancelBargain"
          >{{ $t('public.cancel') }}</a-button>
      </template>
    </a-modal>
    <Drawer
      ref="Drawer"
      :data="propsData"
      @getList="getList"
    />
    <Modal
      @getSaveList="getSaveList"
      @viewPicture="viewPicture"
      :cusNo="cusNo"
      ref="modal"
    />
    <editDrawer
      ref="editDrawer"
      @getRow="getRow"
    />
    <print ref="print" />
    <Export ref="Export" />

    <!-- 上传 -->
    <Upload
      ref="Upload"
      @insertEvent="insertEvent"
      @seeFile="seeFile"
    />
    <ModalPic ref="ModalPic" />
    <ContQuery ref="ContQuery" />
  </a-card>
</template>

<script>
import { mapState } from 'vuex'
import MySelectList from '@/components/MySelectList'
import { fetchCust, save, del, findBody, findInvNo, fetchBilNo,PoLiZhMfMon} from '@/api/srm/invoice'
import { findBilType } from '@/api/srm/prdiversion'
import { paytypeFind } from '@/api/srm/inquiryoffer'
import { contractQuery } from '@/api/srm/entrustment'
import Modal from './modal'
import Drawer from './drawer'
import editDrawer from './editdrawer'
import print from './print'
import Export from './export'
import moment from 'moment'
import XEUtils from 'xe-utils'
import { Alert } from 'element-ui'
import Upload from './Upload'
import { fetchList, down, isHaveMac } from '@/api/srm/zlxz'
import axios from 'axios'
import ModalPic from './ModalPic'
import ContQuery from './ContQuery'
export default {
  components: {
    Drawer,
    MySelectList,
    Modal,
    editDrawer,
    print,
    Export,
    Upload,
    ModalPic,
    ContQuery
  },
  data () {
    return {
      srm_invoice_reset: 'srm_invoice_reset',
      srm_invoice_save: 'srm_invoice_save',
      srm_invoice_search: 'srm_invoice_search',
      srm_invoice_del: 'srm_invoice_del',
      srm_invoice_print: 'srm_invoice_print',
      srm_invoice_export: 'srm_invoice_export',
      srm_invoice_add: 'srm_invoice_add',
      tableData: [],
      columns: [],
      propsData: [],
      delPssList: [],
      printData: [],
      loading: false,
      disabled: false,
      isVisible: true,
      save: false,
      isRefresh: true,
      tablePage: {
        currentPage: 1,
        pageSize: 100,
        total: 0
      },
      data: '',
      cusNo: '',
      cusNotwo:'',
      id1Tax: '',
      cusName: '',
      cusNameone: '',
      cusNametwo: '',
      totalNoTax: null,
      Len: null,
      form: {
        lzNo: '',
        cusNo: '',
        invNo: '',
        rem: '',
        lzDd: moment(new Date(), 'YYYY-MM-DD'),
        bilType: null,
        payType: null,
        payAttach: null,
        amtTotal: null
      },
      totalForm: {},
      obj: {},
      queryParam: {},
      formItemLayout: {
        labelCol: {
          xl: { span: 7 },
          lg: { span: 12 },
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xl: { span: 17 },
          lg: { span: 15 },
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 15 },
        },
      },
      rules: {
        lzDd: [{ required: true, message: this.$t('invoice.placeholder.date') }],
        cusNo: [{ required: true, message: this.$t('invoice.placeholder.cusNo'), trigger: 'blur' }],
        lzNo: [{ required: true, message: this.$t('invoice.placeholder.lzNo'), trigger: 'blur' }],
      },
      sexList: [],
      documentCate: [],
      tableHeight: window.innerHeight - 375,
      payselect: null,
      imageUrlList: [],
      invoice: '',
      imageUrlIn: [],
      detailAll: [],
      wenzi: 'contractinquiry.paymoney',
      imageUrlIn2:[],
      visibleBargain:false,
      tableDatazeren:[],
       tablePagedep: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      monthlast:null,
      confirmLoading: true,
      monthstart:'',
      datatwo:'',
      startDd:null,
       endDd:null,
    }
  },
  watch: {
    'form.cusNo': {
      handler (newValue, oldValue) {
        this.cusNo = newValue
      },
      deep: true, // 对象内部的属性监听，也叫深度监听
      immediate: true, // 立即监听
    },
    'form.lzNo' (val) {
      if (this.form.lzNo.indexOf('LP') != -1) {
        this.wenzi = '发票付款金额'
      } else if (this.form.lzNo == '' || this.form.lzNo == null) {
        this.wenzi = '付款金额'
      } else {
        this.wenzi = '进货单付款金额'
      }
      // if (val) {
      //   this.queryParam.staDd = moment(val).format('YYYY-MM-DD 12:00:00')
      // } else {
      //   this.queryParam.staDd = null
      // }
    },
    monthlast (val) {
      if (val) {
        this.monthlast = moment(val).format('YYYY-MM')
      } else {
        this.monthlast = null
      }

    },
    // 'monthlast' (val) {

    // }
  },
  computed: {
    ...mapState({
      info: (state) => state.user,
    }),
  },
  created () {
    if (this.info.info.supNo) {
      this.cusNo = this.info.info.supNo
      this.cusNotwo = this.info.info.supNo
      this.data = this.info.info.supName
      this.datatwo = this.info.info.supName
       this.cusNameone = this.info.info.supName
       this.cusNametwo = this.info.info.supName
      this.form.cusNo = this.info.info.supNo
      this.getId1Tax()
      this.getLzNo(this.info.info.supNo)
      this.disabled = true
    }
  },
  mounted () {
    this.sexList = []
    paytypeFind().then((res) => {
      res.data.forEach((item, index) => {
        let sexarry = { label: item.name, value: index }
        this.sexList.push(sexarry)
      })
      let catearryall2 = { label: '全部', value: '' }
      this.sexList.push(catearryall2)
    })
    this.documentCate = []
    findBilType().then((res) => {
      res.data.forEach((item, index) => {
        let catearry = { label: item.bilTypeName, value: item.bilType }
        this.documentCate.push(catearry)
      })
      let catearryall = { label: '全部', value: '' }
      this.documentCate.push(catearryall)
    })
    window.onresize = () => {
      return (() => {
        this.tableHeight = window.innerHeight - 375
      })()
    }
  },
  methods: {
    moment,
    xunJiapagequery(){
      
      if(this.monthlast){
  this.startDd = this.monthlast + '-01'
      }else{
        this.startDd = null
      }
    // endDd
    let lastDay
    if(this.startDd){
      var date = new Date(this.startDd), y = date.getFullYear(), m = date.getMonth();
      var firstDay = new Date(y, m, 1);
       lastDay = new Date(y, m + 1, 0);
      this.endDd = moment(lastDay).format('YYYY-MM-DD')
    }else{
      lastDay = null
      this.endDd = null
    }
       this.querymoney()
    },
    querymoney(){
      PoLiZhMfMon({
         startDd:this.startDd,
         endDd:this.endDd,
         cusNo:this.cusNotwo
         })
        .then((res) => {
          this.tableDatazeren = res.data
        })
        .catch((err) => {
          this.requestFailed(err)
        })
    },
    onChange(date, dateString) {
   
      this.monthlast = dateString
     this.monthstart = this.monthlast + '-01'

      

    },
     handlePageChangedep({ currentPage, pageSize }) {
      this.tablePagedep.currentPage = currentPage
      this.tablePagedep.pageSize = pageSize
      this.querymoney()
    },
    monthpay(){
      this.visibleBargain = true

    },
    handleCancelBargain(){
      this.visibleBargain = false

    },
    saveBargain(){
    
      this.visibleBargain = false
    },
    getDatalist (data) {
      contractQuery({
        bilNo: data.bilNo,
        bilItm: data.bilItm,
        estItm: data.estItm
      }).then((res) => {
        this.projectJbrUserlist = res.data
        this.tableData.forEach((i) => {
          if (data.wyID == i.wyID) {
            if (!!i.projectJbrUserlist) {
              // i.projectJbrUserlist.flag = ''
              // i.flag = ''
              // i.projectJbrUserlist.forEach((item) => {
              //   item.flag = ''
              // })
            }
            i.projectJbrUserlist = this.projectJbrUserlist
          }

        })
        this.tableData.sort()

        // res.data.forEach((item, index) => {
        //   let sexarry = { label: item.name, value: index }
        //   this.sexList.push(sexarry)
        // })
        // let catearryall2 = { label: '全部', value: '' }
        // this.sexList.push(catearryall2)
      }).catch(err => this.requestFailed(err))
    },
    downloadtwo (row) {
      this.tableData.forEach((i) => {
        if (row.wyID == i.wyID) {
          // this.fileNameload = row.projectJbrUserlist.flag.fileName
          this.fileNameload = row.flag.fileName
          // this.tableData.sort()
        }
      })

      let url
      url = '/srm/ftp/downLoad'
      axios({
        method: 'get',
        url: url,
        responseType: 'blob',
        params: {
          fileName: this.fileNameload,
          bilId: row.bilId,
          bilNo: row.bilNo,
          bilItm: row.bilItm
        }
      })
        .then((response) => {
          const blob = new Blob([response])
          if (blob.size != '0') {
            const blobUrl = window.URL.createObjectURL(blob)
            this.download(blobUrl, this.fileNameload)
          } else {
            this.$message.warning('没有合同')
          }

          // const blob = new Blob([response.data])
          // if (blob.size != '0') {
          //   const blobUrl = window.URL.createObjectURL(blob)
          //   this.download(blobUrl, response.headers["content-disposition"].split('=')[1])
          // } else {
          //   this.$message.warning('没有合同')
          // }
        })
        .catch((err) => {
          this.loading = false
          this.$message.warning('没有合同')
          // this.requestFailed(err)
        })
    },
    seepic () {
      this.$refs.ModalPic.create({ title: '查看' }, this.imageUrlIn2)
    },
    contQuery (row) {
      let url
      url = '/srm/ftp/downLoad'
      axios({
        method: 'get',
        url: url,
        responseType: 'blob',
        params: {
          bilId: row.bilId,
          bilNo: row.bilNo,
        }
      })
        .then((response) => {
          const blob = new Blob([response])

          if (blob.size != '0') {
            const blobUrl = window.URL.createObjectURL(blob)
            this.download(blobUrl, row.fileName)
          } else {
            this.$message.warning('没有合同')
          }
        })
        .catch((err) => {
          this.loading = false
          this.requestFailed(err)
        })
    },
    // contQuery (row) {
    //   let url
    //   url = '/admin/sys-file/file-fm/461924562dde483bad638f25a93fd8a0.png'
    //   window.open(url, '_self');
    // },
    // 下载pdf
    downInvoice () {
      // isHaveMac({
      //   ip: returnCitySN['cip'],
      // })
      //   .then((res) => {
      //     // 先判断是否有绑定ip地址
      //     if (res.data === '0') {
      //       // 未绑定ip,提示
      //       const that = this
      //       this.$confirm({
      //         title: this.$t('public.del.title'),
      //         content: this.$t('zlsq.isHaveMac'),
      //         okText: this.$t('public.sure'),
      //         okType: 'danger',
      //         cancelText: this.$t('public.cancel'),
      //         onOk () {
      //           that.requestDwnFile(row)
      //         },
      //         onCancel () { },
      //       })
      //     } else if (res.data === '1') {
      //       // 直接下载
      //       this.requestDwnFile(row)
      //     }
      //   })
      //   .catch((err) => {
      // this.requestFailed(err)
      // })

      this.requestFailed()
    },
    requestDwnFile (row) {
      const hide = this.$message.loading(this.$t('public.waitDown'), 0)
      let obj = {
        ip: '',
        ...row,
      }
      obj.ip = returnCitySN['cip']
      down(obj)
        .then((res) => {
          if (res.data) {
            if (res.data.flag) {
              let url = res.data.returnUrl
              this.downFile(url, res.data.fileName, hide)
            } else {
              setTimeout(hide, 5)
              this.$message.error(res.data.message)
            }
          }
        })
        .catch((err) => {
          setTimeout(hide, 5)
          this.requestFailed(err)
        })
    },
    downFile (url, fileName, hide) {
      axios({
        method: 'get',
        url: url,
        responseType: 'blob',
      })
        .then((response) => {
          setTimeout(hide, 5)
          this.tablePage.currentPage = 1
          this.getList()
          this.$message.success(this.$t('public.downSuccess'))
          const blob = new Blob([response])
          const blobUrl = window.URL.createObjectURL(blob)
          this.download(blobUrl, fileName)
        })
        .catch((err) => {
          setTimeout(hide, 5)
          this.loading = false
          this.requestFailed(err)
        })
    },
    download (blobUrl, fileName) {
      let a = document.createElement('a')
      a.style.display = 'none'
      a.download = fileName
      a.href = blobUrl
      a.click()
    },
    // 上传
    insertEvent (row) { },
    // 批量添加
    seeFile (imageUrlList) {
      // this.imageUrlList = imageUrlList

      imageUrlList.forEach((item, index) => {
        this.imageUrlIn.push(item)
        this.imageUrlIn2.push(item)
      })

      // this.tableData.forEach((item, index) => {
      //   if (this.formD.wyID === item.wyID) {
      //     item.imageUrlList = this.formD.imageUrlList
      //   }
      // })
      // this.tableData.imageUrlList = imageUrlList

    },
    viewPicture (imageUrlList) {
      // this.imageUrlList = imageUrlList
      this.imageUrlIn2 = []
      imageUrlList.forEach((item2, index2) => {
        this.imageUrlIn2.push(item2)
      })
      // 数组去重
      let result = [];
      let obj2 = {};
      this.detailAll = this.imageUrlIn2
      for (let d = 0; d < this.detailAll.length; d++) {
        if (!obj2[this.detailAll[d].url]) {
          result.push(this.detailAll[d]);
          obj2[this.detailAll[d].url] = true;
        }
      }
      this.detailAll = result
      this.imageUrlIn2 = this.detailAll

    },
    // 批量文件
    uploadpic () {
      // let formD = {
      //   fiDd: moment(this.queryParam.fiDd).format('YYYY-MM-DD HH:mm:ss'),
      //   flNo: this.queryParam.flNo,
      //   usr: this.queryParam.usr,
      //   dep: this.queryParam.dep,
      //   depNo: this.queryParam.depNo,
      //   usrNo: this.queryParam.usrNo,
      //   idxNomf: this.queryParam.idxNomf,
      //   idxName: this.form.idxName
      // }
      this.$refs.Upload.create(
        {
          title: '上传'
        },
      )
    },
    // 获取扣税类别
    getId1Tax () {
      fetchCust({
        current: 1,
        size: 10,
        cusNo: this.form.cusNo,
      }).then((res) => {
        this.id1Tax = res.data.records[0].id1Tax
      }).catch(err => this.requestFailed(err))
    },
    getLzNo (cusNo) {
      fetchBilNo({ cusNo })
        .then((res) => {
          this.form.lzNo = res.data
        })
        .catch((err) => {
          this.requestFailed(err)
        })
    },
    choose (obj) {
      this.form[obj.obj.name] = obj.obj.data.cusNo
      this.cusNameone = obj.obj.value
      this.getId1Tax()
      fetchBilNo({ cusNo: obj.obj.data.cusNo })
        .then((res) => {
          if (res.data === 'F') {
            this.form.lzNo = null
            this.$message.info('该客户未设置立账方式')
          } else {
            this.form.lzNo = res.data
          }
        })
        .catch((err) => {
          this.requestFailed(err)
        })
      // this.getLzNo(obj.obj.data.cusNo)
    },
    choosetwo(obj){
      if(obj.obj.data){
        this.cusNotwo = obj.obj.data.cusNo
       this.cusNametwo = obj.obj.value
      }else{
        this.cusNotwo = null
       this.cusNametwo = null
      }
 
    },
    handleFind () {
      this.delPssList = []
      this.$refs.modal.create({
        title: '单据查询',
      }, this.form)
    },

    // 根据编号查询已保存数据
    getSaveList (obj) {
      this.delPssList = []
      this.printData = []
      this.obj = obj
      this.id = obj.lzNo
      this.give = false
      this.disabled = true
      this.loading = true
      if (obj.invNo) {
        this.isVisible = false
        this.save = true
      } else {
        this.isVisible = true
        this.save = false
      }
      this.form = {
        lzNo: obj.lzNo,
        cusNo: obj.cusNo,
        invNo: obj.invNo,
        lzDd: moment(obj.lzDd),
      }
      if (obj.rem != null) {
        if (obj.rem.indexOf('备注发票号:') > -1) this.form.rem = obj.rem.split(':')[1]
      }
      this.totalForm = {}
      this.tableData = []
      this.propsData = this.tableData
      this.form.amtTotal = obj.amtTotal

      findBody(
        Object.assign({
          current: 1,
          size: -1,
          lzNo: obj.lzNo,
          cusNo: obj.cusNo,
          bilId: obj.bilId,
        })
      )
        .then((res) => {
          this.give = false
          this.tableData = res.data.records
          this.tableData.forEach((i,index) => {
            i.wyID = i.bilId + i.bilItm + i.bilNo
            i.id = index + 1
            i.projectJbrUserlist = this.projectJbrUserlist
            i.projectJbrUserlist = ''
            // taxId   1 不计税 2 应税内含 3 应税外加
            if (i.taxId == '1') {
              i.taxId = '不计税'
            } else if (i.taxId == '2') {
              i.taxId = '应税内含'
            } else if (i.taxId == '3') {
              i.taxId = '应税外加'
            }
          })
          this.propsData = this.tableData
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
          if (res.data.records.length === 0) {
            this.totalForm = {}
            this.Len = 0
            return
          }
          if (res.data.records.length !== 0) {
            if (this.info.info.supNo == null) {
              this.data = res.data.records[0].cusName
              this.form.cusNo = res.data.records[0].cusNo
            }
            this.chuPrintData(res.data.records)
            this.computerTotal()
          }
        })
        .catch((err) => {
          this.totalForm = {}
          this.tableData = []
          this.propsData = this.tableData
          this.loading = false
          this.requestFailed(err)
        })
    },
    // 添加表身数据
    getList (data) {
      if (data == [] || data.length == 0) {
        this.loading = false
        this.tableData = data
        if (this.tableData.length) {
          this.tableData.forEach((i,index) => {
            i.id = index + 1
            // taxId   1 不计税 2 应税内含 3 应税外加
            if (i.taxId == '1') {
              i.taxId = '不计税'
            } else if (i.taxId == '2') {
              i.taxId = '应税内含'
            } else if (i.taxId == '3') {
              i.taxId = '应税外加'
            }
          })
        }
      } else {
        this.loading = true
        this.save = false
        this.tableData.push(...data)
        this.propsData = this.tableData
        this.tableData.forEach((i,index) => {
          i.wyID = i.bilId + i.bilItm + i.bilNo
          i.id = index + 1
          i.projectJbrUserlist = this.projectJbrUserlist
          i.projectJbrUserlist = ''
          if (i.taxId == '1') {
            i.taxId = '不计税'
          } else if (i.taxId == '2') {
            i.taxId = '应税内含'
          } else if (i.taxId == '3') {
            i.taxId = '应税外加'
          }
          // if (i.payTypeName) {
          //   if (i.payTypeName == '0') {
          //     i.payTypeName = '月结'
          //   } else if (i.payTypeName == '1') {
          //     i.payTypeName = '其他'
          //   } else if (i.payTypeName == '2') {
          //     i.payTypeName = '分期付款'
          //   }
          // }
          // if (i.curName) {
          //   if (i.curName == '0') {
          //     i.curName = '人民币'
          //   } else if (i.curName == '1') {
          //     i.curName = '欧元'
          //   } else if (i.curName == '2') {
          //     i.curName = '美元'
          //   }
          // }
          // if (i.bilTypeName) {
          //   if (i.bilTypeName == '0') {
          //     i.bilTypeName = '原料仓'
          //   } else if (i.bilTypeName == '1') {
          //     i.bilTypeName = '办公用品'

          //   } else if (i.bilTypeName == '2') {
          //     i.bilTypeName = '生产原料'
          //   } else if (i.bilTypeName == '3') {
          //     i.bilTypeName = '全部'
          //   }
          // }
        })
        this.computerTotal()
        this.loading = false
      }
      this.Len = 0
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
    },
    // 新增重置
    handReset () {
      this.imageUrlIn = []
      this.imageUrlIn2 = []
      this.disabled = false
      this.save = false
      this.isVisible = true
      // this.getForm()
      // if (this.info.info.supNo) {
      //   this.getLzNo(this.info.info.supNo)
      // }
      // if(){

      // }
      this.form.invNo = ''
      this.form.rem = ''
      this.form.lzDd = moment(new Date(), 'YYYY-MM-DD')
      // this.tableData = []
      this.propsData = []
      this.printData = []
      this.delPssList = []
      this.totalForm = {}
      this.Len = 0
      // this.form.lzNo = []
      // if (this.info.info.supNo) {
      //   this.getLzNo(this.info.info.supNo)
      // }
      if (this.tableData.length == 0) {
      }
      else {
        this.form.lzNo = []
        this.getLzNo(this.cusNo)
        this.tableData = []
      }
      this.form.amtTotal = null
    },
    // 添加
    handleAdd () {
      this.printData = []
      if (this.form.cusNo && this.form.lzDd && this.form.lzNo) {
        // if (this.form.cusNo) {
        findInvNo({ lzNo: this.form.lzNo })
          .then(res => {
            if (res.data == null || res.data == '') {
              let form = {
                lzDd: moment(this.form.lzDd).format('YYYY-MM-DD HH:mm:ss'),
                lzNo: this.form.lzNo,
                cusNo: this.form.cusNo,
                invNo: this.form.invNo,
                rem: this.form.rem
              }
              this.$refs.Drawer.create({ title: this.$t('public.add') }, form)
            } else {
              this.form.invNo = res.data.invNo
              this.$notification['warn']({
                message: this.$t('public.message'),
                description: this.$t('submission.notDel'),
              })
              return false
            }
          }).catch(err => this.requestFailed(err))
      } else {

        this.$notification['warning']({
          // this.$notification['error']({
          // message: this.$t('public.message'),
          // description: this.$t('submission.customerCode'),
          message: '提示',
          description: '请先输入日期，开票单号和客户代号！',
        })
      }
    },
    // cellDBLClickEvent ({ row, rowIndex }) {
    //   this.$refs.editDrawer.edit({ title: this.$t('public.Detailed') }, row, rowIndex, { id1Tax: this.id1Tax })
    // },
    getRow ({ form, rowIndex }) {
      this.$set(this.tableData, rowIndex, form)
      this.computerTotal()
    },
    // 保存
    handDFile (e) {
      let target = e.target
      if (target.nodeName == 'SPAN' || target.nodeName == 'I') {
        target = e.target.parentNode
      }
      target.blur()
      const that = this
      let flag = false


      for (var j = 0; j < this.tableData.length; j++) {
        if (this.tableData[0].curName != this.tableData[j].curName) {
          flag = true
          break
        }
      }

      if (flag) {
        this.$message.error('币别不同，无法生成立账单')
        return
      }

      if (this.tableData.length > 0) {
        var bilNo = []
        this.tableData.forEach((e) => {
          bilNo.push(e.bilNo)
          // taxId   1 不计税 2 应税内含 3 应税外加
          if (e.taxId == '不计税') {
            e.taxId = '1'
          } else if (e.taxId == '应税内含') {
            e.taxId = '2'
          } else if (e.taxId == '应税外加') {
            e.taxId = '3'
          }
        })
        var set = new Set(bilNo)
        let obj = {
          lzDd: moment(this.form.lzDd).format('YYYY-MM-DD HH:mm:ss'),
          lzNo: this.form.lzNo,
          cusNo: this.form.cusNo,
          rem: '备注发票号:' + this.form.rem, // 备注发票号
          invNo: this.form.invNo,
          ...this.totalForm,
          bilNos: [...set].join(','),
          bilId: 'PO',
          pssList: this.tableData,
          mfLzFileUrlList: this.imageUrlIn,
          delPssList: this.delPssList
        }
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('invoice.file'),
          okText: this.$t('public.sure'),
          okType: 'warn',
          cancelText: this.$t('public.cancel'),
          onOk () {
            save(obj)
              .then((res) => {
                if (res) {
                  that.$message.success(that.$t('public.success'))
                  that.tableData = res.data
                  that.tableData.forEach((i,index) => {
                    i.id = index + 1
                    // taxId   1 不计税 2 应税内含 3 应税外加
                    if (i.taxId == '1') {
                      i.taxId = '不计税'
                    } else if (i.taxId == '2') {
                      i.taxId = '应税内含'
                    } else if (i.taxId == '3') {
                      i.taxId = '应税外加'
                    }
                  })
                  that.propsData = that.tableData
                  that.chuPrintData(res.data)
                  that.computerTotal()
                  that.delPssList = []
                  if (res.data[0].invNo) {
                    that.save = true
                    that.isVisible = false
                    that.form.invNo = res.data[0].invNo
                    that.form.lzNo = res.data[0].lzNo
                    that.$notification['warn']({
                      message: that.$t('public.message'),
                      description: that.$t('submission.notDel'),
                    })
                  }
                }
              })
              .catch((err) => {
                that.loading = false
                that.handReset()
                that.requestFailed(err)
              })
          },
          onCancel () { },
        })
      } else {
        this.$notification['warn']({
          message: this.$t('public.message'),
          description: '请先添加表身数据后保存单据！',
        })
      }
    },
    // 删除整张单据
    handleDel () {
      const that = this
      let form = {
        lzNo: this.form.lzNo,
        bilId: 'PO',
        pssList: this.tableData,
      }
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('invoice.delFile'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk () {
          del(form)
            .then((res) => {
              if (res && res.data != null) {
                if (res.data) {
                  that.$message.success(that.$t('public.success'))
                  that.handReset()
                } else {
                  that.$notification['error']({
                    message: that.$t('public.message'),
                    description: that.$t('invoice.content'),
                  })
                }
              }
            })
            .catch((err) => {
              that.loading = false
              that.requestFailed(err)
            })
        },
        onCancel () { },
      })
    },
    // 删除单行数据
    delTableColwn (row) {
      this.printData = []
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('invoice.del'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk () {
          // let filData = []
          // const lzBilNo = row.lzBilNo
          // filData = that.tableData.filter(i => i.lzBilNo !== lzBilNo)
          // that.tableData.forEach(e => {
          //   if (e.lzBilNo === lzBilNo) {
          //     if (e.lzNo !== null) that.delPssList.push(Object.assign({}, { oneItm: e.oneItm, bilId: e.bilId, lzBilNo: e.lzBilNo }))
          //   }
          // })
          let filData = []
          const lzBilNo = row.lzBilNo + row.id
          filData = that.tableData.filter(i => i.lzBilNo + i.id !== lzBilNo)
          that.tableData.forEach(e => {
            e.lzBilNo = e.lzBilNo + e.id
            if (e.lzBilNo === lzBilNo) {
              if (e.lzNo !== null) that.delPssList.push(Object.assign({}, { oneItm: e.oneItm, bilId: e.bilId, lzBilNo: e.lzBilNo }))
            }
          })
          that.isRefresh = false
          that.$nextTick(() => {
            that.isRefresh = true
          })
          that.tableData = filData
          that.propsData = that.tableData
          if (that.tableData.length == 0 || that.tableData === []) {
            that.totalForm = {}
          } else {
            that.computerTotal()
          }
        },
        onCancel () { }
      })
    },
    // 处理打印的数据
    chuPrintData (data) {
      this.printData = []
      this.printData = data
      this.totalNoTax = this.computer(data)
      this.cusName = data[0].cusName
      if (data.length <= 0) {
        this.Len = 1
      } else {
        this.Len = Math.ceil(data.length / 23.0)
      }
    },
    computer (arr) {
      var totalNoTaxs = 0
      arr.forEach((i) => {
        totalNoTaxs += +i.amtnNet
      })
      return totalNoTaxs.toFixed(arr[0].poiTax)
    },
    print () { },
    handleExport () {
      this.delPssList = []
      this.printData = []
      this.$refs.Export.create(this.form)
    },
    total () {
      var totalAmt = 0 // 金额合计
      var totalNoTax = 0 // 未税合计
      var totalTax = 0 // 税额合计
      var totalQty = 0 // 数量合计
      if (this.id1Tax === '1') {
        var amtKeep = 0
        this.tableData.forEach((item) => {
          amtKeep = item.qty * item.up
          item.amtnSnet = amtKeep.toFixed(item.poiTax)
          item.taxs = 0
          totalAmt += +item.amt
          this.totalForm.totalAmt = totalAmt.toFixed(item.poiAmt)
          totalNoTax += +item.amtnSnet
          this.totalForm.totalNoTax = totalNoTax.toFixed(item.poiTax)
          totalTax += +item.taxs
          this.totalForm.totalTax = totalTax.toFixed(item.poiTax)
          totalQty += +item.qty
          this.totalForm.totalQty = totalQty.toFixed(item.poiQty)
        })
      } else if (this.id1Tax === '2') {
        var amtnNetKeep = 0 // 计算未税
        var tax = 0 // 计算税额
        this.tableData.forEach((item) => {
          amtnNetKeep = (item.qty * item.up) / (1 + item.taxRto / 100)
          item.amtnSnet = amtnNetKeep.toFixed(item.poiTax)
          tax = item.qty * item.up * item.taxRto
          item.taxs = tax.toFixed(item.poiTax)
          totalAmt += +item.amt
          this.totalForm.totalAmt = totalAmt.toFixed(item.poiAmt)
          totalNoTax += +item.amtnSnet
          this.totalForm.totalNoTax = totalNoTax.toFixed(item.poiTax)
          totalTax += +item.taxs
          this.totalForm.totalTax = totalTax.toFixed(item.poiTax)
          totalQty += +item.qty
          this.totalForm.totalQty = totalQty.toFixed(item.poiQty)
        })
      } else if (this.id1Tax === '3') {
        var amtKeepPlus = 0
        var taxPlus = 0
        this.tableData.forEach((item) => {
          amtKeepPlus = item.qty * item.up
          item.amtnSnet = amtKeepPlus.toFixed(item.poiTax)
          taxPlus = item.qty * item.up * item.taxRto
          item.taxs = taxPlus.toFixed(item.poiTax)
          totalAmt += +item.amt
          this.totalForm.totalAmt = totalAmt.toFixed(item.poiAmt)
          totalNoTax += +item.amtnSnet
          this.totalForm.totalNoTax = totalNoTax.toFixed(item.poiTax)
          totalTax += +item.taxs
          this.totalForm.totalTax = totalTax.toFixed(item.poiTax)
          totalQty += +item.qty
          this.totalForm.totalQty = totalQty.toFixed(item.poiQty)
        })
      }
    },
    computerTotal () {
      var totalAmts = 0 // 金额合计
      var totalNoTaxs = 0 // 未税合计
      var totalTaxs = 0 // 税额合计
      var totalQtys = 0 // 数量合计
      this.tableData.forEach((item) => {
        totalQtys += +item.qty
        totalNoTaxs += +item.amtnNet
        totalTaxs += +item.tax
      })
      this.totalForm.totalQty = totalQtys.toFixed(this.tableData[0].poiQty)
      this.totalForm.totalAmt = (totalNoTaxs + totalTaxs).toFixed(this.tableData[0].poiAmt)
      this.totalForm.totalNoTax = totalNoTaxs.toFixed(this.tableData[0].poiTax)
      this.totalForm.totalTax = totalTaxs.toFixed(this.tableData[0].poiTax)
    },
    toDecimal2 (x, length) {
      var f = parseFloat(x)
      if (isNaN(f)) {
        return false
      }
      var f = Math.round(x * 100) / 100
      var s = f.toString()
      var rs = s.indexOf('.')
      if (rs < 0) {
        //找不到.
        rs = s.length
        s += '.'
      }
      while (s.length <= rs + length) {
        s += '0'
      }
      return s
    },
  },
}
</script>
<style lang="less">
.header_invoice {
  .ant-input[disabled] {
    color: rgba(0, 0, 0, 0.65);
    background-color: #fff;
  }
  .last_col {
    height: 38px;
  }
}
.footer_invoice {
  margin-top: 10px;
  .ant-form-item {
    margin-bottom: 0px;
    .ant-input[disabled] {
      color: rgba(0, 0, 0, 0.65);
    }
  }
}
#ant_form .ant-form-item {
  margin-bottom: 8px;
}
#showPrint {
  page-break-before: always;
}
@media print {
  #showPrint {
    page-break-before: always;
  }
}
</style>
