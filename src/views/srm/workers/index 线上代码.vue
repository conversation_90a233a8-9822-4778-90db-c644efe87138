<template>
  <a-card :bordered="false">

    <!-- 打印区域 -->
    <div v-show="false">
      <div
        id="showPrint"
        style="width: 1000px"
      >
        <div
          :key="i"
          v-for="i in Len"
        >
          <div style="position: relative;">
            <a-row>
              <a-col
                :span="24"
                style="text-align: center"
              >
                <h3>上海申菱钢结构有限公司</h3>
              </a-col>
            </a-row>
            <a-row>
              <a-col
                :span="24"
                style="text-align: center"
              >
                <h4>托工缴回开票明细表</h4>
              </a-col>
            </a-row>
            <a-row style="margin-bottom: 5px">
              <a-col
                :span="6"
                style="font-size: 10px; font-weight: 700"
              > 开票单号： {{ form.lzNo }}</a-col>
              <a-col
                :span="5"
                style="font-size: 10px; font-weight: 700"
              > 备注发票号码： {{ form.rem }}</a-col>
              <a-col
                :span="6"
                style="font-size: 10px; font-weight: 700"
              > 合计未税金额： {{ totalNoTax }}</a-col>
              <a-col
                :span="5"
                style="font-size: 10px; font-weight: 700"
              > 供应商： {{ cusName }}</a-col>
            </a-row>
            <img
              src="../../../../public/shenlin.png"
              style="position: absolute;top:280px;left:43%;z-index:-1;"
            >
          </div>
          <!-- 表格 -->
          <div>
            <table
              id="table"
              border="1"
              align="center"
              style="width: 100%; border-collapse: collapse"
            >
              <tr>
                <th
                  align="center"
                  style="text-align: center; font-size: 12px"
                >序号</th>
                <th
                  align="center"
                  style="text-align: center; font-size: 12px"
                >托工缴回单号</th>
                <th
                  align="center"
                  style="text-align: center; font-size: 12px"
                >托工单号</th>
                <th
                  align="center"
                  style="text-align: center; font-size: 12px"
                >托外货品</th>
                <th
                  align="center"
                  style="text-align: center; font-size: 12px"
                >托外货品名</th>
                <th
                  align="center"
                  style="text-align: center; font-size: 12px"
                >数量</th>
                <th
                  align="center"
                  style="text-align: center; font-size: 12px"
                >单位</th>
                <th
                  align="center"
                  style="text-align: center; font-size: 12px"
                >托外单价</th>
                <th
                  align="center"
                  style="text-align: center; font-size: 12px"
                >金额</th>
                <th
                  align="center"
                  style="text-align: center; font-size: 12px"
                >库位</th>
                <th
                  align="center"
                  style="text-align: center; font-size: 12px"
                >仓库收货</th>
                <th
                  align="center"
                  style="text-align: center; font-size: 12px"
                >质量检验</th>
              </tr>
              <tr
                v-for="(item, index) in 23"
                :key="item"
              >
                <td>{{ index + 23 * (i - 1) + 1 }}</td>
                <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].lzBilNo }}</td>
                <td v-else></td>
                <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].bilNo }}</td>
                <td v-else></td>
                <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].prdNo.slice(0,28) }}</td>
                <td v-else></td>
                <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].prdName.slice(0, 10) }}</td>
                <td v-else></td>
                <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].qty }}</td>
                <td v-else></td>
                <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].unitName }}</td>
                <td v-else></td>
                <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].up }}</td>
                <td v-else></td>
                <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].amt }}</td>
                <td v-else></td>
                <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].whName }}</td>
                <td v-else></td>
                <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].storageName }}</td>
                <td v-else></td>
                <td v-if="printData[index + 23 * (i - 1)]">{{ printData[index + 23 * (i - 1)].checkName }}</td>
                <td v-else></td>
              </tr>
            </table>
            <div style="height: 50px;text-align:center;line-height:50px;">
              <span>第 {{ i }} 页</span><span style="margin-left: 10px;">共 {{ printData.length }} 条</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <a-card :bordered="true">
      <div
        class="table-page-search-wrapper"
        id="ant_form"
      >
        <div class="header_workers">
          <a-form-model
            layout="horizontal"
            ref="ruleForm"
            :rules="rules"
            :model="form"
          >
            <a-row :gutter="48">
              <a-col
                :md="6"
                :sm="24"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.date')"
                  prop="lzDd"
                >
                  <a-date-picker
                    style="width: 100%"
                    v-model="form.lzDd"
                    :disabled="disabled"
                    :placeholder="$t('purchase.placeholder.day')"
                  />
                </a-form-model-item>
              </a-col>
              <a-col
                :md="6"
                :sm="24"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.invoiiceNo')"
                  prop="lzNo"
                >
                  <a-input
                    :disabled="true"
                    v-model="form.lzNo"
                    :placeholder="$t('invoice.placeholder.invoiiceNo')"
                  />
                </a-form-model-item>
              </a-col>
              <a-col
                :md="6"
                :sm="24"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.customer')"
                  prop="cusNo"
                >
                  <my-selectList
                    url="/srm/cust/page"
                    :tableColumn="$Column.srmcus"
                    :form="$Form.srmcus"
                    :data="data"
                    :disabled="disabled"
                    name="cusNo"
                    @choose="choose($event)"
                    ref="selectList"
                    :placeholder="$t('invoice.placeholder.customer')"
                  >
                  </my-selectList>
                </a-form-model-item>
              </a-col>
              <a-col
                :md="6"
                :sm="24"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.rem')"
                >
                  <a-input
                    v-model="form.rem"
                    :placeholder="$t('invoice.placeholder.rem')"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="48">
              <a-col
                :md="6"
                :sm="24"
                class="last_col"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.rem1')"
                >
                  <a-input
                    v-model="form.invNo"
                    :placeholder="$t('invoice.rem1')"
                    :readOnly="true"
                  />
                </a-form-model-item>
              </a-col>
              <a-col
                :md="18"
                :sm="24"
                style="text-align: right;float:right;"
              >
                <div style="float: right">
                  <a-button
                    type="primary"
                    @click="handReset()"
                    v-permission="srm_workers_reset"
                  >{{ $t('public.add') }}</a-button>
                  <a-button
                    style="margin-left: 10px"
                    type="danger"
                    :disabled="save"
                    @click="handDFile($event)"
                    v-permission="srm_workers_save"
                  >{{ $t('public.save') }}</a-button>
                  <a-button
                    style="margin-left: 10px"
                    type="primary"
                    @click="handleFind()"
                    v-permission="srm_workers_search"
                  >{{ $t('public.query') }}</a-button>
                  <a-button
                    style="margin-left: 10px"
                    type="primary"
                    @click="handleDel()"
                    v-permission="srm_workers_del"
                  >{{ $t('public.delete') }}</a-button>
                  <a-button
                    style="margin-left: 10px"
                    type="primary"
                    v-print="'showPrint'"
                    v-permission="srm_workers_print"
                  >{{ $t('public.print') }}</a-button>
                  <a-button
                    style="margin-left: 10px"
                    type="primary"
                    @click="handleExport()"
                    v-permission="srm_workers_export"
                  >{{ $t('public.export') }}</a-button>
                </div>
              </a-col>
              <!-- <a-col :span="6">
                <a-form-item
                  v-bind="formItemLayout"
                  :label="$t('prdiversion.bilType')"
                >
                  <a-select
                    style="width: 100%"
                    v-model="form.bilType"
                    :placeholder="$t('prdiversion.placeholder.bilType')"
                  >
                    <a-select-option
                      v-for="(val, index) in documentCate"
                      :key="index"
                      :value="val.value"
                    ><span style="margin-right:8px;">{{val.value}}</span><span>{{ val.label}}</span>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item
                  v-bind="formItemLayout"
                  :label="$t('inquiryoffer.paytype')"
                >
                  <a-select
                    style="width: 100%"
                    v-model="form.payType"
                  >
                    <a-select-option
                      v-for="(val, index) in sexList"
                      :key="index"
                      :value="val.value"
                    >{{
                        val.label
                      }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              
              <a-col :span="6">
                <a-form-item
                  v-bind="formItemLayout"
                  :label="$t('发票附件')"
                >
                  <div style="position: relative;">
                    <a-input
                      :readOnly="true"
                      v-model="invoice"
                      :placeholder="$t('发票附件')"
                    />
                    <div style="position:absolute;top:0px;right:65px;">
                      <a-button
                        :loading="loading"
                        type="primary"
                        size='small'
                        @click="seepic()"
                      >查看
                      </a-button>
                    </div>
                    <div style="position:absolute;top:0px;right:2px;">
                      <a-button
                        :loading="loading"
                        type="primary"
                        size='small'
                        @click="uploadpic()"
                      >上传
                      </a-button>
                    </div>

                  </div>
                </a-form-item>
              </a-col> -->
            </a-row>
            <a-row>

            </a-row>
          </a-form-model>
        </div>
      </div>
    </a-card>

    <vxe-toolbar>
      <template v-slot:buttons>
        <a-button
          type="primary"
          :disabled="save"
          icon="plus"
          @click="handleAdd()"
          v-permission="srm_workers_add"
        >{{ $t('public.addTo') }}</a-button>
      </template>
    </vxe-toolbar>
    <vxe-table
      border
      resizable
      stripe
      highlight-current-row
      show-overflow
      highlight-hover-row
      export-config
      ref="xTable"
      size="mini"
      @cell-dblclick="cellDBLClickEvent"
      :loading="loading"
      :data="tableData"
      :height="tableHeight"
      :keyboard-config="{ isArrow: true }"
      :seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
      :edit-config="{ trigger: 'click', mode: 'row' }"
      v-if="isRefresh"
    >
      <!-- height="580" -->
      <div>
        <vxe-table-column
          fixed="left"
          type="seq"
          title="invoice.itm"
          align="center"
          :width="50"
        > </vxe-table-column>
        <vxe-table-column
          field="lzBilNo"
          title="invoice.lzBilNos"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="bilNo"
          title="invoice.source2"
          align="center"
          :width="120"
        > </vxe-table-column>
        <!-- <vxe-table-column
          field="bilType"
          title="prdiversion.bilType"
          align="center"
          :width="120"
        > </vxe-table-column> -->
        <vxe-table-column
          field="prdNo"
          title="invoice.prdNo"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="prdName"
          title="invoice.prdName"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="unitName"
          title="invoice.unit"
          align="center"
          :width="100"
        > </vxe-table-column>
        <vxe-table-column
          field="qty"
          title="invoice.qty"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="up"
          title="invoice.up"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="amtnNet"
          title="invoice.amtnSnet"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="tax"
          title="invoice.taxs"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="amt"
          title="invoice.amt"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="taxRto"
          title="invoice.taxRto"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="amtFp"
          title="invoice.amtFp"
          align="center"
          :width="120"
        > </vxe-table-column>
        <vxe-table-column
          field="amtUn"
          title="invoice.AmtUn"
          align="center"
          :width="120"
        > </vxe-table-column>
        <!-- <vxe-table-column
          field="payType"
          title="inquiryoffer.paytype"
          align="center"
          :width="120"
        > </vxe-table-column> -->
        <vxe-table-column
          title="操作"
          align="center"
          fixed="right"
          :width="100"
          v-if="isVisible"
        >
          <template v-slot="scope">
            <a-button
              size="small"
              @click="delTableColwn(scope.row)"
            >删除</a-button>
          </template>
        </vxe-table-column>
        <!-- <vxe-table-column
          field="curId"
          title="prdiversion.curId"
          align="center"
          :width="120"
        > </vxe-table-column> -->
        <!-- <vxe-table-column
          field="contractNo"
          title="entrustment.contractNo"
          align="center"
          :width="120"
        > </vxe-table-column> -->
        <!-- <vxe-table-column
          field="contractNo"
          title="entrustment.contractNo"
          align="center"
          :width="120"
        >
          <template slot-scope="scope">
            <vxe-button
              type="text"
              @click="contQuery(scope.row)"
            >
              
              {{ scope.row.contractNo }}
            </vxe-button>
          </template>
        </vxe-table-column> -->
        <!-- <vxe-table-column
          field="contractNo"
          title="entrustment.contractNo"
          align="center"
          :width="120"
        >
          <template slot-scope="scope">
            <vxe-button
              type="text"
              @click="seepic(scope.row)"
            >
              {{ scope.row.contractNo }}
            </vxe-button>
          </template>
        </vxe-table-column> -->

        <!-- <vxe-table-column
          field="enclosure"
          title="inquiryoffer.enclosure"
          align="center"
          :width="110"
        >
          <template slot-scope="scope">
            <vxe-button
              type="text"
              @click="seepic(scope.row)"
            >查看</vxe-button>
            <vxe-button
              type="text"
              @click="uploadpic(scope.row)"
            >
              上传
            </vxe-button>
          </template>
        </vxe-table-column> -->
      </div>
    </vxe-table>
    <div class="footer_workers">
      <a-card :bordered="true">
        <div class="table-page-search-wrapper">
          <a-form-model
            layout="horizontal"
            ref="ruleForm"
            :rules="rules"
            :model="form"
          >
            <a-row :gutter="48">
              <a-col
                :md="6"
                :sm="24"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.totalQty')"
                >
                  <a-input
                    :disabled="true"
                    v-model="totalForm.totalQty"
                    :placeholder="$t('invoice.totalQty')"
                  />
                </a-form-model-item>
              </a-col>
              <a-col
                :md="6"
                :sm="24"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.totalNoTax')"
                >
                  <a-input
                    :disabled="true"
                    v-model="totalForm.totalNoTax"
                    :placeholder="$t('invoice.totalNoTax')"
                  />
                </a-form-model-item>
              </a-col>
              <a-col
                :md="6"
                :sm="24"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.totalTax')"
                >
                  <a-input
                    :disabled="true"
                    v-model="totalForm.totalTax"
                    :placeholder="$t('invoice.totalTax')"
                  />
                </a-form-model-item>
              </a-col>
              <a-col
                :md="6"
                :sm="24"
              >
                <a-form-model-item
                  v-bind="formItemLayout"
                  :label="$t('invoice.totalAmt')"
                >
                  <a-input
                    :disabled="true"
                    v-model="totalForm.totalAmt"
                    :placeholder="$t('invoice.totalAmt')"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </div>
      </a-card>
    </div>
    <Drawer
      ref="Drawer"
      :data="propsData"
      @getList="getList"
    />
    <Modal
      :cusNo="cusNo"
      ref="modal"
      @getSaveList="getSaveList"
    />
    <print ref="print" />
    <Export ref="Export" />
    <!-- 上传 -->
    <Upload
      ref="Upload"
      @insertEvent="insertEvent"
      @seeFile="seeFile"
    />
    <ModalPic ref="ModalPic" />
    <ContQuery ref="ContQuery" />
  </a-card>
</template>

<script>
import { mapState } from 'vuex'
import MySelectList from '@/components/MySelectList'
import { fetchCust, save, del, findBody, findInvNo, fetchBilNo } from '@/api/srm/invoice'
import { findBilType } from '@/api/srm/prdiversion'
import { paytypeFind } from '@/api/srm/inquiryoffer'
import Modal from './modal'
import Drawer from './drawer'
import print from './print'
import Export from './export'
import moment from 'moment'
import Upload from './Upload'
import { fetchList, down, isHaveMac } from '@/api/srm/zlxz'
import ModalPic from './ModalPic'
import ContQuery from './ContQuery'
import axios from 'axios'
export default {
  components: {
    Drawer,
    MySelectList,
    Modal,
    print,
    Export,
    Upload,
    ModalPic,
    ContQuery
  },
  data () {
    return {
      srm_workers_reset: 'srm_workers_reset',
      srm_workers_save: 'srm_workers_save',
      srm_workers_search: 'srm_workers_search',
      srm_workers_del: 'srm_workers_del',
      srm_workers_print: 'srm_workers_print',
      srm_workers_export: 'srm_workers_export',
      srm_workers_add: 'srm_workers_add',
      tableData: [],
      propsData: [],
      delPssList: [],
      printData: [],
      obj: {},
      totalForm: {},
      loading: false,
      disabled: false,
      isVisible: true,
      save: false,
      isRefresh: true,
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      data: '',
      cusNo: '',
      cusName: '',
      totalNoTax: null,
      Len: null,
      form: {
        lzNo: '',
        cusNo: '',
        invNo: '',
        rem: '',
        lzDd: moment(new Date(), 'YYYY-MM-DD'),
        payType: '',
        bilType: ''
      },
      queryParam: {},
      formItemLayout: {
        labelCol: {
          xl: { span: 7 },
          lg: { span: 12 },
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xl: { span: 17 },
          lg: { span: 15 },
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 15 },
        },
      },
      rules: {
        lzDd: [{ required: true, message: this.$t('invoice.placeholder.date') }],
        cusNo: [{ required: true, message: this.$t('invoice.placeholder.cusNo'), trigger: 'blur' }],
        lzNo: [{ required: true, message: this.$t('invoice.placeholder.lzNo'), trigger: 'blur' }],
      },
      tableHeight: window.innerHeight - 375,
      payselect: null,
      documentCate: [],
      sexList: [],
      imageUrlList: [],
      invoice: ''
    }
  },
  watch: {
    'form.cusNo': {
      handler (newValue, oldValue) {
        this.cusNo = newValue
      },
      deep: true, // 对象内部的属性监听，也叫深度监听
      immediate: true, // 立即监听
    },
  },
  computed: {
    ...mapState({
      info: (state) => state.user,
    }),
  },
  created () {
    if (this.info.info.supNo) {
      this.cusNo = this.info.info.supNo
      this.data = this.info.info.supName
      this.form.cusNo = this.info.info.supNo
      this.getLzNo(this.info.info.supNo)
      this.disabled = true
    }
  },
  mounted () {
    this.sexList = []
    paytypeFind().then((res) => {
      res.data.forEach((item, index) => {
        let sexarry = { label: item.name, value: index }
        this.sexList.push(sexarry)

      })
    })
    this.documentCate = []
    findBilType().then((res) => {
      res.data.forEach((item, index) => {
        let catearry = { label: item.bilTypeName, value: item.bilType }
        this.documentCate.push(catearry)
      })
      let catearryall = { label: '全部', value: null }
      this.documentCate.push(catearryall)
      // this.queryParam.bilType

    })
    window.onresize = () => {
      return (() => {
        this.tableHeight = window.innerHeight - 375
      })()
    }
  },
  methods: {
    seepic () {
      this.$refs.ModalPic.create({ title: '查看' }, this.tableData.imageUrlList)
    },
    contQuery (row) {
      let url
      url = '/admin/sys-file/file-fm/461924562dde483bad638f25a93fd8a0.png'
      window.open(url, '_self');
      // this.$refs.ContQuery.create({ title: '查看' }, row)
    },
    // 下载pdf
    downInvoice () {

      isHaveMac({
        ip: returnCitySN['cip'],
      })
        .then((res) => {
          // 先判断是否有绑定ip地址
          if (res.data === '0') {
            // 未绑定ip,提示
            const that = this
            this.$confirm({
              title: this.$t('public.del.title'),
              content: this.$t('zlsq.isHaveMac'),
              okText: this.$t('public.sure'),
              okType: 'danger',
              cancelText: this.$t('public.cancel'),
              onOk () {
                that.requestDwnFile(row)
              },
              onCancel () { },
            })
          } else if (res.data === '1') {
            // 直接下载
            this.requestDwnFile(row)
          }
        })
        .catch((err) => {
          this.requestFailed(err)
        })
    },
    requestDwnFile (row) {
      const hide = this.$message.loading(this.$t('public.waitDown'), 0)
      let obj = {
        ip: '',
        ...row,
      }
      obj.ip = returnCitySN['cip']
      down(obj)
        .then((res) => {
          if (res.data) {
            if (res.data.flag) {
              let url = res.data.returnUrl
              this.downFile(url, res.data.fileName, hide)
            } else {
              setTimeout(hide, 5)
              this.$message.error(res.data.message)
            }
          }
        })
        .catch((err) => {
          setTimeout(hide, 5)
          this.requestFailed(err)
        })
    },
    downFile (url, fileName, hide) {
      axios({
        method: 'get',
        url: url,
        responseType: 'blob',
      })
        .then((response) => {
          setTimeout(hide, 5)
          this.tablePage.currentPage = 1
          this.getList()
          this.$message.success(this.$t('public.downSuccess'))
          const blob = new Blob([response])
          const blobUrl = window.URL.createObjectURL(blob)
          this.download(blobUrl, fileName)
        })
        .catch((err) => {
          setTimeout(hide, 5)
          this.loading = false
          this.requestFailed(err)
        })
    },
    download (blobUrl, fileName) {
      let a = document.createElement('a')
      a.style.display = 'none'
      a.download = fileName
      a.href = blobUrl
      a.click()
    },
    // 上传
    insertEvent (row) { },
    // 批量添加
    seeFile (imageUrlList) {
      this.imageUrlList = imageUrlList
      this.tableData.imageUrlList = imageUrlList
     
    },
    // 批量文件
    uploadpic () {
      // let formD = {
      //   fiDd: moment(this.queryParam.fiDd).format('YYYY-MM-DD HH:mm:ss'),
      //   flNo: this.queryParam.flNo,
      //   usr: this.queryParam.usr,
      //   dep: this.queryParam.dep,
      //   depNo: this.queryParam.depNo,
      //   usrNo: this.queryParam.usrNo,
      //   idxNomf: this.queryParam.idxNomf,
      //   idxName: this.form.idxName
      // }
      this.$refs.Upload.create(
        {
          title: '上传'
        },
      )
    },
    // // 查询头部
    // getForm (cusNo) {
    //   this.loading = true
    //   fetchBilNo({ cusNo })
    //     .then((res) => {
    //       this.loading = false
    //       this.form.lzNo = res.data
    //     })
    //     .catch((err) => {
    //       this.loading = false
    //       this.requestFailed(err)
    //     })
    // },
    getLzNo (cusNo) {
      fetchBilNo({ cusNo })
        .then((res) => {
          this.form.lzNo = res.data
        })
        .catch((err) => {
          this.requestFailed(err)
        })
    },
    choose (obj) {
      this.form[obj.obj.name] = obj.obj.data.cusNo
      fetchBilNo({ cusNo: obj.obj.data.cusNo })
        .then((res) => {
          if (res.data === 'F') {
            this.form.lzNo = null
            this.$message.info('该客户未设置立账方式')
          } else {
            this.form.lzNo = res.data
          }
        })
        .catch((err) => {
          this.requestFailed(err)
        })
    },
    handleFind () {
      this.delPssList = []
      this.$refs.modal.create({
        title: '单据查询',
      })
    },
    // 根据编号查询已保存数据
    getSaveList (obj) {
      this.delPssList = []
      this.printData = []
      this.loading = true
      this.obj = obj
      this.id = obj.lzNo
      this.give = false
      this.disabled = true
      if (obj.invNo) {
        this.isVisible = false
        this.save = true
      } else {
        this.isVisible = true
        this.save = false
      }
      this.form = {
        lzNo: obj.lzNo,
        cusNo: obj.cusNo,
        invNo: obj.invNo,
        lzDd: moment(obj.lzDd),
      }
      if (obj.rem != null) {
        if (obj.rem.indexOf('备注发票号:') > -1) this.form.rem = obj.rem.split(':')[1]
      }
      this.totalForm = {}
      this.tableData = []
      this.propsData = this.tableData
      findBody(
        Object.assign({
          current: this.tablePage.currentPage,
          size: -1,
          lzNo: obj.lzNo,
          cusNo: obj.cusNo,
          bilId: obj.bilId,
        })
      )
        .then((res) => {
          this.give = false
          this.tableData = res.data.records
          this.propsData = this.tableData
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
          if (res.data.records.length === 0) {
            this.totalForm = {}
            this.Len = 0
            return
          }
          if (res.data.records.length !== 0) {
            if (this.info.info.supNo == null) {
              this.data = res.data.records[0].cusName
              this.form.cusNo = res.data.records[0].cusNo
            }
            this.chuPrintData(res.data.records)
            this.computerTotal()
          }
        })
        .catch((err) => {
          this.loading = false
          this.totalForm = {}
          this.tableData = []
          this.propsData = this.tableData
          this.requestFailed(err)
        })
    },
    // 添加表身数据
    getList (data) {
      if (data == [] || data.length == 0) {
        this.loading = false
        this.tableData = data
      } else {
        this.loading = true
        this.save = false
        this.tableData.push(...data)
        this.propsData = this.tableData
        this.computerTotal()
        this.loading = false
      }
      this.Len = 0
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
    },
    // 新增重置
    handReset () {
      this.disabled = false
      this.save = false
      this.isVisible = true
      if (this.info.info.supNo) {
        this.getLzNo(this.info.info.supNo)
      }
      // this.getForm(this.info.info.supNo)
      this.form.invNo = ''
      this.form.rem = ''
      this.form.lzDd = moment(new Date(), 'YYYY-MM-DD')
      this.tableData = []
      this.propsData = []
      this.printData = []
      this.delPssList = []
      this.totalForm = {}
      this.Len = 0
      this.form.lzNo = []
    },
    // 添加
    handleAdd () {
      this.printData = []
      if (this.form.cusNo && this.form.lzDd && this.form.lzNo) {
        // if (this.form.cusNo) {
        findInvNo({ lzNo: this.form.lzNo })
          .then(res => {
            if (res.data == null || res.data == '') {
              let form = {
                lzDd: moment(this.form.lzDd).format('YYYY-MM-DD HH:mm:ss'),
                lzNo: this.form.lzNo,
                cusNo: this.form.cusNo,
                invNo: this.form.invNo,
                rem: this.form.rem
              }
              this.$refs.Drawer.create({ title: this.$t('public.add') }, form)
            } else {
              this.form.invNo = res.data.invNo
              this.$notification['warn']({
                message: this.$t('public.message'),
                description: this.$t('submission.notDel')
              })
              return false
            }
          }).catch(err => this.requestFailed(err))
      } else {
        this.$notification['warning']({
          // this.$notification['error']({
          // message: this.$t('public.message'),
          // description: this.$t('submission.customerCode')
          message: '提示',
          description: '请先输入日期，开票单号和客户代号！',
        })
      }
    },
    cellDBLClickEvent ({ row }) {
      this.$refs.Drawer.edit({ title: this.$t('public.Detailed') }, row)
    },
    // 保存
    handDFile (e) {
      let target = e.target
      if (target.nodeName == 'SPAN' || target.nodeName == 'I') {
        target = e.target.parentNode
      }
      target.blur()
      const that = this
      if (this.tableData.length > 0) {
        var bilNo = []
        this.tableData.forEach((e) => {
          bilNo.push(e.bilNo)
        })
        var set = new Set(bilNo)
        let obj = {
          lzDd: moment(this.form.lzDd).format('YYYY-MM-DD HH:mm:ss'),
          lzNo: this.form.lzNo,
          cusNo: this.form.cusNo,
          rem: '备注发票号:' + this.form.rem,
          invNo: this.form.invNo,
          ...this.totalForm,
          bilNos: [...set].join(','),
          bilId: 'TW',
          pssList: this.tableData,
          delPssList: this.delPssList,
          // 后添加
          payType: this.form.payType,
          bilType: this.form.bilType
        }
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('invoice.file'),
          okText: this.$t('public.sure'),
          okType: 'warn',
          cancelText: this.$t('public.cancel'),
          onOk () {
            save(obj)
              .then((res) => {
                if (res) {
                  that.$message.success(that.$t('public.success'))
                  that.tableData = res.data
                  that.propsData = that.tableData
                  that.chuPrintData(res.data)
                  that.computerTotal()
                  that.delPssList = []
                  if (res.data[0].invNo) {
                    that.save = true
                    that.isVisible = false
                    that.form.invNo = res.data[0].invNo
                    that.form.lzNo = res.data[0].lzNo
                    that.$notification['warn']({
                      message: '提示',
                      description: '已有发票号,不能修改删除'
                    })
                  }
                }
              })
              .catch((err) => {
                that.loading = false
                that.handReset()
                that.requestFailed(err)
              })
          },
          onCancel () { },
        })
      } else {
        this.$notification['warn']({
          message: this.$t('public.message'),
          description: '请先添加表身数据后保存单据！'
        })
      }
    },
    // 删除整张单据
    handleDel () {
      const that = this
      let form = {
        lzNo: this.form.lzNo,
        bilId: 'TW',
        pssList: this.tableData
      }
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('invoice.delFile'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk () {
          del(form)
            .then((res) => {
              if (res && res.data != null) {
                if (res.data) {
                  that.$message.success(that.$t('public.success'))
                  that.handReset()
                } else {
                  that.$notification['error']({
                    message: that.$t('public.message'),
                    description: that.$t('invoice.content'),
                  })
                }
              }
            })
            .catch((err) => {
              that.loading = false
              that.requestFailed(err)
            })
        },
        onCancel () { }
      })
    },
    // 删除单行数据
    delTableColwn (row) {
      this.printData = []
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('invoice.del'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk () {
          let filData = []
          const lzBilNo = row.lzBilNo
          filData = that.tableData.filter(i => i.lzBilNo !== lzBilNo)
          that.tableData.forEach(e => {
            if (e.lzBilNo === lzBilNo) {
              if (e.lzNo !== null) that.delPssList.push(Object.assign({}, { oneItm: e.oneItm, bilId: e.bilId, lzBilNo: e.lzBilNo }))
            }
          })
          that.isRefresh = false
          that.$nextTick(() => {
            that.isRefresh = true
          })
          that.tableData = filData
          that.propsData = that.tableData
          if (that.tableData.length == 0 || that.tableData === []) {
            that.totalForm = {}
          } else {
            that.computerTotal()
          }
        },
        onCancel () { }
      })
    },
    // 处理打印的数据
    chuPrintData (data) {
      this.printData = []
      this.printData = data
      this.totalNoTax = this.computer(data)
      this.cusName = data[0].cusName
      if (data.length <= 0) {
        this.Len = 1
      } else {
        this.Len = Math.ceil(data.length / 23.0)
      }
    },
    computer (arr) {
      var totalNoTaxs = 0
      arr.forEach((i) => {
        totalNoTaxs += +i.amtnNet
      })
      return totalNoTaxs.toFixed(arr[0].poiTax)
    },
    print () { },
    handleExport () {
      this.delPssList = []
      this.printData = []
      this.$refs.Export.create(this.form)
    },
    computerTotal () {
      var totalAmts = 0 // 金额合计
      var totalNoTaxs = 0 // 未税合计
      var totalTaxs = 0 // 税额合计
      var totalQtys = 0 // 数量合计
      this.tableData.forEach((item) => {
        totalQtys += +item.qty
        totalNoTaxs += +item.amtnNet
        totalTaxs += +item.tax
      })
      this.totalForm.totalQty = totalQtys.toFixed(this.tableData[0].poiQty)
      this.totalForm.totalAmt = (totalNoTaxs + totalTaxs).toFixed(this.tableData[0].poiAmt)
      this.totalForm.totalNoTax = totalNoTaxs.toFixed(this.tableData[0].poiTax)
      this.totalForm.totalTax = totalTaxs.toFixed(this.tableData[0].poiTax)
    }
  },
}
</script>
<style lang="less">
.header_workers {
  .ant-input[disabled] {
    color: rgba(0, 0, 0, 0.65);
    background-color: #fff;
  }
  .last_col {
    height: 38px;
  }
}
.footer_workers {
  margin-top: 10px;
  .ant-form-item {
    margin-bottom: 0px;
    .ant-input[disabled] {
      color: rgba(0, 0, 0, 0.65);
    }
  }
}
#ant_form .ant-form-item {
  margin-bottom: 8px;
}
#showPrint {
  page-break-before: always;
}
@media print {
  #showPrint {
    page-break-before: always;
  }
}
</style>
