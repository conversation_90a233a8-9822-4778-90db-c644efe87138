<template>
  <div>
    <a-card
      :bordered="false"
      class="fb-form"
    >
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row>
            <a-col :span="5">
              <a-form-item
                v-bind="formItemLayout"
                :label="$t('inquiryoffer.goodsno')"
              >
                <a-input
                  v-model="queryParam.prdNo"
                  :placeholder="$t('inquiryoffer.goodsno')"
                  style="width: 95%"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="5">
              <a-form-item
                v-bind="formItemLayout"
                :label="$t('inquiryoffer.cusNo')"
              >
                <a-input
                  v-model="queryParam.cusNo"
                  :placeholder="$t('inquiryoffer.cusNo')"
                  style="width:95%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="7">
              <a-form-item
                v-bind="formItemLayout"
                :label="$t('inquirycompri.qudate')"
              >
                <a-date-picker
                  v-model="qcStartDd"
                  :placeholder="$t('inquirycompri.icpstartD')"
                  :inputReadOnly="true"
                  format="YYYY-MM-DD"
                  @openChange="handleStartOpenChange"
                  @change="onChange"
                  style="width: 48%"
                />

                <a-date-picker
                  v-model="qcEndDd"
                  format="YYYY-MM-DD"
                  :placeholder="$t('inquirycompri.icpendDd')"
                  :inputReadOnly="true"
                  :open="endOpen"
                  @openChange="handleEndOpenChange"
                  style="width: 48%; margin-left: 8px"
                />
              </a-form-item>
            </a-col>
            <a-col :span="7">
              <a-form-item
                v-bind="formItemLayout"
                :label="$t('inquirycompri.subranda')"
              >
                <a-date-picker
                  v-model="submitStartDd"
                  :placeholder="$t('inquirycompri.icpstartD')"
                  :inputReadOnly="true"
                  format="YYYY-MM-DD"
                  @openChange="preStartOpenChange"
                  @change="onChange"
                  style="width: 48%"
                />
                <a-date-picker
                  v-model="submitEndEndDd"
                  format="YYYY-MM-DD"
                  :placeholder="$t('inquirycompri.icpendDd')"
                  :inputReadOnly="true"
                  :open="preendOpen"
                  @openChange="preEndOpenChange"
                  style="width: 48%; margin-left: 8px"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="5">
              <a-form-item
                v-bind="formItemLayout"
                :label="$t('inquiryoffer.goodsname')"
              >
                <a-input
                  v-model="queryParam.prdName"
                  :placeholder="$t('inquiryoffer.goodsname')"
                  style="width: 95%"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="5">
              <a-form-item
                v-bind="formItemLayout"
                :label="$t('inquiryoffer.suppliername')"
              >
                <a-input
                  v-model="queryParam.cusName"
                  :placeholder="$t('inquiryoffer.suppliername')"
                  style="width:95%"
                />
              </a-form-item>
            </a-col>

            <a-col :span="7">
              <a-form-item
                v-bind="formItemLayout"
                :label="$t('inquirycompri.sysDate')"
              >
                <a-date-picker
                  v-model="hzStartDd"
                  :placeholder="$t('inquirycompri.icpstartD')"
                  :inputReadOnly="true"
                  format="YYYY-MM-DD"
                  @openChange="handleStartOpenChange2"
                  @change="onChange"
                  style="width: 48%"
                />

                <a-date-picker
                  v-model="hzBjEndDd"
                  format="YYYY-MM-DD"
                  :placeholder="$t('inquirycompri.icpendDd')"
                  :inputReadOnly="true"
                  :open="endOpen2"
                  @openChange="handleEndOpenChange2"
                  style="width: 48%; margin-left: 8px"
                />
              </a-form-item>
            </a-col>

            <a-col :span="7">
              <a-form-item :label="$t('inquiryoffer.Processstatus')">
                <a-select
                  v-model="queryParam.qcYes"
                  @change="radioChange"
                  style="width: 98%"
                >
                  <a-select-option value="0">
                    {{ $t('inquirycompri.inqcomrele.0') }}
                  </a-select-option>

                  <a-select-option value="2">
                    {{ $t('inquirycompri.inqcomrele.2') }}
                  </a-select-option>
                  <a-select-option value="1">
                    {{ $t('inquirycompri.inqcomrele.1') }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

          </a-row>
          <a-row>
            <a-col :span="5">
              <a-form-item
                v-bind="formItemLayout"
                :label="$t('inquiryoffer.qcno')"
              >
                <a-input
                  v-model="queryParam.qcNo"
                  :placeholder="$t('inquiryoffer.qcno')"
                  id="qcno"
                  style="width: 95%"
                />
              </a-form-item>
            </a-col>

            <a-col :span="5">
              <a-form-item
                v-bind="formItemLayout"
                :label="$t('inquirycompri.sumno')"
              >
                <a-input
                  v-model="queryParam.offerno"
                  :placeholder="$t('inquirycompri.sumno')"
                  style="width: 95%"
                />
              </a-form-item>
            </a-col>

            <a-col
              :span="9"
              v-if="queryParam.qcYes == '1'"
            >
              <a-form-item
                v-bind="formItemLayout"
                :label="$t('产生采购单否')"
              >
                <a-radio-group
                  class="moban"
                  :options="plainOptions"
                  :default-value="value2"
                  @change="onChange3"
                  v-model="printSign"
                />
              </a-form-item>
              <!-- queryParam.posStatus -->
            </a-col>

            <!-- <a-col :span="6">
              <a-form-item :label="$t('prdiversion.bilType')">
                <a-select
                  @change="radioChange"
                  style="width: 100%"
                  v-model="queryParam.bilType"
                  :placeholder="$t('prdiversion.placeholder.bilType')"
                >
                  <a-select-option
                    v-for="(val, index) in documentCate"
                    :key="index"
                    :value="val.value"
                  ><span style="margin-right:8px;">{{val.value}}</span><span>{{ val.label}}</span>
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col> -->

            <a-col
              :span="5"
              style="text-align:right;float:right;"
            >
              <!-- <a-button
                style="margin-right:8px;"
                :loading="loading"
                type="primary"
                @click="handleRenovate($event)"
              >{{ $t('inquiryoffer.refresh') }}
              </a-button> -->

              <a-button
                :loading="loading"
                type="primary"
                @click="handleQuery"
                v-permission="srm_inquirycompri_query"
              >{{ $t('public.query') }}
              </a-button>
              <a-button
                style="margin-left: 8px"
                @click="reset"
                v-permission="srm_inquirycompri_reset"
              >{{ $t('public.reset') }}</a-button>
              <!--  <a-button
                style="
              margin-right:8px;"
                :loading="loading"
                type="primary"
                @click="refresh"
              >{{
                    $t('inquiryoffer.refresh')
                  }}
              </a-button>
             <a-button
                style="margin-right:8px;"
                :loading="loading"
                type="primary"
                @click="storage"
              >{{
                    $t('public.save')
                  }}
              </a-button>
              <a-button
                :loading="loading"
                type="primary"
                @click="drcontract"
                style="margin-right:8px;"
              >{{
                    $t('inquirycompri.drcontract')
                  }}
              </a-button>

              <a-button
                style="margin-right:8px;"
                :loading="loading"
                type="primary"
                @click="iqyquery"
              >{{
                    $t('inquiryoffer.iqyquery')
                  }}
              </a-button>
              <a-button
                :loading="loading"
                type="primary"
                style="margin-right:8px;"
                @click="makeorder"
              >{{
                    $t('inquirycompri.makeorder')
                  }}
              </a-button> -->

            </a-col>
          </a-row>
        </a-form>
        <div class="vxe_table">
          <vxe-toolbar>
            <template v-slot:buttons>
              <a-dropdown :trigger="['click']">
                <a-button>{{ $t('public.action') }}
                  <a-icon type="down" />
                </a-button>
                <a-menu slot="overlay">
                  <!-- <a-menu-item key="0">
                  <a
                    @click="dropdownMenuEvent('publish')"
                    :disabled="queryParam.fbStatus !== '0'"
                  >{{
                  $t('entrustment.publish')
                }}</a>
                </a-menu-item> -->
                  <a-menu-item
                    key="0"
                    v-permission="srm_inquirycompri_save"
                  >
                    <a
                      :disabled="queryParam.qcYes == '1'"
                      @click="storage()"
                    >{{ $t('inquirycompri.priceComparison') }}</a>
                  </a-menu-item>
                  <a-menu-item
                    key="1"
                    v-permission="srm_inquirycompri_sure"
                  >
                    <a
                      :disabled="queryParam.qcYes == '1'"
                      @click="batchsheet()"
                    >{{ $t('inquirycompri.comAudit')}}</a>
                  </a-menu-item>
                  <a-menu-item key="2">
                    <a
                      :disabled="pursheng"
                      @click="purchase()"
                    >生成采购单</a>
                  </a-menu-item>

                  <a-menu-item key="2">
                    <a
                      @click="generate()"
                    >生成合同草稿</a>
                  </a-menu-item>
                  <!-- <a-menu-item key="3">
                    <a
                      :disabled="queryParam.qcYes == '1' || queryParam.qcYes == '2'"
                      @click="bargaining()"
                    >议价</a>
                  </a-menu-item> -->
                  <!-- v-permission="srm_inquirycompri_purchase" -->
                  <!-- <a-menu-item
                    key="2"
                    v-permission="srm_inquirycompri_contract"
                  >
                    <a @click="drcontract()">{{ $t('inquirycompri.drcontract') }}</a>
                  </a-menu-item> -->

                </a-menu>
              </a-dropdown>
            </template>
          </vxe-toolbar>
        </div>
      </div>
      <vxe-table
        border
        resizable
        stripe
        size="mini"
        highlight-current-row
        :height="tableHeight"
        show-overflow
        highlight-hover-row
        ref="xTable"
        :checkbox-config="{ strict: true, checkMethod: checCheckboxkMethod2}"
        :loading="loading"
        :mouse-config="{selected: panduan}"
        :data="tableData"
        :edit-rules="validRules"
        :keyboard-config="{ isEnter: true, isEdit: true,isArrow:true,isClip:true}"
        @checkbox-change="selectChangeEvent"
        @checkbox-all="selectChangeAll"
        :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true}"
        @edit-actived="editActivedEvent"
      >
        <vxe-table-column
          type="checkbox"
          align="center"
          :width="50"
        ></vxe-table-column>

        <vxe-table-column
          field="itm"
          title="inquirycompri.Item"
          align="center"
          :width="50"
        ></vxe-table-column>

        <vxe-table-column
          field="prdNo"
          title="inquiryoffer.goodsno"
          align="center"
          :width="150"
        >
          <template v-slot="{ row }">
            <div @click="cancopy"> {{row.prdNo}}</div>
          </template>
        </vxe-table-column>

        <vxe-table-column
          field="prdName"
          title="inquiryoffer.goodsname"
          align="center"
          :width="150"
        >
          <template v-slot="{ row }">
            <div @click="cancopy"> {{row.prdName}}</div>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="spc"
          title="inquiryoffer.goodsspecs"
          align="center"
          :width="150"
        >
          <template v-slot="{ row }">
            <div @click="cancopy"> {{row.spc}}</div>
          </template>
        </vxe-table-column>
        <!-- <vxe-table-column
              field="company"
              title="prdtBarcodeBox.batNo"
              align="center"
              :width="150"
            ></vxe-table-column> -->

        <vxe-table-column
          field="unit"
          title="inquiryoffer.company"
          align="center"
          :width="50"
        >
          <template v-slot="{ row }">
            <div @click="cancopy"> {{row.unit}}</div>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="qtyHz"
          title="inquiryoffer.quantity"
          align="center"
          :width="80"
        >
          <template v-slot="{ row }">
            <div @click="cancopy"> {{row.qtyHz}}</div>
          </template>
          <!-- <template slot-scope="scope">
            <span
              v-show="scope.row.colorJudg"
              style="color:#f00"
            >
              {{scope.row.qtyHz}}
            </span>
            <span v-show="!scope.row.colorJudg">{{scope.row.qtyHz}}
            </span>

          </template> -->

        </vxe-table-column>
        <vxe-table-column
          field="cusNo"
          title="inquiryoffer.cusNo"
          align="center"
          :width="80"
        >
          <template v-slot="{ row }">
            <div @click="cancopy"> {{row.cusNo}}</div>
          </template>
        </vxe-table-column>

        <vxe-table-column
          field="cusName"
          title="inquiryoffer.suppliername"
          align="center"
          :width="110"
        >
          <template v-slot="{ row }">
            <div @click="cancopy"> {{row.cusName}}</div>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="qty"
          title="inquiryoffer.submitnum"
          align="center"
          :width="90"
        >
        </vxe-table-column>
        <!-- <vxe-table-column
          field="qty"
          title="inquiryoffer.submitnum"
          align="center"
          :width="90"
          :edit-render="{ name: 'input', attrs: {disabled: nameDisabled }}"
        >
          <template v-slot="{ row }">
            去掉<div v-show="queryParam.qcYes == '0'|| queryParam.qcYes == '1'">{{ row.qty }}</div>
            <div v-show="queryParam.qcYes == '2'">{{ printNum(row) }}</div>去掉
                <div>{{ printNum(row) }}</div>
            </template>
        </vxe-table-column> -->
        <vxe-table-column
          field="qtyPos"
          title="采购数量"
          align="center"
          :width="110"
          :edit-render="{ name: 'input',attrs: {disabled: qtyPosDisabled }}"
        >
          <template slot-scope="scope">
            <div @click="upclick()">
              <span
                v-show="scope.row.colorJudg"
                style="color:#f00"
              >
                {{printNumpos(scope.row)}}

                <!-- {{scope.row.qtyPos}} -->
              </span>
              <span v-show="!scope.row.colorJudg">
                <!-- {{scope.row.qtyPos}} -->
                {{printNumpos(scope.row)}}
              </span>
            </div>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="qtyErpBuy"
          title="erp已下单数量"
          align="center"
          :width="110"
        >
          <template v-slot="{ row }">
            <div @click="cancopy"> {{row.qtyErpBuy}}</div>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="qtyErpLeft"
          title="erp结余数量"
          align="center"
          :width="110"
        >
          <template v-slot="{ row }">
            <div @click="cancopy"> {{row.qtyErpLeft}}</div>
          </template>
        </vxe-table-column>
        <!-- <vxe-table-column
          v-if="queryParam.qcYes != '1'"
          field="qty"
          title="inquiryoffer.submitnum"
          align="center"
          :width="90"
          :edit-render="{ name: 'input', attrs: {disabled: nameDisabled }}"
        ></vxe-table-column> -->
        <!-- <vxe-table-column
          field="qty"
          title="inquiryoffer.submitnum"
          align="center"
          :width="90"
          v-if="queryParam.qcYes == '1'"
        ></vxe-table-column> -->

        <vxe-table-column
          field="up"
          title="inquiryoffer.repprice"
          align="center"
          :width="90"
        >
          <template v-slot="{ row }">
            <div @click="cancopy"> {{row.up}}</div>
          </template>
        </vxe-table-column>
        <!-- <vxe-table-column
          field="up"
          title="inquiryoffer.bargaining"
          align="center"
          :width="90"
          :edit-render="{ name: 'input'}"
        ></vxe-table-column> -->
        <vxe-table-column
          field="estDd"
          title="entrustment.estDd"
          align="center"
          :width="90"
        >
          <template v-slot="{ row }">
            <div @click="cancopy"> {{row.estDd}}</div>
          </template>
        </vxe-table-column>

        <vxe-table-column
          field="submitDd"
          title="inquiryoffer.submitDd"
          align="center"
          :width="90"
        >
          <template v-slot="{ row }">
            <div @click="cancopy"> {{row.submitDd}}</div>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="payType"
          title="inquiryoffer.paymethod"
          align="center"
          :width="80"
        >
          <template v-slot="{ row }">
            <div @click="cancopy"> {{row.payType}}</div>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="orgUp"
          title="inquiryoffer.oriQuotation"
          align="center"
          :width="90"
        >
          <template v-slot="{ row }">
            <div @click="cancopy"> {{row.orgUp}}</div>
          </template>
        </vxe-table-column>
        <!-- :edit-render="{name: '$select', options: sexList, props: {disabled: sexDisabled}}" -->
        <!-- <vxe-table-column
          field="repamoun"
          title="inquiryoffer.repamoun"
          align="center"
          :width="150"
        ></vxe-table-column> -->
        <vxe-table-column
          field="qcYes"
          title="inquirycompri.trresults"
          align="center"
          :width="80"
        >
          <template v-slot="{ row }">
            <div @click="cancopy"> {{row.qcYes}}</div>
          </template>
        </vxe-table-column>
        <vxe-table-column
          v-if="queryParam.qcYes == '1' || queryParam.qcYes == '2'"
          field="qtyTj"
          title="inquiryoffer.orIsubmitnum"
          align="center"
          :width="100"
        >
          <template v-slot="{ row }">
            <div v-if="queryParam.qcYes == '1' || queryParam.qcYes == '2'">{{ row.qtyTj }}</div>
            <div v-if="queryParam.qcYes == '0'"></div>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="qcNo"
          title="inquiryoffer.qcno"
          align="center"
          :width="100"
        >
          <template v-slot="{ row }">
            <div @click="cancopy"> {{row.qcNo}}</div>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="hzNo"
          title="inquirycompri.sumno"
          align="center"
          :width="100"
        >
          <template v-slot="{ row }">
            <div @click="cancopy"> {{row.hzNo}}</div>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="rem"
          title="inquirycompri.remarks"
          align="center"
          :width="160"
          :edit-render="{ name: 'input', attrs: {disabled: nameDisabledrem }}"
        ></vxe-table-column>

        <!-- <vxe-table-column
              field="puquantity"
              title="inquirycompri.puquantity"
              align="center"
              :width="150"
            ></vxe-table-column> -->
        <!-- <vxe-table-column
              field="puprice"
              title="inquirycompri.puprice"
              align="center"
              :width="150"
            ></vxe-table-column> -->

        <!-- <vxe-table-column
          field="nightofferDd"
          title="inquiryoffer.offerno"
          align="center"
          :width="150"
          :edit-render="{ name: 'input', attrs: {disabled: nameDisabled }}"
        ></vxe-table-column> -->

        <vxe-table-column
          field="action"
          title="public.action"
          align="center"
          :width="130"
        >
          <template slot-scope="scope">
            <!-- <span>{{scope.row }} </span> -->
            <!-- <vxe-button type="text">清除</vxe-button> -->
            <vxe-button
              type="text"
              @click="clickdetails(scope.row)"
            >{{ $t('inquirycompri.Details') }}</vxe-button>
            <!-- 议价详情 -->
            <vxe-button
              type="text"
              @click="detailsBargain(scope.row)"
            >{{ $t('议价详情') }}</vxe-button>
          </template>
        </vxe-table-column>
        <!-- 6.15 -->
        <vxe-table-column
          field="enclosure"
          title="inquiryoffer.enclosure"
          align="center"
          :width="110"
        >
          <template slot-scope="scope">
            <vxe-button
              type="text"
              @click="seepicprd(scope.row)"
            >询价
            </vxe-button>
            <vxe-button
              type="text"
              @click="seepic(scope.row)"
            > 报价 </vxe-button>

            <!-- <vxe-button
              type="text"
              @click="seepic(scope.row)"
            >{{ $t('inquirycompri.See') }}</vxe-button> -->
          </template>
        </vxe-table-column>
      </vxe-table>
      <vxe-pager
        :loading="loading"
        :current-page="tablePage.currentPage"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :page-sizes="[5,10, 20, 100, 200, 500]"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange"
      >
      </vxe-pager>
    </a-card>
    <a-modal
      :title="title3"
      destroyOnClose
      width="918px"
      height="50%"
      :visible.sync="visibleBar"
      :confirmLoading="confirmLoading"
      @cancel="handleCancelBar"
    >

      <vxe-table
        border
        resizable
        stripe
        size="mini"
        highlight-current-row
        show-overflow
        highlight-hover-row
        ref="xTable2"
        :checkbox-config="{ strict: true, checkMethod: checCheckboxkMethod2}"
        :loading="loading"
        :data="tableData2"
        :edit-rules="validRules2"
        :keyboard-config="{ isArrow:true}"
        @checkbox-change="selectChangeEvent"
        @checkbox-all="selectChangeAll"
        :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true}"
        @edit-actived="editActivedEvent"
      >
        <!-- <vxe-table-column
          type="checkbox"
          align="center"
          :width="50"
        ></vxe-table-column> -->

        <vxe-table-column
          field="itm"
          title="inquirycompri.Item"
          align="center"
          :width="50"
        ></vxe-table-column>

        <vxe-table-column
          field="prdNo"
          title="inquiryoffer.goodsno"
          align="center"
          :width="150"
        ></vxe-table-column>

        <vxe-table-column
          field="prdName"
          title="inquiryoffer.goodsname"
          align="center"
          :width="150"
        ></vxe-table-column>
        <vxe-table-column
          field="spc"
          title="inquiryoffer.goodsspecs"
          align="center"
          :width="150"
        ></vxe-table-column>
        <vxe-table-column
          field="cusNo"
          title="inquiryoffer.cusNo"
          align="center"
          :width="80"
        ></vxe-table-column>

        <vxe-table-column
          field="cusName"
          title="inquiryoffer.suppliername"
          align="center"
          :width="110"
        ></vxe-table-column>
        <vxe-table-column
          field="up"
          title="inquiryoffer.repprice"
          align="center"
          :width="90"
          :edit-render="{ name: '$input', props: {type: 'number'}}"
        ></vxe-table-column>
        <vxe-table-column
          field="orgUp"
          title="inquiryoffer.oriQuotation"
          align="center"
          :width="90"
        ></vxe-table-column>
      </vxe-table>

      <div style="height:10px;"></div>
      <template slot="footer">
        <a-button
          key="ok"
          @click="saveBar()"
        >{{ $t('public.save') }}</a-button>
        <a-button
          key="cancel"
          @click="handleCancelBar"
        >{{ $t('public.cancel') }}</a-button>
      </template>
    </a-modal>
    <a-modal
      :title="title"
      destroyOnClose
      width="30%"
      height="40%"
      :visible.sync="purvisible"
      :confirmLoading="confirmLoading"
      @cancel="handleCancel2"
    >
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-form-item :label="$t('prdiversion.bilType')">
            <a-select
              style="width: 95%"
              v-model="queryParam.bilType"
              :placeholder="$t('prdiversion.placeholder.bilType')"
            >
              <a-select-option
                v-for="(val, index) in documentCate"
                :key="index"
                :value="val.value"
              ><span style="margin-right:8px;">{{val.value}}</span><span>{{ val.label}}</span>
              </a-select-option>
            </a-select>

            <!-- <a-select
                v-model="queryParam.bilType"
                @change="radioChange"
                style="width: 95%"
              >
                <a-select-option value="0">
                  {{}}{{ $t('prdiversion.bi.0') }}
                </a-select-option>
              </a-select> -->
          </a-form-item>
        </a-form>
      </div>
      <template slot="footer">

        <a-button
          key="cancel"
          @click="handleCancel2"
        >{{ $t('public.cancel') }}</a-button>
        <a-button
          key="ok"
          @click="pursave()"
          type="danger"
        >{{ $t('public.sure') }}</a-button>
      </template>
    </a-modal>
    <a-modal
      :title="title2"
      destroyOnClose
      width="55%"
      height="auto"
      :visible.sync="nopurvisible"
      :confirmLoading="confirmLoading"
      @cancel="handleCancel"
    >
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <div v-for="(item,index) in notGenerate">
            <!-- prdNo: item.prdNo, qty: item.qty, hzNo: item.hzNo  -->
            <!-- notGenerate -->
            货品代号为{{item.prdNo}}，单号为{{item.hzNo}}，可提交数量为{{item.qty}}，不能生成采购单
          </div>
          <!-- <a-select
                v-model="queryParam.bilType"
                @change="radioChange"
                style="width: 95%"
              >
                <a-select-option value="0">
                  {{}}{{ $t('prdiversion.bi.0') }}
                </a-select-option>
              </a-select> -->
        </a-form>
      </div>
      <template slot="footer">

        <a-button
          key="cancel"
          @click="handleCancel"
        >{{ $t('public.cancel') }}</a-button>
        <!-- <a-button
          key="ok"
          @click="pursave()"
          type="danger"
        >{{ $t('public.sure') }}</a-button> -->
      </template>
    </a-modal>
    <a-modal
      :title="title2"
      destroyOnClose
      width="55%"
      height="auto"
      :visible.sync="nopurvisible2"
      :confirmLoading="confirmLoading"
      @cancel="handleCancelshen"
    >
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <div v-for="(item,index) in notGenerate2">
            货品代号为{{item.prdNo}}，单号为{{item.hzNo}}，可提交数量为{{item.qty}}，请修改数量，点比价预审，完成检验。
          </div>
        </a-form>
      </div>
      <template slot="footer">
        <a-button
          key="cancel"
          @click="handleCancelshen"
        >{{ $t('public.cancel') }}</a-button>
      </template>
    </a-modal>

    <!-- 点击才消失
 -->
    <el-dialog
      class="tankuang"
      title="采购数量"
      :visible.sync="visiblenum"
      width="55%"
      height="auto"
      :before-close="handleClose"
    >
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <div v-for="(item,index) in quanPrompt">
            货品代号为{{item.prdNo}}，单号为{{item.hzNo}}，采购数量为{{item.qtyPos}}，erp结余数量为{{item.qtyErpLeft}}，采购数量之和大于erp结余数量。
          </div>
        </a-form>
      </div>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <a-button @click="handleCancelnum">取 消</a-button>
        <a-button
          key="ok"
          @click="promptSure()"
          type="danger"
          style="margin-left:10px;"
        >{{ $t('public.sure') }}</a-button>
        <!-- <el-button
          type="primary"
          @click="dialogVisible = false"
        >确 定</el-button> -->
      </span>
    </el-dialog>
    <!-- 点击才消失 -->

    <!-- <a-modal
      :title="titlenum"
      destroyOnClose
      width="55%"
      height="auto"
      :visible.sync="visiblenum"
      :confirmLoading="confirmLoading"
      @cancel="handleCancelnum"
    >
     
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <div v-for="(item,index) in quanPrompt">
            货品代号为{{item.prdNo}}，单号为{{item.hzNo}}，可提交数量为{{item.qty}}，采购数量之和大于erp结余数量。
          </div>
        </a-form>
      </div>
      <template slot="footer">
        <a-button
          key="cancel"
          @click="handleCancelnum"
        >{{ $t('public.cancel') }}</a-button>
        <a-button
          key="ok"
          @click="promptSure()"
          type="danger"
        >{{ $t('public.sure') }}</a-button>
      </template>
    </a-modal> -->
    <!-- 议价详情 -->
    <a-modal
      :title="titleBargain"
      destroyOnClose
      width="50%"
      height="50%"
      :visible.sync="visibleBargain"
      :confirmLoading="confirmLoading"
      @cancel="handleCancelBargain"
    >

      <vxe-table
        border
        resizable
        stripe
        size="mini"
        highlight-current-row
        show-overflow
        highlight-hover-row
        ref="xTableBargain"
        :checkbox-config="{ strict: true}"
        :loading="loading"
        :data="tableDataBargain"
        :edit-rules="validRules2"
        :keyboard-config="{ isArrow: true }"
        @checkbox-change="selectChangeEvent"
        @checkbox-all="selectChangeAll"
        :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true}"
      >
        <!-- <vxe-table-column
          field="itm"
          title="inquirycompri.Item"
          align="center"
          :width="50"
        ></vxe-table-column> -->
        <vxe-table-column
          field="hisUp"
          title="inquiryoffer.repprice"
          align="center"
        ></vxe-table-column>
        <vxe-table-column
          field="priceDd"
          title="报价时间"
          align="center"
        ></vxe-table-column>
      </vxe-table>
      <div style="height:10px;"></div>

      <template slot="footer">
        <!-- <a-button
          key="ok"
          @click="saveBargain()"
        >{{ $t('public.save') }}</a-button> -->
        <div>
          <span style="display:inline-block;float:left;">第一次报价：{{tableDataBargain.orgUp}}</span>
          <a-button
            style="display:inline-block"
            key="cancel"
            @click="handleCancelBargain"
          >{{ $t('public.cancel') }}</a-button>
        </div>
      </template>
    </a-modal>
    <!-- 议价详情 -->
    <ModalPic ref="ModalPic" />
    <ModalPicprd ref="ModalPicprd" />
    <ModalDetail ref="ModalDetail" />
  </div>
</template>

<script>
// var arr = [{
//   "id": "1001",
//   "name": "值1",
//   "value": "111"
// },
// {
//   "id": "1001",
//   "name": "值1",
//   "value": "11111"
// },
// {
//   "id": "1002",
//   "name": "值2",
//   "value": "25462"
// },
// {
//   "id": "1002",
//   "name": "值2",
//   "value": "23131"
// },
// {
//   "id": "1002",
//   "name": "值2",
//   "value": "2315432"
// },
// {
//   "id": "1003",
//   "name": "值3",
//   "value": "333333"
// }
// ];

// var map = {},
//   dest = [];
// for (var i = 0; i < arr.length; i++) {
//   var ai = arr[i];
//   if (!map[ai.id]) { //依赖分组字段可自行更改！
//     dest.push({
//       id: ai.id, //依赖分组字段可自行更改！
//       data: [ai]
//     });
//     map[ai.id] = ai; //依赖分组字段可自行更改！
//   } else {
//     for (var j = 0; j < dest.length; j++) {
//       var dj = dest[j];
//       if (dj.id == ai.id) { //依赖分组字段可自行更改！
//         dj.data.push(ai);
//         break;
//       }
//     }
//   }
// }




function getBase64 (img, callback) {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result));
  reader.readAsDataURL(img);
}
import validatenumord from '@/util/validate'
import moment from 'moment'
import ModalPic from './ModalPic'
import ModalPicprd from './ModalPicprd'
import ModalDetail from './ModalDetail'
import { mapState } from 'vuex'
import { fetchList, savedetails, quotationsub, saveComparePriceToHj, genPurchase, bargain,draftContra } from '@/api/srm/inquirycompri'
import { paytypeFind } from '@/api/srm/inquiryoffer'
import { findBilType } from '@/api/srm/prdiversion'
import XEUtils from 'xe-utils'
export default {
  components: {
    ModalPic,
    ModalDetail,
    ModalPicprd
  },
  data () {
    return {
      srm_inquirycompri_query: 'srm_inquirycompri_query',
      srm_inquirycompri_reset: 'srm_inquirycompri_reset',
      srm_inquirycompri_save: 'srm_inquirycompri_save',
      srm_inquirycompri_sure: 'srm_inquirycompri_sure',
      srm_inquirycompri_contract: 'srm_inquirycompri_contract',
      srm_inquirycompri_purchase: 'srm_inquirycompri_purchase',
      tablePage: {
        currentPage: 1,
        pageSize: 200,
        total: 0
      },
      qcStartDd: '',
      qcEndDd: '',
      hzStartDd: '',
      hzBjEndDd: '',
      srstartD: '',
      srendDd: '',

      lastendOpen: false,
      endOpen: false,
      endOpen2: false,
      preendOpen: false,
      loading: false,
      submitStartDd: null,
      submitEndEndDd: null,
      loading2: null,
      loading3: null,
      loading4: null,
      loading5: null,
      loading6: null,
      queryParam: {
        qcYes: '0',
        prdNo: '',
        prdName: '',
        cusNo: '',
        qcStartDd: moment(new Date() - 7 * 24 * 3600 * 1000).format('YYYY-MM-DD 00:00:00'),
        qcEndDd: moment(new Date()).format('YYYY-MM-DD 23:59:59'),
        hzStartDd: null,
        hzBjEndDd: null,
        submitStartDd: null,
        submitEndEndDd: null,
        cusName: null,
        posStatus: 'F'
      },
      formItemLayout: {
        labelCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 15 },
        },
      },
      tableData: [],
      tableData2: [],
      loading: false,
      imageUrl: '',
      tableHeight: window.innerHeight - 330,
      nameDisabled: false,
      nameDisabledrem: false,
      qtyPosDisabled: false,
      multipleSelection: [],
      sexList: [],
      sexDisabled: false,
      paytypename: [],
      purvisible: false,
      documentCate: [],
      confirmLoading: false,
      title: '生成采购单',
      mfXjBjiaSlVoListorder: [],
      nopurvisible: false,
      nopurvisible2: false,
      visiblenum: false,
      plainOptions: ['是', '否', '全部'],
      value1: '是',
      value2: '否',
      value3: '全部',
      printSign: '否',
      qcYesbian: false,
      notGenerate: [],
      notGenerate2: [],
      qcYesbian2: false,
      title2: '不能生成采购单',
      shuxing: { name: 'input' },
      sumArryList: [],
      sumArry: {},
      itm: [],
      pursheng: false,
      qtyflag: false,
      qtyflagsubm: false,
      title3: '议价',
      titlenum: '采购数量',
      visibleBar: false,
      upbar: null,
      mfXjBjiaSlVoList2: [],
      validRules: {
        qtyHz: [
          { type: 'number', message: '请录入数字', trigger: 'change' },
          { validator: validatenumord, trigger: 'change' }
        ],
        qty: [
          { type: 'number', message: '请录入数字', trigger: 'change' },
          { validator: validatenumord, trigger: 'change' }
        ],
        qtyPos: [
          { type: 'number', message: '请录入数字', trigger: 'change' },
          { validator: validatenumord, trigger: 'change' }
        ],
      },
      validRules2: {
        up: [
          { type: 'number', message: '请录入数字', trigger: 'change' },
          { validator: validatenumord, trigger: 'change' }
        ],
      },
      math: null,
      quanPrompt: [],
      purchaseFlag: false,
      purchaseFlag2: false,
      purchaseFlag3: false,
      selectRecordStore: [],
      selectRecordStore2: [],
      selectRecordStore3: [],
      panduan: true,
      visibleBargain: false,
      tableDataBargain: [],
      titleBargain: '议价详情',
    }
  },
  computed: {
  },
  created () {
    this.qcStartDd = moment(new Date() - 7 * 24 * 3600 * 1000)
    this.hzStartDd = null
    this.hzBjEndDd = null
    this.qcEndDd = moment(new Date())
  },
  mounted () {
    // if (e.target.value == '全部') {
    //   this.queryParam.posStatus == null
    // } else if (e.target.value == '否') {
    //   this.queryParam.posStatus == 'F'
    // } else {
    //   this.queryParam.posStatus == 'T'
    // }
    if (this.queryParam.qcYes == '2' || this.queryParam.qcYes == '0') {
      this.pursheng = true
    } else {
      if (this.queryParam.posStatus == null) {
        this.pursheng = true
      }
    }


    this.documentCate = []
    findBilType().then((res) => {
      res.data.forEach((item, index) => {
        let catearry = { label: item.bilTypeName, value: item.bilType }
        this.documentCate.push(catearry)
      })
      let catearryall = { label: '全部', value: '' }
      this.documentCate.push(catearryall)
    }).catch(err => this.requestFailed(err))
    this.sexList = []
    paytypeFind().then((res) => {
      this.paytypename = res.data
      res.data.forEach((item, index) => {
        let sexarry = { label: item.name, value: index }
        this.sexList.push(sexarry)
      })
    }).catch(err => this.requestFailed(err))
    window.onresize = () => {
      return (() => {
        this.tableHeight = window.innerHeight - 330
      })()
    }
  },
  filters: {
  },
  watch: {
    submitStartDd (val) {
      if (val) {
        this.queryParam.submitStartDd = moment(val).format('YYYY-MM-DD 00:00:00')
      } else {
        this.queryParam.submitStartDd = null
      }
    },
    qcStartDd (val) {
      if (val) {
        this.queryParam.qcStartDd = moment(val).format('YYYY-MM-DD 00:00:00')
      } else {
        this.queryParam.qcStartDd = null
      }
    },
    hzStartDd (val) {
      if (val) {
        this.queryParam.hzStartDd = moment(val).format('YYYY-MM-DD 00:00:00')
      } else {
        this.queryParam.hzStartDd = null
      }
    },
    hzBjEndDd (val) {
      if (val) {
        this.queryParam.hzBjEndDd = moment(val).format('YYYY-MM-DD 23:59:59')
      } else {
        this.queryParam.hzBjEndDd = null
      }
    },
    submitEndEndDd (val) {
      if (val) {
        this.queryParam.submitEndEndDd = moment(val).format('YYYY-MM-DD 23:59:59')
      } else {
        this.queryParam.submitEndEndDd = null
      }
    },
    qcEndDd (val) {
      if (val) {
        this.queryParam.qcEndDd = moment(val).format('YYYY-MM-DD 23:59:59')
      } else {
        this.queryParam.qcEndDd = null
      }
    },
  },
  methods: {
    generate(){
      let selectRecords = this.multipleSelection
      if(selectRecords.length){
        let zerodata
        let zerotrue = false
        
        selectRecords.forEach((item, index) => {
          if(index == 0){
            zerodata = item.cusName
          }else{
            if(item.cusName == zerodata ){
            }else{
              zerotrue = true
            }
          }
        })
        if(zerotrue){
          this.$message.warning('供应商名称不同，不能生成合同草稿')
          return
        }
        selectRecords.forEach((item, index) => {
          // let sexarry = { itm: item.itm, hzNo: item.hzNo, cusNo: item.cusNo, qty: item.qty }
          // mfXjBjiaSlVoList.push(sexarry)
          // estDd submitDd
          if (item.submitDd == null || item.submitDd == '') { } else {
            if (item.submitDd.indexOf(':') == -1) {
              item.submitDd = item.submitDd + ' 00:00:00'
            }
          }
          if (item.estDd == null || item.estDd == '') { } else {
            if (item.estDd.indexOf(':') == -1) {
              item.estDd = item.estDd + ' 00:00:00'
            }
          }
          // 提示
          if (this.queryParam.qcYes == '2') {
            if (XEUtils.subtract(item.qtyTj, item.qty) < 0) {
              item.qty = ''
              this.qtyflagsubm = true
            }
          }
          // 提示
        })
        draftContra({mfXjBjiaSlVoList:this.multipleSelection,contraSource:'2'}).then((res) => {
           if (res.data) {
            this.$message.success(this.$t('public.success'))
          }
        }).catch((err) => {
          this.loading = false
          this.requestFailed(err)
        })
      }else {
        // this.loading = false
        this.$message.warning(this.$t('public.list'))
      }
    },
    handleClose () {
      // this.nopurvisible = true
    },
    handleCancelBar () {
      this.visibleBar = false
    },
    bargaining () {
      this.mfXjBjiaSlVoList2 = this.$refs.xTable.getCheckboxRecords()
      if (this.mfXjBjiaSlVoList2.length === 0) return this.$message.warning('请至少选择一条数据！')
      let selectRecords = JSON.stringify(this.$refs.xTable.getCheckboxRecords())
      this.tableData2 = JSON.parse(selectRecords)
      this.visibleBar = true
    },
    handleCancel () {
      this.visibleBar = false
    },
    upclick () {
      this.panduan = true
      if (this.queryParam.qcYes == '1') {
        this.panduan = false
      }
    },
    cancopy () {
      this.panduan = false
    },
    detailsBargain (row) {
      row.disPriceList.orgUp = row.orgUp
      this.tableDataBargain = row.disPriceList
      if (row.disPriceList == null || row.disPriceList == '') {
        this.$message.warning('无议价记录')
      } else {
        this.visibleBargain = true
      }

    },
    handleCancelBargain () {
      this.visibleBargain = false
    },
    saveBar () {
      this.tableData2.forEach((item, index) => {
        if (item.submitDd == null || item.submitDd == '') { } else {
          if (item.submitDd.indexOf(':') == -1) {
            item.submitDd = item.submitDd + ' 00:00:00'
          }
        }
        if (item.estDd == null || item.estDd == '') { } else {
          if (item.estDd.indexOf(':') == -1) {
            item.estDd = item.estDd + ' 00:00:00'
          }
        }
        if (item.qcYes == '未处理') {
          item.qcYes = '0'

        } else if (item.qcYes == '已审核') {
          item.qcYes = '1'

        } else if (item.qcYes == '比价预审') {
          item.qcYes = '2'
        }
      })
      // const selectRecords = this.$refs.xTable.getCheckboxRecords()
      // selectRecords.forEach(i => {
      //   this.tableData.forEach(j => {
      //     if (i.wyID === j.wyID) {
      //       j.submitDd = moment(this.subdata).format('YYYY-MM-DD')
      //     }
      //   })
      // })
      bargain({
        mfXjBjiaSlVoList: this.tableData2
      })
        .then((res) => {
          this.getList()
        }).catch((err) => {
          this.loading = false
          this.requestFailed(err)
        })
      this.visibleBar = false

    },
    checCheckboxkMethod2 ({ row }) {
      return parseFloat(row.qty) != 0 && parseFloat(row.qtyHz) != 0
    },
    bjDate (date, date1) {
      var date = new Date(date);
      var date1 = new Date(date1);
      if (date.getTime() - date1.getTime() < 0) {
        return '第二个时间大';
      } else {
        return '第一个时间大';
      }
    },
    editActivedEvent ({ rowIndex, row }) {
      // this.nameDisabled = row.qcYes === '已审核'
      if (row.qcYes === '已审核') {
        this.nameDisabled = true
      } else if (row.qcYes === '未处理') {
        this.nameDisabled = true
      } else {
        this.nameDisabled = false
      }
      if (row.qcYes === '已审核') {
        this.qtyPosDisabled = true
      }
      else {
        this.qtyPosDisabled = false
      }
    },
    onChange3 (e) {
      if (e.target.value == '全部') {
        this.queryParam.posStatus = null
      } else if (e.target.value == '否') {
        this.queryParam.posStatus = 'F'
      } else {
        this.queryParam.posStatus = 'T'
      }
      // if (this.queryParam.qcYes == '2' || this.queryParam.qcYes == '0') {
      //   this.pursheng = true
      // } else {
      //   if (this.queryParam.posStatus == null) {
      //     this.pursheng = true
      //   }
      // }
      if (this.queryParam.qcYes == '2' || this.queryParam.qcYes == '0') {
        this.pursheng = true
      } else if (this.queryParam.posStatus == null) {
        this.pursheng = true
      } else {
        this.pursheng = false
      }

    },
    handleCancel () {
      this.notGenerate = []
      this.nopurvisible = false
    },
    handleCancelshen () {
      this.notGenerate2 = []
      this.nopurvisible2 = false
    },
    handleCancelnum () {
      this.quanPrompt = []
      this.visiblenum = false
      this.purvisible = false
      this.purchaseFlag = false
      this.purchaseFlag2 = false
      this.purchaseFlag3 = false

    },
    printNumpos (row) {
      if (!isNaN(row.qtyPos)) {
      } else {
        row.qtyPos = null
      }
      return row.qtyPos
    },
    printNum (row) {
      // if (this.queryParam.qcYes != '1' && this.queryParam.qcYes != '0') {
      // if (XEUtils.subtract(row.qtyTj, row.qty) < 0) {
      //   this.$message.error('修改数量已大于供应能力！')
      //   row.qty = ''
      //   return ''
      // }
      if (row.qty < 0) {
        this.$message.error('可提交数量不能小于0！')
        row.qty = ''
        return ''
      }
      if (!isNaN(row.qty)) {
      } else {
        row.qty = null
      }
      return row.qty
      // } else {
      //   return row.qty
      // }
    },
    handleCancel2 () {
      this.purvisible = false
      this.nopurvisible = false
    },
    clickdetails (row) {
      this.$refs.ModalDetail.create({ title: '详情' }, row)
    },
    reset () {
      this.data = {}
      this.queryParam = {
        qcYes: '0',
        prdNo: '',
        prdName: '',
        cusNo: '',
        cusName: null,
        qcStartDd: moment(new Date() - 7 * 24 * 3600 * 1000).format('YYYY-MM-DD 00:00:00'),
        qcEndDd: moment(new Date()).format('YYYY-MM-DD 23:59:59'),
        hzStartDd: null,
        hzBjEndDd: null,
        submitStartDd: null,
        submitEndEndDd: null,
      }
      this.hzStartDd = null
      this.hzBjEndDd = null
      this.submitStartDd = null
      this.submitEndEndDd = null
    },
    handleRenovate (e) {
      let target = e.target
      if (target.nodeName == 'SPAN' || target.nodeName == 'I') {
        target = e.target.parentNode
      }
      target.blur()
      refresh()
        .then((res) => {
          if (res.data) {
            this.$message.success(this.$t('public.success'))
          }
        })
        .catch((err) => {
          this.requestFailed(err)
        })
    },
    getList () {
      this.loading = true
      this.tableData = []
      if (this.queryParam.prdNo != '' && this.queryParam.prdNo != null) {
        this.queryParam.prdNo = this.queryParam.prdNo.replace(/^\s+|\s+$/g, "")
      }
      if (this.queryParam.prdName != '' && this.queryParam.prdName != null) {
        this.queryParam.prdName = this.queryParam.prdName.replace(/^\s+|\s+$/g, "")
      }
      if (this.queryParam.cusNo != '' && this.queryParam.cusNo != null) {
        this.queryParam.cusNo = this.queryParam.cusNo.replace(/^\s+|\s+$/g, "")
      }
      if (this.queryParam.cusName != '' && this.queryParam.cusName != null) {
        this.queryParam.cusName = this.queryParam.cusName.replace(/^\s+|\s+$/g, "")
      }
      if (this.queryParam.qcno != '' && this.queryParam.qcno != null) {
        this.queryParam.qcno = this.queryParam.qcno.replace(/^\s+|\s+$/g, "")
      }
      if (this.queryParam.offerno != '' && this.queryParam.offerno != null) {
        this.queryParam.offerno = this.queryParam.offerno.replace(/^\s+|\s+$/g, "")
      }
      if (this.queryParam.qcNo != '' && this.queryParam.qcNo != null) {
        this.queryParam.qcNo = this.queryParam.qcNo.replace(/^\s+|\s+$/g, "")
      }

      if (this.queryParam.qcYes == '0' || this.queryParam.qcYes == '2') {
        this.queryParam.posStatus = ''
        // delete this.queryParam.posStatus;
      }
      if (this.queryParam.qcYes == '1') {
        if (this.printSign == '全部') {
          this.queryParam.posStatus = null
        } else if (this.printSign == '否') {
          this.queryParam.posStatus = 'F'
        } else {
          this.queryParam.posStatus = 'T'
        }
      }


      if (this.queryParam.qcYes == '2' || this.queryParam.qcYes == '0') {
        this.pursheng = true
      } else {
        if (this.queryParam.posStatus == null) {
          this.pursheng = true
        }
      }


      // prdNo prdName  cusNo cusName  qcno offerno
      fetchList(
        Object.assign(
          {
            qcYes: this.queryParam.qcYes,
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
          },
          this.queryParam,
        )
      )
        .then((res) => {
          if (this.queryParam.qcYes == '1') {
            this.panduan = false
          }
          let that = this
          this.loading = false
          this.tableData = res.data.records
          // let grouping = []
          // for (let i = 0; i < this.tableData.length; i++) {
          //   let hznoItm = tableData[i].hzNo + tableData[i].itm
          //   for (let j = i + 1; j < this.tableData.length; j++) {
          //     if (i > 0) {
          //       if (hznoItm == tableData[i - 1].hzNo + tableData[i - 1].itm) {

          //       } else {
          //         if (tableData[j].hzNo + tableData[j].itm == hznoItm) {
          //           grouping[i].push(tableData[j])
          //         }

          //       }
          //     }
          //     if (i == 0) {
          //       if (tableData[j].hzNo + tableData[j].itm == hznoItm) {
          //         grouping[i].push(tableData[j])
          //       }
          //     }

          //   }
          //   let abcd = this.tableData[i].hzNo + this.tableData[i].itm
          //   if (tableData[i].hzNo + tableData[i].itm == abcd) {
          //     grouping.push(tableData[i])
          //   }
          // }

          var map = {},
            dest = [];
          for (var i = 0; i < this.tableData.length; i++) {

            var ai = this.tableData[i];
            ai.id = ai.hzNo + ai.itm
            if (!map[ai.id]) { //依赖分组字段可自行更改！
              dest.push({
                id: ai.id, //依赖分组字段可自行更改！
                data: [ai]
              });
              map[ai.id] = ai; //依赖分组字段可自行更改！
            } else {
              for (var j = 0; j < dest.length; j++) {
                var dj = dest[j];
                if (dj.id == ai.id) { //依赖分组字段可自行更改！
                  dj.data.push(ai);
                  break;
                }
              }
            }
          }
          dest.forEach((item, index) => {
            item.totalQty = 0
            // var totalQty = 0
            item.data.forEach((item2, index2) => {
              item.totalQty += parseFloat(item2.qtyHz)
              if (item.totalQty > item2) {
              }
            })
          })
          // this.tableData.forEach((item, index) => {
          //   let abcd = item.hzNo + item.itm
          //   // if (index != 0) {
          //   if (item.hzNo + item.itm == abcd) {
          //     grouping.push(item)
          //   }
          //   // }
          // })

          this.tableData.forEach((item, index) => {
            item.qtyErpLeft = +item.qtyHz - +item.qtyErpBuy
            item.colorJudg = false
            // if (item.qcYes == 'T') {
            //   item.qcYes = '已审核'
            // } else if (item.saveYes == 'T') {
            //   item.qcYes = '比价预审'
            // } else {
            //   item.qcYes = '未处理'
            // }
            // if (item.qcYes == '' && item.qcStatus == 'T' && item.saveYes == '') {
            //   item.qcYes = '未处理'
            // } else if (item.qcStatus == 'T' && item.qcYes == 'T') {
            //   item.qcYes = '已审核'
            // } else if (item.qcStatus == 'T' && item.saveYes == 'T') {
            //   item.qcYes = '比价预审'
            // }
            if (this.queryParam.qcYes == '0') {
              item.qcYes = '未处理'
            } else if (this.queryParam.qcYes == '1') {
              item.qcYes = '已审核'
            } else if (this.queryParam.qcYes == '2') {
              item.qcYes = '比价预审'
            }
            if (item.estDd == null) {
            } else {
              item.estDd = item.estDd.substring(0, 10)
            }
            item.id = index
            item.wyID = item.hzNo + item.itm + item.cusNo
            if (item.orgUp) {
              item.orgUp = parseFloat(item.orgUp).toFixed(3)
            }
            if (item.qtyErpLeft) {
              item.qtyErpLeft = parseFloat(item.qtyErpLeft).toFixed(3)
            }
            if (item.qtyErpBuy) {
              item.qtyErpBuy = parseFloat(item.qtyErpBuy).toFixed(3)
            }
            if (this.queryParam.qcYes == '0') {
              if (!!item.qty) {
                item.qtyPos = item.qty
              }
            }
            item.up = parseFloat(item.up).toFixed(3)
            item.qtyHz = parseFloat(item.qtyHz).toFixed(2)
            if (item.payType === null || item.payType === '') {
            } else {
              // this.tableData.forEach((item, index) => {
              for (let i = 0; i < this.paytypename.length; i++) {
                if (item.payType == '' || item.payType == null) { } else {
                  if (parseInt(item.payType) == i) {
                    item.payType = this.paytypename[i].name
                  }
                }
              }
              // })
              // else if (item.payType == '1') {
              //   item.payType = that.paytypename[1].name
              // } else if (item.payType == '2') {
              //   item.payType = that.paytypename[2].name
              // } else if (item.payType == '3') {
              //   item.payType = that.paytypename[3].name
              // }
            }
            if (item.submitDd) {
              item.submitDd = item.submitDd.substring(0, 10)
            }
            if (item.estDd) {
              item.estDd = item.estDd.substring(0, 10)
            }
          })
          this.purchaseFlag = false
          this.purchaseFlag2 = false
          this.quanPrompt = []
          this.visiblenum = false
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.multipleSelection = []
        })
        .catch((err) => {
          this.loading = false
          this.tableData = []
          this.requestFailed(err)
        })
    },
    handleQuery () {
      this.tablePage.currentPage = 1
      if (this.multipleSelection.length != 0) this.$refs.xTable.clearSelection()
      this.getList()
    },
    UploadUrl () { return '' },
    file2Xce (file) {
      return new Promise(function (resolve, reject) {
        const reader = new FileReader()
        reader.onload = function (e) {
          const data = e.target.result
          this.wb = XLSX.read(data, {
            type: 'binary',
          })
          const result = []
          this.wb.SheetNames.forEach((sheetName) => {
            result.push({
              sheetName: sheetName,
              sheet: XLSX.utils.sheet_to_json(this.wb.Sheets[sheetName]),
            })
          })
          resolve(result)
        }
        reader.readAsBinaryString(file.raw)
      })
    },
    importExcel (param) {
      let _this = this
      _this
        .file2Xce(param)
        .then((item) => {
          if (item && item.length > 0) {
            if (item[0] && item[0].sheet && item[0].sheet.length) {
              let data = item[0].sheet
              let arr = []
              data.forEach((i) => {
                arr.push(Object.values(i))
              })
              _this.fileNos = [].concat.apply([], arr)
              let obj = {
                title: '导入',
                isShow: false,
                fileNo: _this.queryParam.fileNo,
                fileNos: _this.fileNos,
              }
              _this.$refs.SearchModal.create(obj)
            }
          }
        })
        .catch((error) => { })
    },
    beforeUpload (file) {
      const Xls = file.name.split('.')
      const islt20 = file.size / 1024 / 1024 < 20
      if (Xls[1] == 'xls' || Xls[1] == 'xlsx') {
      } else {
        this.$message.warning('只能上传xls或者xlsx格式的文件')
        return
      }
      if (!islt20) {
        this.$message.warning('文件大小不能超过20M')
        return
      }
      return true
    },
    handleChange (info) {
      if (info.file.status === 'uploading') {
        this.loading = true;
        return;
      }
      if (info.file.status === 'done') {
        // Get this url from response in real world.
        getBase64(info.file.originFileObj, imageUrl => {
          this.imageUrl = imageUrl;
          this.loading = false;
        });
      }
    },
    beforeUpload (file) {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        this.$message.error('You can only upload JPG file!');
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error('Image must smaller than 2MB!');
      }
      return isJpgOrPng && isLt2M;
    },
    seepic (row) {
      this.$refs.ModalPic.create({ title: '查看' }, row.imageUrlList)
    },
    seepicprd (row) {
      this.$refs.ModalPicprd.create({ title: '查看' }, row.homeImageUrlList, row)
    },
    callback () {
      setTimeout(() => {
        this.$nextTick(() => {

        })
      }, 0)
    },
    upload () {

    },
    selectChangeEvent ({ checked, records, reserves, row, rowIndex }) {
      this.multipleSelection = records
      this.math = Math.random()
      var map = {},
        dest = [];
      for (var i = 0; i < records.length; i++) {
        var ai = records[i];
        ai.id = ai.hzNo + ai.itm
        if (!map[ai.id]) { //依赖分组字段可自行更改！
          dest.push({
            id: ai.id, //依赖分组字段可自行更改！
            data: [ai],
            wyID: ai.wyID
          });
          map[ai.id] = ai; //依赖分组字段可自行更改！
        } else {
          for (var j = 0; j < dest.length; j++) {
            var dj = dest[j];
            if (dj.id == ai.id) { //依赖分组字段可自行更改！
              dj.data.push(ai);
              break;
            }
          }
        }
      }
      this.tableData.forEach((i, ff) => {
        i.colorJudg = false
        if (!dest.length) {
          i.colorJudg = false
        }
        dest.forEach((item, index) => {
          // if (item.wyID == i.wyID) {
          item.totalQty = 0
          // var totalQty = 0
          item.data.forEach((item2, index2) => {
            // if (item.wyID == i.wyID) {
            item.totalQty += parseFloat(item2.qtyPos)
            // }
          })
          item.data.forEach((item2, index2) => {

            if (item2.wyID == i.wyID) {
              if (+item.totalQty > +item2.qtyErpLeft) {
                i.colorJudg = this.math
                // alert(item2.hzNo + item2.itm)
              } else {
                i.colorJudg = false
              }
            }
            // else {
            //   i.colorJudg = false
            // }
          })
          // }
        })
      })
      this.tableData.sort()
    },
    selectChangeAll ({ records, checked }) {
      this.multipleSelection = records
      this.math = Math.random()
      var map = {},
        dest = [];
      for (var i = 0; i < records.length; i++) {
        var ai = records[i];
        ai.id = ai.hzNo + ai.itm
        if (!map[ai.id]) { //依赖分组字段可自行更改！
          dest.push({
            id: ai.id, //依赖分组字段可自行更改！
            data: [ai],
            wyID: ai.wyID
          });
          map[ai.id] = ai; //依赖分组字段可自行更改！
        } else {
          for (var j = 0; j < dest.length; j++) {
            var dj = dest[j];
            if (dj.id == ai.id) { //依赖分组字段可自行更改！
              dj.data.push(ai);
              break;
            }
          }
        }
      }
      this.tableData.forEach((i, ff) => {
        i.colorJudg = false
        if (!dest.length) {
          i.colorJudg = false
        }
        dest.forEach((item, index) => {
          // if (item.wyID == i.wyID) {
          item.totalQty = 0
          // var totalQty = 0
          item.data.forEach((item2, index2) => {
            // if (item.wyID == i.wyID) {
            item.totalQty += parseFloat(item2.qtyPos)
            // }
          })
          item.data.forEach((item2, index2) => {
            if (item2.wyID == i.wyID) {
              if (+item.totalQty > +item2.qtyErpLeft) {
                i.colorJudg = this.math
              } else {
                i.colorJudg = false
              }
            }
            // else {
            //   i.colorJudg = false
            // }
          })
        })
      })
      this.tableData.sort()
    },
    onChange (date) {
    },
    handleStartOpenChange (open) {
      if (!open) {
        this.endOpen = true
      }
    },
    handleStartOpenChange2 (open) {
      if (!open) {
        this.endOpen2 = true
      }
    },
    handleEndOpenChange (open) {
      this.endOpen = open
    },
    handleEndOpenChange2 (open) {
      this.endOpen2 = open
    },
    preStartOpenChange (open) {
      if (!open) {
        this.preendOpen = true
      }
    },
    preEndOpenChange (open) {
      this.preendOpen = open
    },
    lastEndOpenChange () {
      this.lastendOpen = open
    },
    lastStartOpenChange () {
      if (!open) {
        this.lastendOpen = true
      }
    },
    radioChange () {
      if (this.queryParam.qcYes == '1') {
        this.qcYesbian = true
        this.shuxing = ''
      } else {
        this.qcYesbian = false
        this.shuxing = { name: 'input' }
      }
      if (this.qcYesbian2 == '0' || this.qcYesbian2 == '2') {
        this.qcYesbian2 = true
      } else {
        this.qcYesbian2 = false
      }
      this.tableData.sort()
      if (this.queryParam.qcYes == '2' || this.queryParam.qcYes == '0') {
        this.pursheng = true
      } else if (this.queryParam.posStatus == null) {
        this.pursheng = true
      } else {
        this.pursheng = false
      }
    },
    refresh () {
    },
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    promptSure () {
      this.visiblenum = false
      let that = this
      if (this.purchaseFlag) {
        this.purchaseFlag = false
        let selectRecordszero = []
        that.selectRecordStore.forEach((item, index) => {
          if (item.qtyPos == 0 || item.qtyErpLeft == 0 || item.qtyErpLeft == null) {
          } else {
            selectRecordszero.push(item)
          }
        })
        if (selectRecordszero.length) {
        } else {
          this.$message.warning('采购数量、erp结余数量不能为0')
          return
        }
        this.$confirm({
          title: this.$t('public.del.title'),
          content: "确定比价预审？",
          okText: this.$t('public.sure'),
          okType: 'danger',
          cancelText: this.$t('public.cancel'),
          onOk () {
            that.loading = true
            selectRecordszero.forEach((item, index) => {
              if (item.qcYes == '未处理') {
                item.qcYes = '0'
              } else if (item.qcYes == '已审核') {
                item.qcYes = '1'

              } else if (item.qcYes == '比价预审') {
                item.qcYes = '2'
              }
            })
            savedetails({
              mfXjBjiaSlVoList: selectRecordszero,
              qcYes: that.queryParam.qcYes
            })
              .then(() => {
                that.loading = false
                that.getList()
                that.$message.success(that.$t('public.success'))
              })
              .catch((err) => {
                that.loading = false
                that.requestFailed(err)
              })
          },
          onCancel () {
            that.loading = false
          },
        })
      } else if (this.purchaseFlag2) {
        this.purchaseFlag2 = false
        let selectRecordszero = []
        that.selectRecordStore2.forEach((item, index) => {
          if (item.qtyPos == 0 || item.qtyErpLeft == 0 || item.qtyErpLeft == null) {
          } else {
            selectRecordszero.push(item)
          }
        })
        if (selectRecordszero.length) {
        } else {
          this.$message.warning('采购数量、erp结余数量不能为0')
          return
        }
        // erp
        saveComparePriceToHj({
          mfXjBjiaSlVoList: selectRecordszero,
        })
          .then(() => {
            that.loading = false
          })
          .catch((err) => {
            that.loading = false
            that.requestFailed(err)
          })
        // erp
        this.$confirm({
          title: this.$t('public.del.title'),
          content: "确定比价审核？",
          okText: this.$t('public.sure'),
          okType: 'danger',
          cancelText: this.$t('public.cancel'),
          onOk () {
            that.loading = true
            selectRecordszero.forEach((item, index) => {
              if (item.qcYes == '未处理') {
                item.qcYes = '0'
              } else if (item.qcYes == '已审核') {
                item.qcYes = '1'

              } else if (item.qcYes == '比价预审') {
                item.qcYes = '2'
              }
            })
            quotationsub({
              // mfXjBjiaSlVoList,
              mfXjBjiaSlVoList: selectRecordszero,
              qcYes: that.queryParam.qcYes
            })
              .then((res) => {

                if (res.data.length > 0) {
                  that.nopurvisible2 = true

                  res.data.forEach((item, index) => {


                    let notGenarry2 = { prdNo: item.prdNo, qty: item.qty.toString(), hzNo: item.hzNo }
                    that.notGenerate2.push(notGenarry2)
                  })

                } else {
                  that.nopurvisible2 = false
                }
                that.loading = false
                that.getList()
                that.$message.success(that.$t('public.success'))
              })
              .catch((err) => {
                that.loading = false
                that.requestFailed(err)
              })
          },
          onCancel () {
            that.loading = false
          },
        })
      } else if (this.purchaseFlag3) {
        this.purchaseFlag3 = false
        this.purvisible = true
        // let mfXjBjiaSlVoList
        // mfXjBjiaSlVoList = that.selectRecordStore3
        // genPurchase({
        //   mfXjBjiaSlVoList,
        //   bilType: this.queryParam.bilType,
        // })
        //   .then((res) => {

        //     this.purvisible = false

        //     if (res.data.length > 0) {
        //       this.nopurvisible = true
        //       res.data.forEach((item, index) => {
        //         let notGenarry = { prdNo: item.prdNo, qty: item.qty, hzNo: item.hzNo }
        //         this.notGenerate.push(notGenarry)
        //       })

        //     } else {
        //       this.nopurvisible = false
        //     }
        //     that.loading = false

        //     that.getList()
        //     that.$message.success(that.$t('public.success'))

        //   })
        //   .catch((err) => {
        //     that.loading = false
        //     that.requestFailed(err)
        //   })

      }

    },
    storage () {
      let that = this
      this.quanPrompt = []
      let itm = []
      let hzNo = []
      let mfXjBjiaSlVoList = []
      let selectRecords = this.multipleSelection
      if (selectRecords.length) {
        // 提示代码下
        // for (var i = 0; i < selectRecords.length; i++) {
        //   let qtySum = +selectRecords[i].qty
        //   for (var j = 0; j < selectRecords.length; j++) {
        //     let storitm = selectRecords[i].itm
        //     if (j != i) {
        //       if (selectRecords[j].itm == storitm) {
        //         qtySum = qtySum + +selectRecords[j].qty
        //       }
        //     }
        //   }
        //   qtySum = qtySum.toFixed(2)
        //   if (itm.length) { } else {
        //     if (itm.indexof(selectRecords[i].itm) == -1) {
        //       itm.push(selectRecords[i].itm)
        //     }
        //   }
        // 提示代码上

        // if (qtySum > +selectRecords[i].qtyHz || this.sumArry.itm.indexof(selectRecords[i].itm) != -1) {
        //   this.sumArry = { prdNo: selectRecords[i].prdNo, qty: selectRecords[i].qty, hzNo: selectRecords[i].hzNo, itm: selectRecords[i].itm }
        //   this.sumArryList.push(sumArry)
        // }
        // }
        // selectRecords.forEach((item, index) => {
        //   let storitm = item.itm
        //   if (item.itm == storitm) {
        //     item.qty
        //   }
        // })

        this.qtyflag = false
        selectRecords.forEach((item, index) => {
          if (item.submitDd == null || item.submitDd == '') { } else {
            if (item.submitDd.indexOf(':') == -1) {
              item.submitDd = item.submitDd + ' 00:00:00'
            }
          }
          if (item.estDd == null || item.estDd == '') { } else {
            if (item.estDd.indexOf(':') == -1) {
              item.estDd = item.estDd + ' 00:00:00'
            }
          }

          let sexarry = { itm: item.itm, hzNo: item.hzNo, cusNo: item.cusNo, qty: item.qty }
          mfXjBjiaSlVoList.push(sexarry)
          // 提示
          if (this.queryParam.qcYes == '2') {
            if (XEUtils.subtract(item.qtyTj, item.qty) < 0) {
              item.qty = ''
              this.qtyflag = true
            }
          }
          // 提示
        })
        mfXjBjiaSlVoList.forEach((item, index) => {
          if (item.submitDd == null || item.submitDd == '') { } else {
            if (item.submitDd.indexOf(':') == -1) {
              item.submitDd = item.submitDd + ' 00:00:00'
            }
          }
          if (item.estDd == null || item.estDd == '') { } else {
            if (item.estDd.indexOf(':') == -1) {
              item.estDd = item.estDd + ' 00:00:00'
            }
          }
        })


        if (this.qtyflag) {
          this.$message.error('修改数量已大于供应能力！')
        } else {
          // 采购量是否大
          // this.visiblenum = false
          this.purchaseFlag = false
          this.purchaseFlag2 = false
          this.math = Math.random()
          var map = {},
            dest = [];
          for (var i = 0; i < selectRecords.length; i++) {
            var ai = selectRecords[i];
            ai.id = ai.hzNo + ai.itm
            if (!map[ai.id]) { //依赖分组字段可自行更改！
              dest.push({
                id: ai.id, //依赖分组字段可自行更改！
                data: [ai],
                wyID: ai.wyID
              });
              map[ai.id] = ai; //依赖分组字段可自行更改！
            } else {
              for (var j = 0; j < dest.length; j++) {
                var dj = dest[j];
                if (dj.id == ai.id) { //依赖分组字段可自行更改！
                  dj.data.push(ai);
                  break;
                }
              }
            }
          }
          this.tableData.forEach((i, ff) => {
            i.colorJudg = false
            if (!dest.length) {
              i.colorJudg = false
            }
            dest.forEach((item, index) => {
              // if (item.wyID == i.wyID) {
              item.totalQty = 0
              // var totalQty = 0
              item.data.forEach((item2, index2) => {
                // if (item.wyID == i.wyID) {
                item.totalQty += parseFloat(item2.qtyPos)
                // }
              })
              item.data.forEach((item2, index2) => {
                if (item2.wyID == i.wyID) {
                  if (+item.totalQty > +item2.qtyErpLeft) {
                    i.colorJudg = this.math
                  } else {
                    i.colorJudg = false
                  }
                }
                // else {
                //   i.colorJudg = false
                // }
              })
            })
          })
          this.tableData.sort()
          for (var i = 0; i < selectRecords.length; i++) {
            if (selectRecords[i].colorJudg != null && selectRecords[i].colorJudg != false && selectRecords[i].colorJudg != '') {
              this.purchaseFlag = true
              let promptArr = { prdNo: selectRecords[i].prdNo, hzNo: selectRecords[i].hzNo, itm: selectRecords[i].itm, wyID: selectRecords[i].wyID, qtyPos: selectRecords[i].qtyPos, qtyErpLeft: selectRecords[i].qtyErpLeft }
              this.quanPrompt.push(promptArr)
            }
          }

          if (this.purchaseFlag) {
            this.visiblenum = true
            this.selectRecordStore = selectRecords
            return
          } else {
            this.visiblenum = false
          }
          let selectRecordszero = []
          selectRecords.forEach((item, index) => {
            if (item.qtyPos == 0 || item.qtyErpLeft == 0 || item.qtyErpLeft == null) {
            } else {
              selectRecordszero.push(item)
            }
          })
          if (selectRecordszero.length) {
          } else {
            this.$message.warning('采购数量、erp结余数量不能为0')
            return
          }
          // 采购量是否大
          this.$confirm({
            title: this.$t('public.del.title'),
            content: "确定比价预审？",
            okText: this.$t('public.sure'),
            okType: 'danger',
            cancelText: this.$t('public.cancel'),
            onOk () {
              that.loading = true
              selectRecordszero.forEach((item, index) => {
                if (item.qcYes == '未处理') {
                  item.qcYes = '0'

                } else if (item.qcYes == '已审核') {
                  item.qcYes = '1'

                } else if (item.qcYes == '比价预审') {
                  item.qcYes = '2'
                }
              })
              savedetails({
                mfXjBjiaSlVoList: selectRecordszero,
                qcYes: that.queryParam.qcYes
              })
                .then(() => {
                  that.loading = false
                  that.getList()
                  that.$message.success(that.$t('public.success'))
                })
                .catch((err) => {
                  that.loading = false
                  that.requestFailed(err)
                })
            },
            onCancel () {
              that.loading = false
            },
          })
        }
      } else {
        this.loading = false
        this.$message.warning(this.$t('public.list'))
      }
    },
    iqyquery () { },
    makeorder () { },
    drcontract () { },
    pursave () {
      let that = this
      let mfXjBjiaSlVoList = []
      mfXjBjiaSlVoList = this.mfXjBjiaSlVoListorder
      // mfXjBjiaSlVoList.push(this.queryParam.bilType)
      // let bilchuan = that.queryParam.bilType
      // queryParam.bilType
      // let chuanzhi = { genpurorder, bilType: bilchuan }

      // if (this.printSign == '否') {
      //   this.queryParam.posStatus = 'F'
      // } else if (this.printSign == '是') {
      //   this.queryParam.posStatus = 'T'
      // } else {
      //   this.queryParam.posStatus = null
      // }

      genPurchase({
        mfXjBjiaSlVoList,
        bilType: this.queryParam.bilType,
        posStatus: this.queryParam.posStatus
      })
        .then((res) => {

          this.purvisible = false

          if (res.data.length > 0) {
            this.nopurvisible = true
            res.data.forEach((item, index) => {
              let notGenarry = { prdNo: item.prdNo, qty: item.qty, hzNo: item.hzNo }
              this.notGenerate.push(notGenarry)
            })

          } else {
            this.nopurvisible = false
          }
          that.loading = false

          that.getList()
          that.$message.success(that.$t('public.success'))

        })
        .catch((err) => {
          that.loading = false
          that.requestFailed(err)
        })
      // let that = this
      // this.$confirm({
      //   title: this.$t('public.del.title'),
      //   content: "确定生成采购单吗？",
      //   okText: this.$t('public.sure'),
      //   okType: 'danger',
      //   cancelText: this.$t('public.cancel'),
      //   onOk () {
      //     that.loading = true

      //   },
      //   onCancel () {
      //     that.loading = false
      //   },
      // })

    },
    purchase () {
      let that = this
      this.quanPrompt = []
      let itm = []
      let hzNo = []
      this.mfXjBjiaSlVoListorder = []
      let selectRecords = this.multipleSelection
      this.purvisible = false
      if (selectRecords.length) {
        // selectRecords.forEach((item, index) => {
        //   let sexarry = { itm: item.itm, hzNo: item.hzNo, cusNo: item.cusNo }
        //   this.mfXjBjiaSlVoListorder.push(sexarry)
        // })
        selectRecords.forEach((item, index) => {
          if (item.submitDd == null || item.submitDd == '') { } else {
            if (item.submitDd.indexOf(':') == -1) {
              item.submitDd = item.submitDd + ' 00:00:00'
            }
          }
          if (item.estDd == null || item.estDd == '') { } else {
            if (item.estDd.indexOf(':') == -1) {
              item.estDd = item.estDd + ' 00:00:00'
            }
          }
        })
        this.mfXjBjiaSlVoListorder = selectRecords

        // 采购量是否大
        // this.visiblenum = false
        this.purchaseFlag = false
        this.purchaseFlag2 = false
        this.purchaseFlag3 = false
        this.math = Math.random()
        var map = {},
          dest = [];
        for (var i = 0; i < selectRecords.length; i++) {
          var ai = selectRecords[i];
          ai.id = ai.hzNo + ai.itm
          if (!map[ai.id]) { //依赖分组字段可自行更改！
            dest.push({
              id: ai.id, //依赖分组字段可自行更改！
              data: [ai],
              wyID: ai.wyID
            });
            map[ai.id] = ai; //依赖分组字段可自行更改！
          } else {
            for (var j = 0; j < dest.length; j++) {
              var dj = dest[j];
              if (dj.id == ai.id) { //依赖分组字段可自行更改！
                dj.data.push(ai);
                break;
              }
            }
          }
        }
        this.tableData.forEach((i, ff) => {
          i.colorJudg = false
          if (!dest.length) {
            i.colorJudg = false
          }
          dest.forEach((item, index) => {
            // if (item.wyID == i.wyID) {
            item.totalQty = 0
            // var totalQty = 0
            item.data.forEach((item2, index2) => {
              // if (item.wyID == i.wyID) {
              item.totalQty += parseFloat(item2.qtyPos)
              // }
            })
            item.data.forEach((item2, index2) => {
              if (item2.wyID == i.wyID) {
                if (+item.totalQty > +item2.qtyErpLeft) {
                  i.colorJudg = this.math
                } else {
                  i.colorJudg = false
                }
              }
              // else {
              //   i.colorJudg = false
              // }
            })
          })
        })
        this.tableData.sort()
        for (var i = 0; i < selectRecords.length; i++) {
          if (selectRecords[i].colorJudg != null && selectRecords[i].colorJudg != false && selectRecords[i].colorJudg != '') {
            this.purchaseFlag3 = true
            let promptArr = { prdNo: selectRecords[i].prdNo, hzNo: selectRecords[i].hzNo, itm: selectRecords[i].itm, wyID: selectRecords[i].wyID, qtyPos: selectRecords[i].qtyPos, qtyErpLeft: selectRecords[i].qtyErpLeft }
            this.quanPrompt.push(promptArr)
          }
        }

        if (this.purchaseFlag3) {
          this.visiblenum = true
          this.selectRecordStore3 = selectRecords
          return
        } else {
          this.purvisible = true
          this.visiblenum = false
        }
        // 采购量是否大



      } else {
        this.loading = false
        this.$message.warning(this.$t('public.list'))
      }
      // genPurchase
    },
    batchsheet () {
      let that = this
      this.quanPrompt = []
      let itm = []
      let hzNo = []
      let mfXjBjiaSlVoList = []
      let selectRecords = this.multipleSelection
      if (selectRecords.length) {
        this.qtyflagsubm = false
        selectRecords.forEach((item, index) => {
          let sexarry = { itm: item.itm, hzNo: item.hzNo, cusNo: item.cusNo, qty: item.qty }
          mfXjBjiaSlVoList.push(sexarry)
          // estDd submitDd
          if (item.submitDd == null || item.submitDd == '') { } else {
            if (item.submitDd.indexOf(':') == -1) {
              item.submitDd = item.submitDd + ' 00:00:00'
            }
          }
          if (item.estDd == null || item.estDd == '') { } else {
            if (item.estDd.indexOf(':') == -1) {
              item.estDd = item.estDd + ' 00:00:00'
            }
          }
          // 提示
          if (this.queryParam.qcYes == '2') {
            if (XEUtils.subtract(item.qtyTj, item.qty) < 0) {
              item.qty = ''
              this.qtyflagsubm = true
            }
          }
          // 提示
        })
        if (this.qtyflag) {
          this.$message.error('修改数量已大于供应能力！')
        } else {
          // 采购量是否大
          // this.visiblenum = false
          this.purchaseFlag2 = false
          this.math = Math.random()
          var map = {},
            dest = [];
          for (var i = 0; i < selectRecords.length; i++) {
            var ai = selectRecords[i];
            ai.id = ai.hzNo + ai.itm
            if (!map[ai.id]) { //依赖分组字段可自行更改！
              dest.push({
                id: ai.id, //依赖分组字段可自行更改！
                data: [ai],
                wyID: ai.wyID
              });
              map[ai.id] = ai; //依赖分组字段可自行更改！
            } else {
              for (var j = 0; j < dest.length; j++) {
                var dj = dest[j];
                if (dj.id == ai.id) { //依赖分组字段可自行更改！
                  dj.data.push(ai);
                  break;
                }
              }
            }
          }
          this.tableData.forEach((i, ff) => {
            i.colorJudg = false
            if (!dest.length) {
              i.colorJudg = false
            }
            dest.forEach((item, index) => {
              // if (item.wyID == i.wyID) {
              item.totalQty = 0
              // var totalQty = 0
              item.data.forEach((item2, index2) => {
                // if (item.wyID == i.wyID) {
                item.totalQty += parseFloat(item2.qtyPos)
                // }
              })
              item.data.forEach((item2, index2) => {
                if (item2.wyID == i.wyID) {
                  if (+item.totalQty > +item2.qtyErpLeft) {
                    i.colorJudg = this.math
                  } else {
                    i.colorJudg = false
                  }
                }
                // else {
                //   i.colorJudg = false
                // }
              })
            })
          })
          this.tableData.sort()
          for (var i = 0; i < selectRecords.length; i++) {
            if (selectRecords[i].colorJudg != null && selectRecords[i].colorJudg != false && selectRecords[i].colorJudg != '') {
              this.purchaseFlag2 = true
              let promptArr = { prdNo: selectRecords[i].prdNo, hzNo: selectRecords[i].hzNo, itm: selectRecords[i].itm, wyID: selectRecords[i].wyID, qtyPos: selectRecords[i].qtyPos, qtyErpLeft: selectRecords[i].qtyErpLeft }
              this.quanPrompt.push(promptArr)
            }
          }
          if (this.purchaseFlag2) {
            this.visiblenum = true
            this.selectRecordStore2 = selectRecords
            return
          } else {
            this.visiblenum = false
          }
          let selectRecordszero = []
          selectRecords.forEach((item, index) => {
            if (item.qtyPos == 0 || item.qtyErpLeft == 0 || item.qtyErpLeft == null) {
            } else {
              selectRecordszero.push(item)
            }
          })
          if (selectRecordszero.length) {
          } else {
            this.$message.warning('采购数量、erp结余数量不能为0')
            return
          }
          // erp
          saveComparePriceToHj({
            mfXjBjiaSlVoList: selectRecordszero,
          })
            .then(() => {
              that.loading = false
              // that.getList()
              // that.$message.success(that.$t('public.success'))
            })
            .catch((err) => {
              that.loading = false
              that.requestFailed(err)
            })
          // erp
          // 采购量是否大
          this.$confirm({
            title: this.$t('public.del.title'),
            content: "确定比价审核？",
            okText: this.$t('public.sure'),
            okType: 'danger',
            cancelText: this.$t('public.cancel'),
            onOk () {
              that.loading = true

              selectRecordszero.forEach((item, index) => {
                if (item.qcYes == '未处理') {
                  item.qcYes = '0'
                } else if (item.qcYes == '已审核') {
                  item.qcYes = '1'

                } else if (item.qcYes == '比价预审') {
                  item.qcYes = '2'
                }
              })
              quotationsub({
                // mfXjBjiaSlVoList,
                mfXjBjiaSlVoList: selectRecordszero,
                qcYes: that.queryParam.qcYes
              })
                .then((res) => {

                  if (res.data.length > 0) {
                    that.nopurvisible2 = true

                    res.data.forEach((item, index) => {


                      let notGenarry2 = { prdNo: item.prdNo, qty: item.qty.toString(), hzNo: item.hzNo }
                      that.notGenerate2.push(notGenarry2)
                    })


                  } else {
                    that.nopurvisible2 = false
                  }
                  that.loading = false
                  that.getList()
                  that.$message.success(that.$t('public.success'))
                })
                .catch((err) => {
                  that.loading = false
                  that.requestFailed(err)
                })
            },
            onCancel () {
              that.loading = false
            },
          })
        }
      } else {
        this.loading = false
        this.$message.warning(this.$t('public.list'))
      }
    },
    drcontract () { },
  },
}
</script>

<style lang="less" >
.tankuang .el-icon-close:before {
  content: '' !important;
}
.tankuang .el-dialog__body {
  padding: 15px 15px;
}
</style>

<style lang="less" scoped>
.buttontwocon {
  margin-top: 30px;
  .buttontwo {
    margin-right: 30px;
  }
}
.title {
  margin-bottom: 15px;
}
.avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
