<template>
  <a-card :bordered="false">
    <!-- 三力士下 -->
    <div v-show="false">
      <div id="form3">
        <div
          v-for="(bodyObj, index2) in bodyObj2"
          :key="index2"
          style="margin-bottom:15px;"
        >
          <!-- <div
            v-for="(i,cindex) in list[index].chrildList"
            :key="cindex"
          > -->
          <div
            v-for="(item, index) in bodyObj2[index2].form1Arr"
            :key="item.id"
            style="font-weight: 700;"
          >
            <div
              style="width: 100mm;background-color: pink; padding: 4px; margin-right: 3mm;margin-left: 3mm; position: relative;"
              id="showPrint"
            >
              <div style="position: absolute; right: 26px; top: 10px; padding-top: 4px">{{ bodyObj.cw }}</div>
              <div
                style="position: absolute; right: 22px; top: 35px; padding-top: 4px;"
                :id="`sanlishi${index2}${index}`"
              ></div>
              <!-- :id="`qrcodeLeftForm1${index}`" -->

              <!-- <div style="position: absolute;right: 35px; top: 118px;">{{ newDate }}</div> -->
              <!-- <div style="position: absolute;right: 15px; bottom: 9px;border: 1px solid #696969">
                <div style="width: 90px;height:19px;font-size: 12px;text-align: center;">检验</div>
                <div style="width: 90px;height:73px;border-top: 1px solid #696969"></div>
              </div> -->
              <div style="text-align: center;font-size: 16px;margin-top: 7px;">
                物料标识卡
              </div>
              <div class="form_one">
                <div style="font-size: 12px;display: flex;margin-bottom:21px;">
                  <div>品号:</div>
                  <div style="padding-bottom:2px;width: 150px;">{{ bodyObj.prdNo }}</div>
                </div>
              </div>
              <div class="form_one">
                <div style="font-size: 12px;display: flex;margin-bottom:21px;">
                  <div style="flex:1">
                    <div style="display:inline-block;">品名:</div>
                    <div style="display:inline-block;padding-bottom:2px; width: 150px;height: 100%;">
                      {{ bodyObj.prdName == null || bodyObj.prdName == "" ? bodyObj.prdName : bodyObj.prdName.slice(0, 25) }}
                    </div>
                  </div>
                  <div style="flex:1">
                    <div style="display:inline-block;">数量:</div>
                    <div style="display:inline-block;padding-bottom:2px; width: 50px;">{{ item.qty }}</div>
                  </div>
                </div>
              </div>
              <div class="form_one">
                <div style="font-size: 12px;display: flex;margin-bottom:21px;">
                  <div style="flex:1">
                    <div style="display:inline-block;">批号:</div>
                    <div style="display:inline-block;padding-bottom:2px;width: 120px;">{{ bodyObj.prnNo == null || bodyObj.prnNo == undefined || bodyObj.prnNo == "" ? "" : bodyObj.prnNo }}</div>
                  </div>
                  <div style="flex:1">
                    <div style="display:inline-block;">入库日期：</div>
                    <div style="display:inline-block;padding-bottom:2px;width: 120px;">
                      {{newDateday}}
                    </div>
                  </div>
                </div>
                <div class="form_one">
                  <div style="font-size: 12px;display: flex;">
                    <div style="flex:1">
                      <div>检验结果:</div>
                      <div style="padding-bottom:2px; width: 120px;height: 100%;">

                      </div>
                    </div>
                    <div style="flex:1">
                      <div style="display:inline-block;">储存期限:</div>
                      <div style="display:inline-block;padding-bottom:2px; width: 120px;margin-left:5px;">{{ bodyObj.validDays }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- <div class="form_one">
                <div style="font-size: 12px;display: flex;">
                  <div>批号:</div>
                  <div
                    v-if="modify"
                    style="padding-bottom:2px; border-bottom:1px solid #000;width: 150px;"
                  >{{ prnDd }}
                  </div>
                  <div
                    v-else
                    style="padding-bottom:2px; border-bottom:1px solid #000;width: 150px;"
                  >{{ bodyObj.prnDd == null || bodyObj.prnDd == undefined || bodyObj.prnDd == "" ? "" : bodyObj.prnDd }}</div>
                  <div>任务单：{{ bodyObj.taskNo == null || bodyObj.taskNo == undefined ? '' : bodyObj.taskNo }}</div>
                  <div style="padding-bottom:2px;border-bottom:1px solid #000;width: 50px;"></div>
                </div>
              </div>
              <div class="form_one">
                <div style="font-size: 12px;display: flex;">
                  <div>品名:</div>
                  <div style="padding-bottom:2px; border-bottom:1px solid #000;width: 236px;height: 100%;">
                    {{ bodyObj.prdName == null || bodyObj.prdName == "" ? bodyObj.prdName : bodyObj.prdName.slice(0, 25) }}
                  </div>
                </div>
              </div>
              <div class="form_last">
                <div style="font-size: 12px;">
                  <div style="position: relative;">规格:
                    <div style="position: absolute;top: 0px;left: 30px;padding-bottom:2px; border-bottom:1px solid #000;width: 234px;height: 100%;">
                      {{ bodyObj.spc == null || bodyObj.spc == "" ? bodyObj.spc : bodyObj.spc.slice(0, 25) }}
                    </div>
                  </div>
                </div>
              </div> -->
              <!-- <div class="form_last">
                <div style="font-size: 12px;display: flex;">
                  <div>供应商:</div>
                  <div style="padding-bottom:2px; border-bottom:1px solid #000;width: 224px;">{{ bodyObj.cusName }}</div>
                </div>
              </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 三力士上 -->

    <!-- 单标签 -->
    <!-- v-show="danbiao" -->
    <!-- v-show="false" -->
    <div v-show="false">
      <div id="form1">
        <div
          v-for="(bodyObj, index2) in bodyObj2"
          :key="index2"
          style="margin-bottom:15px;"
        >
          <!-- <div
            v-for="(i,cindex) in list[index].chrildList"
            :key="cindex"
          > -->
          <div
            v-for="(item, index) in bodyObj2[index2].form1Arr"
            :key="item.id"
            style="font-weight: 700;"
          >
            <div
              style="width: 100mm;background-color: pink; padding: 4px; margin-right: 3mm;margin-left: 3mm; position: relative;"
              id="showPrint"
            >
              <div style="position: absolute; right: 26px; top: 10px; padding-top: 4px">{{ bodyObj.cw }}</div>
              <div
                style="position: absolute; right: 22px; top: 35px; padding-top: 4px;"
                :id="`qrcodeLeftForm1${index2}${index}`"
              ></div>
              <!-- <div>{{qrcodeLeftForm1}} {{index2}}{{index}}</div> -->
              <!-- :id="`qrcodeLeftForm1${index}`" -->

              <div style="position: absolute;right: 35px; top: 118px;">{{ newDate }}</div>
              <!-- <div style="position: absolute;right: 15px; bottom: 9px;border: 1px solid #696969">
                <div style="width: 90px;height:19px;font-size: 12px;text-align: center;">检验</div>
                <div style="width: 90px;height:73px;border-top: 1px solid #696969"></div>
              </div> -->
              <div style="text-align: center;font-size: 16px;margin-top: 7px;">
                物料标识卡
              </div>
              <div class="form_one">
                <div style="font-size: 12px;display: flex;">
                  <div>P/O:</div>
                  <div style="padding-bottom:2px;border-bottom:1px solid #000;width: 150px;">{{ bodyObj.bilNo }}</div>
                  <div>颜色：</div>
                  <div style="padding-bottom:2px;border-bottom:1px solid #000;width: 50px;">{{ bodyObj.ys }}</div>
                </div>
              </div>
              <div class="form_one">
                <div style="font-size: 12px;display: flex;">
                  <div>料号:</div>
                  <div style="padding-bottom:2px;border-bottom:1px solid #000;width: 150px;">{{ bodyObj.prdNo }}</div>
                  <div>单重：</div>
                  <div style="padding-bottom:2px;border-bottom:1px solid #000;width: 50px;">{{ bodyObj.dz == null || bodyObj.dz == '' || bodyObj.dz == undefined ? bodyObj.dz : Number(bodyObj.dz).toFixed(2) }}</div>
                </div>
              </div>
              <div class="form_one">
                <div style="font-size: 12px;display: flex;">
                  <div>数量:</div>
                  <div style="padding-bottom:2px; border-bottom:1px solid #000;width: 150px;">{{ item.qty }}</div>
                  <div>净重：</div>
                  <div
                    style="padding-bottom:2px;border-bottom:1px solid #000;width: 50px;"
                    v-if="bodyObj.dz == null || bodyObj.dz == ''"
                  ></div>
                  <div
                    style="padding-bottom:2px;border-bottom:1px solid #000;width: 50px;"
                    v-else
                  >{{ ((Number(bodyObj.dz) * item.qty) / 1000).toFixed(2) }}</div>
                </div>
              </div>
              <div class="form_one">
                <div style="font-size: 12px;display: flex;">
                  <div>批号:</div>
                  <!-- <div
                    v-if="modify"
                    style="padding-bottom:2px; border-bottom:1px solid #000;width: 150px;"
                  >{{ prnDd }}
                  </div> -->
                  <div
                    v-if="modify"
                    style="padding-bottom:2px; border-bottom:1px solid #000;width: 150px;"
                  >{{ bodyObj.prnDd }}
                  </div>
                  <div
                    v-else
                    style="padding-bottom:2px; border-bottom:1px solid #000;width: 150px;"
                  >{{ bodyObj.prnDd == null || bodyObj.prnDd == undefined || bodyObj.prnDd == "" ? "" : bodyObj.prnDd }}</div>
                  <div>任务单：{{ bodyObj.taskNo == null || bodyObj.taskNo == undefined ? '' : bodyObj.taskNo }}</div>
                  <div style="padding-bottom:2px;border-bottom:1px solid #000;width: 50px;"></div>
                </div>
              </div>
              <div class="form_one">
                <div style="font-size: 12px;display: flex;">
                  <div>品名:</div>
                  <div style="padding-bottom:2px; border-bottom:1px solid #000;width: 236px;height: 100%;">
                    {{ bodyObj.prdName == null || bodyObj.prdName == "" ? bodyObj.prdName : bodyObj.prdName.slice(0, 25) }}
                  </div>
                </div>
              </div>
              <div class="form_last">
                <div style="font-size: 12px;">
                  <div style="position: relative;">规格:
                    <div style="position: absolute;top: 0px;left: 30px;padding-bottom:2px; border-bottom:1px solid #000;width: 234px;height: 100%;">
                      {{ bodyObj.spc == null || bodyObj.spc == "" ? bodyObj.spc : bodyObj.spc.slice(0, 25) }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="form_last">
                <div style="font-size: 12px;display: flex;">
                  <div>供应商:</div>
                  <div style="padding-bottom:2px; border-bottom:1px solid #000;width: 224px;">{{ bodyObj.cusName }}</div>
                </div>
              </div>
              <!-- 暂时添加 -->
              <!-- <div>

                {{item.erwei}}
              </div> -->
              <!-- 暂时添加  -->

            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 双标签 -->

    <div v-show="false">
      <div id="form2">
        <div>
          <div
            v-for="(bodyObj, index2) in bodyObj2"
            :key="index2"
            style="margin-bottom:15px;"
          >
            <div
              v-for="(item, index) in bodyObj2[index2].form1Arr"
              :key="item.id"
              style="font-weight: 700; display: flex"
            >
              <!-- {{`erweima${index2}${index}`}} -->
              <!-- 左边条码 -->
              <div style="width: 50mm; background-color: pink; padding: 2px; margin-right: 3mm; position: relative;">
                <div
                  style="position: absolute; right: 10px; top: 7px; padding-top: 4px"
                  :id="`erweima${index2}${index}`"
                ></div>

                <!-- :id="`qrcodeLeft${index}`" -->
                <div style="position: absolute;right: -5px; top: 61px;font-size: 12px;-webkit-transform-origin-x: -27px; -webkit-transform: scale(0.6);">{{ newDate }}</div>
                <div style="position: absolute; left: 9px; top: 22px;font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">任务单：<u>{{ bodyObj.taskNo == null || bodyObj.taskNo == undefined ? '' : bodyObj.taskNo }}</u></div>
                <div style="position: absolute; right: 43px; top: 3px; padding-top: 3px;font-size: 12px;-webkit-transform-origin-x: -27px; -webkit-transform: scale(0.7);">{{ bodyObj.cw }}</div>
                <div style="margin-left: 18px; font-size: 16px; -webkit-transform-origin-x: -27px; -webkit-transform: scale(0.7); padding-top: 3px;">
                  物料标识卡
                </div>
                <div style="margin-top: 7px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                    P/O: <u>{{ bodyObj.bilNo }}</u>
                  </div>
                </div>
                <div style="margin-top: -2px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                    料号: <u>{{ bodyObj.prdNo == null || bodyObj.prdNo == "" || bodyObj.prdNo == undefined ? "" : bodyObj.prdNo.slice(0, 15) }}</u>
                  </div>
                </div>
                <div style="margin-top: -2px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                    <div style="width: 100%;position: relative;">
                      数量: <u>{{ item.qty }}</u>
                      <div style="position: absolute;left:157px;top: 0px;width:90px;">
                        单重: <u>{{ bodyObj.dz == null || bodyObj.dz == '' || bodyObj.dz == undefined ? bodyObj.dz : Number(bodyObj.dz).toFixed(2) }}</u>
                      </div>
                    </div>
                  </div>
                </div>
                <div style="display: flex; margin-top: -2px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                    <div style="width: 150px;">
                      批号:
                      <u v-if="modify">{{ bodyObj.prnDd}}</u>
                      <u v-else>{{ bodyObj.prnDd == null || bodyObj.prnDd == undefined || bodyObj.prnDd == "" ? "" : bodyObj.prnDd }}</u>
                      <!-- <u>{{ bodyObj.prnNo == null || bodyObj.prnNo == "" || bodyObj.prnNo == undefined ? "" : bodyObj.prnNo.slice(0, 20) }}</u> -->
                    </div>
                  </div>
                  <div>
                    <div
                      style="font-size: 12px; -webkit-transform-origin-x: -123px; -webkit-transform: scale(0.7);width:90px;"
                      v-if="bodyObj.dz == null || bodyObj.dz == ''"
                    >净重: <u></u></div>
                    <div
                      style="font-size: 12px; -webkit-transform-origin-x: -123px; -webkit-transform: scale(0.7);width:90px;"
                      v-else
                    >净重: <u>{{ ((Number(bodyObj.dz) * item.qty) / 1000).toFixed(2) }}</u></div>
                  </div>
                </div>

                <div style="margin-top: -2px;margin-left: 7px">
                  <div style="position: relative;font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);">
                    品名:
                    <div style="position: absolute;top: 0px; left: 30px;width: 135px;">
                      <u>{{ bodyObj.prdName == null || bodyObj.prdName == "" || bodyObj.prdName == undefined ? "" : bodyObj.prdName.slice(0, 13) }}</u>
                    </div>
                    <div style="position: absolute;top: -3px; left: 194px;font-size: 17px; -webkit-transform-origin-x: -123px; -webkit-transform: scale(0.7);width:120px;">颜色: <u>{{ bodyObj.ys }}</u></div>
                  </div>
                </div>

                <div style="display: flex; margin-top: -2px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                    <div style="width: 150px;">规格: <u>{{ bodyObj.spc == null || bodyObj.spc == "" || bodyObj.spc == undefined ? "" : bodyObj.spc.slice(0, 13) }}</u></div>
                  </div>
                  <div style="margin-left: 10px">
                    <div style="font-size: 12px; -webkit-transform-origin-x: -160px; -webkit-transform: scale(0.7);width:90px;">
                      件数: <u></u>
                    </div>
                  </div>
                </div>
                <div style="margin-top: -4px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);width: 190px;">
                    <div style="width:250px;">供应商: <u>{{ bodyObj.cusName == null || bodyObj.cusName == "" || bodyObj.cusName == undefined ? "" : bodyObj.cusName.slice(0, 17) }}</u></div>
                  </div>
                </div>
                <!-- 暂时添加 -->
                <!-- <div>
                  {{item.erwei}}
                </div> -->
                <!-- 暂时添加 -->

              </div>

              <!-- 右边条码 -->
              <div style="width: 50mm; background-color: pink; padding: 2px; position: relative;">
                <div
                  style="position: absolute; right: 10px; top: 7px; padding-top: 4px"
                  :id="`erweimaright${index2}${index}`"
                ></div>
                <!-- :id="`qrcodeRight${index}`" -->
                <div style="position: absolute;right: -5px; top: 61px;font-size: 12px;-webkit-transform-origin-x: -27px; -webkit-transform: scale(0.6);">
                  {{ newDate }}
                </div>
                <div style="position: absolute; left: 9px; top: 22px;font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">任务单：<u>{{ bodyObj.taskNo == null || bodyObj.taskNo == undefined ? '' : bodyObj.taskNo }}</u></div>
                <div style="position: absolute; right: 43px; top: 3px; padding-top: 3px;font-size: 12px;-webkit-transform-origin-x: -27px; -webkit-transform: scale(0.7);">
                  {{ bodyObj.cw }}
                </div>
                <div style="margin-left: 18px;font-size: 16px;-webkit-transform-origin-x: -27px;-webkit-transform: scale(0.7);padding-top: 3px;">
                  物料标识卡
                </div>
                <div style="margin-top: 7px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                    P/O: <u>{{ bodyObj.bilNo }}</u>
                  </div>
                </div>
                <div style="margin-top: -2px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                    料号: <u>{{ bodyObj.prdNo == null || bodyObj.prdNo == "" || bodyObj.prdNo == undefined ? "" : bodyObj.prdNo.slice(0, 15) }}</u>
                  </div>
                </div>
                <div style="margin-top: -2px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);width: 100%;position: relative;">
                    <div
                      style="width: 100px;"
                      v-if="item.lastQty"
                    >
                      数量: <u>{{ item.lastQty }}</u>
                    </div>
                    <div
                      style="width: 100px;"
                      v-else
                    >
                      数量: <u>{{ item.qty }}</u>
                    </div>
                    <div style="position: absolute;left:157px;top: 0px;width:90px;">单重: <u>{{ bodyObj.dz == null || bodyObj.dz == '' || bodyObj.dz == undefined ? bodyObj.dz : Number(bodyObj.dz).toFixed(2) }}</u></div>
                  </div>
                </div>
                <div style="margin-top: -2px;margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);">
                    <div style="width: 100%;position: relative;">
                      批号: <u v-if="modify">{{ bodyObj.prnDd }}</u><u v-else>{{ bodyObj.prnDd == null || bodyObj.prnDd == undefined || bodyObj.prnDd == "" ? "" : bodyObj.prnDd }}</u>
                      <div
                        style="position: absolute;left:157px;top: 0px;width:90px;"
                        v-if="bodyObj.dz == null || bodyObj.dz == ''"
                      >净重: <u></u></div>
                      <div
                        style="position: absolute;left:157px;top: 0px;width:90px;"
                        v-else-if="item.lastQty"
                      >净重: <u>{{ ((Number(bodyObj.dz) * item.lastQty) / 1000).toFixed(2) }}</u></div>
                      <div
                        style="position: absolute;left:157px;top: 0px;width:90px;"
                        v-else
                      >净重: <u>{{ ((Number(bodyObj.dz) * item.qty) / 1000).toFixed(2) }}</u></div>
                    </div>
                  </div>
                </div>

                <div style="margin-top: -2px;margin-left: 7px">
                  <div style="position: relative;font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);">
                    品名:
                    <div style="position: absolute;top: 0px; left: 30px;width: 135px;">
                      <u>{{ bodyObj.prdName == null || bodyObj.prdName == "" || bodyObj.prdName == undefined ? "" : bodyObj.prdName.slice(0, 13) }}</u>
                    </div>
                    <div style="position: absolute;top: -3px; left: 194px;font-size: 17px; -webkit-transform-origin-x: -123px; -webkit-transform: scale(0.7);width:120px;">颜色: <u>{{ bodyObj.ys }}</u></div>
                  </div>
                </div>

                <div style="margin-top: -2px;margin-left: 7px;position: relative;">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);">
                    <div style="width: 150px;">
                      规格: <u>{{ bodyObj.spc == null || bodyObj.spc == "" || bodyObj.spc == undefined ? "" : bodyObj.spc.slice(0, 13) }}</u>
                    </div>
                  </div>
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);position: absolute;left: 110px;top: 0px;">
                    件数: <u></u>
                  </div>
                </div>
                <div style="margin-top: -4px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);width: 190px;">
                    <div style="width:250px;">供应商: <u>{{ bodyObj.cusName == null || bodyObj.cusName == "" || bodyObj.cusName == undefined ? "" : bodyObj.cusName.slice(0, 17) }}</u></div>
                  </div>
                </div>
                <!-- 暂时添加 -->
                <!-- <div>
                  {{item.erwei}}
                </div> -->
                <!-- 暂时添加 -->

              </div>
            </div>

            <!-- 是否显示最后一个条码 -->
            <!-- <div
              style="width: 50mm;background-color: pink;padding: 2px;margin-right: 3mm;position: relative;font-weight: 700;"
              v-if="showLastCard"
            >
              <div
                style="position: absolute; right: 10px; top: 7px; padding-top: 4px"
                id="qrcodeLast"
              ></div>
              <div style="position: absolute;right: -5px; top: 61px;font-size: 12px;-webkit-transform-origin-x: -27px; -webkit-transform: scale(0.6);">{{ newDate }}</div>
              <div style="position: absolute; left: 9px; top: 22px;font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">任务单：<u>{{ bodyObj.taskNo == null || bodyObj.taskNo == undefined ? '' : bodyObj.taskNo }}</u></div>
              <div style="position: absolute; right: 43px; top: 3px; padding-top: 3px;font-size: 12px;-webkit-transform-origin-x: -27px; -webkit-transform: scale(0.7);">{{ bodyObj.cw }}</div>
              <div style=" margin-left: 18px; font-size: 16px; -webkit-transform-origin-x: -27px; -webkit-transform: scale(0.7); padding-top: 3px;">
                物料标识卡
              </div>
              <div style="margin-top: 7px; margin-left: 7px">
                <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                  P/O: <u>{{ bodyObj.bilNo }}</u>
                </div>
              </div>
              <div style="margin-top: -2px; margin-left: 7px">
                <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                  料号: <u>{{ bodyObj.prdNo == null || bodyObj.prdNo == "" || bodyObj.prdNo == undefined ? "" : bodyObj.prdNo.slice(0, 15) }}</u>
                </div>
              </div>

              <div style="margin-top: -2px; margin-left: 7px">
                <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                  <div style="width: 100%;position: relative;">
                    数量: <u>{{ delObj.qty }}</u>
                    <div style="position: absolute;left:157px;top: 0px;width:90px;">
                      单重: <u>{{ bodyObj.dz == null || bodyObj.dz == '' || bodyObj.dz == undefined ? bodyObj.dz : Number(bodyObj.dz).toFixed(2) }}</u>
                    </div>
                  </div>
                </div>
              </div>

              <div style="display: flex; margin-top: -2px; margin-left: 7px">
                <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                  <div style="width: 150px;">
                    批号: <u v-if="modify">{{ prnDd }}</u><u v-else>{{ bodyObj.prnDd == null || bodyObj.prnDd == undefined || bodyObj.prnDd == "" ? "" : bodyObj.prnDd }}</u>
                  </div>
                </div>
                <div>
                  <div
                    style="font-size: 12px; -webkit-transform-origin-x: -123px; -webkit-transform: scale(0.7);width:90px;"
                    v-if="bodyObj.dz == null || bodyObj.dz == ''"
                  >净重: <u></u></div>
                  <div
                    style="font-size: 12px; -webkit-transform-origin-x: -123px; -webkit-transform: scale(0.7);width:90px;"
                    v-else
                  >净重: <u>{{ ((Number(bodyObj.dz) * delObj.qty) / 1000).toFixed(2) }}</u></div>
                </div>
              </div>

              <div style="margin-top: -2px;margin-left: 7px">
                <div style="position: relative;font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);">
                  品名:
                  <div style="position: absolute;top: 0px; left: 30px;width: 135px;">
                    <u>{{ bodyObj.prdName == null || bodyObj.prdName == "" || bodyObj.prdName == undefined ? "" : bodyObj.prdName.slice(0, 13) }}</u>
                  </div>
                  <div style="position: absolute;top: -3px; left: 194px;font-size: 17px; -webkit-transform-origin-x: -123px; -webkit-transform: scale(0.7);width:120px;">颜色: <u>{{ bodyObj.ys }}</u></div>
                </div>
              </div>

              <div style="display: flex; margin-top: -2px; margin-left: 7px">
                <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                  <div style="width: 150px;">规格: <u>{{ bodyObj.spc == null || bodyObj.spc == "" || bodyObj.spc == undefined ? "" : bodyObj.spc.slice(0, 13) }}</u></div>
                </div>
                <div style="margin-left: 10px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: -160px; -webkit-transform: scale(0.7);width:90px;">
                    件数: <u></u>
                  </div>
                </div>
              </div>
              <div style="margin-top: -4px; margin-left: 7px">
                <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);width: 190px;">
                  <div style="width:250px;">供应商: <u>{{ bodyObj.cusName == null || bodyObj.cusName == "" || bodyObj.cusName == undefined ? "" : bodyObj.cusName.slice(0, 17) }}</u></div>
                </div>
              </div>

            </div> -->
          </div>
        </div>
      </div>
    </div>

    <!-- v-show="false" -->
    <!-- v-show="danbiao2" -->
    <!-- v-show="false" -->

    <!-- <div
              v-for="(item, index) in printData"
              :key="item.id"
              style="font-weight: 700; display: flex"
            > -->
    <!-- <div v-show="danbiao2">
      <div id="form2">
        <div>
          <div
            v-for="(bodyObj, index2) in bodyObj2"
            :key="index2"
            style="margin-bottom:15px;"
          >
            <div
              v-for="(item, index) in bodyObj2[index2].form1Arr"
              :key="item.id"
              style="font-weight: 700; display: flex"
            >
              
               左边条码 
              <div style="width: 50mm; background-color: pink; padding: 2px; margin-right: 3mm; position: relative;">
                <div
                  style="position: absolute; right: 10px; top: 7px; padding-top: 4px"
                  :id="`qrcodeLeft${index}`"
                ></div>
                <div style="position: absolute;right: -5px; top: 61px;font-size: 12px;-webkit-transform-origin-x: -27px; -webkit-transform: scale(0.6);">{{ newDate }}</div>
                <div style="position: absolute; left: 9px; top: 22px;font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">任务单：<u>{{ bodyObj.taskNo == null || bodyObj.taskNo == undefined ? '' : bodyObj.taskNo }}</u></div>
                <div style="position: absolute; right: 43px; top: 3px; padding-top: 3px;font-size: 12px;-webkit-transform-origin-x: -27px; -webkit-transform: scale(0.7);">{{ bodyObj.cw }}</div>
                <div style="margin-left: 18px; font-size: 16px; -webkit-transform-origin-x: -27px; -webkit-transform: scale(0.7); padding-top: 3px;">
                  物料标识卡
                </div>
                <div style="margin-top: 7px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                    P/O: <u>{{ bodyObj.bilNo }}</u>
                  </div>
                </div>
                <div style="margin-top: -2px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                    料号: <u>{{ bodyObj.prdNo == null || bodyObj.prdNo == "" || bodyObj.prdNo == undefined ? "" : bodyObj.prdNo.slice(0, 15) }}</u>
                  </div>
                </div>
                <div style="margin-top: -2px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                    <div style="width: 100%;position: relative;">
                      数量: <u>{{ item.qty }}</u>
                      <div style="position: absolute;left:157px;top: 0px;width:90px;">
                        单重: <u>{{ bodyObj.dz == null || bodyObj.dz == '' || bodyObj.dz == undefined ? bodyObj.dz : Number(bodyObj.dz).toFixed(2) }}</u>
                      </div>
                    </div>
                  </div>
                </div>
                <div style="display: flex; margin-top: -2px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                    <div style="width: 150px;">
                      批号:
                      <u v-if="modify">{{ prnDd}}</u>
                      <u v-else>{{ bodyObj.prnDd == null || bodyObj.prnDd == undefined || bodyObj.prnDd == "" ? "" : bodyObj.prnDd }}</u>
                      
                    </div>
                  </div>
                  <div>
                    <div
                      style="font-size: 12px; -webkit-transform-origin-x: -123px; -webkit-transform: scale(0.7);width:90px;"
                      v-if="bodyObj.dz == null || bodyObj.dz == ''"
                    >净重: <u></u></div>
                    <div
                      style="font-size: 12px; -webkit-transform-origin-x: -123px; -webkit-transform: scale(0.7);width:90px;"
                      v-else
                    >净重: <u>{{ ((Number(bodyObj.dz) * item.qty) / 1000).toFixed(2) }}</u></div>
                  </div>
                </div>

                <div style="margin-top: -2px;margin-left: 7px">
                  <div style="position: relative;font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);">
                    品名:
                    <div style="position: absolute;top: 0px; left: 30px;width: 135px;">
                      <u>{{ bodyObj.prdName == null || bodyObj.prdName == "" || bodyObj.prdName == undefined ? "" : bodyObj.prdName.slice(0, 13) }}</u>
                    </div>
                    <div style="position: absolute;top: -3px; left: 194px;font-size: 17px; -webkit-transform-origin-x: -123px; -webkit-transform: scale(0.7);width:120px;">颜色: <u>{{ bodyObj.ys }}</u></div>
                  </div>
                </div>

                <div style="display: flex; margin-top: -2px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                    <div style="width: 150px;">规格: <u>{{ bodyObj.spc == null || bodyObj.spc == "" || bodyObj.spc == undefined ? "" : bodyObj.spc.slice(0, 13) }}</u></div>
                  </div>
                  <div style="margin-left: 10px">
                    <div style="font-size: 12px; -webkit-transform-origin-x: -160px; -webkit-transform: scale(0.7);width:90px;">
                      件数: <u></u>
                    </div>
                  </div>
                </div>
                <div style="margin-top: -4px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);width: 190px;">
                    <div style="width:250px;">供应商: <u>{{ bodyObj.cusName == null || bodyObj.cusName == "" || bodyObj.cusName == undefined ? "" : bodyObj.cusName.slice(0, 17) }}</u></div>
                  </div>
                </div>
              </div>

               右边条码 
              <div style="width: 50mm; background-color: pink; padding: 2px; position: relative;">
                <div
                  style="position: absolute; right: 10px; top: 7px; padding-top: 4px"
                  :id="`qrcodeRight${index}`"
                ></div>
                <div style="position: absolute;right: -5px; top: 61px;font-size: 12px;-webkit-transform-origin-x: -27px; -webkit-transform: scale(0.6);">
                  {{ newDate }}
                </div>
                <div style="position: absolute; left: 9px; top: 22px;font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">任务单：<u>{{ bodyObj.taskNo == null || bodyObj.taskNo == undefined ? '' : bodyObj.taskNo }}</u></div>
                <div style="position: absolute; right: 43px; top: 3px; padding-top: 3px;font-size: 12px;-webkit-transform-origin-x: -27px; -webkit-transform: scale(0.7);">
                  {{ bodyObj.cw }}
                </div>
                <div style="margin-left: 18px;font-size: 16px;-webkit-transform-origin-x: -27px;-webkit-transform: scale(0.7);padding-top: 3px;">
                  物料标识卡
                </div>
                <div style="margin-top: 7px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                    P/O: <u>{{ bodyObj.bilNo }}</u>
                  </div>
                </div>
                <div style="margin-top: -2px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                    料号: <u>{{ bodyObj.prdNo == null || bodyObj.prdNo == "" || bodyObj.prdNo == undefined ? "" : bodyObj.prdNo.slice(0, 15) }}</u>
                  </div>
                </div>
                <div style="margin-top: -2px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);width: 100%;position: relative;">
                    <div
                      style="width: 100px;"
                      v-if="item.lastQty"
                    >
                      数量: <u>{{ item.lastQty }}</u>
                    </div>
                    <div
                      style="width: 100px;"
                      v-else
                    >
                      数量: <u>{{ item.qty }}</u>
                    </div>
                    <div style="position: absolute;left:157px;top: 0px;width:90px;">单重: <u>{{ bodyObj.dz == null || bodyObj.dz == '' || bodyObj.dz == undefined ? bodyObj.dz : Number(bodyObj.dz).toFixed(2) }}</u></div>
                  </div>
                </div>
                <div style="margin-top: -2px;margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);">
                    <div style="width: 100%;position: relative;">
                      批号: <u v-if="modify">{{ prnDd }}</u><u v-else>{{ bodyObj.prnDd == null || bodyObj.prnDd == undefined || bodyObj.prnDd == "" ? "" : bodyObj.prnDd }}</u>
                      <div
                        style="position: absolute;left:157px;top: 0px;width:90px;"
                        v-if="bodyObj.dz == null || bodyObj.dz == ''"
                      >净重: <u></u></div>
                      <div
                        style="position: absolute;left:157px;top: 0px;width:90px;"
                        v-else-if="item.lastQty"
                      >净重: <u>{{ ((Number(bodyObj.dz) * item.lastQty) / 1000).toFixed(2) }}</u></div>
                      <div
                        style="position: absolute;left:157px;top: 0px;width:90px;"
                        v-else
                      >净重: <u>{{ ((Number(bodyObj.dz) * item.qty) / 1000).toFixed(2) }}</u></div>
                    </div>
                  </div>
                </div>

                <div style="margin-top: -2px;margin-left: 7px">
                  <div style="position: relative;font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);">
                    品名:
                    <div style="position: absolute;top: 0px; left: 30px;width: 135px;">
                      <u>{{ bodyObj.prdName == null || bodyObj.prdName == "" || bodyObj.prdName == undefined ? "" : bodyObj.prdName.slice(0, 13) }}</u>
                    </div>
                    <div style="position: absolute;top: -3px; left: 194px;font-size: 17px; -webkit-transform-origin-x: -123px; -webkit-transform: scale(0.7);width:120px;">颜色: <u>{{ bodyObj.ys }}</u></div>
                  </div>
                </div>

                <div style="margin-top: -2px;margin-left: 7px;position: relative;">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);">
                    <div style="width: 150px;">
                      规格: <u>{{ bodyObj.spc == null || bodyObj.spc == "" || bodyObj.spc == undefined ? "" : bodyObj.spc.slice(0, 13) }}</u>
                    </div>
                  </div>
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);position: absolute;left: 110px;top: 0px;">
                    件数: <u></u>
                  </div>
                </div>
                <div style="margin-top: -4px; margin-left: 7px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);width: 190px;">
                    <div style="width:250px;">供应商: <u>{{ bodyObj.cusName == null || bodyObj.cusName == "" || bodyObj.cusName == undefined ? "" : bodyObj.cusName.slice(0, 17) }}</u></div>
                  </div>
                </div>
              </div>
            </div>

            是否显示最后一个条码 
            <div
              style="width: 50mm;background-color: pink;padding: 2px;margin-right: 3mm;position: relative;font-weight: 700;"
              v-if="showLastCard"
            >
              <div
                style="position: absolute; right: 10px; top: 7px; padding-top: 4px"
                id="qrcodeLast"
              ></div>
              <div style="position: absolute;right: -5px; top: 61px;font-size: 12px;-webkit-transform-origin-x: -27px; -webkit-transform: scale(0.6);">{{ newDate }}</div>
              <div style="position: absolute; left: 9px; top: 22px;font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">任务单：<u>{{ bodyObj.taskNo == null || bodyObj.taskNo == undefined ? '' : bodyObj.taskNo }}</u></div>
              <div style="position: absolute; right: 43px; top: 3px; padding-top: 3px;font-size: 12px;-webkit-transform-origin-x: -27px; -webkit-transform: scale(0.7);">{{ bodyObj.cw }}</div>
              <div style=" margin-left: 18px; font-size: 16px; -webkit-transform-origin-x: -27px; -webkit-transform: scale(0.7); padding-top: 3px;">
                物料标识卡
              </div>
              <div style="margin-top: 7px; margin-left: 7px">
                <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                  P/O: <u>{{ bodyObj.bilNo }}</u>
                </div>
              </div>
              <div style="margin-top: -2px; margin-left: 7px">
                <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                  料号: <u>{{ bodyObj.prdNo == null || bodyObj.prdNo == "" || bodyObj.prdNo == undefined ? "" : bodyObj.prdNo.slice(0, 15) }}</u>
                </div>
              </div>

              <div style="margin-top: -2px; margin-left: 7px">
                <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                  <div style="width: 100%;position: relative;">
                    数量: <u>{{ delObj.qty }}</u>
                    <div style="position: absolute;left:157px;top: 0px;width:90px;">
                      单重: <u>{{ bodyObj.dz == null || bodyObj.dz == '' || bodyObj.dz == undefined ? bodyObj.dz : Number(bodyObj.dz).toFixed(2) }}</u>
                    </div>
                  </div>
                </div>
              </div>

              <div style="display: flex; margin-top: -2px; margin-left: 7px">
                <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                  <div style="width: 150px;">
                    批号: <u v-if="modify">{{ prnDd }}</u><u v-else>{{ bodyObj.prnDd == null || bodyObj.prnDd == undefined || bodyObj.prnDd == "" ? "" : bodyObj.prnDd }}</u>
                  </div>
                </div>
                <div>
                  <div
                    style="font-size: 12px; -webkit-transform-origin-x: -123px; -webkit-transform: scale(0.7);width:90px;"
                    v-if="bodyObj.dz == null || bodyObj.dz == ''"
                  >净重: <u></u></div>
                  <div
                    style="font-size: 12px; -webkit-transform-origin-x: -123px; -webkit-transform: scale(0.7);width:90px;"
                    v-else
                  >净重: <u>{{ ((Number(bodyObj.dz) * delObj.qty) / 1000).toFixed(2) }}</u></div>
                </div>
              </div>

              <div style="margin-top: -2px;margin-left: 7px">
                <div style="position: relative;font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);">
                  品名:
                  <div style="position: absolute;top: 0px; left: 30px;width: 135px;">
                    <u>{{ bodyObj.prdName == null || bodyObj.prdName == "" || bodyObj.prdName == undefined ? "" : bodyObj.prdName.slice(0, 13) }}</u>
                  </div>
                  <div style="position: absolute;top: -3px; left: 194px;font-size: 17px; -webkit-transform-origin-x: -123px; -webkit-transform: scale(0.7);width:120px;">颜色: <u>{{ bodyObj.ys }}</u></div>
                </div>
              </div>

              <div style="display: flex; margin-top: -2px; margin-left: 7px">
                <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7)">
                  <div style="width: 150px;">规格: <u>{{ bodyObj.spc == null || bodyObj.spc == "" || bodyObj.spc == undefined ? "" : bodyObj.spc.slice(0, 13) }}</u></div>
                </div>
                <div style="margin-left: 10px">
                  <div style="font-size: 12px; -webkit-transform-origin-x: -160px; -webkit-transform: scale(0.7);width:90px;">
                    件数: <u></u>
                  </div>
                </div>
              </div>
              <div style="margin-top: -4px; margin-left: 7px">
                <div style="font-size: 12px; -webkit-transform-origin-x: 10px; -webkit-transform: scale(0.7);width: 190px;">
                  <div style="width:250px;">供应商: <u>{{ bodyObj.cusName == null || bodyObj.cusName == "" || bodyObj.cusName == undefined ? "" : bodyObj.cusName.slice(0, 17) }}</u></div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div> -->

    <div class="form-query-no">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-form-item :label="$t('delivery.sysDate')">
            <a-date-picker
              v-model="startDd"
              :inputReadOnly="true"
              format="YYYY-MM-DD"
              :placeholder="$t('entrustment.startD')"
              @openChange="handleStartOpenChange"
              id="a-date"
            />
            <a-date-picker
              v-model="endDd"
              :inputReadOnly="true"
              format="YYYY-MM-DD"
              :placeholder="$t('entrustment.endD')"
              :open="endOpen"
              @openChange="handleEndOpenChange"
              style="margin-left: 8px;"
              id="a-date"
            />
          </a-form-item>
          <a-form-item :label="$t('scheduling.number')">
            <a-input
              v-model="queryParam.bilNo"
              placeholder="请输入单号"
              id="a-date"
            />
          </a-form-item>
          <a-form-item>
            <span class="table-page-search-submitButtons">
              <a-button
                type="primary"
                @click="handleQuery"
                v-permission="srm_barcodeprinting_search"
              >{{ $t('public.query') }}</a-button>
              <a-button
                style="margin-left: 8px"
                @click="reset_a"
                v-permission="srm_barcodeprinting_reset"
              >{{ $t('public.reset') }}</a-button>
              <a-button
                type="primary"
                @click="print"
                v-permission="srm_barcodeprinting_print"
                style="margin-left: 8px"
              >{{ $t('public.print') }}</a-button>
              <a-button
                type="primary"
                @click="orderprint"
                v-permission="srm_barcodeprinting_print"
                style="margin-left: 8px"
              >{{ $t('public.print') }}</a-button>
              <a-button
                type="primary"
                @click="setUp"
                v-permission="srm_barcodeprinting_setup"
                style="margin-left: 8px"
              >{{ $t('public.setup') }}</a-button>
              <!-- 补单打印 -->
              <!-- <a-button
                type="primary"
                @click="modifyBox"
                v-permission="srm_barcodeprinting_print"
                style="margin-left: 8px"
              >{{ $t('补单打印') }}</a-button> -->
              <!-- 补单打印 -->
            </span>
          </a-form-item>
        </a-row>
      </a-form>
    </div>
    <a-row :gutter="24">
      <a-col :span="6">
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
          </a-form>
        </div>
        <vxe-toolbar custom>
        </vxe-toolbar>
        <vxe-table
          border
          stripe
          highlight-current-row
          show-overflow
          highlight-hover-row
          export-config
          max-height="450"
          ref="xTable"
          size="mini"
          :loading="loading"
          :data="tableData"
          :keyboard-config="{ isArrow: true }"
          @cell-dblclick="clickEvent"
          :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }"
        >
          <vxe-table-column
            field="bilNo"
            title="单号"
            align="center"
            fixed="left"
          ></vxe-table-column>
          <vxe-table-column
            field="prnDd"
            title="打印日期"
            align="center"
            :edit-render="{name: '$input', props: {type: 'date'},events: {change: sexChangeEvent}}"
          >
          </vxe-table-column>
          <vxe-table-column
            field="bilDd"
            title="delivery.sysDate"
            align="center"
          ></vxe-table-column>
        </vxe-table>
        <div style="margin-top:10px;">
          <vxe-pager
            :loading="loading"
            :current-page="tablePage.currentPage"
            :page-size="tablePage.pageSize"
            :total="tablePage.total"
            :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
            @page-change="handlePageChange"
          >
          </vxe-pager>
        </div>
      </a-col>
      <a-col :span="18">
        <div>
          <div>
            <vxe-toolbar custom>
            </vxe-toolbar>
          </div>
          <vxe-table
            border
            stripe
            highlight-current-row
            show-overflow
            highlight-hover-row
            export-config
            ref="xTable2"
            class="inputdata"
            size="mini"
            max-height="450"
            :loading="loading2"
            :data="tableData2"
            :keyboard-config="{ isArrow: true }"
            :seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
            @checkbox-change="selectChangeEvent"
            @checkbox-all="selectChangeAll"
            :edit-rules="validRules"
            :edit-config="{trigger: 'click', mode: 'cell'}"
          >

            <!-- :radio-config="{highlight: true}" -->
            <!-- @radio-change="radioChangeEvent" -->
            <!-- <vxe-table-column
              type="radio"
              width="50"
              align="center"
            >
              <template v-slot:header>
                <vxe-button
                  type="text"
                  @click="clearRadioRowEevnt"
                  :disabled="!selectRow"
                  align="center"
                >取消</vxe-button>
              </template>
            </vxe-table-column> -->
            <vxe-table-column
              type="checkbox"
              align="center"
              :width="50"
              fixed="left"
            ></vxe-table-column>
            <vxe-table-column
              type="seq"
              title="delivery.bilItm"
              align="center"
              width="50"
              fixed="left"
            ></vxe-table-column>
            <vxe-table-column
              field="bilNo"
              title="delivery.bilNo"
              align="center"
              width="110"
            ></vxe-table-column>
            <vxe-table-column
              field="prdNo"
              title="delivery.prdNo"
              align="center"
              width="140"
            ></vxe-table-column>
            <vxe-table-column
              field="prdName"
              title="delivery.prdName"
              align="center"
              width="150"
            ></vxe-table-column>
            <vxe-table-column
              field="utName"
              title="delivery.unit"
              align="center"
              width="50"
            ></vxe-table-column>
            <vxe-table-column
              field="qty"
              title="单据数量"
              align="center"
              width="100"
            ></vxe-table-column>
            <vxe-table-column
              field="prnQty"
              title="打印数量"
              align="center"
              width="110"
              :edit-render="{name: '$input'}"
            >
              <template v-slot="{ row }">
                <span>{{ printNum(row) }}</span>
              </template>
            </vxe-table-column>
            <!-- <vxe-table-column
              field="boxQty"
              title="包装数量"
              align="center"
              width="110"
              :edit-render="{name: '$input', props: {type: 'float'}}"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.boxQty"
                  @focus="loseblur(row)"
                  type="text"
                ></el-input>
              </template>
            </vxe-table-column> -->
            <vxe-table-column
              field="boxQty"
              title="包装数量"
              align="center"
              width="110"
            >
              <template v-slot="{ row }">
                <el-row class="demo-autocomplete">
                  <el-col :span="12">
                    <el-autocomplete
                      class="inline-input"
                      v-model="row.boxQty"
                      @focus="queryfocus(row)"
                      :fetch-suggestions="querySearch"
                      placeholder="请输入内容"
                      @select="handleSelect"
                    ></el-autocomplete>
                  </el-col>
                  <!-- value-key="address" -->
                </el-row>
              </template>
            </vxe-table-column>
            <vxe-table-column
              field="prnDd"
              title="打印日期"
              align="center"
              width="110"
            ></vxe-table-column>
            <vxe-table-column
              field="prnNo"
              title="打印批号"
              :width="prnNowidth"
              align="center"
              :edit-render="{name: 'input'}"
            ></vxe-table-column>
          </vxe-table>
        </div>
        <!-- <vxe-pager
          :loading="loading2"
          :current-page="tablePage2.currentPage"
          :page-size="tablePage2.pageSize"
          :total="tablePage2.total"
          :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
          @page-change="handlePageChange1"
        >
        </vxe-pager> -->
      </a-col>
      <!-- 添加body弹出框 -->
      <drawerClass
        ref="body"
        @getProList="getProList"
      />
    </a-row>
    <!-- 补单打印 -->
    <a-modal
      title="补单打印"
      v-model="modifyVisible"
      :destroyOnClose="true"
      :maskClosable="false"
      @cancel="onClose"
    >
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row>
            <a-col :span="12">
              <a-form-model-item
                v-bind="formItemLayout"
                :label="$t('批号')"
              >
                <a-date-picker
                  style="width: 95%"
                  v-model="prnDd"
                  :placeholder="$t('批号')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                v-bind="formItemLayout"
                :label="$t('数量')"
              >
                <a-input
                  style="width: 95%"
                  v-model="boxQty"
                  :placeholder="$t('数量')"
                />
              </a-form-model-item>

            </a-col>
          </a-row>
        </a-form>
      </div>
      <template slot="footer">
        <a-button
          key="ok"
          @click="save()"
        >{{ $t('public.sure') }}</a-button>
        <a-button
          key="cancel"
          @click="onClose"
        >{{ $t('public.cancel') }}</a-button>
      </template>
    </a-modal>
    <!-- 补单打印 -->
    <a-modal
      title="设置"
      v-model="drawerVisible"
      :destroyOnClose="true"
      :maskClosable="false"
      :footer="null"
    >
      <div class="table-page-search-wrapper">
        <a-form
          :label-col="{ span: 5 }"
          :wrapper-col="{ span: 12 }"
        >
          <a-form-item label="箱条码规则">
            <a-select
              v-model="barCodeRules"
              placeholder="请选择箱条码规则"
              @change="selectChange"
              allowClear
            >
              <a-select-option
                :lable="item.rem"
                :value="item.ruleNo"
                v-for="item in ruleType2"
                :key="item.id"
              >
                {{ item.text }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="批号规则">
            <a-select
              v-model="batchNumberRule"
              placeholder="请选择批号规则"
              @change="selectChangePh"
              allowClear
            >
              <a-select-option
                :lable="item.rem"
                :value="item.ruleNo"
                v-for="item in ruleType3"
                :key="item.id"
              >
                {{ item.text }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="打印模板">
            <a-select
              v-model="printSign"
              placeholder="请选择打印模板"
              @change="selectChangeMb"
            >
              <a-select-option :value="'1'">
                单标签(100mm*70mm)
              </a-select-option>
              <a-select-option :value="'2'">
                双标签(103mm*40mm)
              </a-select-option>
              <a-select-option :value="'3'">
                三力士
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
        <div style="height: 20px;">
          <a-button
            type="primary"
            @click="drawerVisible=false"
            style="float: right;"
          >确定</a-button>
        </div>
      </div>
    </a-modal>
    <a-modal
      v-model="isPrint"
      title="提示"
      :maskClosable="false"
      :closable="false"
      :footer="null"
      width="400px"
    >
      <div style="height: 95px;">
        <div
          id="print_icon"
          style="font-size:22px;color: red;"
        >
          <div style="color:#f00;font-size:16px;margin-bottom:6px;">温馨提示，请按正确包装数量，否则无法收货。</div>
          <a-icon type="printer" />
          <span style="color: rgba(0, 0, 0, 0.85);margin-left:8px;">确定要打印吗？</span>
        </div>
        <div style="float: right;margin-top:10px;">
          <a-button @click="printClose">取消</a-button>
          <a-button
            type="primary"
            v-print="printObj"
            @click="printSure"
            :disabled="isPrintButton"
            style="margin-left: 8px"
          >确定</a-button>
        </div>
      </div>
    </a-modal>
  </a-card>
</template>
<script>
import { fetchList, findPrnTf, barCodeSearch, barCodeAdd, dropDown } from '@/api/srm/barcodeprinting'
import drawerClass from '../drawer'
import moment from 'moment'
import XEUtils from 'xe-utils'
import QRCode from 'qrcodejs2'
export default {
  components: {
    drawerClass
  },
  data () {
    return {
      srm_barcodeprinting_search: 'srm_barcodeprinting_search',
      srm_barcodeprinting_reset: 'srm_barcodeprinting_reset',
      srm_barcodeprinting_print: 'srm_barcodeprinting_print',
      srm_barcodeprinting_setup: 'srm_barcodeprinting_setup',
      tableData: [],
      tableData2: [],
      printData: [],
      row: {},
      headerRow: {},
      delObj: {},
      bodyObj: {},
      bodyObj2: {},
      batchNumberRuleObj: {},
      data: [],
      ruleType2: [],
      ruleType3: [],
      getCodeArr: [],
      name: '',
      check: '',
      numb: '',
      wh: '',
      os: '',
      cusName: '',
      bjDd: '',
      sqlTable: '',
      barCodeRules: '',
      batchNumberRule: '',
      printSign: '',
      prnNowidth: '380',
      amtnTotal: null,
      Len: null,
      startDd: null,
      endDd: null,
      newDate: moment(new Date()).format('YYYY-MM'),
      toDayDate: moment(new Date()).format('YYYY-MM-DD'),
      endOpen: false,
      drawerVisible: false,
      isPrint: false,
      selectRow: null,
      showLastCard: false,
      isPrintButton: false,
      formItemLayout: {
        labelCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          md: { span: 24 },
          xs: { span: 24 },
          sm: { span: 15 },
        },
      },
      tablePage: {
        currentPage: 1,
        pageSize: 100,
        total: 0,
      },
      tablePage2: {
        currentPage: 1,
        pageSize: 100,
        total: 0,
      },
      printObj: {
        id: "",
      },
      loading: false,
      loading2: false,
      btn: true,
      queryParam: {
        bilNo: '',
        startDd: moment(new Date()).format('YYYY-MM-DD 00:00:00'),
        endDd: moment(new Date()).format('YYYY-MM-DD 23:59:59')
      },
      queryParam2: {},
      validRules: {
        prnQty: [
          { required: true, message: '打印数量不能为空' },
          { validator: prnQty1 }
        ],
        boxQty: [
          { required: true, message: '包装数量不能为空' }
        ]
      },
      // 补单打印
      modifyVisible: false,
      modify: false,
      prnDd: null,
      boxQty: null,
      budanobj: null,
      danbiao: false,
      danbiao2: false,
      restaurants: [],
      state1: '',
      state2: '',
      newDateday: null,
      storageDate: null,
      rowdata: {},
      flag: true,
      oneCountList: [
        // { "value": "4", "address": "5" },
        // { "value": "6", "address": "7" },
      ]
    }

    const prnQty1 = ({ cellValue }) => {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          if (!cellValue || cellValue.length == 0) {
            reject(new Error('打印数量不能为空'))
          } else {
            resolve()
          }
        }, 20)
      })
    }
  },
  watch: {
    startDd (val) {
      if (val) {
        this.queryParam.startDd = moment(val).format('YYYY-MM-DD 00:00:00')
      } else {
        this.queryParam.startDd = null
      }
    },
    endDd (val) {
      if (val) {
        this.queryParam.endDd = moment(val).format('YYYY-MM-DD 23:59:59')
      } else {
        this.queryParam.endDd = null
      }
    },
    queryParam2: {
      handler (val, oldval) {
        this.tableData2 = []
        // this.getProList()
      },
      deep: true, // 对象内部的属性监听，也叫深度监听
      immediate: true,
    },
  },
  created () {
    this.startDd = moment(new Date())
    this.endDd = moment(new Date())
    if (window.innerWidth > 1670 && window.innerWidth < 1900) {
      this.prnNowidth = '200'
      return
    }
    if (window.innerWidth > 1590 && window.innerWidth < 1680) {
      this.prnNowidth = '150'
      return
    }
    if (window.innerWidth < 1600) {
      this.prnNowidth = '110'
      return
    }
  },
  mounted () {
    this.BarCode()
    this.restaurants = this.loadAll();
  },
  methods: {
    queryfocus (row) {
      this.oneCountList = []
      this.rowdata = row
      // { "value": "4", "address": "5" },
      row.oneCountList.forEach((item, index) => {
        var downvalue = { value: item.toString() }
        this.oneCountList.push(downvalue)
      })
 
      // this.rowdata.oneCountList = this.oneCountList
    },
    querySearch (queryString, cb) {
      this.restaurants = this.oneCountList
      // this.restaurants = this.rowdata.oneCountList
      var restaurants = this.restaurants;
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    // createFilter (queryString) {
    //   return (restaurant) => {
    //     return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
    //   };
    // },
    createFilter (queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
      };
    },
    loadAll () {
      return this.oneCountList
    },
    handleSelect (item) {
     
    },
    selectChangeEvent () {

    },
    selectChangeAll () {

    },
    sexChangeEvent (val) {
      this.clickEvent(val)
    },
    loseblur (row) {
      if (parseFloat(row.boxQty) == 0) {
        row.boxQty = null
      }
    },
    BarCode () {
      barCodeSearch().then(res => {
        this.ruleType2 = res.data.filter(item => item.ruleType == '2')
        this.ruleType2.forEach(i => {
          i.text = i.ruleNo + ' ' + i.rem
        })
        this.ruleType3 = res.data.filter(item => item.ruleType == '3')
        this.ruleType3.forEach(e => {
          e.text = e.ruleNo + ' ' + e.rem
        })
        if (this.$ls.get(this.$store.state.user.info.userId + 'XTM') != null)
          this.barCodeRules = this.$ls.get(this.$store.state.user.info.userId + 'XTM')
        this.barCodeRules = this.ruleType2[0].ruleNo
        if (this.$ls.get(this.$store.state.user.info.userId + 'PH') != null)
          this.batchNumberRule = this.$ls.get(this.$store.state.user.info.userId + 'PH')
        this.batchNumberRule = this.ruleType3[0].ruleNo
        if (this.$ls.get(this.$store.state.user.info.userId + 'MB') != null)
          this.printSign = this.$ls.get(this.$store.state.user.info.userId + 'MB')
        else
          this.printSign = '2'
        this.printSign = '1'

      })
        .catch(err => {
          this.ruleType2 = []
          this.ruleType3 = []
          this.printSign = '2'
          this.requestFailed(err)
        })
    },
    printNum (row) {
      if (XEUtils.subtract(row.qty, row.prnQty) < 0) {
        this.$message.error('打印数量不能大于单据数量！')
        row.prnQty = ''
        return ''
      }
      if (row.prnQty < 0) {
        this.$message.error('打印数量不能小于0！')
        row.prnQty = ''
        return ''
      }

      if (parseFloat(row.prnQty) == 0) {
        row.prnQty = null
        return null
      }
      else if (row.prnQty) {
        if (row.prnQty.indexOf('.') != -1) {
          if (row.prnQty.split(".")[1].length) {
            if (row.prnQty.split(".")[1].length > 3) {
              row.prnQty = row.prnQty.split(".")[0] + '.' + row.prnQty.split(".")[1].substring(0, 3)
            }
          }
        }
      }
      if (!isNaN(row.prnQty)) {
      } else {
        row.prnQty = null
      }
      return row.prnQty
    },
    // 箱条码规则
    selectChange (val) {
      if (val) {
        this.$ls.set(this.$store.state.user.info.userId + 'XTM', val)
      } else {
        this.$ls.remove(this.$store.state.user.info.userId + 'XTM')
      }
    },
    // 批号规则
    selectChangePh (val) {
      if (val) {
        this.batchNumberRuleObj = {}
        this.ruleType3.forEach(i => {
          if (i.ruleNo == val) {
            this.batchNumberRuleObj = i
          }
          this.$ls.set(this.$store.state.user.info.userId + 'PH', val)
        })
      } else {
        this.$ls.remove(this.$store.state.user.info.userId + 'PH')
        this.batchNumberRuleObj = {}
      }
      if (this.headerRow.bilNo) this.getBody(this.headerRow.bilNo, this.batchNumberRuleObj)
    },
    // 打印模板
    selectChangeMb (val) {
      if (val) {
        this.$ls.set(this.$store.state.user.info.userId + 'MB', val)
      }
    },
    setUp () {
      if (this.ruleType2 == [] || this.ruleType2.length == 0) this.BarCode()
      this.drawerVisible = true
    },
    clearRadioRowEevnt () {
      this.selectRow = null
      this.$refs.xTable2.clearRadioRow()
    },
    radioChangeEvent ({ row }) {
      this.selectRow = row
    },
    // 补单打印

    modifyBox () {
      // this.budanobj = this.$refs.xTable2.getRadioRecord()
      this.budanobj = this.$refs.xTable2.getCheckboxRecords()
      if (this.budanobj.length == 0)
        return this.$message.warning('请至少选择一条数据')
      this.budanobj.forEach((item, index) => {
        this.prnDd = item.prnDd
      })
      this.modifyVisible = true
      this.modify = true


      //       else
      //         if (obj.boxQty == '' || obj.boxQty == undefined || obj.boxQty == null)
      //     return this.$message.error('标签数量不能为空！')
      //         else
      //           if (+ obj.boxQty == 0)
      //   return this.$message.error('标签数量不能为0！')
      //           else
      //             if (+ obj.boxQty < 0)
      // return this.$message.error('标签数量不能小于0！')
      // // this.isPrint = true
      // this.bodyObj = obj
      // this.printObj.id = this.printSign == '1' ? 'form1' : 'form2'
      // this.printSign == '1' ? this.computedForm1Num(obj.prnQty, obj.boxQty) : this.computedForm2Num(obj.prnQty, obj.boxQty)

    },
    onClose () {
      this.modifyVisible = false
      this.modify = false
    },
    save () {

      if (!!this.prnDd) {
        this.prnDd = moment(this.prnDd).format('YYYY-MM-DD')
      }
      let obj = JSON.stringify(this.$refs.xTable2.getCheckboxRecords())
      obj = JSON.parse(obj)
      obj.forEach((item, index) => {
        item.boxQty = this.boxQty
        item.prnDd = this.prnDd
      })
      // obj.boxQty = this.boxQty
      // obj.prnDd = this.prnDd
      if (this.boxQty == '' || this.boxQty == undefined || this.boxQty == null)
        return this.$message.error('包装数量不能为空！')
      else if (+this.boxQty == 0)
        return this.$message.error('包装数量不能为0！')
      else if (+this.boxQty < 0)
        return this.$message.error('包装数量不能小于0！')
      else if (this.prnDd == '' || this.prnDd == undefined || this.prnDd == null)
        return this.$message.error('批号不能为空！')
      else
        if (+obj.prnQty < +this.boxQty)
          return this.$message.error('包装数量不能大于打印数量！')
        else
          if (this.barCodeRules == '' || this.barCodeRules == undefined)
            return this.$message.error('请选择箱条码规则！')
          else
            if (this.printSign == '' || this.printSign == null)
              return this.$message.error('请选择打印模板！')
            else
              this.modifyVisible = false
      this.isPrint = true
      this.bodyObj = obj
      this.bodyObj2 = obj
      this.printObj.id = this.printSign == '1' ? 'form1' : 'form2'
      this.printSign == '1' ? this.computedForm1Num(obj.prnQty, this.boxQty) : this.computedForm2Num(obj.prnQty, this.boxQty)
    },
    printSure () {
      this.isPrint = false
    },
    printClose () {
      this.isPrint = false
      this.modify = false
    },
    // printRepair () {
    //   this.isPrint = true
    //   this.bodyObj = this.$refs.xTable2.getRadioRecord()
    //   this.printObj.id = this.printSign == '1' ? 'form1' : 'form2'
    //   this.printSign == '1' ? this.computedForm1Num(obj.prnQty, obj.boxQty) : this.computedForm2Num(obj.prnQty, obj.boxQty)
    // },
    // 补单打印
    print () {

      this.modify = false
      // this.$refs.xTable.getCheckboxRecords()
      let obj = this.$refs.xTable2.getCheckboxRecords()

      if (obj.length == 0)
        return this.$message.warning('请至少选择一条数据')
      for (var j = 0; j < obj.length; j++) {

        if (obj[j].boxQty == null || obj[j].boxQty == '' || obj[j].boxQty == undefined) {

          return this.$message.error('包装数量不能为空！')
          // break;

        }
        else if (+ obj[j].boxQty == 0) {
          return this.$message.error('包装数量不能为0！')
          // break;
        }
        else if (+ obj[j].boxQty < 0) {
          return this.$message.error('包装数量不能小于0！')
          // break;
        }

        else if (+ obj[j].prnQty < + obj[j].boxQty) {
          // alert(obj[j].prnQty)
          // alert(obj[j].boxQty)
          return this.$message.error('包装数量不能大于打印数量！')
          // break;
        }
        // else if (this.barCodeRules == '' || obj[j].barCodeRules == undefined) {
        //   this.$message.error('请选择箱条码规则！')
        //   break;
        // }
        // else if (obj[j].printSign == '' || obj[j].printSign == null) {
        //   this.$message.error('请选择打印模板！')
        //   break;
        // } 
      }
      if (this.barCodeRules == '' || this.barCodeRules == undefined)
        return this.$message.error('请选择箱条码规则！')
      else if (this.printSign == '' || this.printSign == null)
        return this.$message.error('请选择打印模板！')
      else
        this.isPrint = true
      this.bodyObj = obj
      this.bodyObj2 = obj
      dropDown({
        tfPrnList: this.bodyObj2
      })
        .then(res2 => {
      


        }).catch(err => {
          this.requestFailed(err)
        })
      // this.printObj.id = this.printSign == '1' ? 'form1' : 'form2'
      // 三力士下
      if (this.printSign == '1') {
        this.printObj.id = 'form1'
      } else if (this.printSign == '2') {
        this.printObj.id = 'form2'
      } else {
        this.printObj.id = 'form3'
      }
      // 三力士上
      // this.printSign == '1' ? this.computedForm1Num(obj) : this.computedForm2Num(obj)

      //  三力士下
      if (this.printSign == '1') {
        this.computedForm1Num(obj)
      } else if (this.printSign == '2') {
        this.computedForm2Num(obj)
      } else {
        // this.computedForm1Num(obj)
        this.computedForm3Num(obj)
      }
      // 三力士上



      // let obj = this.$refs.xTable2.getRadioRecord()
      // if (obj.length == 0)
      //   return this.$message.warning('请至少选择一条数据')
      // else
      //   if (obj.boxQty == '' || obj.boxQty == undefined || obj.boxQty == null)
      //     return this.$message.error('标签数量不能为空！')
      //   else
      //     if (+ obj.boxQty == 0)
      //       return this.$message.error('标签数量不能为0！')
      //     else
      //       if (+ obj.boxQty < 0)
      //         return this.$message.error('标签数量不能小于0！')
      //       else
      //         if (+obj.prnQty < +obj.boxQty)
      //           return this.$message.error('打印数量不能大于标签数量！')
      //         else
      //           if (this.barCodeRules == '' || this.barCodeRules == undefined)
      //             return this.$message.error('请选择箱条码规则！')
      //           else
      //             if (this.printSign == '' || this.printSign == null)
      //               return this.$message.error('请选择打印模板！')
      //             else
      //               this.isPrint = true
      // this.bodyObj = obj
      // this.bodyObj2 = obj

      // this.printObj.id = this.printSign == '1' ? 'form1' : 'form2'
      // this.printSign == '1' ? this.computedForm1Num(obj) : this.computedForm2Num(obj)


    },
    // 采购单打印
    orderprint () {
      this.modify = false
      // this.$refs.xTable.getCheckboxRecords()
      let obj = this.$refs.xTable2.getCheckboxRecords()
      if (obj.length == 0)
        return this.$message.warning('请至少选择一条数据')
      for (var j = 0; j < obj.length; j++) {
        if (obj[j].boxQty == null || obj[j].boxQty == '' || obj[j].boxQty == undefined) {
          return this.$message.error('包装数量不能为空！')
          // break;
        }
        else if (+ obj[j].boxQty == 0) {
          return this.$message.error('包装数量不能为0！')
          // break;
        }
        else if (+ obj[j].boxQty < 0) {
          return this.$message.error('包装数量不能小于0！')
          // break;
        }
        else if (+ obj[j].prnQty < + obj[j].boxQty) {
          // alert(obj[j].prnQty)
          // alert(obj[j].boxQty)
          return this.$message.error('包装数量不能大于打印数量！')
          // break;
        }
        // else if (this.barCodeRules == '' || obj[j].barCodeRules == undefined) {
        //   this.$message.error('请选择箱条码规则！')
        //   break;
        // }
        // else if (obj[j].printSign == '' || obj[j].printSign == null) {
        //   this.$message.error('请选择打印模板！')
        //   break;
        // } 
      }
      if (this.barCodeRules == '' || this.barCodeRules == undefined)
        return this.$message.error('请选择箱条码规则！')
      else if (this.printSign == '' || this.printSign == null)
        return this.$message.error('请选择打印模板！')
      else
        this.isPrint = true
      this.bodyObj = obj
      this.bodyObj2 = obj
      dropDown({
        tfPrnList: this.bodyObj2
      })
        .then(res2 => {
       
        }).catch(err => {
          this.requestFailed(err)
        })
      // this.printObj.id = this.printSign == '1' ? 'form1' : 'form2'
      // 三力士下
      if (this.printSign == '1') {
        this.printObj.id = 'form1'
      } else if (this.printSign == '2') {
        this.printObj.id = 'form2'
      } else {
        this.printObj.id = 'form3'
      }
      // 三力士上
      // this.printSign == '1' ? this.computedForm1Num(obj) : this.computedForm2Num(obj)
      //  三力士下
      if (this.printSign == '1') {
        this.computedForm1Num(obj)
      } else if (this.printSign == '2') {
        this.computedForm2Num(obj)
      } else {
        // this.computedForm1Num(obj)
        this.computedForm3Num(obj)
      }
      // 三力士上
    },
    computedForm1Num (obj) {
      this.bodyObj2.forEach((item, index) => {
        this.printData = []
        const form1Arr = []
        this.showLastCard = false
        if ((+item.prnQty % +item.boxQty) == 0) {
          const form1Num = +item.prnQty / +item.boxQty
          for (let i = 0; i < form1Num; i++) {
            form1Arr.push(Object.assign({}, { qty: +item.boxQty }))
          }
        } else {
          const form1remainder = +item.prnQty % +item.boxQty
          const form1numOne = (+item.prnQty - form1remainder) / +item.boxQty
          for (let i = 0; i < form1numOne; i++) {
            form1Arr.push(Object.assign({}, { qty: +item.boxQty }))
          }
          const obj = { qty: form1remainder }
          form1Arr.push(obj)
        }
        item.form1Arr = form1Arr
        item.pageCount = form1Arr.length
        this.printData = form1Arr
      })
      this.getBarCodeAdd()
      this.danbiao = true
      // this.printData = []
      // const form1Arr = []
      // this.showLastCard = false
      // if ((+prnQty % +boxQty) == 0) {
      //   const form1Num = +prnQty / +boxQty
      //   for (let i = 0; i < form1Num; i++) {
      //     form1Arr.push(Object.assign({}, { qty: +boxQty }))
      //   }
      // } else {
      //   const form1remainder = +prnQty % +boxQty
      //   const form1numOne = (+prnQty - form1remainder) / +boxQty
      //   for (let i = 0; i < form1numOne; i++) {
      //     form1Arr.push(Object.assign({}, { qty: +boxQty }))
      //   }
      //   const obj = { qty: form1remainder }
      //   form1Arr.push(obj)
      // }
      // this.getBarCodeAdd(form1Arr.length)
      // this.printData = form1Arr
    },
    computedForm3Num (obj) {
      this.bodyObj2.forEach((item, index) => {
        this.printData = []
        const form1Arr = []
        this.showLastCard = false
        if ((+item.prnQty % +item.boxQty) == 0) {
          const form1Num = +item.prnQty / +item.boxQty
          for (let i = 0; i < form1Num; i++) {
            form1Arr.push(Object.assign({}, { qty: +item.boxQty }))
          }
        } else {
          const form1remainder = +item.prnQty % +item.boxQty
          const form1numOne = (+item.prnQty - form1remainder) / +item.boxQty
          for (let i = 0; i < form1numOne; i++) {
            form1Arr.push(Object.assign({}, { qty: +item.boxQty }))
          }
          const obj = { qty: form1remainder }
          form1Arr.push(obj)
        }
        item.form1Arr = form1Arr
        item.pageCount = form1Arr.length
        this.printData = form1Arr
      })
      this.getBarCodeAdd()
      this.danbiao = true
    },
    computedForm2Num (obj) {
      this.bodyObj2.forEach((item, index) => {
        const arr = []
        if ((+item.prnQty % +item.boxQty) == 0) {
          const num = +item.prnQty / +item.boxQty
          for (let i = 0; i < num; i++) {
            arr.push(Object.assign({}, { qty: +item.boxQty }))
          }
          item.form1Arr = arr
          // this.computedCardNum(arr)
          // 多个
          let len = arr.length
          item.pageCount = arr.length
          const printArr = []
          if (String(len / 2).indexOf('.') > -1) {
            const count = Math.floor(len / 2)
            this.delObj = arr[arr.length - 1]
            for (let i = 0; i < count; i++) {
              printArr.push(Object.assign({}, { qty: arr[i].qty }))
            }
            this.printData = printArr
            // item.form1Arr = printArr
            this.showLastCard = true
          } else {
            const countTwo = len / 2
            for (var i = 0; i < countTwo; i++) {
              if (i == (countTwo - 1)) {
                printArr.push(Object.assign({}, { qty: arr[i].qty, lastQty: arr[arr.length - 1].qty }))
              } else {
                printArr.push(Object.assign({}, { qty: arr[i].qty }))
              }
            }
            this.printData = printArr
            // item.form1Arr = printArr
            this.showLastCard = false
          }
          // 多个
          // 添加
          // const len = arr.length
          // const printArr = []
          // if (String(len / 2).indexOf('.') > -1) {
          //   const count = Math.floor(len / 2)
          //   this.delObj = arr[arr.length - 1]
          //   for (let i = 0; i < count; i++) {
          //     printArr.push(Object.assign({}, { qty: arr[i].qty }))
          //   }
          //   this.printData = printArr
          //   this.showLastCard = true
          // } else {
          //   const countTwo = len / 2
          //   for (var i = 0; i < countTwo; i++) {
          //     if (i == (countTwo - 1)) {
          //       printArr.push(Object.assign({}, { qty: arr[i].qty, lastQty: arr[arr.length - 1].qty }))
          //     } else {
          //       printArr.push(Object.assign({}, { qty: arr[i].qty }))
          //     }
          //   }
          //   this.printData = printArr
          //   this.showLastCard = false
          // }
          // 添加
        } else {
          const remainder = +item.prnQty % +item.boxQty
          const numTwo = (+item.prnQty - remainder) / +item.boxQty
          for (let i = 0; i < numTwo; i++) {
            arr.push(Object.assign({}, { qty: +item.boxQty }))
          }
          const obj = { qty: remainder }
          arr.push(obj)
          // item.form1Arr = arr
          // this.computedCardNum(arr)
          // 多个
          // 暂时
          item.form1Arr = arr
          // 暂时
          let len = arr.length
          item.pageCount = arr.length
          const printArr = []
          if (String(len / 2).indexOf('.') > -1) {
            const count = Math.floor(len / 2)
            this.delObj = arr[arr.length - 1]
            for (let i = 0; i < count; i++) {
              printArr.push(Object.assign({}, { qty: arr[i].qty }))
            }
            this.printData = printArr
            // item.form1Arr = printArr
            this.showLastCard = true
          } else {
            const countTwo = len / 2
            for (var i = 0; i < countTwo; i++) {
              if (i == (countTwo - 1)) {
                printArr.push(Object.assign({}, { qty: arr[i].qty, lastQty: arr[arr.length - 1].qty }))
              } else {
                printArr.push(Object.assign({}, { qty: arr[i].qty }))
              }
            }
            this.printData = printArr
            // item.form1Arr = printArr
            this.showLastCard = false
          }
          // 多个
        }
        // item.form1Arr = this.printData
      })
      this.getBarCodeAdd()
      this.danbiao2 = true
      // const arr = []
      // if ((+prnQty % +boxQty) == 0) {
      //   const num = +prnQty / +boxQty
      //   for (let i = 0; i < num; i++) {
      //     arr.push(Object.assign({}, { qty: +boxQty }))
      //   }
      //   this.computedCardNum(arr)
      // } else {
      //   const remainder = +prnQty % +boxQty
      //   const numTwo = (+prnQty - remainder) / +boxQty
      //   for (let i = 0; i < numTwo; i++) {
      //     arr.push(Object.assign({}, { qty: +boxQty }))
      //   }
      //   const obj = { qty: remainder }
      //   arr.push(obj)
      //   this.computedCardNum(arr)
      // }
      // this.getBarCodeAdd(arr.length)
    },
    computedCardNum (arr) {
      const len = arr.length
      const printArr = []
      if (String(len / 2).indexOf('.') > -1) {
        const count = Math.floor(len / 2)
        this.delObj = arr[arr.length - 1]
        for (let i = 0; i < count; i++) {
          printArr.push(Object.assign({}, { qty: arr[i].qty }))
        }
        this.printData = printArr
        this.showLastCard = true
      } else {
        const countTwo = len / 2
        for (var i = 0; i < countTwo; i++) {
          if (i == (countTwo - 1)) {
            printArr.push(Object.assign({}, { qty: arr[i].qty, lastQty: arr[arr.length - 1].qty }))
          } else {
            printArr.push(Object.assign({}, { qty: arr[i].qty }))
          }
        }
        this.printData = printArr
        this.showLastCard = false
      }
    },
    getBarCodeAdd () {
      this.flag = true
      let obj = {}
      this.getCodeArr = []
      // if (this.$ls.get(this.$store.state.user.info.userId + 'XTM') != null) {

      let val = this.$ls.get(this.$store.state.user.info.userId + 'XTM')
      this.ruleType2.forEach(i => {
        // if (i.ruleNo == val) {
        if (this.barCodeRules == i.ruleNo) {
          obj.splitFields = i.splitFields
          obj.splitStr = i.splitStr
          obj.barLen = i.barLen
        }
        // }
      })
      // }
      // let PrnCodeVo = {
      //   ...this.bodyObj,
      //   oneCount: Number(this.bodyObj.boxQty),
      //   pageCount: count,
      //   barcodeRuleVo: {
      //     ...obj
      //   }
      // }

      for (var i = 0; i < this.bodyObj2.length; i++) {
        
        if (!isNaN(this.bodyObj2[i].boxQty)) {
        } else {
          this.flag = false
          this.isPrint = false
          this.$message.warning('请检查包装数量')
          break;
        }
        // else {
        //   i.boxQty = null
        // }
      }
      if (this.flag) {
        this.bodyObj2.forEach((item, index) => {
          item.oneCount = item.boxQty
          item.oneCount = Number(item.oneCount)
        })
        let PrnCodeVo = {
          boxDetailList: this.bodyObj2,
          // oneCount: arrLength,
          // pageCount: count,
          barcodeRuleVo: {
            ...obj
          }
        }
        // 获取流水号 ↵20T2P30-388-97-01#102021-01-22 04:31:38#00001

        this.isPrintButton = true

        barCodeAdd(PrnCodeVo)
          .then(res => {
            if (res.data) {
              this.getCodeArr = res.data
              // this.$message.warning('请检查默认包装数量是否正确，错误拒收')

              // 多个
              this.bodyObj2.forEach((item, index) => {
                for (let i = 0; i < res.data.length; i++) {
                  if (index == i) {
                    item.getCodeArr = res.data[i].boxCodeList
                  }
                  // res.data.boxDetailList.forEach((item2, index2) => {
                  //   item.getCodeArr = item2
                }
              })
              // 暂时
              this.bodyObj2.forEach((item, index) => {
                item.form1Arr.forEach((item2, index2) => {

                  item2.erwei = item.getCodeArr[index2]
                })
              })
              // 暂时
              // 多个
              //  三力士下
              if (this.printSign == '1') {
                this.getBarCodeForm1()
              } else if (this.printSign == '2') {
                this.getBarCodeForm2()
              } else {
                this.getBarCodeForm3()
              }
              // 三力士上
              // this.printSign == '1' ? this.getBarCodeForm1() : this.getBarCodeForm2()
              this.isPrintButton = false
            } else {
              this.isPrintButton = true
              this.$message.error('流水码未生成！')
            }
          })
          .catch(err => {
            this.getCodeArr = []
            this.isPrintButton = true
            this.$message.error('流水码未生成！')
            this.requestFailed(err)
          })
      }
    },
    // getBarCodeForm1 () {
    //   this.$nextTick(() => {
    //     for (let i = 0; i < this.printData.length; i++) {
    //       let dom = 'qrcodeLeftForm1' + i
    //       if (document.getElementById(dom).innerHTML == null || document.getElementById(dom).innerHTML == '') {
    //       } else {
    //         document.getElementById(dom).innerHTML = ''
    //       }
    //     }
    //   })
    //   this.$nextTick(() => {
    //     for (let i = 0; i < this.printData.length; i++) {
    //       let indexForm1 = i.toString()
    //       new QRCode('qrcodeLeftForm1' + indexForm1, {
    //         width: 78,
    //         height: 78,
    //         text: this.getCodeArr[i], // 二维码地址
    //       })
    //     }
    //   })
    //   this.isPrintButton = false
    // },
    getBarCodeForm3 () {
      // 三力士下
      this.bodyObj2.forEach((item, index) => {
        this.$nextTick(() => {
          for (let i = 0; i < item.form1Arr.length; i++) {
            let dom6 = 'sanlishi' + index + i
            let dom7 = 'sanlishi' + index + i
            // let dom11 = 'qrcodeLeftForm101'
            // if (document.getElementById(dom11).innerHTML == null || document.getElementById(dom11).innerHTML == '') {
            // } else {
            //   document.getElementById(dom11).innerHTML = ''
            // }
            if (document.getElementById(dom6).innerHTML == null || document.getElementById(dom6).innerHTML == '') {
            } else {
              document.getElementById(dom6).innerHTML = ''
            }
            if (document.getElementById(dom7).innerHTML == null || document.getElementById(dom7).innerHTML == '') {
            } else {
              document.getElementById(dom7).innerHTML = ''
            }
          }
        })
      })
      this.bodyObj2.forEach((item, index) => {
        this.$nextTick(() => {
          for (let i = 0; i < item.form1Arr.length; i++) {
            item.form1Arr[i].sanlishi = 'sanlishi' + index + i
            // let indexForm1 = i.toString()
            new QRCode('sanlishi' + index + i, {
              width: 78,
              height: 78,
              text: item.getCodeArr[i], // 二维码地址
            })
          }
        })
      })
      this.isPrintButton = false
      // 三力士上
    },
    getBarCodeForm1 () {
      this.bodyObj2.forEach((item, index) => {
        this.$nextTick(() => {
          for (let i = 0; i < item.form1Arr.length; i++) {

            let dom8 = 'qrcodeLeftForm1' + index + i
            let dom9 = 'qrcodeLeftForm1' + index + i
            // let dom10 = 'qrcodeLeftForm101'
            // if (document.getElementById(dom10).innerHTML == null || document.getElementById(dom10).innerHTML == '') {
            // } else {
            //   document.getElementById(dom10).innerHTML = ''
            // }
            if (document.getElementById(dom8).innerHTML == null || document.getElementById(dom8).innerHTML == '') {
            } else {
              document.getElementById(dom8).innerHTML = ''
            }
            if (document.getElementById(dom9).innerHTML == null || document.getElementById(dom9).innerHTML == '') {
            } else {
              document.getElementById(dom9).innerHTML = ''
            }
          }
        })
      })
      this.bodyObj2.forEach((item, index) => {
        this.$nextTick(() => {
          for (let i = 0; i < item.form1Arr.length; i++) {
            item.form1Arr[i].qrcodeLeftForm1 = 'qrcodeLeftForm1' + index + i
            // let indexForm1 = i.toString()
            new QRCode('qrcodeLeftForm1' + index + i, {
              width: 78,
              height: 78,
              text: item.getCodeArr[i], // 二维码地址
            })
          }
        })
        // this.$nextTick(() => {
        //   for (let i = 0; i < this.printData.length; i++) {
        //     let indexForm1 = i.toString()
        //     new QRCode('qrcodeLeftForm1' + index + i, {
        //       width: 78,
        //       height: 78,
        //       text: item.getCodeArr[i], // 二维码地址
        //     })
        //   }
        // })
      })
      this.isPrintButton = false
    },
    // 备份
    // getBarCodeForm1 () {
    //   this.$nextTick(() => {
    //     for (let i = 0; i < this.printData.length; i++) {
    //       let dom = 'qrcodeLeftForm1' + i
    //       if (document.getElementById(dom).innerHTML == null || document.getElementById(dom).innerHTML == '') {
    //       } else {
    //         document.getElementById(dom).innerHTML = ''
    //       }
    //     }
    //   })
    //   this.bodyObj2.forEach((item, index) => {
    //     this.$nextTick(() => {
    //       for (let i = 0; i < item.form1Arr.length; i++) {
    //         let indexForm1 = i.toString()
    //         new QRCode('qrcodeLeftForm1' + indexForm1, {
    //           width: 78,
    //           height: 78,
    //           text: item.getCodeArr[i], // 二维码地址
    //         })
    //       }
    //     })
    //     this.$nextTick(() => {
    //       for (let i = 0; i < this.printData.length; i++) {
    //         let indexForm1 = i.toString()
    //         new QRCode('qrcodeLeftForm1' + indexForm1, {
    //           width: 78,
    //           height: 78,
    //           text: item.getCodeArr[i], // 二维码地址
    //         })
    //       }
    //     })
    //   })
    //   this.isPrintButton = false
    // },
    // 备份
    // 多个
    getBarCodeForm2 () {
      this.bodyObj2.forEach((item, index) => {
        this.$nextTick(() => {
          for (let i = 0; i < item.form1Arr.length; i++) {
            let dom = 'qrcodeLeft' + i
            let dom2 = 'qrcodeRight' + i
            if (document.getElementById(dom).innerHTML == null || document.getElementById(dom).innerHTML == '') {
            } else {
              document.getElementById(dom).innerHTML = ''
            }
            if (document.getElementById(dom2).innerHTML == null || document.getElementById(dom2).innerHTML == '') {
            } else {
              document.getElementById(dom2).innerHTML = ''
            }
          }
          // if (this.showLastCard) document.getElementById('qrcodeLast').innerHTML = ''
        })
      })

      this.bodyObj2.forEach((item, index) => {
        this.$nextTick(() => {
          for (let i = 0; i < item.form1Arr.length; i++) {
            let dom = 'erweima' + index + i
            let dom2 = 'erweima' + index + i
            if (document.getElementById(dom).innerHTML == null || document.getElementById(dom).innerHTML == '') {
            } else {
              document.getElementById(dom).innerHTML = ''
            }
            if (document.getElementById(dom2).innerHTML == null || document.getElementById(dom2).innerHTML == '') {
            } else {
              document.getElementById(dom2).innerHTML = ''
            }
          }
          // if (this.showLastCard) document.getElementById('qrcodeLast').innerHTML = ''
        })
      })
      this.bodyObj2.forEach((item, index) => {
        this.$nextTick(() => {
          for (let i = 0; i < item.form1Arr.length; i++) {
            let dom = 'erweimaright' + index + i
            let dom2 = 'erweimaright' + index + i
            if (document.getElementById(dom).innerHTML == null || document.getElementById(dom).innerHTML == '') {
            } else {
              document.getElementById(dom).innerHTML = ''
            }
            if (document.getElementById(dom2).innerHTML == null || document.getElementById(dom2).innerHTML == '') {
            } else {
              document.getElementById(dom2).innerHTML = ''
            }
          }
          // if (this.showLastCard) document.getElementById('qrcodeLast').innerHTML = ''
        })
      })

      // this.bodyObj2.forEach((item, index) => {
      //   this.$nextTick(() => {
      //     // var count = -1
      //     for (let i = 0; i < item.form1Arr.length; i++) {
      //       if (index == i) {
      //         new QRCode('qrcodeLeft' + index, {
      //           width: 54,
      //           height: 54,
      //           text: item.getCodeArr[count], // 二维码地址
      //         })
      //       }

      //       let index = i.toString()
      //       if (i == 0)
      //         count = i
      //       else
      //         count = count + 1
      //       new QRCode('qrcodeLeft' + index, {
      //         width: 54,
      //         height: 54,
      //         text: item.getCodeArr[count], // 二维码地址
      //       })
      //       count = count + 1
      //       new QRCode('qrcodeRight' + index, {
      //         width: 54,
      //         height: 54,
      //         text: item.getCodeArr[count], // 二维码地址
      //       })
      //     }
      //     if (this.showLastCard) {
      //       new QRCode('qrcodeLast', {
      //         width: 54,
      //         height: 54,
      //         text: item.getCodeArr[item.getCodeArr.length - 1], // 二维码地址
      //       })
      //     }
      //     this.isPrintButton = false
      //   })
      // })

      this.bodyObj2.forEach((item, index2) => {
        this.$nextTick(() => {
          var count = -1
          for (let i = 0; i < item.form1Arr.length; i++) {
            item.form1Arr[i].erweima = 'erweima' + index2 + i
            item.form1Arr[i].erweimaright = 'erweimaright' + index2 + i

            new QRCode('erweima' + index2 + i, {
              width: 54,
              height: 54,
              text: item.getCodeArr[i], // 二维码地址
            })
            new QRCode('erweimaright' + index2 + i, {
              width: 54,
              height: 54,
              text: item.getCodeArr[i], // 二维码地址
            })

            // item.form1Arr[i].erweima = erweima + index2 + i
            // alert(item.form1Arr[i].erweima)
            // if (index2 = i) {
            //   let index = i.toString()
            //   if (i == 0)
            //     count = i
            //   else
            //     count = count + 1
            //   alert('qrcodeLeft' + index)
            //   new QRCode('qrcodeLeft' + index, {
            //     width: 54,
            //     height: 54,
            //     text: item.getCodeArr[count], // 二维码地址
            //   })
            //   count = count + 1
            //   new QRCode('qrcodeRight' + index, {
            //     width: 54,
            //     height: 54,
            //     text: item.getCodeArr[count], // 二维码地址
            //   })
            // }
            // if (this.showLastCard) {
            //   new QRCode('qrcodeLast', {
            //     width: 54,
            //     height: 54,
            //     text: item.getCodeArr[item.getCodeArr.length - 1], // 二维码地址
            //   })
            // }
            this.isPrintButton = false
          }

        })
      })
      // this.bodyObj2.forEach((item, index) => {
      //   this.$nextTick(() => {
      //     var count = -1
      //     for (let i = 0; i < item.form1Arr.length; i++) {
      //       let index = i.toString()
      //       if (i == 0)
      //         count = i
      //       else
      //         count = count + 1
      //       new QRCode('qrcodeLeft' + index, {
      //         width: 54,
      //         height: 54,
      //         text: item.getCodeArr[count], // 二维码地址
      //       })
      //       count = count + 1
      //       new QRCode('qrcodeRight' + index, {
      //         width: 54,
      //         height: 54,
      //         text: item.getCodeArr[count], // 二维码地址
      //       })
      //     }
      //     if (this.showLastCard) {
      //       new QRCode('qrcodeLast', {
      //         width: 54,
      //         height: 54,
      //         text: item.getCodeArr[item.getCodeArr.length - 1], // 二维码地址
      //       })
      //     }
      //     this.isPrintButton = false
      //   })
      // })
    },
    // 多个
    // getBarCodeForm2 () {
    //   this.$nextTick(() => {
    //     for (let i = 0; i < this.printData.length; i++) {
    //       let dom = 'qrcodeLeft' + i
    //       let dom2 = 'qrcodeRight' + i
    //       if (document.getElementById(dom).innerHTML == null || document.getElementById(dom).innerHTML == '') {
    //       } else {
    //         document.getElementById(dom).innerHTML = ''
    //       }
    //       if (document.getElementById(dom2).innerHTML == null || document.getElementById(dom2).innerHTML == '') {
    //       } else {
    //         document.getElementById(dom2).innerHTML = ''
    //       }
    //     }
    //     if (this.showLastCard) document.getElementById('qrcodeLast').innerHTML = ''
    //   })
    //   this.$nextTick(() => {
    //     var count = -1
    //     for (let i = 0; i < this.printData.length; i++) {
    //       let index = i.toString()
    //       if (i == 0)
    //         count = i
    //       else
    //         count = count + 1
    //       new QRCode('qrcodeLeft' + index, {
    //         width: 54,
    //         height: 54,
    //         text: this.getCodeArr[count], // 二维码地址
    //       })
    //       count = count + 1
    //       new QRCode('qrcodeRight' + index, {
    //         width: 54,
    //         height: 54,
    //         text: this.getCodeArr[count], // 二维码地址
    //       })
    //     }
    //     if (this.showLastCard) {
    //       new QRCode('qrcodeLast', {
    //         width: 54,
    //         height: 54,
    //         text: this.getCodeArr[this.getCodeArr.length - 1], // 二维码地址
    //       })
    //     }
    //     this.isPrintButton = false
    //   })
    // },
    // 查询列表
    async getList () {
      this.loading = true
      this.tableData = []
      try {
        const res = await fetchList(
          Object.assign(
            {
              current: this.tablePage.currentPage,
              size: this.tablePage.pageSize,
            },
            this.queryParam
          )
        )
        this.loading = false
        this.tableData = res.data.records
        this.tablePage.total = res.data.total
        this.tablePage.currentPage = res.data.current
        this.reset_b()
      } catch (err) {
        this.reset_b()
        this.requestFailed(err)
      } finally {
        this.loading = false
      }
    },
    handleQuery () {
      this.tablePage.currentPage = 1
      this.getList()
    },
    // 查询表身
    async getProList () {
      this.loading2 = true
      this.tableData2 = []
      try {
        // eslint-disable-next-line no-undef
        const res = await findList(
          Object.assign(
            {
              current: this.tablePage2.currentPage,
              size: this.tablePage2.pageSize,
              prnNo: this.row.prnNo,
            },
            this.queryParam2
          )
        )
        this.loading2 = false
        this.tableData2 = res.data.records

        this.tablePage2.total = res.data.total
        this.tablePage2.currentPage = res.data.current
      } catch (err) {
        this.tableData2 = []
        this.requestFailed(err)
      } finally {
        this.loading2 = false
      }
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    handlePageChange1 ({ currentPage, pageSize }) {
      this.tablePage2.currentPage = currentPage
      this.tablePage2.pageSize = pageSize
      this.getProList()
    },
    // 单击事件
    clickEvent ({ row }) {
      this.printData = []
      this.headerRow = row
      // if (this.$ls.get(this.$store.state.user.info.userId + 'PH') != null) {
      //   let val = this.$ls.get(this.$store.state.user.info.userId + 'PH')
      //   this.ruleType3.forEach(i => {
      //     if (i.ruleNo == val) {
      //       this.batchNumberRuleObj = i
      //     }
      //   })
      // }
  
      let val = this.$ls.get(this.$store.state.user.info.userId + 'PH')
      this.ruleType3.forEach(i => {
        // if (i.ruleNo == val) {
        if (i.ruleNo == this.batchNumberRule) {
          this.batchNumberRuleObj = i
        }
        // }
      })


      this.getBody(this.headerRow.bilNo, this.batchNumberRuleObj)
    },
    getDay (day) {
      var today = new Date();
      var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
      today.setTime(targetday_milliseconds);
      var tYear = today.getFullYear();
      var tMonth = today.getMonth();
      var tDate = today.getDate();
      tMonth = this.doHandleMonth(tMonth + 1);
      tDate = this.doHandleMonth(tDate);
      return tYear + "-" + tMonth + "-" + tDate;
    },
    doHandleMonth (month) {
      var m = month;
      if (month.toString().length == 1) {
        m = "0" + month;
      }
      return m;
    },
    getBody (bilNo, obj = {}) {
     
  
      let that = this
      this.newDateday = moment(new Date()).format('YYYY-MM-DD')
      this.loading2 = true
      // this.bodyObj = {}
      findPrnTf({
        // current: this.tablePage2.currentPage,
        // size: this.tablePage2.pageSize,
        bilNo: bilNo,
        splitFields: obj.splitFields,
        splitStr: obj.splitStr,
        barLen: obj.barLen
      })
        .then(res => {
          this.loading2 = false
          const resArr = res.data
          resArr.forEach(i => {
            i.prnDd = moment(this.headerRow.prnDd).format('YYYY-MM-DD')
          })
          this.tableData2 = resArr
          this.tablePage2.total = res.data.total

          this.tableData2.forEach((item, index) => {
            if (parseFloat(item.boxQty) == 0) {
              item.boxQty = null
            }
            if (item.validDays == null || item.validDays == '') {
            } else {
              item.validDays = this.getDay(item.validDays)
            }
            if (item.oneCountList.length) {
              if (item.oneCountList[0]) {
                item.boxQty = item.oneCountList[0].toString()
              } else {
                item.boxQty = null
              }

            }
            // if (item.prnNo) {
            //   item.prnNo = item.prnNo.substring(0, 10)
            // }
          })

        })
        .catch(err => {
          this.tableData2 = []
          this.loading2 = false
          this.requestFailed(err)
        })
    },
    // 重置搜索内容
    reset_a () {
      this.queryParam = {}
      this.printData = []
      this.bodyObj = {}
      this.startDd = null
      this.endDd = null
    },
    reset_b () {
      this.queryParam2 = {}
      this.data = []
      this.Len = 0
    },
    // 新增表头
    add () {
      this.$refs.head.create({ title: this.$t('public.add') })
    },
    cellDBLClickEvent ({ row }) {
      this.$refs.body.edit({ title: this.$t('public.Detailed') }, row)
    },
    disabledStartDate (startValue) {
      const endValue = this.fbEndDd
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate (endValue) {
      const startValue = this.fbStartDd
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() >= endValue.valueOf()
    },
    handleStartOpenChange (open) {
      if (!open) {
        this.endOpen = true
      }
    },
    handleEndOpenChange (open) {
      this.endOpen = open
    }
  }
}
</script>

<style>
.inputdata .el-col-12 {
  width: 100%;
}
.inputdata input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
.inputdata .el-input__inner {
  height: 28px;
  line-height: 28px;
}
.el-autocomplete-suggestion li {
  line-height: 28px !important;
}
</style>

<style lang="less" scoped>
.form-query-no {
  margin-bottom: 10px;
  .ant-row {
    margin-left: 0px !important;
  }
}
.a-date {
  max-width: 174px;
  width: 100%;
}
.form_one {
  padding-top: 6px;
  padding-bottom: 6px;
  padding-left: 3px;
}
.form_last {
  padding-top: 5px;
  padding-bottom: 5px;
  padding-left: 3px;
}
#showPrint {
  page-break-before: always;
}
@media print {
  nav,
  aside {
    display: none;
  }
}
@page {
  margin: 0;
}
</style>
