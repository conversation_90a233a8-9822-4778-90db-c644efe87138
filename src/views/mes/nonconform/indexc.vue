<template>
  <div class="consrm" style="position:relative">
    <div style="position: absolute;top: 6px;left:195px;z-index:1">
      <a-button icon="search" style="margin-left: 8px" type="primary" size="small" @click="queryonetwo">{{
          $t('public.query') }}</a-button>
      <a-button icon="reload" size="small"
                style="margin-left: 8px;    color: #909399;background: #f4f4f5;border-color: #d3d4d6;" @click="reset">{{
          $t('public.reset') }}</a-button>
    </div>
    <el-tabs type="border-card" v-model="activeNameMain" @tab-click="handleClick">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="开始日期">
                <a-date-picker style="width:100%" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                               v-model="queryParam.staDd" placeholder="请输入开始日期" />

              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="结束日期">
                <a-date-picker style="width:100%" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                               v-model="queryParam.endDd" placeholder="请输入结束日期" />
              </a-form-item>
            </a-col>

            <a-col :md="6" :sm="24" v-if="activeNameMain == 'second'">
              <a-form-item :label="$t('评审单号')">
                <a-input ref="jianyantwo" @keyup.enter.native="queryonetwo" v-model="queryParam.lsNo"
                         :placeholder="$t('评审单号')" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('检验单号')">
                <a-input ref="jianyan" @keyup.enter.native="queryonetwo" v-model="queryParam.tyNo"
                         :placeholder="$t('检验单号')" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('工序')">
                <a-input v-model="queryParam.zcNo" :placeholder="$t('工序')" />
              </a-form-item>
            </a-col>
            <!-- <a-col :md="6" :sm="24">
              <a-form-item :label="$t('工单号')">
                <a-input v-model="queryParam.moNo" :placeholder="$t('工单号')" />
              </a-form-item>
            </a-col> -->
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('品号')">
                <a-input v-model="queryParam.prdNo" :placeholder="$t('品号')" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('工单号')">
                <el-popover placement="bottom" width="600" trigger="click" v-model="visibledan" title=""
                            :popper-options="{ boundariesElement: 'body'}">
                  <div id="popover">
                    <el-input slot="reference" v-model="queryParam.moNo" clearable placeholder="请选择"
                              style="max-width: 200px;width:100%;" @focus="zsMcdan" @input="zsMcdaninput"
                              size="mini"></el-input>
                    <el-table :data="zcNoListdan" @row-click="handdletwodan"
                              style="width: 100%;overflow:scroll;height:450px;" border size="mini">
                      <el-table-column prop="moNo" label="工单号"></el-table-column>
                      <el-table-column prop="mrpNo" label="品号"></el-table-column>
                      <el-table-column prop="prdName" label="品名"></el-table-column>
                    </el-table>
                    <div style="display: flex;justify-content: space-between;margin: 2px">
                      <el-pagination background :page-sizes="[20, 30, 50,100]" :page-size="20" :pager-count="5"
                                     @size-change="pageSizeChangedan" :current-page="tablePagedan.currentPage"
                                     @current-change="currentChangedan" layout="total,sizes,prev, pager, next"
                                     :total="totalCoundan"></el-pagination>
                    </div>
                  </div>
                  <el-input slot="reference" @input="zsMcdaninput" v-model="queryParam.moNo" clearable placeholder="请选择"
                            style="width:100%;" @focus="zsMcdan" size="mini"></el-input>
                </el-popover>
              </a-form-item>
            </a-col>


            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('品名')">
                <a-input v-model="queryParam.prdName" :placeholder="$t('品名')" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('制单人')">
                <my-selectListwo url="mes/basicData/salData" :read-only="true" allowClear :tableColumn="$Column.salm"
                                 :form="$Form.salm" :data="data" name="salNo" @choose="choose($event)" ref="selectList"
                                 v-decorator="['salNo', { rules: [{ required: true, message:'请选择制单人' } ] }]"
                                 :placeholder="'请选择制单人'"></my-selectListwo>
              </a-form-item>
            </a-col>
            <a-col :span="2">
              <a-form-item label="复检:" prop="chkKnd">
                <a-checkbox v-model="queryParam.chkKnd"></a-checkbox>
              </a-form-item>
            </a-col>
            <a-col :span="3">
              <a-form-item label="实验室检验:" prop="sys">
                <a-checkbox v-model="queryParam.sys"></a-checkbox>
              </a-form-item>
            </a-col>

          </a-row>
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <span class="table-page-search-submitButtons">

              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <el-tab-pane label="待评审" name="first">
        <a-row :gutter="8">
          <a-col :span="24">
            <a-row>
              <vxe-toolbar custom>
                <template v-slot:buttons>
                </template>
              </vxe-toolbar>
              <vxe-table size='small' border="inner" resizable stripe highlight-current-row show-overflow
                         highlight-hover-row export-config :loading="loading" :data="tableData"
                         :keyboard-config="{ isArrow: true }" :edit-config="{ trigger: 'click', mode: 'row' }"
                         :header-cell-style="{ backgroundColor: '#F4F5F9', color: '#7A8085', fontWeight: '400', fontSize: '12px' }"
                         :cell-style="{ fontSize: '12px' }" height="580px" @cell-dblclick="mfBxHanddleone">
                <vxe-table-column type="checkbox" align="center" :width="50"></vxe-table-column>
                <vxe-table-column field="tyDd" title="检验日期" width="110" align="center">
                  <template slot-scope="scope">
                    {{scope.row.tyDd | formatDate}}
                  </template>
                </vxe-table-column>
                <vxe-table-column field="tyNo" title="检验单号" width="130" align="center"></vxe-table-column>
                <vxe-table-column field="prdNo" title="品号" width="120" align="center"></vxe-table-column>
                <vxe-table-column field="prdName" title="品名" width="120" align="center"></vxe-table-column>
                <vxe-table-column field="qty" title="检验数量" width="100" align="center"></vxe-table-column>
                <vxe-table-column field="qtyLost" title="不合格量" width="100" align="center"></vxe-table-column>
                <vxe-table-column field="zcNo" title="工序" width="130" align="center">
                  <template slot-scope="scope">
                    {{scope.row.zcNo}}/{{scope.row.zcName}}
                  </template>
                </vxe-table-column>
                <vxe-table-column field="chkTypeName" title="检验类型" width="100" align="center"></vxe-table-column>
                <vxe-table-column field="salName" title="检验人员" width="100" align="center"></vxe-table-column>
                <vxe-table-column field="bilNo" title="来源单号" width="140" align="center"></vxe-table-column>
                <vxe-table-column field="moNo" title="工单号" width="130" align="center">
                </vxe-table-column>
                <vxe-table-column field="rem" title="备注" width="150" align="center"
                                  show-overflow-tooltip></vxe-table-column>
              </vxe-table>
              <vxe-pager :loading="loading" :current-page="tablePage.currentPage" :page-size="tablePage.pageSize"
                         :total="totalCount" :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
                         @page-change="handlePageChange"></vxe-pager>

              <!-- <el-table
                stripe
                :data="tableData"
                highlight-current-row
                :cell-style="{ verticalAlign: 'top' }"
                :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                style="width: 100%;"
                @selection-change="handleSelectionChange"
                height="580px"
                @row-dblclick="mfBxHanddleone"
              >
                <el-table-column prop="tyDd" width="100" align="left" label="检验日期">
                  <template slot-scope="scope">
                    {{scope.row.tyDd | formatDate}}
                  </template>
                </el-table-column>
                <el-table-column prop="tyNo" width="130" align="left" label="检验单号"></el-table-column>
                <el-table-column prop="prdNo" width="110" align="left" label="品号"></el-table-column>
                <el-table-column prop="prdName" width="120" align="left" label="品名"></el-table-column>
                <el-table-column prop="qty" width="90" align="left" label="检验数量"></el-table-column>
                <el-table-column prop="qtyLost" width="90" align="left" label="不合格量"></el-table-column>
                <el-table-column prop="zcName" width="150" align="left" label="工序">
                  <template slot-scope="scope">
                    {{scope.row.zcNo}}/{{scope.row.zcName}}
                  </template>
                </el-table-column>
                <el-table-column prop="chkTypeName" width="110" align="left" label="检验类型"></el-table-column>
                <el-table-column prop="salName" width="110" align="left" label="检验人员"></el-table-column>
                <el-table-column prop="bilNo" width="150" align="left" label="来源单号"></el-table-column>
                <el-table-column prop="moNo" width="150" align="left" label="工单号"></el-table-column>
                <el-table-column prop="rem" width="110" align="left" label="备注"></el-table-column>
              </el-table>
              <div style="display: flex;justify-content: space-between;margin: 2px">
                <el-pagination
                  background
                  :page-sizes="[5, 10,20, 30, 50]"
                  :page-size="20"
                  :pager-count="5"
                  @size-change="pageSizeChange"
                  :current-page="tablePage.currentPage"
                  @current-change="currentChange"
                  layout="total,sizes,prev, pager, next"
                  :total="totalCount"
                ></el-pagination>
              </div> -->
            </a-row>
          </a-col>
        </a-row>
      </el-tab-pane>
      <el-tab-pane label="已评审" name="second">
        <vxe-toolbar custom>
          <template v-slot:buttons>
          </template>
        </vxe-toolbar>
        <vxe-table size='small' border="inner" resizable stripe highlight-current-row show-overflow highlight-hover-row
                   export-config :loading="loading" :data="tableDatatwo" :keyboard-config="{ isArrow: true }"
                   :edit-config="{ trigger: 'click', mode: 'row' }"
                   :header-cell-style="{ backgroundColor: '#F4F5F9', color: '#7A8085', fontWeight: '400', fontSize: '12px' }"
                   :cell-style="{ fontSize: '12px' }" height="580px" @cell-dblclick="mfBxHanddle">
          <vxe-table-column type="checkbox" align="center" :width="50"></vxe-table-column>
          <vxe-table-column field="lsDd" title="评审日期" width="110" align="center">
            <template slot-scope="scope">
              {{scope.row.lsDd | formatDate}}
            </template>
          </vxe-table-column>
          <vxe-table-column field="lsNo" title="评审单号" width="130" align="center"></vxe-table-column>
          <vxe-table-column field="prdNo" title="品号" width="120" align="center"></vxe-table-column>
          <vxe-table-column field="prdName" title="品名" width="120" align="center"></vxe-table-column>
          <vxe-table-column field="qty" title="检验数量" width="100" align="center"></vxe-table-column>
          <vxe-table-column field="qtyLost" title="不合格量" width="100" align="center"></vxe-table-column>
          <vxe-table-column field="zcNo" title="工序" width="130" align="center">
            <template slot-scope="scope">
              {{scope.row.zcNo}}/{{scope.row.zcName}}
            </template>
          </vxe-table-column>
          <vxe-table-column field="chkTypeName" title="检验类型" width="100" align="center"></vxe-table-column>
          <vxe-table-column field="salName" title="检验人员" width="100" align="center"></vxe-table-column>

          <vxe-table-column field="moNo" title="工单号" width="130" align="center">
          </vxe-table-column>
          <vxe-table-column field="bilNo" title="来源单号" width="140" align="center"></vxe-table-column>
          <vxe-table-column field="rem" title="备注" width="150" align="center" show-overflow-tooltip></vxe-table-column>
        </vxe-table>
        <vxe-pager :loading="loading" :current-page="tablePagetwo.currentPage" :page-size="tablePagetwo.pageSize"
                   :total="totalCount" :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
                   @page-change="handlePageChangetwo"></vxe-pager>
        <!-- <el-table
                :data="tableDatatwo"
                 stripe
                highlight-current-row
                :cell-style="{ verticalAlign: 'top' }"
                :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                style="width: 100%;"
                @row-dblclick="mfBxHanddle"
                height="580px"
              >
                <el-table-column prop="lsDd" width="110" align="left" label="评审日期">
                  <template slot-scope="scope">
                    {{scope.row.lsDd | formatDate}}
                  </template>
                </el-table-column>
                <el-table-column prop="lsNo" width="130" align="left" label="评审单号"></el-table-column>

                 <el-table-column prop="prdNo" width="110" align="left" label="品号"></el-table-column>
                <el-table-column prop="prdName" width="110" align="left" label="品名"></el-table-column>
                <el-table-column prop="qty" width="90" align="left" label="检验数量"></el-table-column>
                <el-table-column prop="qtyLost" width="90" align="left" label="不合格量"></el-table-column>
                <el-table-column prop="zcName" width="110" align="left" label="工序">
                  <template slot-scope="scope">
                    {{scope.row.zcNo}}/{{scope.row.zcName}}
                  </template>
                </el-table-column>
                <el-table-column prop="chkTypeName" width="120" align="left" label="检验类型"></el-table-column>
                <el-table-column prop="salName" width="120" align="left" label="检验人员"></el-table-column>
                <el-table-column prop="moNo" width="150" align="left" label="工单号"></el-table-column>
                <el-table-column prop="bilNo" width="150" align="left" label="来源单号"></el-table-column>
               <el-table-column prop="rem" width="110" align="left" label="备注"></el-table-column>
              </el-table>

              <div style="display: flex;justify-content: space-between;margin: 2px">
                <el-pagination
                  background
                  :page-sizes="[5, 10,20, 30, 50]"
                  :page-size="20"
                  @size-change="pageSizeChangetwo"
                  :current-page="tablePagetwo.currentPage"
                  @current-change="currentChangetwo"
                  layout="total,sizes,prev, pager, next"
                  :total="totalCount"
                ></el-pagination>
              </div> -->
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import {
  fetchList, mfMoPage, getItem, start, lsqcdps, lsqcyps
} from '@/api/mes/quality'
import { mapGetters, mapState } from 'vuex'
import moment from 'moment'
import { first } from 'xe-utils/methods'
import MySelectListwo from '@/components/MySelectListwo'

export default {
  components: {
    MySelectListwo
  },
  data() {
    return {
      mes_quality_query: 'mes_quality_query',
      mes_quality_reset: 'mes_quality_reset',
      mes_quality_newOpen: 'mes_quality_newOpen',
      mes_quality_inspection: 'mes_quality_inspection',
      mes_quality_pause: 'mes_quality_inspection',
      mes_quality_continues: 'mes_quality_continues',
      mes_quality_task: 'mes_quality_task',
      queryParam: {},
      tableData: [],
      tableDatatwo: [],
      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      tablePagetwo: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      row: {},
      rowIndex: {},
      // 按钮控制
      btn: {
        newOpen: true, // 首检开工
        open: true, // 开始
        pause: true, // 暂停
        continues: true, // 继续
        report: true, // 报工
        exceptional: true, // 异常
        inspection: true, // 送检
        introspecting: true // 自检
      },
      queryParam: {
        lsNo: '',
        tyNo: '',
        tiNo: '',
        zcNo: '',
        moNo: '',
        prdName: '',
        prdNo: '',
        staDd: '',
        endDd: '',
        chkKnd: false,
        sys: false,
      },
      activeName: '1',
      valueone: [],
      multipleSelection: [],
      activeNameMain: "first",
      radioSelection: null,
      radio: '',
      totalCount: -1,
      data: '',
      salName: '',
      zcNoListdan: [],
      totalCoundan: -1,
      visibledan: false,
      tablePagedan: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    }),
    ...mapGetters(['permissions'])
  },
  created() {
    this.$nextTick(() => {
      this.$refs.jianyan.focus();
    })
    if (this.$route.params.tab) {
      this.activeNameMain = this.$route.params.tab

      if (this.$route.params.tab == 'second') {
        this.queryParam = JSON.parse(localStorage.getItem('bhgpsdyi'))
        this.data = this.queryParam.usr
        this.getListtwo()
      } else {
        this.queryParam = JSON.parse(localStorage.getItem('bhgpsddai'))
        this.data = this.queryParam.usr
        this.getList()
      }
    } else {
      this.queryParam.staDd = moment().subtract(30, "days").format('YYYY-MM-DD')
      this.queryParam.endDd = moment(new Date()).format('YYYY-MM-DD')
      this.getList()
    }
  },
  methods: {
    pageSizeChangedan(pageSize) {
      this.tablePagedan.pageSize = pageSize;
      this.zcNodan()
    },
    currentChangedan(currentPage) {
      this.tablePagedan.currentPage = currentPage;
      this.zcNodan()
    },
    zcNodan() {
      mfMoPage(
        {
          current: this.tablePagedan.currentPage,
          size: this.tablePagedan.pageSize,
          moNo: this.queryParam.moNo,
          staDd: this.queryParam.staDd,
          endDd: this.queryParam.endDd
        }).then(response => {

        this.zcNoListdan = response.data.records
        this.totalCoundan = response.data.total
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    zsMcdan() {
      this.tablePagedan.currentPage = 1
      this.zcNodan()
    },
    zsMcdaninput() {
      this.tablePagedan.currentPage = 1
      this.zcNodan()
    },
    handdletwodan(row, event, column) {
      this.queryParam.moNo = row.moNo
      this.queryParam.tzNo = row.tzNo
      this.queryParam.closeId = row.closeId
      this.visibledan = false;
    },
    choose(obj) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.salName = ''
          this.queryParam.usr = ''
          return
        }
      }
      if (obj.obj.name === 'salNo') {
        this.salName = obj.obj.data.name
        this.queryParam.usr = obj.obj.data.salNo
      }
    },
    handlePageChangetwo({ currentPage, pageSize }) {
      this.tablePagetwo.currentPage = currentPage
      this.tablePagetwo.pageSize = pageSize
      this.getListtwo();
    },
    queryonetwo() {
      if (this.activeNameMain == 'first') {
        this.tablePage.currentPage = 1
        this.getList()
      } else {
        this.tablePagetwo.currentPage = 1
        this.getListtwo()
      }
    },
    raidchange(row) {
      // 获取选中数据 row表示选中这一行的数据，可以从里面提取所需要的值
      this.multipleSelection = row


    },
    pageSizeChange(pageSize) {
      this.tablePage.pageSize = pageSize;
      this.getList();
    },
    pageSizeChangetwo(pageSize) {
      this.tablePagetwo.pageSize = pageSize;
      this.getListtwo();
    },
    currentChange(currentPage) {

      // this.visible = true;
      this.tablePage.currentPage = currentPage;
      this.getList();
    },
    currentChangetwo(currentPage) {
      // this.visible = true;
      this.tablePagetwo.currentPage = currentPage;
      this.getListtwo();
    },
    // 表格双击事件
    mfBxHanddle({ row }) {
      this.edit = row;
      this.editId = row.id;
      localStorage.setItem("nopingshenlsNo", row.lsNo);
      localStorage.setItem("bhgpsdyi", JSON.stringify(this.queryParam));
      localStorage.removeItem('nopingshentyNo')
      this.$router.push({
        name: 'nonconformdetail', params: {
          lsNo: row.lsNo
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    mfBxHanddleone({ row }) {
      if (this.multipleSelection) {
        localStorage.setItem('checkdata', JSON.stringify(row))
        localStorage.setItem("nopingshentyNo", row.tyNo);
        localStorage.setItem("bhgpsddai", JSON.stringify(this.queryParam));
        localStorage.removeItem('nopingshenlsNo')
        this.$router.push({
          name: 'nonconformdetail', params: {
            id: row.tyNo
          }
        })
      }
    },
    handleClick(val) {
      this.reset()

      if (val.name == 'first') {
        this.getList()
        this.$nextTick(() => {
          this.$refs.jianyan.focus();
        })
      } else {
        this.getListtwo()
        this.$nextTick(() => {
          this.$refs.jianyantwo.focus();
        })
      }

    },
    // onChange (data, dateString) {
    //
    //   this.queryParam.date = dateString

    // },
    onChange(data, dateString) {
      this.queryParam.date = dateString
      this.queryParam.staDd = this.queryParam.date[0]
      this.queryParam.endDd = this.queryParam.date[1]

    },
    // 按钮初始化
    btnInit() {
      this.btn.newOpen = true // 首检开工 null||""
      this.btn.pause = true // 暂停 2
      this.btn.continues = true // 继续 3
      this.btn.inspection = true // 首检 1
    },
    getListtwo() {
      this.loading = true
      lsqcyps(
        Object.assign({
          current: this.tablePagetwo.currentPage,
          // current: this.tablePage.currentPage,
          size: this.tablePagetwo.pageSize,
          staDd: this.queryParam.staDd,
          endDd: this.queryParam.endDd,
          tyNo: this.queryParam.tyNo,
          zcNo: this.queryParam.zcNo,
          moNo: this.queryParam.moNo,
          prdNo: this.queryParam.prdNo,
          prdName: this.queryParam.prdName,
          lsNo: this.queryParam.lsNo,
          sys: this.queryParam.sys ? 'T' : 'F',
          chkKnd: this.queryParam.chkKnd ? 'T' : 'F',
          usr: this.queryParam.usr,
          // bilNo:this.queryParam.bilNo,
          // pgNo: this.queryParam.pgNo
        })
      ).then(response => {
        this.loading = false
        this.tableDatatwo = response.data.records
        this.totalCount = response.data.total
        // this.tablePage.currentPage = response.data.current
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 获取列表数据
    getList() {
      this.loading = true
      lsqcdps(
        Object.assign({
          current: this.tablePage.currentPage,
          ...this.queryParam,
          sys: this.queryParam.sys ? 'T' : 'F',
          chkKnd: this.queryParam.chkKnd ? 'T' : 'F',
          size: this.tablePage.pageSize,
          //   staDd:this.queryParam.staDd,
          // endDd:this.queryParam.endDd,
          // tiNo:this.queryParam.tiNo,
          // zcNo:this.queryParam.zcNo,
          // moNo:this.queryParam.moNo,
          // prdNo:this.queryParam.prdNo,
          // prdName:this.queryParam.prdName,

        })
      ).then(response => {
        this.loading = false
        this.tableData = response.data.records
        this.tableData.forEach((i, index) => {
          i.addid = index + 1
        })
        this.totalCount = response.data.total
        // this.tablePage.currentPage = response.data.current
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    ClickEvent({ row }) {
      this.btnInit()
      this.rowIndex = row
      getItem(
        Object.assign({
          pgNo: row.pgNo,
          itm: row.itm,
          tiNo: row.tiNo,
          prdNo: row.prdNo
        })
      ).then(response => {
        this.row = response.data
        this.btnState(this.row)
      }).catch(err => this.requestFailed(err))
    },
    // 按钮状态
    btnState(row) {
      // 首检 ：3 暂停：2  继续：1
      if (row.state === '-1') {
        this.btn.newOpen = false
      }
      if (row.state === '0' || row.state === '3' || row.state === '1') {
        this.btn.pause = false // 暂停
        this.btn.inspection = false // 首检
      }
      if (row.state === '2') {
        this.btn.continues = false // 继续 3
      }
    },
    getBtnValue(state) {
      // 获取对应按钮提示
      let value = ''
      switch (state) {
        case '0':
          value = this.$t('quality.newOpen')
          break
        case '3':
          value = this.$t('quality.inspection')
          break
        case '2':
          value = this.$t('quality.pause')
          break
        case '1':
          value = this.$t('quality.continues')
          break
        case '4':
          value = this.$t('quality.task')
          break
      }
      return value
    },
    // 按钮
    onSubmit(state) {
      switch (state) {
        case '0':
        case '1':
        case '2':
          this.loading = true
          let btnMes = this.getBtnValue(state)
          start({
            state: state,
            pgNo: this.row.pgNo,
            itm: this.row.itm,
            id: this.row.id
          }).then((res) => {

            if (res.code === 0) {
              if (res.msg === 'fail') {
                btnMes = res.data
              } else {
                btnMes = btnMes + this.$t('public.success')
                const row = this.row
                this.ClickEvent({ row })
              }
            }
            this.$notification.success({
              message: this.$t('public.tip'),
              description: btnMes
            })
            this.loading = false
          }).catch(err => this.requestFailed(err))

          break
        case '3':
          const row = this.rowIndex
          this.$refs.a.create(row)
          break
      }
    },
    reset() {
      this.queryParam = {
        lsNo: '',
        tyNo: '',
        tiNo: '',
        zcNo: '',
        moNo: '',
        prdName: '',
        prdNo: '',
        staDd: '',
        endDd: '',
        usr: ''
      },
        this.data = ''
      this.$refs.selectList.clear()
      this.queryParam.staDd = moment().subtract(30, "days").format('YYYY-MM-DD')
      this.queryParam.endDd = moment(new Date()).format('YYYY-MM-DD')
    }
  }
}
</script>

<style>
.table-page-search-wrapper .table-page-search-submitButtons {
  margin-bottom: 0;
}

.el-table .el-table__cell {
  padding: 6px 0;
}

.ant-input {
  height: 28px;
}

/* .ant-tabs-nav-container{font-size:16px}
::v-deep .el-radio__label{
display:none
} */
</style>