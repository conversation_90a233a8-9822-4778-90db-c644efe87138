<template>
  <div class="consrm compoct">
    <div style="text-align:right;margin-right:16px;position: fixed;top: 90px;right:0px;z-index:999">
      <!-- <a-button
      style="margin-left: 8px"
      type="primary"
      @click="reset"
      v-if="$route.query.tyNo"
    >新增</a-button> -->
      <!-- <a-button
      style="margin-left: 8px"
      type="primary"
       :disabled="isClaim"
      :style="isClaim ? 'background-color: #909399;border-color: #909399;' : ''"
      @click="editclick"
    >编辑</a-button> -->
      <a-button style="margin-left: 8px;background-color: #f56c6c;border-color: #f56c6c;color:#fff;" type="primary"
        @click="deldata">删除</a-button>
      <!-- :disabled="!this.entity.tyNo" -->
      <a-button style="margin-left: 8px" type="primary" @click="save">保存</a-button>
      <!-- :disabled="isClaim"
      :style="isClaim ? 'background-color: #909399;border-color: #909399;' : ''" -->
      <a-button style="margin-left: 8px;    color: #909399;
    background: #f4f4f5;
    border-color: #d3d4d6;" type="primary" @click="cosetiao">关闭</a-button>

      <!-- 新增	编辑	删除	保存	关闭 -->
    </div>
    <el-tabs type="border-card">
      <el-tab-pane label="不合格评审单详情">
        <el-row :gutter="10">
          <el-col :span="16" :xs="24">
            <div class="sup_info_basics_header">
              <div style="float: left;margin-left: 8px;">基础信息</div>
            </div>
            <div ref="leftHeight" style="border:1px solid #e5e5e5;margin:0 auto;padding: 10px;padding-top:5px;padding-bottom:5px;
            background-color:#fff;border-radius:10px;">
              <el-form label-width="100px" :model="entity" :rules="exitRulesone" ref="addEntityFormone">
                <div style="padding-bottom:10px;">
                  <!-- border-bottom: 1px solid #E5E5E5; -->
                  <el-row :gutter="0">
                    <el-col :span="8">
                      <el-form-item label="评审日期:" prop="lsDd">
                        <span>{{ entity.lsDd | formatDate }}</span>
                        <!-- <el-date-picker
                        v-model="entity.tiDd"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        format="yyyy-MM-dd HH:mm:ss"
                        style="max-width: 200px;width:100%;"
                        size="mini"
                        type="datetime"
                        placeholder="请输入送检日期"
                      ></el-date-picker> -->
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="评审单号:" class="lsNo">
                        <!-- <el-input
                        v-model="entity.tiNo"
                        v-if="isEdit"
                        size="mini"
                        disabled
                        style="max-width: 200px;width:100%;"
                        placeholder="送检单号"
                      ></el-input> -->
                        <span>{{ entity.lsNo }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="检验类型:" prop="chkTypeId">
                        <!-- <el-select
                      v-if="isEdit"
                        v-model="entity.chkTypeId"
                        :disabled="isShowDisables"
                        placeholder="请选择检验类型"
                        size="mini"
                        style="max-width:200px; width:100%;"
                      >
                        <el-option
                          v-for="item in inspectiontype"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select> -->
                        <span v-if="entity.chkTypeId == 1">完工检验</span>
                        <span v-if="entity.chkTypeId == 2">首检检验</span>
                        <span v-if="entity.chkTypeId == 3">托工检验</span>


                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="0">
                    <el-col :span="8">
                      <!-- 检验是单选框 ： 一个勾选， 勾选"T"  默认不勾选 ‘F’   -->
                      <el-form-item label="发现地点:" prop="dor">
                        <!-- <div style="font-size:12px;"> {{entity.dor}}</div> -->
                        <el-input v-model="entity.dor" size="mini" :disabled="limits1"
                          style="max-width: 200px;width:100%;" placeholder="发现地点"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="送检单号:" prop="tiNo">
                        <div @click="jumpentrust" style="cursor: pointer;color:#1890FF"><span>{{ entity.tiNo }}</span>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="检验单号:" prop="tyNo">
                        <div @click="jumpcheck" style="cursor: pointer;color:#1890FF"><span>{{ entity.tyNo }}</span>
                        </div>
                      </el-form-item>
                    </el-col>

                  </el-row>
                  <el-row :gutter="0">
                    <el-col :span="8">
                      <el-form-item label="检验人员:" prop="salNo">
                        <!-- <el-input
                      v-if="isEdit"
                        v-model="entity.salNo"
                        size="mini"
                        style="max-width: 200px;width:100%;"
                        placeholder="请输入检验人员"
                      ></el-input> -->
                        <my-selectListwo url="mes/basicData/salData" :read-only="true" :disabled="limits1"
                          :tableColumn="$Column.salm" :form="$Form.salm" :data="datacheck" name="salNo"
                          @choose="choosecheck($event)" ref="selectList"
                          v-decorator="['salNo', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                          placeholder="请选择检验人员"></my-selectListwo>
                        <!-- <span>{{ entity.salNo }}</span> -->
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="单据类别:" prop="bilType">
                        <!-- 先不管 -->
                        <!-- <el-input
                      v-if="isEdit"
                        v-model="entity.bilType"
                        size="mini"
                        style="max-width: 200px;width:100%;"
                        placeholder="请输入单据类别"
                      ></el-input> -->
                        <span>{{ entity.bilType }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="实验室检验:" prop="sys">
                        <!-- 和检验一样 勾选T 不勾选F -->
                        <a-checkbox disabled v-model="entity.sys" @change="onChangeLaboratory"></a-checkbox>
                        <!-- <el-input
                      v-if="isEdit"
                        v-model="entity.sys"
                        size="mini"
                        style="max-width: 200px;width:100%;"
                        placeholder="请输入实验室检验"
                      ></el-input>
                      <span v-else>{{ entity.sys }}</span> -->
                      </el-form-item>
                    </el-col>

                  </el-row>
                  <el-row :gutter="0">
                    <el-col :span="8">
                      <el-form-item label="复检:" prop="chkKnd">
                        <!-- 和检验一样   T打钩  不打勾F 刚开始用返回值，保存时候判断 -->
                        <a-checkbox v-model="entity.chkKnd" disabled></a-checkbox>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="品号:" prop="prdNo">
                        <!-- <el-input
                      v-if="isEdit"
                        v-model="entity.prdNo"
                        size="mini"
                        style="max-width: 200px;width:100%;"
                        placeholder="请选择品号"
                        clearable
                        v-popover:popoverZuZhi
                      ></el-input> -->
                        <span>{{ entity.prdNo }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="品名:" prop="prdName">
                        <!-- <el-input
                      v-if="isEdit"
                        v-model="entity.prdName"
                        size="mini"
                        style="max-width: 200px;width:100%;"
                      ></el-input> -->
                        <span>{{ entity.prdName }}</span>
                      </el-form-item>
                    </el-col>


                  </el-row>
                  <el-row :gutter="0">
                    <el-col :span="8">
                      <el-form-item label="特征:">
                        <span>{{ entity.prdMark }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="单位:">
                        <span>{{ entity.unitName }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="批号:">
                        <span>{{ entity.batNo }}</span>
                      </el-form-item>
                    </el-col>


                  </el-row>
                  <el-row :gutter="0">
                    <el-col :span="8">
                      <el-form-item label="可疑数量:">
                        <span>{{ entity.qtySps }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="检验数量:">
                        <span>{{ entity.qty }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="不合格量:">
                        <span>{{ entity.qtyLost }}</span>
                      </el-form-item>
                    </el-col>


                  </el-row>
                  <el-row :gutter="0">
                    <el-col :span="8">
                      <el-form-item label="不合格率:">
                        <!-- <el-input >{{ entity.qtyLostRto }}</el-input> -->
                        <span>{{entity.qtyLostRto}}%</span>
                        <!-- <el-input
                        :disabled="limits1"
                        v-model="entity.qtyLostRto"
                        size="mini"
                        style="max-width: 200px;width:100%;"
                      ></el-input> -->
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <!-- <el-form-item label="责任部门:"> -->
                      <!-- <span>{{ entity.dep }}</span> -->
                      <el-form-item :label="$t('责任部门')" prop="dep">
                        <my-selectListwo :disabled="limits1" url="mes/basicData/depPage" :read-only="true"
                          :tableColumn="$Column.salmDep" :form="$Form.salmDep" :data="data" name="dep"
                          @choose="choose($event)" ref="selectList"
                          v-decorator="['dep', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                          :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="责任人:">
                        <span>{{ entity.salNoZr }}</span>
                      </el-form-item>
                    </el-col>


                  </el-row>
                  <el-row :gutter="0">
                    <el-col :span="8">
                      <el-form-item label="品质责任人:">
                        <span>{{ entity.salNoZrQc }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="工单号:">
                        <span>{{ entity.moNo }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="派工单号:">
                        <!-- <el-input
                      v-if="isEdit"
                        :disabled="true"
                        v-model="entity.pgNo"
                        size="mini"
                        style="max-width: 200px;width:100%;"
                      ></el-input> -->
                        <span>{{ entity.pgNo }}</span>
                      </el-form-item>
                    </el-col>


                  </el-row>
                  <el-row :gutter="0">
                    <el-col :span="8">
                      <el-form-item label="工序:">
                        <span>{{entity.zcName}}/{{ entity.zcNo }}</span>
                      </el-form-item>
                    </el-col>

                    <el-col :span="8">
                      <el-form-item label="处理结案:">
                        <span>{{ entity.closeId }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="损失费用合计:">
                        <el-input v-model="entity.tle" size="mini" style="max-width: 200px;width:100%;"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="0">
                    <el-col :span="24">
                      <el-form-item label="周转箱号:">
                        <el-input disabled v-model="entity.boxNo" size="mini" style="width:100%;"></el-input>
                        <!-- <span v-else>{{ entity.boxNo }}</span> -->
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="0">
                    <el-col :span="24">
                      <el-form-item label="备注:">
                        <el-input :disabled="limits1" v-model="entity.rem" size="mini" style="width:100%;"></el-input>
                        <!-- <span v-else>{{ entity.rem }}</span> -->
                      </el-form-item>
                    </el-col>


                  </el-row>
                  <el-row :gutter="0">
                    <!-- <el-col
                    :span="8"
                  >
                    
                  </el-col> -->

                    <!-- <el-col :span="8">
                    <el-form-item label="部门:" prop="dep">
                      <el-select
                      v-if="isEdit"
                        v-model="entity.dep"
                        :disabled="isShowDisables"
                        placeholder="请选择部门"
                        size="mini"
                        style="max-width:200px; width:100%;"
                      >
                        <el-option
                          v-for="item in options"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                      <span v-else>{{ entity.dep }}</span>
                    </el-form-item>
                  </el-col> -->
                  </el-row>
                </div>

              </el-form>
            </div>
          </el-col>
          <el-col :span="8" :xs="24">
            <!-- 附件 -->
            <!-- <a-col :span="8">
                    <el-form-item label="附件:">
                      <div style="position: relative;">
                        <a-input
                          :readOnly="true"
                          :placeholder="$t('附件')"
                        />
                        <div style="position:absolute;top:0px;right:65px;">
                          <a-button
                            :loading="loading"
                            type="primary"
                            size='small'
                            @click="seepic()"
                          >查看
                          </a-button>
                        </div>
                        <div style="position:absolute;top:0px;right:2px;">
                          <a-button
                            :loading="loading"
                            type="primary"
                            size='small'
                            @click="uploadpic()"
                          >上传
                          </a-button>
                        </div>
                      </div>
                    </el-form-item>
                  </a-col> -->
            <!-- 附件 -->
            <!-- 开始 -->
            <div class="sup_info_basics_header">
              <div style="float: left;margin-left: 8px;">附件</div>
            </div>
            <div class="sup_info_basics_container" :style="{ height: rightHeight }">
              <div style="text-align: right;margin-top:10px;">
                <el-button type="success" @click="handleAddVersion($event)">上传附件</el-button>
              </div>

              <el-table v-loading="loading" stripe :data="tableData" highlight-current-row
                :header-cell-style="{ backgroundColor: '#F4F5F9', fontWeight: '400' }"
                style="width: 100%;margin-top:10px;" :height="tablerightHeight">
                <!-- <el-table-column prop="fjNo" label="代号" />
                              <el-table-column prop="fjName" label="附件名称" /> -->

                <el-table-column label="附件">
                  <template v-slot="{row}">
                    <span class="file-name" @click="handleDown(item)">{{ row.original }}</span>
                    <!-- <i class="el-icon-close el-icon--right" @click="handleDel(item)" /> -->
                    <!-- .contraFileUrlList -->
                    <!-- <span v-for="(item, index) in row" :key="index" style="margin-right: 15px;">
                                    <span class="file-name" @click="handleDown(item)">{{ item.fileName }}</span><i class="el-icon-close el-icon--right" @click="handleDel(item)" />
                                  </span> -->
                    <!-- <div>
                                    <el-button style="border: 1px solid #0877C6;color: #0877C6;padding: 2px 2px;" icon="el-icon-upload" plain size="mini" @click="handleAdd(row)">{{ $t('companyInfo.addenclosure') }}</el-button>
                                  </div> -->
                  </template>
                </el-table-column>
                <el-table-column prop="materialName" label="操作">
                  <template slot-scope="scope">
                    <el-button :loading="loading" type="primary" size='mini' @click="seepic(scope.$index)">查看
                    </el-button>
                    <el-button size='mini' type="primary" @click="handleDelVersion(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!-- <div style="margin-top: 20px;">
                              <el-pagination
                                background
                                :current-page="tablePage.currentPage"
                                :page-sizes="[5, 10, 50, 100]"
                                :page-size="100"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="tablePage.total"
                                @size-change="handleSizeChange"
                                @current-change="handleCurrentChange"
                              />
                            </div> -->
            </div>
            <!-- 结束 -->


          </el-col>
        </el-row>
        <el-row :gutter="10" style="margin-top:15px;">
          <el-col :span="12" :xs="24">
            <!-- <el-row>
                <el-col :span="16"> -->
            <div class="sup_info_basics_container" :style="{ height: chuzhiHeight }" style="padding-bottom:5px;">
              <div class="sup_info_basics_header">
                <div style="float: left;margin-left: 8px;">不合格原因</div>
              </div>
              <el-table ref="multipleTable" border :data="testData" class="tableheight bordervisibtwo"
                style="width: 100%;margin-top:10px;min-height:160px;" @row-click="handRowClick" :height="chuzhitable"
                @selection-change="handleSelectionChangereason">
                <el-table-column align="center" type="index" width="38"></el-table-column>
                <el-table-column label="原因代号" width="100" height="30">
                  <template slot-scope="scope">
                    {{scope.row.spcNo}}
                  </template>
                </el-table-column>
                <el-table-column label="不合格原因">
                  <template slot-scope="scope">
                    {{scope.row.spcName}}
                  </template>
                </el-table-column>
                <el-table-column label="不合格量" width="100">
                  <template slot-scope="scope">
                    {{scope.row.qtyLost}}
                  </template>
                </el-table-column>
                <el-table-column label="不合格量(副)" width="100" prop="qty1Lost">
                  <template slot-scope="scope">
                    {{scope.row.qty1Lost}}
                  </template>
                </el-table-column>
                <el-table-column prop="ygNo" width="180" align="left" label="作业人员">
                  <template slot-scope="scope">
                    {{scope.row.ygNo}}
                  </template>
                </el-table-column>
                <el-table-column prop="dep" width="180" align="left" label="制造部门">
                  <template slot-scope="scope">
                    {{scope.row.dep}}
                  </template>
                </el-table-column>
                <el-table-column label="质量单价" width="100">
                  <template slot-scope="scope">
                    {{scope.row.qup}}
                  </template>
                </el-table-column>
                <el-table-column label="检验项目">
                  <template slot-scope="scope">
                    {{scope.row.qcItm}}
                    <!-- <el-input
                            type="number"
                              class="cell-input"
                              size="mini"
                              @input="changeqtyLost(scope)"
                              v-model="scope.row.qtyLost"
                              :disabled="!scope.row.spcNo"
                            ></el-input> -->
                  </template>
                </el-table-column>
              </el-table>

              <!--focus激活选择货品  -->
              <div @mouseleave="prdt3leave">
                <el-popover placement="bottom" :style="objStyle" width="400" style="position: fixed;z-index: 99999;"
                  v-model="visible">
                  <div>
                    <el-table class="el-table" :data="pickerList" stripe max-height="360px" size="mini" height="360px"
                      highlight-current-row :cell-style="{ verticalAlign: 'top' }"
                      :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                      style="width: 100%;">
                      <el-table-column fixed="left" align="center" label="选择" width="80">
                        <template slot-scope="scope">
                          <el-button round type="primary" style="padding: 5px 5px 5px 5px;" size="mini"
                            icon="el-icon-d-arrow-right" @click="getGoodsPopover(scope.row)">选择</el-button>
                        </template>
                      </el-table-column>
                      <el-table-column fixed="left" prop="spcNo" align="left" label="原因代号" width="70"></el-table-column>
                      <!-- :show-overflow-tooltip="true" -->
                      <el-table-column prop="name" align="left" label="不合格原因" width="130"></el-table-column>
                    </el-table>
                  </div>
                  <!-- <div>
                  <el-pagination
                    background
                    :pager-count="5"
                    :page-sizes="[5,10, 30, 50]"
                    :page-size="10"
                    @size-change="goods_pageSizeChange"
                    :current-page="goods_currentPage"
                    @current-change="goods_currentChange"
                    layout="prev, pager, next"
                    :total="goods_pickerCount"
                    style="margin-top: 5px;"
                  ></el-pagination>
                </div> -->
                </el-popover>
              </div>
            </div>
            <!-- </el-col>
            </el-row> -->
          </el-col>
          <el-col :span="12" :xs="24">
            <div ref="chuzhiref" style="border:1px solid #e5e5e5;margin:0 auto;padding: 10px;padding-top:5px;padding-bottom:5px;
            background-color:#fff;border-radius:10px;">
              <div class="sup_info_basics_header">
                <div style="float: left;margin-left: 8px;">处置结果:</div>
              </div>
              <div style="padding-top:5px;">
                <el-button size="mini" icon="el-icon-plus" id="add_table" :disabled="limits4" type="primary"
                  @click="addTableRowtwo()"></el-button>
                <el-button :disabled="limits4" size="mini" icon="el-icon-minus" id="add_table" type="primary"
                  :style="'background-color: #f56c6c;border-color: #f56c6c;color:#fff;'"
                  @click="locaDelTableRowtwo()"></el-button>
              </div>
              <el-table ref="multipleTabletwo" stripe border :data="mesLsQc1s" style="margin-top:10px;width:100%"
                @selection-change="handleSelectionresult">
                <el-table-column type="selection" width="48"></el-table-column>
                <el-table-column prop="czNo" align="left" label="处理方式">
                  <template slot-scope="scope">
                    <el-select v-if="isEdit" v-model="scope.row.czNo" :disabled="limits4" placeholder="请选择检验类型"
                      size="mini" clearable @input="clfsinput(scope.$index,scope.row, $event)" style="width:100%;">
                      <el-option v-for="item in disposalresults" :key="item.value" :label="item.label"
                        :value="item.value"></el-option>
                    </el-select>
                  </template>

                </el-table-column>
                <el-table-column label="处理数量" width="110">
                  <template slot-scope="scope">
                    <el-input :disabled="limits4" type="number" class="cell-input" size="mini"
                      @input="changeprocess(scope)" @wheel.native.prevent="stopScroll($event)"
                      v-model="scope.row.qty"></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="bilNo" width="120" align="left" label="生成单据"></el-table-column>
                <el-table-column prop="rem" align="left" label="备注">
                  <template slot-scope="scope">
                    <el-input class="cell-input" type="textarea" style="width:100%;"
                      :autosize="{ minRows: 1, maxRows: 2}" size="mini" :disabled="limits4"
                      v-model="scope.row.rem"></el-input>
                  </template>
                </el-table-column>
              </el-table>
              <el-form label-width="120px" :model="entity" ref="addEntityForm">
                <el-row :gutter="0" style="margin-top:15px;">
                  <el-col :span="12">
                    <el-form-item label="确认人:" class="lsNo">
                      <my-selectListwo url="mes/basicData/salData" :read-only="true" :disabled="limits4"
                        :tableColumn="$Column.salm" :form="$Form.salm" :data="dataczSal" name="salNo"
                        @choose="chooseczSal($event)" ref="selectList"
                        v-decorator="['salNo', { rules: [{ required: true, message:'请选择确认人' } ] }]"
                        :placeholder="'请选择确认人'"></my-selectListwo>
                      <!-- <el-input
                  v-model="entity.czSal"
                  :disabled="limits4"
                  size="mini"
                  style="max-width: 200px;width:100%;"
                  placeholder="确认人"
                ></el-input> -->
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="日期:" prop="czDate">
                      <!-- <span  v-if="$route.query.tyNo">{{ entity.lsDd | formatDate }}</span> -->
                      <a-date-picker style="width:100%" format="YYYY-MM-DD" :disabled="limits4" valueFormat="YYYY-MM-DD"
                        v-model="entity.czDate" placeholder="日期" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>

          </el-col>
        </el-row>
        <div class="line"></div>
        <div style="border:1px solid #e5e5e5;margin:0 auto;padding: 10px;padding-top:5px;padding-bottom:5px;
            background-color:#fff;border-radius:10px;">
          <el-row :gutter="10">
            <el-col :span="12" :xs="24">
              <!-- 不合格原因分析 -->
              <div class="sup_info_basics_header">
                <div style="float: left;margin-left: 8px;">不合格原因分析：</div>
              </div>
              <el-input style="width:100%" type="textarea" :autosize="{ minRows: 2, maxRows: 10}"
                v-model="entity.rmeSpc" :disabled="limits1" placeholder="请输入不合格原因分析"></el-input>
            </el-col>

            <!-- <div class="sup_info_basics_header">
          <div style="float: left;margin-left: 8px;">建议处置措施：</div>
        </div>
        <el-input
        style="width:100%"
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 10}"
          v-model="entity.remark"
          placeholder="请输入不合格原因分析"
        ></el-input> -->
            <el-col :span="12" :xs="24">
              <div class="sup_info_basics_header">
                <div style="float: left;margin-left: 8px;">建议处置措施：</div>
              </div>
              <el-input style="width:100%" type="textarea" :autosize="{ minRows: 2, maxRows: 10}" :disabled="limits1"
                v-model="entity.remEmg" placeholder="请输入建议处置措施"></el-input>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12" :xs="24">
              <div class="sup_info_basics_header">
                <div style="float: left;margin-left: 8px;">处置理由：</div>
              </div>
              <el-input style="width:100%" type="textarea" :autosize="{ minRows: 2, maxRows: 10}"
                v-model="entity.remRea" :disabled="limits1" placeholder="请输入处置理由"></el-input>
            </el-col>
            <el-col :span="12" :xs="24">
              <div class="sup_info_basics_header">
                <div style="float: left;margin-left: 8px;">改进措施：</div>
              </div>
              <el-input style="width:100%" type="textarea" :autosize="{ minRows: 2, maxRows: 10}"
                v-model="entity.remCor" :disabled="limits1" placeholder="请输入改进措施"></el-input>
            </el-col>
          </el-row>
          <el-form label-width="120px" :model="entity" ref="addEntityForm">
            <el-row :gutter="0" style="margin-top:15px;">
              <el-col :span="6">
                <el-form-item label="改进措施完成日:" prop="finDdCor">
                  <!-- <span  v-if="$route.query.tyNo">{{ entity.lsDd | formatDate }}</span> -->
                  <a-date-picker style="width:100%" format="YYYY-MM-DD" :disabled="limits1" valueFormat="YYYY-MM-DD"
                    v-model="entity.finDdCor" placeholder="改进措施完成日" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="责任人:" class="lsNo">
                  <my-selectListwo url="mes/basicData/salData" :read-only="true" :disabled="limits1"
                    :tableColumn="$Column.salm" :form="$Form.salm" :data="datacuoshi" name="salNo"
                    @choose="choosecuoshi($event)" ref="selectList"
                    v-decorator="['salNo', { rules: [{ required: true, message:'请选择责任人' } ] }]"
                    :placeholder="'请选择责任人'"></my-selectListwo>
                  <!-- <el-input
                        v-model="entity.salNoZr1"
                       
                        size="mini"
                     
                        style="max-width: 200px;width:100%;"
                        placeholder="责任人"
                      ></el-input> -->
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="责任部门确认人:" class="lsNo">
                  <my-selectListwo url="mes/basicData/salData" :read-only="true" :disabled="limits1"
                    :tableColumn="$Column.salm" :form="$Form.salm" :data="datacuoshi2" name="salNo"
                    @choose="choosesureperson($event)" ref="selectList"
                    v-decorator="['salNo', { rules: [{ required: true, message:'请选择责任部门确认人' } ] }]"
                    :placeholder="'请选择责任部门确认人'"></my-selectListwo>

                  <!-- <el-input
                        v-model="entity.salNoZr2"
                       :disabled="limits1"
                        size="mini"
                 
                        style="max-width: 200px;width:100%;"
                        placeholder="责任部门确认人"
                      ></el-input> -->
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="日期:" class="tiDd">
                  <a-date-picker style="width:100%" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" :disabled="limits1"
                    v-model="entity.sysDateZr" placeholder="日期" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="line"></div>
        <div style="border:1px solid #e5e5e5;margin:0 auto;padding: 10px;padding-top:5px;padding-bottom:5px;
            background-color:#fff;border-radius:10px;">
          <el-row :gutter="10">
            <el-col :span="12" :xs="24">
              <div class="sup_info_basics_header">
                <div style="float: left;margin-left: 8px;">品质处置方案:</div>
              </div>
              <el-input style="width:100%" type="textarea" :autosize="{ minRows: 2, maxRows: 10}" :disabled="limits2"
                v-model="entity.remQc" placeholder="请输入品质处置方案"></el-input>
              <el-form label-width="120px" :model="entity" ref="addEntityForm">
                <el-row :gutter="0" style="margin-top:15px;">
                  <el-col :span="12">
                    <el-form-item label="品质部门确认人:" class="lsNo">
                      <my-selectListwo url="mes/basicData/salData" :read-only="true" :disabled="limits2"
                        :tableColumn="$Column.salm" :form="$Form.salm" :data="dataQc" name="salNo"
                        @choose="chooseQc($event)" ref="selectList"
                        v-decorator="['salNo', { rules: [{ required: true, message:'请选择品质部门确认人' } ] }]"
                        :placeholder="'请选择品质部门确认人'"></my-selectListwo>
                      <!-- <el-input
                        v-model="entity.salNoQc"
                         :disabled="limits2"
                        size="mini"
                        style="max-width: 200px;width:100%;"
                        placeholder="品质部门确认人"
                      ></el-input> -->
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="日期:" prop="sysDateQc">
                      <!-- <span  v-if="$route.query.tyNo">{{ entity.lsDd | formatDate }}</span> -->
                      <a-date-picker style="width:100%" :disabled="limits2" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                        v-model="entity.sysDateQc" placeholder="日期" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-col>
            <el-col :span="12" :xs="24">
              <div class="sup_info_basics_header">
                <div style="float: left;margin-left: 8px;">评审意见:</div>
              </div>
              <el-input style="width:100%" type="textarea" :disabled="limits3" :autosize="{ minRows: 2, maxRows: 10}"
                v-model="entity.remLs" placeholder="请输入评审意见"></el-input>

              <el-form label-width="120px" :model="entity" ref="addEntityForm">
                <el-row :gutter="0" style="margin-top:15px;">
                  <el-col :span="12">
                    <el-form-item label="批准人:" class="lsNo">
                      <my-selectListwo url="mes/basicData/salData" :read-only="true" :disabled="limits3"
                        :tableColumn="$Column.salm" :form="$Form.salm" :data="dataAppr" name="salNo"
                        @choose="chooseAppr($event)" ref="selectList"
                        v-decorator="['salNo', { rules: [{ required: true, message:'请选择批准人' } ] }]"
                        :placeholder="'请选择批准人'"></my-selectListwo>

                      <!-- <el-input
                          v-model="entity.salNoAppr"
                          :disabled="limits3"
                          size="mini"
                          style="max-width: 200px;width:100%;"
                          placeholder="批准人"
                        ></el-input> -->
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="日期:" prop="sysDateAppr">
                      <!-- <span  v-if="$route.query.tyNo">{{ entity.lsDd | formatDate }}</span> -->
                      <a-date-picker style="width:100%" :disabled="limits3" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                        v-model="entity.sysDateAppr" placeholder="日期" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-col>
          </el-row>

        </div>
        <!-- <div class="line"></div>
<div style="border:1px solid #e5e5e5;margin:0 auto;padding: 10px;padding-top:5px;padding-bottom:5px;
            background-color:#fff;border-radius:10px;">
        
</div> -->

        <!-- 改进措施实施结果 -->
        <div class="line"></div>
        <div style="border:1px solid #e5e5e5;margin:0 auto;padding: 10px;padding-top:5px;padding-bottom:5px;
            background-color:#fff;border-radius:10px;">
          <el-row :gutter="10">
            <el-col :span="12" :xs="24">
              <div class="sup_info_basics_header">
                <div style="float: left;margin-left: 8px;">改进措施实施结果:</div>
              </div>
              <el-input style="width:100%" type="textarea" :disabled="limits5" :autosize="{ minRows: 2, maxRows: 10}"
                v-model="entity.remEft" placeholder="请输入改进措施实施结果"></el-input>

              <el-form label-width="120px" :model="entity" ref="addEntityForm">
                <el-row :gutter="0" style="margin-top:15px;">
                  <el-col :span="12">
                    <el-form-item label="确认人:" class="lsNo">
                      <my-selectListwo url="mes/basicData/salData" :read-only="true" :disabled="limits5"
                        :tableColumn="$Column.salm" :form="$Form.salm" :data="dataCon" name="salNo"
                        @choose="chooseCon($event)" ref="selectList"
                        v-decorator="['salNo', { rules: [{ required: true, message:'请选择确认人' } ] }]"
                        :placeholder="'请选择确认人'"></my-selectListwo>
                      <!-- <el-input
                        v-model="entity.salNoCon"
                        size="mini"
                        :disabled="limits5"
                        style="max-width: 200px;width:100%;"
                        placeholder="确认人"
                      ></el-input> -->
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="日期:" prop="sysDateCon">
                      <!-- <span  v-if="$route.query.tyNo">{{ entity.lsDd | formatDate }}</span> -->
                      <a-date-picker style="width:100%" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                        v-model="entity.sysDateCon" placeholder="日期" :disabled="limits5" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-col>
            <el-col :span="12" :xs="24">
              <div class="sup_info_basics_header">
                <div style="float: left;margin-left: 8px;">验证结果:</div>
              </div>
              <el-input style="width:100%" type="textarea" :autosize="{ minRows: 2, maxRows: 10}"
                v-model="entity.remEft1" placeholder="请输入验证结果" :disabled="limits6"></el-input>

              <el-form label-width="120px" :model="entity" ref="addEntityForm">
                <el-row :gutter="0" style="margin-top:15px;">
                  <el-col :span="12">
                    <el-form-item label="确认人:" class="lsNo">
                      <my-selectListwo url="mes/basicData/salData" :read-only="true" :disabled="limits6"
                        :tableColumn="$Column.salm" :form="$Form.salm" :data="dataCon1" name="salNo"
                        @choose="chooseCon1($event)" ref="selectList"
                        v-decorator="['salNo', { rules: [{ required: true, message:'请选择确认人' } ] }]"
                        :placeholder="'请选择确认人'"></my-selectListwo>
                      <!-- <el-input
                          v-model="entity.salNoCon1"
                          size="mini"
                          style="max-width: 200px;width:100%;"
                          placeholder="确认人"
                          :disabled="limits6"
                        ></el-input> -->
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="日期:" prop="sysDateCon1">
                      <!-- <span  v-if="$route.query.tyNo">{{ entity.lsDd | formatDate }}</span> -->
                      <a-date-picker style="width:100%" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                        v-model="entity.sysDateCon1" placeholder="日期" :disabled="limits6" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-col>
          </el-row>
        </div>
        <!-- <div class="line"></div>
<div style="border:1px solid #e5e5e5;margin:0 auto;padding: 10px;padding-top:5px;padding-bottom:5px;
            background-color:#fff;border-radius:10px;">
        
</div> -->












        <a-card :bordered="false">
          <!-- <a-row :gutter="20">
      <a-form-item label="检验日期">
      <a-col :span="6" :md="6" :sm="24">
        
          <a-range-picker @change="onChange" format="YYYY-MM-DD"/>
      </a-col>
      </a-form-item>
    </a-row> -->
          <a-row :gutter="8">
            <a-col :span="24">
              <a-row>
                <!-- <el-dialog
      title="不合格原因"
      :visible.sync="visible222"
      width="50%"
      style="height:100%"
    >
    <div class="table-page-search-wrapper" style="padding-bottom:40px;">
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        border
        stripe
        @row-dblclick="mfBxHanddle"
        style
        :header-cell-style="{fontSize:'14px',color: '#606266',backgroundColor: '#F4F5F9', fontWeight: '700' }"
        @selection-change="handleChangereson"
      >
        <el-table-column type="selection" align="center" width="35"></el-table-column>
        <el-table-column prop="zcNo" align="left" label="原因代号"></el-table-column>
        <el-table-column prop="qaNo" align="left" label="描述"></el-table-column>
      </el-table>
     <div slot="footer" style="float: right;margin-top:20px;">
            <el-button size="mini" @click="cancelGetData">取 消</el-button>
            <el-button size="mini" type="primary" @click="otngetData">确 定</el-button>
      </div>
    </div>
    </el-dialog> -->
<!--                <uploadFile ref="uploadFile" @seeFile="seeFile" />-->
              </a-row>
            </a-col>
            <!-- <inspection
        :row="row"
        ref="a"
        @getList="getList"
      /> -->
          </a-row>
        </a-card>
<!--        <ModalPic ref="ModalPic" />-->
        <!-- 不合格原因 -->
        <!-- <el-button
              icon="fa fa-plus"
              @click="insertEvent"
            >新增</el-button>
            <el-button
              @click="removeEvent"
            >删除选中</el-button>
            <el-table
                :data="tableDatatwo"
                v-loading="tableLoading"
                border
                stripe
                @row-dblclick="mfBxHanddle"
                  @selection-change="handleSelectionChangetwo"
                style
                 :header-cell-style="{fontSize:'14px',color: '#606266',backgroundColor: '#F4F5F9', fontWeight: '700' }"
              >
                <el-table-column type="selection" align="center" width="35"></el-table-column>
                <el-table-column prop="qty" width="110" align="left" label="原因代号"></el-table-column>
                <el-table-column prop="up" width="120" align="left" label="不合格原因"></el-table-column>
                <el-table-column prop="up" width="120" align="left" label="不合格量">
                  <template v-slot="scope">
                      <el-input v-model="scope.row.up"></el-input>
                  </template>
                </el-table-column>
            </el-table>

<vxe-toolbar>
          <template #buttons>
            <vxe-button
             
              icon="fa fa-plus"
              @click="insertEvent"
            >新增</vxe-button>
            <vxe-button
              
              @click="removeEvent"
            >删除选中</vxe-button>
          </template>
        </vxe-toolbar>
        <vxe-table
          border
          resizable
          stripe
          id="imp"
          size="mini"
          max-height="450px"
          highlight-current-row
          show-overflow
          highlight-hover-row
          ref="xTable2"
          :checkbox-config="{ strict: true }"
          :loading="loading"
          :data="tableDatatwo"
          @checkbox-change="selectChangeEvent"
          @checkbox-all="selectChangeAll"
          :keyboard-config="{ isArrow: true }"
          :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true}"
        >
          <vxe-table-column
            type="checkbox"
            width="60"
          ></vxe-table-column>
          <vxe-table-column
            field="qty"
            title="原因代号"
            align="center"
            :edit-render="{ name: 'input', attrs: {disabled: nameDisabled }}"
            :width="150"
          ></vxe-table-column>
          <vxe-table-column
            field="unit"
            title="不合格原因"
            align="center"
            :edit-render="{ name: 'input', attrs: {disabled: nameDisabled }}"
            :width="150"
          ></vxe-table-column>
          <vxe-table-column
            field="up"
            title="不合格量"
            align="center"
            :edit-render="{ name: 'input', attrs: {disabled: nameDisabled }}"
            :width="150"
          ></vxe-table-column>
          <vxe-table-column
            field="bDd"
            title="有效开始日期"
            align="center"
            :edit-render="{name: '$input', props: {type: 'date'}}"
            :width="150"
          ></vxe-table-column>
          <vxe-table-column
            field="eDd"
            title="有效结束日期"
            align="center"
            :edit-render="{name: '$input', props: {type: 'date'}}"
            :width="150"
          ></vxe-table-column>
          <vxe-table-column
            field="qcNo"
            title="inquiryoffer.qcno"
            align="center"
            :edit-render="{ name: 'input', attrs: {disabled: nameDisabled }}"
            :width="150"
          ></vxe-table-column>
          <vxe-table-column
            field="rem"
            title="inquirycompri.remarks"
            align="center"
            :edit-render="{ name: 'input', attrs: {disabled: nameDisabled }}"
            :width="150"
          ></vxe-table-column>
        </vxe-table> -->
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
  import {
    prdZFQty, fetchList, getItem, start, fetchListInspect, tiUpdsave, datadepPage, datasalData, lsqcdel, lsqcypslnfo, lsqcadd, lsqcdpsinfo, lsqcupd, spcLstpage, sysfiledel, qctiDel
  } from '@/api/mes/quality'

  import { mapGetters, mapState } from 'vuex'
  // import uploadFile from './uploadFile'
  // import ModalPic from './ModalPic'
  // import ModalPic from '../inspectionForm/ModalPic'
  import moment from 'moment'
  import MySelectListwo from '@/components/MySelectListwo'
  // import formList from './formList'
  // import inspection from './inspection'

  export default {
    components: {

      MySelectListwo
    },
    data() {
      return {
        exitRulesone: {
          dep: [{ required: true, message: "必填:部门", trigger: ["change", "blur"] }],
        },
        isEdit: true,
        mes_quality_query: 'mes_quality_query',
        mes_quality_reset: 'mes_quality_reset',
        mes_quality_newOpen: 'mes_quality_newOpen',
        mes_quality_inspection: 'mes_quality_inspection',
        mes_quality_pause: 'mes_quality_inspection',
        mes_quality_continues: 'mes_quality_continues',
        mes_quality_task: 'mes_quality_task',
        queryParam: {},
        tableData: [],

        loading: false,
        tablePage: {
          currentPage: 1,
          pageSize: 5,
          total: 0
        },
        row: {},
        rowIndex: {},
        // 按钮控制
        btn: {
          newOpen: true, // 首检开工
          open: true, // 开始
          pause: true, // 暂停
          continues: true, // 继续
          report: true, // 报工
          exceptional: true, // 异常
          inspection: true, // 送检
          introspecting: true // 自检
        },
        queryParam: {
          date: [],
        },
        activeName: '1',
        visible: false,
        reasondata: [],
        multiplereason: [],
        entity: {
          salNoCon1Name: '',
          salNoApprName: '',
          salNoQcName: '',
          salNoZr2Name: '',
          salNoZr1Name: '',
          czSal: '',
          czDate: '',
          czSalName: '',
          salNoConName: '',
          salNoCon1: '',
          qtySps: '',
          remEft1: '',
          sysDateCon1: '',
          sysDateCon: '',
          salNoCon: '',
          remEft: '',
          sysDateAppr: '',
          salNoAppr: '',
          remLs: '',
          sysDateQc: '',
          salNoQc: '',
          remQc: '',
          sysDateZr: '',
          salNoZr2: '',
          salNoZr1: '',
          remCor: '',
          remRea: '',
          remEmg: '',
          rmeSpc: '',
          salNoZrQc: '',
          salNoZr: '',
          dep: '',
          dor: '',
          zcName: '',
          zcNo: '',
          batNo: '',
          qtyLostRto: '',
          closeId: '',
          salNo: '',
          lsDd: '',
          lsNo: '',
          tyDd: '',
          chkKnd: '',
          qcId: '',
          sys: '',
          chktyId: '',
          tiNo: '',
          tyNo: '',
          prdt3Name: "",
          way: 2,
          auxId: "",
          otRemark: "",
          bxId: "",
          qaId: "",
          otId: "",
          bxNo: "",
          bxDd: "",
          status: "",
          statusMsg: "",
          kndId: 3,
          applyDepId: null,
          applyDepNo: "",
          applyDepName: "",
          applyUserId: "",
          applyUserNo: "",
          applyUserName: "",
          prdId: "",
          prdNo: "",
          prdName: "",
          prdt3Id: "",
          prdSpc: "",
          faultRem: "",
          cntId: "",
          cntName: "",
          cntTel: "",
          cntAdr: "",
          dcId: "",
          dcName: "",
          dcLmt: "",
          urgent: 0,
          finId: "",
          finName: "",
          finLmt: "",
          bxPic1: "",
          bxPic2: "",
          bxPic3: "",
          serverDeptId: "",
          otUserId: "",
          qty: null,
          serviceCode: "",
          coDd: null,
          prdUt: "",
          bxType: '1',
          initialTime: '',
          completeTime: ''
        },
        inspectiontype: [
          { value: '1', label: '完工检验' },
          { value: '2', label: '首检检验' },
          { value: '3', label: '托工检验' },
        ],
        multipleSelectiontwo: [],
        sysFiles: [],
        colData: [
          { title: "原因代号", istrue: true },
          { title: "不合格原因", istrue: true },
          { title: "不合格量", istrue: true },
          { title: "货品名称", istrue: true },
          { title: "货品代号", istrue: true },
          { title: "规格", istrue: true },
          { title: "现有库存", istrue: true },
          { title: "借出量", istrue: true },
          { title: "单位", istrue: true },
          { title: "单价", istrue: true },
          { title: "数量", istrue: true },
          { title: "已还数量", istrue: true },
          { title: "税率%", istrue: true },
          { title: "未税金额", istrue: true },
          { title: "税额", istrue: true },
          { title: "金额", istrue: true },
        ],
        objStyle: {
          top: "433px",
          left: "",
        },
        pickerList: [],
        testData: [],
        detailIds: [],
        multipleSelection: [],
        multipleSelectionresult: [],
        pickerIndex: 0,
        isShowPopVel: false,
        rightHeight: 0,
        chuzhiHeight: 0,
        tablerightHeight: 0,
        tyNotwo: '',
        tyDdtwo: '',
        flag: false,
        ccc: true,
        isClaim: false,
        data: '',
        tableLoading: false,
        bottomWidth: '',
        disposalresults: [
          { value: '5', label: '放行' },
          { value: '2', label: '报废' },
          { value: '3', label: '返工' },
          { value: '7', label: '复检（全检）' },
          { value: '8', label: '复检（抽检）' },
          { value: '9', label: '重新首检' },
          { value: '1', label: '强制缴库' },
        ],
        mesLsQc1s: [],
        salNoall: [],
        datacuoshi: '',
        datacuoshi2: '',
        limits6: false,
        limits5: false,
        limits4: false,
        limits3: false,
        limits2: false,
        limits1: false,
        dataQc: '',
        dataAppr: '',
        dataczSal: '',
        dataCon: '',
        dataCon1: '',
        depall: '',
        datacheck: '',
        chuzhitable: 0

      }
    },
    computed: {
      ...mapState({
        userInfo: state => state.user.userInfo
      }),
      ...mapGetters(['permissions'])
    },
    created() {
      this.sysFiles = []
      this.tableAdd()
      this.tableAddtwo()
      if (localStorage.getItem('nopingshentyNo')) {
        this.entity.tyNo = localStorage.getItem('nopingshentyNo')
        this.getListtreat()
      } else {
        this.entity.lsNo = localStorage.getItem('nopingshenlsNo')
        this.getList()
      }

    },
    mounted() {


      this.pickerSearch()
      this.$nextTick(() => {
        this.rightHeight = this.$refs.leftHeight.offsetHeight + 6 + 'px'
        this.bottomWidth = this.$refs.leftHeight.offsetWidth + 6 + 'px'

        this.tablerightHeight = this.$refs.leftHeight.offsetHeight - 65 + 'px'
        this.chuzhiHeight = this.$refs.chuzhiref.offsetHeight + 1 + 'px'
        this.chuzhitable = this.$refs.chuzhiref.offsetHeight - 65 + 'px'
      })
    },
    methods: {
      changeqty1Lost(val) {
        if (val.row.qty1Lost < 0) {
          this.$message.warning('不合格量(副)不能为负')
          val.row.qty1Lost = 0
        }
        this.entity.qty1Lost = 0
        this.testData.forEach(i => {
          if (i.spcNo) {
            this.entity.qty1Lost += +i.qty1Lost;
          }
        })
        this.entity.qty1Lost = parseFloat(this.entity.qty1Lost.toFixed(2))
        this.$forceUpdate()
      },
      choosescope(obj, scope) {
        console.log(obj, scope, '9999999999')
        if (obj.obj.clear) {
          if (obj.obj.clear == 1) {

            // this.queryParam.dep=''
            // this.queryParam.depName=''
            return
          }
        }
        var map = {}
        if (obj.obj.name === 'salNo') {

          this.flag = true
          this.queryParam.name = obj.obj.data.name
          if (obj.obj.data.salNo) {
            // this.queryParam.usr = obj.obj.data.salNo
            // this.data = this.queryParam.ygNo+ '-' + this.queryParam.name
            this.testData.forEach((item, index) => {
              if (scope.$index == index) {
                item.ygNo = obj.obj.data.salNo
                // item.name = obj.obj.data.name
              }
              this.$set(this.testData, index, item);

            })
            console.log(this.testData, 'gggggggggggg')
          } else {
            // this.data = ''
            // this.queryParam.salNo=''
            // this.queryParam.name=''
          }
        }
      },
      choosedan(obj, scope) {
        if (obj.obj.clear) {
          if (obj.obj.clear == 1) {

            // this.queryParam.dep=''
            // this.queryParam.depName=''
            return
          }
        }
        var map = {}
        if (obj.obj.name === 'dep') {
          this.flag = true
          // this.queryParam.depName = obj.obj.data.name
          if (obj.obj.data.deptCode) {
            // this.queryParam.dep = obj.obj.data.deptCode
            this.datatwo = obj.obj.data.deptCode + '-' + obj.obj.data.name
            // multipleSelectiontwo
            this.testData.forEach((item, index) => {
              if (scope.$index == index) {
                item.dep = obj.obj.data.deptCode
                item.depName = obj.obj.data.name
              }
              this.$set(this.testData, index, item);
            })
          } else {
            // this.datatwo = ''
            // this.queryParam.dep=''
            // this.queryParam.depName=''
          }
        }
      },
      clfsinput(index, row, e) {
        let clfsdata = JSON.parse(JSON.stringify(this.mesLsQc1s))
        const result = clfsdata.filter((item, index2) => {
          if (index2 !== index) {
            if (item.czNo == row.czNo) {
              return true
            }
          }
        })
        console.log(result, 'w1sgggggggggg')
        if (result.length) {
          row.czNo = ''
          this.$message.warning('不允许重复选择')
        }
      },
      // this.$router.push({name:'entrustworkercheck', params:{
      //       tiNo: this.entity.tiNo
      //     }})
      jumpentrust() {
        if (this.entity.chkTypeId == '1') {
          // this.$router.push({path: '/mes/completinspect/check',
          //   params:{
          //     tiNo: this.entity.tiNo
          //   }
          // })
          localStorage.setItem("completinspecttiNo", this.entity.tiNo);
          this.$router.push({
            name: 'completinspectcheck', params: {
              tiNo: this.entity.tiNo
            }
          })
        } else if (this.entity.chkTypeId == '2') {
          // this.$router.push({path: '/mes/inspectionForm/check',
          //   params:{
          //     tiNo: this.entity.tiNo
          //   }
          // })
          localStorage.setItem("soujiansong", this.entity.tiNo);
          this.$router.push({
            name: 'inspectionFormcheck', params: {
              tiNo: this.entity.tiNo
            }
          })
        } else if (this.entity.chkTypeId == '3') {
          // this.$router.push({path: '/mes/entrustworker/check',
          //   params:{
          //     tiNo: this.entity.tiNo
          //   }
          // })
          localStorage.setItem("tuogongtyNo", this.entity.tiNo);
          this.$router.push({
            name: 'entrustworkercheck', params: {
              tiNo: this.entity.tiNo
            }
          })
        }
        //lsqcypslnfo
        //  <span v-if="entity.chkTypeId == 1">完工检验</span>
        //                     <span v-if="entity.chkTypeId == 2">首检检验</span>
        //                     <span v-if="entity.chkTypeId == 3">托工检验</span>

      },
      jumpcheck() {
        if (this.entity.chkTypeId == '1') {
          // this.$router.push({path: '/mes/completiontwo/detail',
          //   params:{
          //     tyNo: this.entity.tyNo
          //   }
          // })
          localStorage.setItem("completiontwotyNo", this.entity.tyNo);
          this.$router.push({
            name: 'completiontwodetail', params: {
              tyNo: this.entity.tyNo
            }
          })
        } else if (this.entity.chkTypeId == '2') {
          // this.$router.push({path: '/mes/qualitysure/detail',
          //   params:{
          //     tyNo: this.entity.tyNo
          //   }
          // })
          localStorage.setItem("shoujiantyNo", this.entity.tyNo);
          this.$router.push({
            name: 'qualitysuredetail', params: {
              tyNo: this.entity.tyNo
            }
          })
        } else if (this.entity.chkTypeId == '3') {
          // this.$router.push({path: '/mes/entrustwo/detail',
          //   params:{
          //     tyNo: this.entity.tyNo
          //   }
          // })
          localStorage.setItem("tuogongtwotyNo", this.entity.tyNo);
          this.$router.push({
            name: 'entrustwodetail', params: {
              tyNo: this.entity.tyNo
            }
          })
        }
      },


      changeprocess(scope) {
        if (scope.row.qty < 0) {
          this.$message.warning('数量不能为负')
          scope.row.qty = 0
        }
        let bhgsl = 0
        this.mesLsQc1s.forEach(i => {
          bhgsl += +i.qty;
        })
        if (bhgsl > this.entity.qtyLost) {
          this.$message.info('不合格量超出')
          scope.row.qty = ''
        }
        this.$forceUpdate()
      },
      stopScroll(evt) {
        evt = evt || window.event;
        if (evt.preventDefault) {
          // Firefox
          evt.preventDefault();
          evt.stopPropagation();
        } else {
          // IE
          evt.cancelBubble = true;
          evt.returnValue = false;
        }
        return false;
      },
      //  this.saldatamed(response.data.records[0].salNo)
      saldatamed() {
        datasalData(
          {
            current: 1,
            size: '10000',
            salText: '',
          }
        )
          .then(res => {
            this.salNoall = res.data.records

            // this.data=res.data.records[0].salNo + '-' + res.data.records[0].name
            // if(res.msg == 'success'){
            //   this.$message.success('删除成功')
            // }else{
            //   this.$message.success('删除失败')
            // }
            // this.loading = false
            // this.getList()
          })
          .catch(err => {
            this.loading = false
            this.requestFailed(err)
          })
      },
      choosecheck(obj) {
        if (obj.obj.name === 'salNo') {
          // this.entity.salName = obj.obj.data.name
          this.entity.salNo = obj.obj.data.salNo
        }
      },
      chooseCon1(obj) {
        if (obj.obj.name === 'salNo') {
          // this.entity.salName = obj.obj.data.name
          this.entity.salNoCon1 = obj.obj.data.salNo
        }
      },
      chooseCon(obj) {
        if (obj.obj.name === 'salNo') {
          // this.entity.salName = obj.obj.data.name
          this.entity.salNoCon = obj.obj.data.salNo
        }
      },
      chooseczSal(obj) {
        if (obj.obj.name === 'salNo') {
          // this.entity.salName = obj.obj.data.name
          this.entity.czSal = obj.obj.data.salNo
        }
      },
      chooseAppr(obj) {
        if (obj.obj.name === 'salNo') {
          // this.entity.salName = obj.obj.data.name
          this.entity.salNoAppr = obj.obj.data.salNo
        }
      },
      chooseQc(obj) {
        if (obj.obj.name === 'salNo') {
          this.entity.salName = obj.obj.data.name
          this.entity.salNoQc = obj.obj.data.salNo
        }
      },
      choosesureperson(obj) {
        if (obj.obj.name === 'salNo') {
          this.entity.salName = obj.obj.data.name
          this.entity.salNoZr2 = obj.obj.data.salNo
        }
      },
      choosecuoshi(obj) {


        var map = {}
        //   this.data[obj.obj.name] = obj.obj.value
        // this.entity[obj.obj.name] = obj.obj.id
        // if (obj.obj.name === 'upSalNo') {
        //   this.salName = obj.obj.data.name
        //   map[obj.obj.name] = obj.obj.data.salNo
        // }
        // if (obj.obj.name == 'salNo') {
        //     this.data = obj.obj.value
        // this.entity.salNo = obj.obj.data.salNo
        //   // this.salName = obj.obj.data.name
        //   // map[obj.obj.name] = obj.obj.data.salNo
        // }
        if (obj.obj.name === 'salNo') {
          this.entity.salName = obj.obj.data.name

          this.entity.salNoZr1 = obj.obj.data.salNo

        }

        // if (obj.obj.name === 'dep') {
        //   this.flag = true
        //   this.entity.depName = obj.obj.data.name

        //   this.entity.dep = obj.obj.data.deptCode
        // }
        // this.form.setFieldsValue(map)
      },
      cosetiao() {
        if (this.entity.lsNo) {
          this.$router.push({
            name: 'nonconform',
            params: {
              tab: 'second'
            }
          })
        } else {
          this.$router.push({
            name: 'nonconform',
            params: {
              tab: 'first'
            }
          })
        }
      },
      handleDelVersion(row) {
        var list = []
        //  { id, fileName,bucketName }
        if (row.distinguish) {
          this.tableData = this.tableData.filter(i => i.id !== row.id)
        } else {
          sysfiledel(
            {
              id: row.id,
              // fileName:row.fileName,
              // bucketName:row.bucketName,
              // idList:list
            }
          )
            .then(res => {
              if (res.msg == 'success') {
                this.tableData = this.tableData.filter(i => i.id !== row.id)
                this.$message.success('删除成功')
              } else {
                this.$message.success('删除失败')
              }
              this.loading = false
              // this.getList()
            })
            .catch(err => {
              this.loading = false
              this.requestFailed(err)
            })

        }
        //  this.tableData.forEach((item, index) => {
        //     if(item.id === id){
        //       item.contraFileUrlList.forEach((item2, index2) => {
        //       list.push(item2.id)
        //       })
        //     }
        //  })
      },
      handleAddVersion() {
        // this.$refs.uploadFile.create({ title: '上传' }, 'oooooooooo')
        // this.$refs.AddVersion.create(true, true)
      },
      deldata() {
        const that = this
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('identifier.delcont'),
          okText: this.$t('public.sure'),
          okType: 'warn',
          cancelText: this.$t('public.cancel'),
          onOk() {
            that.loading = true
            lsqcdel({
              lsNo: that.entity.lsNo,
              usr: that.entity.usr
            }).then((res) => {
              if (res.code === 0) {
                if (res.msg === 'success') {
                  that.cosetiao()
                  that.$message.success(res.data)
                } else {
                  that.$message.error(res.data)
                }
              }
            }).catch(err => that.requestFailed(err))
              .finally(() => {
                that.loading = false
              })
          },
          onCancel() { }
        })
      },
      // 渲染组件
      choose(obj) {

        // var map = {}
        // if (obj.obj.name === 'upSalNo') {
        //   this.salName = obj.obj.data.name
        //   map[obj.obj.name] = obj.obj.data.salNo
        // }
        // if (obj.obj.name === 'salNo') {
        //   this.salName = obj.obj.data.name
        //   map[obj.obj.name] = obj.obj.data.salNo
        // }
        if (obj.obj.name === 'dep') {
          this.flag = true
          this.entity.depName = obj.obj.data.name
          this.entity.dep = obj.obj.data.deptCode
          this.data = this.entity.dep + '-' + this.entity.depName
        }
        // this.form.setFieldsValue(map)
      },
      changeqtyLost(val) {
        this.entity.qtyLost = 0
        let getQtys = 0;
        if (!this.entity.qtyLost) {
          this.entity.qtyLost = 0
        }
        this.testData.forEach(i => {
          if (i.spcNo) {
            this.entity.qtyLost += +i.qtyLost;
          }
        })
        if (this.entity.qtyLost > 0) {
          this.entity.qcId = 'F'
          if (+this.entity.qty - +this.entity.qtyLost < 0) {

            this.$message.info('不合格量超出')
            val.row.qtyLost = ''
            this.entity.qtyLost = 0
            this.testData.forEach(i => {
              if (i.spcNo) {
                this.entity.qtyLost += +i.qtyLost;
              }
            })
          } else {
            this.entity.qtyOk = +this.entity.qty - +this.entity.qtyLost
          }
        } else {
          this.entity.qcId = 'T'
        }
        this.$forceUpdate()
        prdZFQty({
          prdNo: this.entity.prdNo,
          type: 'z',
          qty: val.row.qtyLost
        }).then((res) => {
          if (res) {
            val.row.qty1Lost = res.data.qty1
            this.$forceUpdate()
            this.changeqty1Lost(val)
          }
        }).catch(err => this.requestFailed(err))
          .finally(() => {
            this.loading = false
          })
      },
      // 货品关键字搜索回显数据
      getGoodsPopover({
        spcNo,
        name,
        prd3No,
        spc,
        qtyNow,
        qtyLrn,
        ut,
        price,
        qty,
        rtoTax,
        amtnNet,
        tax,
        amt,
        itm,
        id,
        qty1Lost,
        ygNo,
        dep,
        qup,
        qtyLost,
      }) {
        let newRow = {
          spcNo,
          spcName: name,
          prdNo: prd3No,
          spc,
          qtyNow,
          qtyLrn,
          ut,
          price,
          qty: "1",
          lastQty: 0,
          rtoTax: rtoTax,
          amtnNet: "",
          tax: "",
          amt: "",
          itm: -1,
          prdtId: id,
          id: null,
          qtyRtn: null,
          qty1Lost,
          ygNo,
          dep,
          qup,
          qtyLost,
        };
        newRow.show = false;
        this.$set(this.testData, this.pickerIndex, newRow);
        this.isShowPopVel = false;
        // let getQtys = 0;
        // this.testData.forEach((item) => {
        //   if (item.qty) {
        //     getQtys += +item.qty;
        //     this.qtys = getQtys;
        //   }
        // });
        this.visible = false;
      },
      prdt3leave() {
        setTimeout(() => {
          this.visible = false;
        }, 100);
      },
      pickerSearch(val) {
        // {
        //     ...this.entity,
        //     sysFiles:this.sysFiles
        //   }
        spcLstpage({ spcNo: val }).then((res) => {
          if (res.code === 0) {

            this.pickerList = res.data.records
          }
        }).catch(err => this.requestFailed(err))
        //    .finally(() => {
        //         this.loading = false
        // })
      },
      // input聚焦
      focus(index, row, e) {
        this.queryKeyword = ''
        this.pickerSearch(row.spcNo);
        this.visible = true;
        // this.isSaveColor = "danger";
        this.pickerIndex = index;
        let getTop = e.target.getBoundingClientRect().top + 35;

        if (e.target.getBoundingClientRect().top - 380 < 0) {
          this.objStyle.top = getTop.toString() + "px";
        } else {
          this.objStyle.top = (e.target.getBoundingClientRect().top - 390).toString() + "px";
        }
        this.objStyle.left = e.target.getBoundingClientRect().left + "px";



        // let getTop = e.target.getBoundingClientRect().top + 35;
        // this.objStyle.left = e.target.getBoundingClientRect().left + "px";
        // this.objStyle.top = getTop.toString() + "px";


      },
      blurNameInput() {
        this.visible = false;
        setTimeout(() => {
          this.isShowPopVel = false;
        }, 100);
      },
      inputNameInput(val) {
        if (this.timer) {
          clearTimeout(this.timer);
        }
        this.timer = setTimeout(() => {
          this.queryKeyword = val;
          this.goods_currentPage = 1;
          this.pickerSearch();
        }, 500);
      },
      handleSelectionChangereason(rows) {
        this.multipleSelection = rows;
      },
      handleSelectionresult(rows) {
        this.multipleSelectionresult = rows;
      },
      handleSelectionChange(rows) {
        this.multipleSelection = rows;
      },
      // 添加行
      addTableRow() {
        this.tableAdd();
      },
      // 添加行
      addTableRowtwo() {
        this.tableAddtwo();
      },
      // 本地删除
      locaDelTableRow() {
        let that = this

        if (this.multipleSelection.length === 0 || this.multipleSelection === []) {
          this.$message.error("请勾选要操作的数据！");
          return false;
        }
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('public.del.content'),
          okText: this.$t('public.sure'),
          okType: 'danger',
          cancelText: this.$t('public.cancel'),
          onOk() {


            let selectRows = that.multipleSelection
            selectRows.forEach((item) => {
              if (item.id === null || item.id === "" || item.id === 0) {
              } else {
                that.detailIds.push(
                  Object.assign(
                    {},
                    {
                      id: item.id,
                      prdNo: item.prdNo,
                      itm: item.itm,
                      lastQty: item.lastQty,
                      prdtId: item.prdtId,
                    }
                  )
                );
              }
            });
            selectRows.forEach((item) => {
              that.testData.splice(that.testData.indexOf(item), 1);
            });
          },
          onCancel() {

            // that.loading = false
            that.$refs.multipleTable.clearSelection();
            that.$message.info("已取消删除");
          }
        })

        // this.$confirm("此操作将删除, 是否继续?", "提示", {
        //   confirmButtonText: "确定",
        //   cancelButtonText: "取消",
        //   type: "warning",
        // })
        // .then(() => {
        // })
        // .catch(() => {
        //   this.$refs.multipleTable.clearSelection();
        //   this.$message.info("已取消删除");
        // });
      },
      // 本地删除
      locaDelTableRowtwo() {
        let that = this

        if (this.multipleSelectionresult.length === 0 || this.multipleSelectionresult === []) {
          this.$message.error("请勾选要操作的数据！");
          return false;
        }
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('public.del.content'),
          okText: this.$t('public.sure'),
          okType: 'danger',
          cancelText: this.$t('public.cancel'),
          onOk() {


            let selectRows = that.multipleSelectionresult
            selectRows.forEach((item) => {
              if (item.id === null || item.id === "" || item.id === 0) {
              } else {
                that.detailIds.push(
                  Object.assign(
                    {},
                    {
                      id: item.id,
                      prdNo: item.prdNo,
                      itm: item.itm,
                      lastQty: item.lastQty,
                      prdtId: item.prdtId,
                    }
                  )
                );
              }
            });
            selectRows.forEach((item) => {
              that.mesLsQc1s.splice(that.mesLsQc1s.indexOf(item), 1);
            });
          },
          onCancel() {
            // that.loading = false
            that.$refs.multipleTabletwo.clearSelection();
            that.$message.info("已取消删除");
          }
        })

        // this.$confirm("此操作将删除, 是否继续?", "提示", {
        //   confirmButtonText: "确定",
        //   cancelButtonText: "取消",
        //   type: "warning",
        // })
        // .then(() => {
        // })
        // .catch(() => {
        //   this.$refs.multipleTable.clearSelection();
        //   this.$message.info("已取消删除");
        // });
      },
      // 表格初始化，往数组里面添加50个对象
      tableAdd() {
        for (let i = 0; i < 3; i++) {
          let obj = {
            spcNo: "",
            spcName: "",
            qtyLost: "",
            name: "", // 品名
            id: null,
          };
          this.testData.push(obj);
        }
      },
      tableAddtwo() {
        for (let i = 0; i < 3; i++) {
          let obj = {
            czNo: "",
            qty: "",
            rem: "",
            id: null,
            bilNo: ''
          };
          this.mesLsQc1s.push(obj);
        }
      },
      //行点击
      handRowClick(row, column, event) {
        var index;
        this.testData.map((c, i) => {
          if (c == row) {
            index = i;
          }
        });
        this.$set(this.testData, index, row);
        event.stopPropagation();
      },
      // this.entity.chktyId: this.entity.chktyId ? 'T' : 'F'
      onChangeinspect(e) {

        // this.checked=e
        //  this.entity.qcId = e.target.checked
      },
      onChange(checked) {
        this.checked = checked;

      },
      onChangeclosecase(e) {


        this.entity.chkKnd = e.target.checked
        this.$forceUpdate()
      },
      onChangeLaboratory(e) {
        this.entity.sys = e.target.checked
      },
      seeFile(obj, formD) {
        this.sysFiles = obj
        this.sysFiles.forEach((item, index) => {
          this.tableData.push(item)
        })
        // this.objpic = obj
        // this.formD = formD
        // this.tableData.forEach((item, index) => {
        //   if (this.formD.wyID === item.wyID) {
        //     if (item.imageUrlList == null) {
        //       item.imageUrlList = []
        //       this.objpic.forEach((item2, index2) => {
        //         item.imageUrlList.push(item2)
        //       })
        //     } else {
        //       this.objpic.forEach((item2, index2) => {
        //         item.imageUrlList.push(item2)
        //       })
        //     }
        //   }
        // })
      },
      save() {
        let errordata = false
        for (let j = 0; j < this.mesLsQc1s.length; j++) {
          if (this.mesLsQc1s[j].czNo == '2' || this.mesLsQc1s[j].czNo == '3') {
            if (this.mesLsQc1s[j].qty == null || this.mesLsQc1s[j].qty == '') {
              errordata = true
              break
            }
          }
        }
        if (errordata) {
          this.$message.warning('处置结果为报废返修栏位，数量必填')
          return
        }
        if (this.entity.rmeSpc || this.entity.remEmg || this.entity.remRea || this.entity.remCor) {
          if (this.entity.finDdCor) { } else {
            this.$message.warning('请选择改进措施完成日')
          }
          if (this.entity.salNoZr1) { } else {
            this.$message.warning('请选择责任人')
          }
          if (this.entity.salNoZr2) { } else {
            this.$message.warning('请选择责任部门确认人')
          }
          if (this.entity.sysDateZr) { } else {
            this.$message.warning('请选择日期')
          }
          if (this.entity.finDdCor && this.entity.salNoZr1 && this.entity.salNoZr2 && this.entity.sysDateZr) { } else {
            return
          }
        }
        if (this.entity.remQc) {
          if (this.entity.salNoQc) { } else {
            this.$message.warning('请选择品质处置方案确认人')
          }
          if (this.entity.sysDateQc) { } else {
            this.$message.warning('请选择品质处置方案日期')
          }
          if (this.entity.salNoQc && this.entity.sysDateQc) {
          } else {
            return
          }
        }
        if (this.entity.remLs) {
          if (this.entity.salNoAppr) { } else {
            this.$message.warning('请选择评审意见批准人')
          }
          if (this.entity.sysDateAppr) { } else {
            this.$message.warning('请选择评审意见日期')
          }
          if (this.entity.salNoAppr && this.entity.sysDateAppr) {
          } else {
            return
          }
        }

        // 改进措施实施结果
        if (this.entity.remEft) {
          if (this.entity.salNoCon) { } else {
            this.$message.warning('请选择改进措施实施结果确认人')
          }
          if (this.entity.sysDateCon) { } else {
            this.$message.warning('请选择改进措施实施结果日期')
          }
          if (this.entity.salNoCon && this.entity.sysDateCon) {
          } else {
            return
          }
        }

        //验证结果
        if (this.entity.remEft1) {
          if (this.entity.salNoCon1) { } else {
            this.$message.warning('请选择验证结果确认人')
          }
          if (this.entity.sysDateCon1) { } else {
            this.$message.warning('请选择验证结果日期')
          }
          if (this.entity.salNoCon1 && this.entity.sysDateCon1) {
          } else {
            return
          }
        }

        //处置结果
        // this.mesLsQc1s
        this.mesLsQc1s = this.mesLsQc1s.filter((item) => {
          return item.czNo !== '' && item.czNo !== null
        })
        let mesLsQc1svalue = false
        for (let j = 0; j < this.mesLsQc1s.length; j++) {
          if (this.mesLsQc1s[j].qty) {
            mesLsQc1svalue = true
            break
          }
        }
        if (mesLsQc1svalue) {
          if (this.entity.czSal) { } else {
            this.$message.warning('请选择处置结果确认人')
          }
          if (this.entity.czDate) { } else {
            this.$message.warning('请选择处置结果日期')
          }
          if (this.entity.czSal && this.entity.czDate) {
          } else {
            return
          }
        }



        let validflag
        this.$refs.addEntityFormone.validate(valid => {
          validflag = valid
          if (this.limits1) {
            validflag = true
          } else {
            if (!valid) {
              this.$message.warning('请选择部门')
              return
            }
          }
        })
        if (validflag) {
          //  this.entity.chkKnd = this.entity.chkKnd ? 'T' : 'F'
          //  this.entity.sys = this. entity.sys ? 'T' : 'F'
          let mesQcTy1s = []

          if (this.testData) {
            mesQcTy1s = this.testData.filter((item) => {
              return item.spcNo !== "" && item.spcNo !== null;
            })
          }
          if (this.tableData.length > 0) {
            this.tableData.forEach(item => {
              if (item.distinguish) {
                delete item.distinguish
              }
            })
          }
          delete this.entity.unitName
          if (this.entity.lsNo) {
            lsqcupd({
              ...this.entity,
              sys: this.entity.sys ? 'T' : 'F',
              chkKnd: this.entity.chkKnd ? 'T' : 'F',
              mesLsQc1s: this.mesLsQc1s,
              mesTiQcFileList: this.tableData,
              mesQcTy1s: mesQcTy1s,
            }).then((res) => {
              if (res.code === 0) {
                if (res.msg === 'success') {
                  this.getList()
                  this.$message.success(res.data)
                } else {
                  this.$message.error(res.data)
                  return
                }

                // if(this.$route.query.id){
                //   this.getListtreat()
                // }else{
                //   this.getList()
                // }
              }
            }).catch(err => this.requestFailed(err))
              .finally(() => {
                this.loading = false
              })

          } else {

            // delete this.entity.tiDd
            lsqcadd({
              ...this.entity,
              sys: this.entity.sys ? 'T' : 'F',
              chkKnd: this.entity.chkKnd ? 'T' : 'F',
              mesLsQc1s: this.mesLsQc1s,
              mesTiQcFileList: this.tableData,
              mesQcTy1s: mesQcTy1s,
            }).then((res) => {
              if (res.code == 0) {
                if (res.msg === 'success') {
                  this.entity.lsDd = res.data.lsDd
                  this.entity.lsNo = res.data.lsNo
                  console.log(res.data.lsNo, 'bnnnnnnnnn')
                  localStorage.setItem("nopingshenlsNo", res.data.lsNo);
                  localStorage.removeItem('nopingshentyNo')
                  this.getList()
                  this.$message.success('保存成功')
                } else {
                  this.$message.error(res.data)
                  return
                }


              }
            }).catch(err => this.requestFailed(err))
              .finally(() => {
                this.loading = false
              })
          }
        }
      },
      selectChangeEvent({ checked, records, reserves, row, rowIndex }) {

        this.multipleSelectiontwo = records
      },
      selectChangeAll({ records, checked }) {

        this.multipleSelectiontwo = records
      },

      editclick() {
        this.isEdit = true
      },
      closeclick() {
        this.isEdit = false
      },
      reset() {
        this.entity = {}
      },
      seepic(index) {
        let arry = []
        arry.push(this.tableData[index])

        // this.$refs.ModalPic.create({ title: '查看' }, arry)
      },
      uploadpic(row) {
        // this.$refs.uploadFile.create({ title: '上传' }, row)
        // this.$refs.Upload.create(
        //   {
        //     title: '上传'
        //   },
        // )
        // this.$refs.Upload.create({ title: '上传' }, row)
      },
      // check(){
      //   this.$router.push('')
      // },
      handleChangereson(val) {

        this.multiplereason = val
      },
      cancelGetData() {
        this.visible = false
      },
      otngetData() {

        if (this.multiplereason.length) {
          this.reasondata = this.multiplereason
        }
        this.visible = false
      },
      reasonclick(val) {
        this.visible = true
      },
      handleSelectionChange(val) {
        this.multipleSelection = val
      },
      handleSelectionChangetwo(val) {
        this.multipleSelectiontwo = val
      },
      handleTabClick(key) {
        if (key == '1') {
          this.getList()
        } else if (key == '2') {
          this.getListtwo()
        }
      },
      onChange(data, dateString) {
        this.queryParam.date = dateString

      },
      getListtreat() {
        this.loading = true
        lsqcdpsinfo(
          Object.assign({
            // total: this.tablePage.currentPage,
            // current: this.tablePage.currentPage,
            // size: this.tablePage.pageSize,
            // staDd:this.queryParam.staDd,
            // endDd:this.queryParam.endDd,
            tyNo: this.entity.tyNo,
            // zcNo:this.queryParam.zcNo,
            // moNo:this.queryParam.moNo,
            // prdNo:this.queryParam.prdNo,
            // prdName:this.queryParam.prdName,
            // bilNo:this.queryParam.bilNo
          })
        ).then(response => {
          this.loading = false
          this.entity = response.data
          this.entity.qtyLostRto = ((this.entity.qtyLost / this.entity.qty) * 100).toFixed(2)
          this.testData = response.data.mesQcTy1s
          if (this.entity.mesLsQcCtrl) {
            if (this.entity.mesLsQcCtrl.ctrl1 == 'T') {
              this.limits1 = false
            } else {
              this.limits1 = true
            }
            if (this.entity.mesLsQcCtrl.ctrl2 == 'T') {
              this.limits2 = false
            } else {
              this.limits2 = true
            }
            if (this.entity.mesLsQcCtrl.ctrl3 == 'T') {
              this.limits3 = false
            } else {
              this.limits3 = true
            }
            if (this.entity.mesLsQcCtrl.ctrl4 == 'T') {
              this.limits4 = false
            } else {
              this.limits4 = true
            }
            if (this.entity.mesLsQcCtrl.ctrl5 == 'T') {
              this.limits5 = false
            } else {
              this.limits5 = true
            }
            if (this.entity.mesLsQcCtrl.ctrl5 == 'T') {
              this.limits6 = false
            } else {
              this.limits6 = true
            }
          }
          if (this.entity.dep) {
            this.depme(this.entity.dep)
          }
          datasalData({
            current: 1,
            size: '10000',
            salText: '',
          })
            .then(res => {
              this.salNoall = res.data.records
              this.salNoall.forEach(item => {
                // 表头检验人员
                if (item.salNo == this.entity.salNo) {
                  // this.entity.salNoZr1Name=item.name
                  this.datacheck = this.entity.salNo + '-' + item.name
                }
              })
            }).catch(err => {
              this.loading = false
              this.requestFailed(err)
            })
          // this.salNoall.forEach(item=>{
          //   // 改进措施责任人
          //  if(item.salNo == this.entity.salNoZr1){

          //   this.entity.salNoZr1Name=item.name
          //   this.entity.salNoZr1=item.salNo

          //   this.datacuoshi = item.salNo + '-' + this.entity.salNoZr1Name
          //  }
          //  // 改进措施责任部门确认人
          //  if(item.salNo = this.entity.salNoZr2){
          //   this.entity.salNoZr2Name=item.name
          //   this.entity.salNoZr2=item.salNo
          //  }
          //  // 品质部门确认人
          //  if(item.salNo = this.entity.salNoQc){
          //   this.entity.salNoQcName=item.name
          //   this.entity.salNoQc=item.salNo
          //  }
          //  // 评审意见批准人
          //  if(item.salNo = this.entity.salNoAppr){
          //   this.entity.salNoApprName=item.name
          //   this.entity.salNoAppr=item.salNo
          //  }
          //   // 验证结果确认人
          //  if(item.salNo = this.entity.salNoCon1){
          //   this.entity.salNoCon1Name=item.name
          //   this.entity.salNoCon1=item.salNo
          //  }
          //   // 处置结果确认人
          //  if(item.salNo = this.entity.czSal){
          //   this.entity.czSalName=item.name
          //   this.entity.czSal=item.salNo
          //  }
          // // 改进措施实施结果
          //   if(item.salNo = this.entity.salNoCon){
          //     this.entity.salNoConName=item.name
          //     this.entity.salNoCon=item.salNo
          //   }
          // })
          //  this.testData=response.data.mesQcTy1List
          this.entity.qtyOk = this.entity.qty

          // if(this.entity.dep){
          //   this.data = this.entity.dep + '-' + this.entity.depName
          // }
          this.tableData = response.data.mesTiQcFileList
          if (this.entity.chkKnd == 'T') {
            this.entity.chkKnd = true
          } else {
            this.entity.chkKnd = false
          }
          if (this.entity.sys == 'T') {
            this.entity.sys = true
          } else {
            this.entity.sys = false
          }
          // if(this.entity.qtyRtn >= this.entity.qty){
          //   this.closeId = 'T'
          // }
          // this.tablePage.total = response.data.total
          // this.tablePage.currentPage = response.data.current
        }).catch(err => this.requestFailed(err))
          .finally(() => {
            this.loading = false
          })
      },
      depme(row) {
        datadepPage(
          Object.assign({
            current: 1,
            size: 10,
            deptCode: row
          })
        ).then(response => {
          this.entity.depName = response.data.records[0].name
          this.data = this.entity.dep + '-' + this.entity.depName

        }).catch(err => this.requestFailed(err))
      },
      getList() {
        this.loading = true
        lsqcypslnfo(
          Object.assign({
            // current: this.tablePage.currentPage,
            // current: this.tablePage.currentPage,
            // size: this.tablePage.pageSize,
            // staDd:this.queryParam.staDd,
            // endDd:this.queryParam.endDd,
            lsNo: this.entity.lsNo,
            // zcNo:this.queryParam.zcNo,
            // moNo:this.queryParam.moNo,
            // prdNo:this.queryParam.prdNo,
            // prdName:this.queryParam.prdName,
            // bilNo:this.queryParam.bilNo
          })
        ).then(response => {
          this.loading = false
          this.entity = response.data
          this.entity.qtyLostRto = ((this.entity.qtyLost / this.entity.qty) * 100).toFixed(2)
          this.testData = response.data.mesQcTy1s
          this.mesLsQc1s = response.data.mesLsQc1s
          this.addTableRowtwo()
          this.$nextTick(() => {
            this.chuzhiHeight = this.$refs.chuzhiref.offsetHeight + 1 + 'px'
            this.chuzhitable = this.$refs.chuzhiref.offsetHeight - 55 + 'px'
          })
          //  if(this.mesLsQc1s.length<3){
          //   let number = 3-this.mesLsQc1s.length
          //   for (let i = 0; i < number; i++) {
          //   let obj = {
          //     czNo: "",
          //     qty: "",
          //     rem: "",
          //     id: null,
          //     bilNo:''
          //   };
          //   this.mesLsQc1s.push(obj);
          // }
          //  }
          datasalData({
            current: 1,
            size: '10000',
            salText: '',
          })
            .then(res => {
              this.salNoall = res.data.records
              this.salNoall.forEach(item => {
                // 表头检验人员
                if (item.salNo == this.entity.salNo) {
                  // this.entity.salNoZr1Name=item.name
                  this.datacheck = this.entity.salNo + '-' + item.name
                }
                // 改进措施责任人
                if (item.salNo == this.entity.salNoZr1) {
                  this.entity.salNoZr1Name = item.name
                  this.datacuoshi = this.entity.salNoZr1 + '-' + item.name

                }
                // 改进措施责任部门确认人
                if (item.salNo == this.entity.salNoZr2) {
                  this.entity.salNoZr2Name = item.name
                  this.datacuoshi2 = this.entity.salNoZr2 + '-' + item.name
                }
                // 品质部门确认人
                if (item.salNo == this.entity.salNoQc) {
                  this.dataQc = this.entity.salNoQc + '-' + item.name
                  this.entity.salNoQcName = item.name
                }
                // 评审意见批准人
                if (item.salNo == this.entity.salNoAppr) {
                  this.dataAppr = this.entity.salNoAppr + '-' + item.name
                  // this.entity.salNoApprName=item.name
                }
                // 处置结果确认人
                if (item.salNo == this.entity.czSal) {
                  this.dataczSal = this.entity.czSal + '-' + item.name
                  // this.entity.czSalName=item.name
                }
                // 改进措施实施结果
                if (item.salNo == this.entity.salNoCon) {
                  this.dataCon = this.entity.salNoCon + '-' + item.name
                  // this.entity.salNoConName=item.name
                }

                // 验证结果确认人
                if (item.salNo == this.entity.salNoCon1) {
                  this.dataCon1 = this.entity.salNoCon1 + '-' + item.name
                  // this.entity.salNoCon1Name=item.name
                }
              })
              // this.data=res.data.records[0].salNo + '-' + res.data.records[0].name
              // if(res.msg == 'success'){
              //   this.$message.success('删除成功')
              // }else{
              //   this.$message.success('删除失败')
              // }
              // this.loading = false
              // this.getList()
            })
            .catch(err => {
              this.loading = false
              this.requestFailed(err)
            })
          if (this.entity.dep) {
            this.depme(this.entity.dep)
          }
          //   datadepPage({
          //     size: '10000',
          //     current: 1,
          //     deptCode: '',
          //     name: ''
          //   })
          // .then(res => {
          //   this.depall = res.data.records
          //   this.depall.forEach(item=>{
          //   // 改进措施责任人
          //     if(item.deptCode == this.entity.dep){
          //       // this.entity.salNoZr1Name=item.name
          //       this.data = this.entity.dep + '-' + item.name
          //     }
          //   })

          // }).catch(err => {
          //   this.loading = false
          //   this.requestFailed(err)
          // })
          if (this.entity.mesLsQcCtrl) {
            if (this.entity.mesLsQcCtrl.ctrl1 == 'T') {
              this.limits1 = false
            } else {
              this.limits1 = true
            }
            if (this.entity.mesLsQcCtrl.ctrl2 == 'T') {
              this.limits2 = false
            } else {
              this.limits2 = true
            }
            if (this.entity.mesLsQcCtrl.ctrl3 == 'T') {
              this.limits3 = false
            } else {
              this.limits3 = true
            }
            if (this.entity.mesLsQcCtrl.ctrl4 == 'T') {
              this.limits4 = false
            } else {
              this.limits4 = true
            }
            if (this.entity.mesLsQcCtrl.ctrl5 == 'T') {
              this.limits5 = false
            } else {
              this.limits5 = true
            }
            if (this.entity.mesLsQcCtrl.ctrl5 == 'T') {
              this.limits6 = false
            } else {
              this.limits6 = true
            }
          }

          // this.testData=response.data.mesQcTy1List

          // this.data = this.entity.dep + '-' + this.entity.depName
          // 不合格评审单
          // if(this.entity.boxNo){
          //   this.isClaim = true
          // }else{
          //    this.isClaim = false
          // }
          this.tableData = response.data.mesTiQcFileList
          if (this.entity.chkKnd == 'T') {
            this.entity.chkKnd = true
          } else {
            this.entity.chkKnd = false
          }

          if (this.entity.sys == 'T') {
            this.entity.sys = true
          } else {
            this.entity.sys = false
          }
          if (this.entity.qtyRtn >= this.entity.qty) {
            this.closeId = 'T'
          }

          // this.tablePage.total = response.data.total
          // this.tablePage.currentPage = response.data.current
        }).catch(err => this.requestFailed(err))
          .finally(() => {
            this.loading = false
          })
      },
      // 获取列表数据
      // getList () {
      //   this.loading = true
      //   fetchList(
      //     Object.assign({
      //       current: this.tablePage.currentPage,
      //       size: this.tablePage.pageSize,
      //       pgNo: this.queryParam.pgNo
      //     })
      //   ).then(response => {
      //     this.loading = false
      //     this.tableData = response.data.records

      //     this.tablePage.total = response.data.total
      //     this.tablePage.currentPage = response.data.current
      //   }).catch(e => {
      //     this.loading = false
      //   })
      // },
    },
  }
</script>

<style lang='scss' scoped>
  .ant-input-wrapper .ant-input-group-addon .ant-btn {
    height: 28px;
  }

  .sup_info_item {
    padding: 10px 0;
  }



  .el-form-item--mini.el-form-item,
  .el-form-item--small.el-form-item {
    margin-bottom: 0px
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    -o-appearance: none !important;
    -ms-appearance: none !important;
    appearance: none !important;
    margin: 0;
  }

  input[type="number"] {
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    -o-appearance: textfield;
    -ms-appearance: textfield;
    appearance: textfield;
  }

  .sup_info_basics_container {
    border: 1px solid #E5E5E5;
    border-radius: 10px;
    padding: 0px 20px 0px 20px;
  }

  .sup_info_basics_header {
    font-size: 15px;
    color: #1F2A3F;
    height: 41px;
    font-weight: 500;
    line-height: 41px;
    overflow: hidden;
  }

  .el-table__fixed,
  .el-table__fixed-right {
    height: 100% !important;
  }

  .ant-input {
    height: 28px;
  }

  .bordervisibtwo .el-input__inner {
    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 0px;
    width: 100%;
    height: 20px;
  }

  /* .el-table tbody tr:hover > td {
    background-color: #fff!important;
} */
  .compoct .el-form-item--small .el-form-item__content,
  .el-form-item--small .el-form-item__label {
    line-height: 24px;
  }

  .bordervisibtwo .el-input__inner {
    height: 20px;
  }

  .tableheight .el-table__cell {
    padding: 1px
  }

  .line {
    height: 0.5px;
    background-color: #E5E5E5;
    margin-top: 15px;
    margin-bottom: 15px;
  }
</style>