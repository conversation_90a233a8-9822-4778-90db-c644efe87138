<template>
  <div>
    <a-form
      :form="form"
      ref="form"
    >
      <!-- 添加 -->
      <!-- <span style="font-size:1rem">{{ $t('propertySettings.ware') }}</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.ware')"
            v-bind="formItemLayout"
          >
            <a-select
              style="width:100%"
              allowClear
              v-decorator="['installedWh', { rules: [{ message:$t('propertySettings.ware') }] }]"
              :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }"
            >
              <a-select-option value="1">来源单仓库</a-select-option>
              <a-select-option value="2">扫描仓库</a-select-option>
              <a-select-option value="3">条码仓库</a-select-option>
              <a-select-option value="4">预设仓库</a-select-option>
              <a-select-option value="5">指定仓库</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.waret')"
            v-bind="formItemLayout"
          >
            <a-select
              show-search
              allowClear
              @search="searchWh"
              @focus="getWhs()"
              :filter-option="false"
              v-decorator="['appointWh',{rules: []}]"
            >
              <a-spin
                v-if="fetching"
                slot="notFoundContent"
                size="small"
              />
              <a-select-option
                v-for="(i, index) in whlist"
                :key="index"
                :value="i.wh"
              >{{ i.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.sheet') }}:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.Separator')"
            v-bind="formItemLayout"
          >
            <a-input v-decorator="['separator', { rules: [{ message:$t('propertySetting.Separator') }] }]"></a-input>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.Quantity') }}:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.Quantitys')"
            v-bind="formItemLayout"
          >
            <a-switch v-model="inputQty" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">扫描设置:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="批号匹配"
            v-bind="formItemLayout"
          >
            <a-switch v-model="scanBatNo" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.detection') }}:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.detection')"
            v-bind="formItemLayout"
          >
            <a-select
              style="width:100%"
              allowClear
              v-decorator="['exceed', { rules: [{ message:$t('propertySettings.detection') }] }]"
              :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }"
            >
              <a-select-option value="1">管制</a-select-option>
             
              <a-select-option value="4">允许在超交比例内</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.Proportion')"
            v-bind="formItemLayout"
          >
            <a-input-number
              :min="0"
              v-decorator="['exceedProportion']"
            />
          </a-form-item>
        </a-col>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.basic')"
            v-bind="formItemLayout"
          >
            <a-switch v-model="source" />
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.Batch') }}:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.serialNumber')"
            v-bind="formItemLayout"
          >
            <a-select
              style="width:100%"
              allowClear
              v-decorator="['batch', { rules: [{ message:$t('propertySettings.Batch') }] }]"
              :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }"
            >
              <a-select-option value="1">按货品基础资料</a-select-option>
              <a-select-option value="2">不管制</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <span style="font-size:1rem">{{ $t('propertySettings.Bill') }}:</span>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            :label="$t('propertySettings.Bill')"
            v-bind="formItemLayout"
          >
            <a-select
              style="width:100%"
              allowClear
              v-decorator="['examine', { rules: [{ message:$t('propertySettings.Bill') }] }]"
              :getPopupContainer=" triggerNode => {
                return triggerNode.parentNode || document.body;
              }"
            >
              <a-select-option value="1">不审核</a-select-option>
              <a-select-option value="2">审核</a-select-option>
              <a-select-option value="3">自动判断</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row> -->
      <!-- 添加上 -->
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="20"
        >
          <a-form-item
            label="免检是否生成托工缴回单、缴库"
            v-bind="formItemLayout"
          >
            <a-switch v-model="automaticTw" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="20"
        >
          <a-form-item
            label="免检是否生成进货单"
            v-bind="formItemLayout"
          >
            <a-switch v-model="automaticPc" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="12"
        >
          <a-form-item
            label="自动获取明细"
            v-bind="formItemLayout"
          >
            <a-switch v-model="automaticList" />
          </a-form-item>
        </a-col>
      </a-row>

    </a-form>
    <a-row :gutter="16">
      <a-col
        class="gutter-row"
        :span="12"
        style="text-align:right"
      >
        <a-button
          id="ok"
          type="primary"
          @click="handleOK"
        >{{ $t('public.save') }}{{ obj.subname }}</a-button>
      </a-col>
      <a-col
        class="gutter-row"
        :span="12"
        style="text-align:left"
      >
        <a-button
          id="cancel"
          @click="handleCancel"
        >{{ $t('public.cancel') }}</a-button>
      </a-col>
    </a-row>
  </div>
</template>
<script>

import { getWh, addBarPswdProp, getBarPswdProps } from '@/api/barcode/propertySettings'
import debounce from 'lodash.debounce'
export default {
  props: {
    obj: {
      required: true,
      type: Object
    },
    cid: {
      required: true,
      type: String
    },
    row: {
      required: true,
      type: Object
    }
  },
  data () {
    // this.getWhs = debounce(this.getWhs, 300)
    return {
      title: '',
      visible: true,
      confirmLoading: true,
      form: this.$form.createForm(this),
      whlist: [],
      userList: [],
      automaticTw: true,
      automaticPc: true,
      automaticList: false,
      fetching: false,
      subData: [],
      onSubmitData: {
        // 保存属性对象
        compno: '',
        roleno: '',
        typeId: '6',
        pgm: '',
        fldName: '',
        fldValue: ''
      },
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 7 },
          md: { span: 8 },
          lg: { span: 8 }
        },
        wrapperCol: {
          xs: { span: 5 },
          sm: { span: 16 },
          md: { span: 17 },
          lg: { span: 16 }
        }
      },
      scanBatNo: false,
      inputQty: false,
      source: false,

    }
  },
  created () {
    if (this.obj.subname) {
      this.getpop()
    }
  },
  methods: {
    // 添加
    // getWhs (page = 1, queryWhs = '') {
    //   this.fetching = true
    //   this.whlist = []
    //   getWh(
    //     Object.assign({
    //       current: page,
    //       size: 10,
    //       wh: queryWhs,
    //       name: queryWhs,
    //       rank: '2'
    //     })
    //   ).then(response => {
    //     this.whlist = response.data.records
    //     this.fetching = false
    //   })
    //     .catch(() => {
    //       this.fetching = false
    //     })
    // },
    // searchWh (value) {
    //   this.getWhs(1, value)
    // },
    // 添加
    getpop () {
      const obj = {
        compno: this.row.compno,
        roleno: this.row.roleno,
        pgm: this.cid
      }
      getBarPswdProps(obj).then(res => {
        this.subData = res.data
        const arr = this.subData
        if (arr.length > 0) {
          setTimeout(() => {
            function tran (name) {
              let ind
              arr.forEach((e, index) => {
                if (e.fldName === name) {
                  return ind = index
                }
              })
              return ind
            }
            // 添加7个
            // this.inputQty = JSON.parse(tran('inputQty') === undefined ? this.inputQty : arr[tran('inputQty')].fldValue)
            // this.scanBatNo = JSON.parse(tran('scanBatNo') === undefined ? this.scanBatNo : arr[tran('scanBatNo')].fldValue)
            // this.source = JSON.parse(tran('source') === undefined ? this.source : arr[tran('source')].fldValue)

            this.automaticTw = JSON.parse(tran('automaticTw') === undefined ? this.automaticTw : arr[tran('automaticTw')].fldValue)
            this.automaticPc = JSON.parse(tran('automaticPc') === undefined ? this.automaticPc : arr[tran('automaticPc')].fldValue)
            this.automaticList = JSON.parse(tran('automaticList') === undefined || arr[tran('automaticList')].fldValue === '' || arr[tran('automaticList')].fldValue === null ? this.automaticList : arr[tran('automaticList')].fldValue)
            // this.form.setFieldsValue({
            //   exceed: tran('exceed') === undefined ? '' : arr[tran('exceed')].fldValue,
            //   installedWh: tran('installedWh') === undefined ? '' : arr[tran('installedWh')].fldValue,
            //   appointWh: tran('appointWh') === undefined ? '' : arr[tran('appointWh')].fldValue,
            //   separator: tran('separator') === undefined ? '' : arr[tran('separator')].fldValue,
            //   exceedProportion: tran('exceedProportion') === undefined ? '' : arr[tran('exceedProportion')].fldValue,
            //   batch: tran('batch') === undefined ? '' : arr[tran('batch')].fldValue,
            //   examine: tran('examine') === undefined ? '' : arr[tran('examine')].fldValue,
            // })
            // if (tran('appointWh') !== undefined && tran('appointWh') !== null && tran('appointWh') !== '') this.getWhs(1, arr[tran('appointWh')].fldValue)
            // else
            //   this.getWhs()
            // 添加7个

          }, 1)
        }
      })
    },
    getData () {
      this.subData = []
      const fidArr = this.obj.fidArr
      fidArr.forEach(i => {
        this.subData.push(this.onSubmitData = {
          compno: this.row.compno,
          roleno: this.row.roleno,
          typeId: '6',
          pgm: this.cid,
          fldName: i,
          fldValue: ''
        })
      })
    },
    handleOK () {


      this.getData()
      this.form.validateFields((err, values) => {
        const arr = this.subData
        arr.forEach(i => {
          // if (i.fldName === 'exceed') {
          //   i.fldValue = values.exceed
          // }
          // if (i.fldName === 'installedWh') {
          //   i.fldValue = values.installedWh
          // }
          // if (i.fldName === 'appointWh') {
          //   i.fldValue = values.appointWh
          // }
          // if (i.fldName === 'separator') {
          //   i.fldValue = values.separator
          // }
          // if (i.fldName === 'inputQty') {
          //   i.fldValue = this.inputQty + ''
          // }
          // if (i.fldName === 'scanBatNo') {
          //   i.fldValue = this.scanBatNo
          // }
          // if (i.fldName === 'exceedProportion') {
          //   i.fldValue = values.exceedProportion
          // }
          // if (i.fldName === 'source') {
          //   i.fldValue = this.source + ''
          // }
          // if (i.fldName === 'batch') {
          //   i.fldValue = values.batch
          // }
          // if (i.fldName === 'examine') {
          //   i.fldValue = values.examine
          // }
          if (i.fldName === 'automaticTw') {
            i.fldValue = this.automaticTw
          }
          if (i.fldName === 'automaticPc') {
            i.fldValue = this.automaticPc
          }
          if (i.fldName === 'automaticList') {
            i.fldValue = this.automaticList
          }
        })

        if (!err) {
          addBarPswdProp(this.subData)
            .then(() => {
              this.$message.success(this.$t('public.success'))
            })
            .catch(() => {
              this.$message.error(this.$t('public.error'))
            })
        }
      })
    },
    handleCancel () {
      this.$emit('Cancel')
      this.subData = []
    }
  }
}
</script>
