<template>
  <div>
    <!-- 单据页面设计-视窗 -->
    <docDialog
      :docDialogVisible.sync="docDialogVisible"
      :form-config="formConfig"
      :grid-config="gridConfig"
    ></docDialog>
    <!-- 自定义栏位设计-视窗 -->
    <defDialog
      :defDialogVisible.sync="defDialogVisible"
      :form-config="formConfig"
      :grid-config="gridConfig"
    ></defDialog>
    <!-- 工具栏 操作按钮 -->
    <!--<vToolbar :data="toolbarItems" @toolbarClick="handleToolbarClick"></vToolbar>-->
    <dToolbar :data="toolbarItems" @toolbarClick="handleToolbarClick"
              :dropSetShow="dropSetShow"
              @handOpenDocDia = "handOpenDocDia"
              @handOpenDefDia = "handOpenDefDia"
    ></dToolbar>
    <!-- 数据 表头vxe-form及表身vxe-grid -->
    <div>
      <el-collapse v-model="activeNames">
        <el-collapse-item style="margin-bottom: 10px" name="1">
          <template #title>
            基础信息
          </template>
          <dForm
            ref = "dfo"
            :formConfig="formConfig"
            :billData.sync="billData"
          >
          </dForm>
        </el-collapse-item>
        <el-collapse-item style="margin-bottom: 10px" name="2">
          <template #title>
            单据明细
          </template>
          <div class="grip-container">
            <div style="flex: 0.7;overflow-x: auto;">
              <dGrid
                ref = "dgr"
                :gridConfig="gridConfig"
                :billData.sync="billData"
                @handCurrentChange="handCurrentChange"
                @createRow="createRow"
              >
              </dGrid>
            </div>
            <div style="flex: 0.3;overflow-x: auto;">
              <dGrid
                ref = "dgrSub"
                :gridConfig="SubGridConfig"
                :billData.sync="SubBillData"
              >
              </dGrid>
            </div>

          </div>
        </el-collapse-item>
        <el-collapse-item style="margin-bottom: 10px" name="3">
          <template #title>
            附件
          </template>
          <annex ref="anRef" @getAnnexData="getAnnexData"></annex>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script>
import docDialog from "@/components/def/DocDialog.vue";
import defDialog from "@/components/def/DefDialog.vue";
import dGrid from "@/components/def/DGrid.vue"
import dForm from "@/components/def/DForm.vue"
import vToolbar from "@/components/amtxts/vTable/vToolbar.vue"
import dToolbar from "@/components/def/DToolbar.vue"
import annex from "@/views/mes/selftest/annex";

import {queryById, saveOrUpdate, del} from '@/api/mes/box0'
import useFieldDefine from "@/utils/def/useFieldDefine";
import {erraddErr} from "@/api/device/category";
import {qcSpcZjqcAdd, qcSpcZjqcUpd, qcSpcZjtyDel, qcSpcZjyjInfo, qctiDel, tiUpdsave} from "@/api/mes/quality";

export default {
  components: {
    docDialog, defDialog,
    dGrid, dForm,
    vToolbar, dToolbar,
    annex
  },

  data() {
    return {
      defDialogVisible: false,
      docDialogVisible: false,
      tableData:[],
      activeNames: ['1', '2', '3'],
      formConfig: {
        PGM:"SJZJSPC",
        MENU_NAME :"SJZJSPC",
        TABLE_NAME: "MES_QC_ZJ",

        initItems: null,
        items: [
          {
            span: 24,
            children: [
            ]
          }
        ],
        rules: {
          dep: [
            {required: true, message: '请填写部门信息！'}
          ],
        },
      },

      formItems: [
        {
          span: 24,
          children: [
            {field: 'zjDd', title: 'selfTest.zjDd', span: 6, itemRender: {name: 'MyElInput', props: {type:"date", placeholder: '',disabled: true } }},
            {field: 'zjNo', title: 'selfTest.zjNo', span: 6, itemRender: {name: 'MyElInput', props: {placeholder: '',disabled: true }}},
            {field: 'qcTypeName', title: 'selfTest.qcTypeName', span: 6, itemRender: {name: 'MyElInput',props: { placeholder: '',disabled: true }}},
            {field: 'cusNo', title: 'selfTest.cusNo', span: 6, itemRender: {name: 'MyElInput',props: {placeholder: '',disabled: true }}},
            {
              field: 'dep', title: 'selfTest.dep', span: 6,
              itemRender: {
                name: 'MySelectList',
                props: {
                  placeholder: '',
                  url: 'mes/basicData/depPage',
                  tableColumn: this.$Column.salmDep,
                  form: this.$Form.salmDep,
                },
                events:{
                  selectListEvent:(param)=>{

                  }
                }
              }
            },
            {field: 'bilNo', title: 'selfTest.bilNo', span: 6, itemRender: {name: 'MyElInput',props: { placeholder: '',disabled: true }}},
            {field: 'salNo', title: 'selfTest.salNo', span: 6, itemRender: {name: 'MyElInput',props: {placeholder: '',disabled: true }}},
            {field: 'bilType', title: 'selfTest.bilType', span: 6, itemRender: {name: 'MyElInput',props: { placeholder: '' ,disabled: true }}},
            {field: 'sys', title: 'selfTest.sys', span: 6,
              itemRender: {
                name: 'MyElCheckBox',
                props: {
                  placeholder: '',
                  trueLabel:'T',
                  falseLabel:'F',
                  disabled:true
                },
                events:{
                  change:(param)=>{console.log('change',param)}
                }
              }},
            {
              field: 'qcId', title: 'selfTest.qcId', span: 6,
              slots: {
                default: ({ data }) => {
                  return this.$createElement('span',
                    data.qcId === 'T' ? '合格' : data.qcId === 'F' ? '不合格' : '')
                }
              },
              itemRender: {props: { placeholder: '',disabled: true }}
            },
            {field: 'chkKnd', title: 'selfTest.chkKnd', span: 6,
              itemRender: {
                name: 'MyElCheckBox',
                props: {
                  placeholder: '',
                  trueLabel:'T',
                  falseLabel:'F',
                  disabled:true
                },
                events:{
                  change:(param)=>{console.log('change',param)}
                }
              }},
            {field: 'prdNo', title: 'selfTest.prdNo', span: 6, itemRender: {name: 'MyElInput',props: { placeholder: '',disabled: true }}},
            {field: 'prdName', title: 'selfTest.prdName', span: 6, itemRender: {name: 'MyElInput',props: {placeholder: '',disabled: true }}},
            {field: 'prdMark', title: 'selfTest.prdMark', span: 6, itemRender: {name: 'MyElInput',props: {placeholder: '',disabled: true }}},
            {field: 'unitName', title: 'selfTest.unitName', span: 6, itemRender: {name: 'MyElInput',props: { placeholder: '',disabled: true }}},
            {field: 'whName', title: 'selfTest.whName', span: 6, itemRender: {name: 'MyElInput',props: {placeholder: '',disabled: true }}},
            {field: 'batNo', title: 'selfTest.batNo', span: 6, itemRender: {name: 'MyElInput',props: { placeholder: '',disabled: true }}},
            {field: 'moNo', title: 'selfTest.moNo', span: 6, itemRender: {name: 'MyElInput',props: {placeholder: '',disabled: true }}},
            {field: 'pgNo', title: 'selfTest.pgNo', span: 6, itemRender: {name: 'MyElInput',props: { placeholder: '',disabled: true }}},
            {field: 'zcNo', title: 'selfTest.zcNo', span: 6, itemRender: {name: 'MyElInput',props: { placeholder: '',disabled: true }}},
            {field: 'qtyRtn', title: 'selfTest.qtyRtn', span: 6, itemRender: {name: 'MyElInput',props: {placeholder: '',disabled: true }}},
            {field: 'lsNo', title: 'selfTest.lsNo', span: 6, itemRender: {name: 'MyElInput',props: { placeholder: '',disabled: true }}},
            {field: 'boxNo', title: 'selfTest.boxNo', span: 6, itemRender: {name: 'MyElInput',props: {placeholder: '',disabled: true }}},
            {field: 'rem', title: 'selfTest.rem', span: 6, itemRender: {name: 'MyElInput',props: { placeholder: '', }}},
            {field: 'qty', title: 'selfTest.qty', span: 6, itemRender: {name: 'MyElInput',props: { placeholder: '',disabled: true }}},
            {field: 'qtyOk', title: 'selfTest.qtyOk', span: 6, itemRender: {name: 'MyElInput',props: {placeholder: '',disabled: true }}},
            {field: 'qtyLost', title: 'selfTest.qtyLost', span: 6, itemRender: {name: 'MyElInput',props: { placeholder: '' }}},

          ]
        }
      ],

      gridConfig: {
        TABLE_NAME: "",
        columns: [
          {
            field: 'tabSeq',
            width: 80,
            title: '自定义列+',
            align: 'center',
            slots: {header: 'tabSeq_header', default: 'tabSeq_default',}
          },
          {
            field: 'qcItm', title: 'selfTest.qcItm', align: 'center',
            editRender: {
              name: 'MySelectList',
              props: {
                placeholder: '',
                url: 'mes/basicData/qcItmPage',
                tableColumn: this.$Column.qcItm,
                form: this.$Form.qcItm,
              },
              events:{
                selectListEvent:(row,field,value)=>{
                  row.qcItm=value.obj.data.qcItm
                  row.qcName=value.obj.data.name
                }
              }
            }
          },
          {field: 'qcName', title: 'selfTest.qcName', align: 'center',
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },
          {field: 'qty', title: 'selfTest.qty', align: 'center',
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },
          {field: 'qcSpc', title: 'selfTest.qcSpc', align: 'center',
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },
          {field: 'stdValue', title: 'selfTest.stdValue', align: 'center',
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },
          {field: 'stdValueId', title: 'selfTest.stdValueId', align: 'center',
            editRender: {
              name: 'MyElSelect',
              options: [
                {value: '1', label: '±'},
                {value: '2', label: '>='},
                {value: '3', label: '<='},
                {value: '4', label: '='},
                {value: '5', label: '+-'},
                {value: '6', label: '~~'},
              ],
              props: {placeholder: '',clearable:true,},
              events: {
                change: this.selectChange,
                focus: () => {console.log('focus')},
                blur: () => {console.log('blur')},
                clear: () => {console.log('clear')},
                visibleChange: (value) => {console.log('visibleChange', value)},
                removeTag: () => {console.log('removeTag')}
              }
            }
          },
          {field: 'val1', title: 'selfTest.val1', align: 'center',
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },
          {field: 'acMin', title: 'selfTest.acMin', align: 'center',
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },

          {field: 'acMax', title: 'selfTest.acMax', align: 'center',
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },
          {field: 'qcType', title: 'selfTest.qcType', align: 'center',
            editRender: {
              name: 'MyElSelect',
              options: [
                {value: '1', label: '计数'},
                {value: '2', label: '测量'},
              ],
              props: {placeholder: '',clearable:true,},
              events: {
                change: this.selectChange,
                focus: () => {console.log('focus')},
                blur: () => {console.log('blur')},
                clear: () => {console.log('clear')},
                visibleChange: (value) => {console.log('visibleChange', value)},
                removeTag: () => {console.log('removeTag')}
              }
            }
          },
          {field: 'qcTool', title: 'firstInspectData.qcTool', align: 'center',
            editRender: {
              name: 'MyElSelect',
              options: [
                {value: '001', label: '游标卡尺'},
                {value: '002', label: '千分尺'},
                {value: '003', label: '数显千分尺'},
                {value: '004', label: '内径千分尺'},
                {value: '005', label: '梅花通止规'},
                {value: '006', label: '代表卡尺'},
              ],
              props: {placeholder: '',clearable:true,},
              events: {
                change: this.selectChange,
                focus: () => {console.log('focus')},
                blur: () => {console.log('blur')},
                clear: () => {console.log('clear')},
                visibleChange: (value) => {console.log('visibleChange', value)},
                removeTag: () => {console.log('removeTag')}
              }
            }
          },

          {field: 'qtyLost', title: 'selfTest.qtyLost', align: 'center',
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },
          {field: 'itm', title: 'selfTest.itm', align: 'center',
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },
          {field: 'qcRec', title: 'selfTest.qcRec', align: 'center',
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },
          {field: 'chkId', title: 'selfTest.chkId', align: 'center',
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },
          {field: 'spcNo', title: 'selfTest.spcNo', align: 'center',
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },
          {field: 'spcName', title: 'selfTest.spcName', align: 'center',
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },
          {field: 'rem', title: 'selfTest.rem', align: 'center',
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },
        ],
        rules: {

        }
      },
      SubGridConfig: {
        TABLE_NAME: "",
        columns:[
          {
            field: 'tabSeq',
            width: 80,
            title: '自定义列+',
            align: 'center',
            slots: {header: 'tabSeq_header', default: 'tabSeq_default',}
          },
          {field: 'itm', title: 'selfTest.itm', align: 'center',width: 80,
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },
          {field: 'qcRec', title: 'selfTest.qcRec', align: 'center',width: 120,
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },
          {field: 'chkId', title: 'selfTest.chkId', align: 'center',width: 120,
            editRender: {
              name: 'MyElSelect',
              options: [
                {value: 'T', label: '合格'},
                {value: 'F', label: '不合格'},

              ],
              props: {placeholder: '',clearable:true,},
              events: {
                change: this.selectChange,
                focus: () => {console.log('focus')},
                blur: () => {console.log('blur')},
                clear: () => {console.log('clear')},
                visibleChange: (value) => {console.log('visibleChange', value)},
                removeTag: () => {console.log('removeTag')}
              }
            }
          },
          {
            field: 'spcNo', title: 'qualitySure.spcNo', align: 'center',width: 120,
            editRender: {
              name: 'MySelectList',
              props: {
                placeholder: '',
                url: 'mes/spcLst/page',
                tableColumn: this.$Column.spcLst,
                form: this.$Form.spcLst,
              },
              events:{
                selectListEvent:(row,field,value)=>{
                  row.spcNo=value.obj.data.spcNo
                  row.spcName=value.obj.data.name
                  for (let i = 0; i <this.billData.gridData.length-1; i++) {
                    if (this.billData.gridData[i].spcNo===value.obj.data.spcNo){
                      row.spcNo=''
                      row.spcName=''
                      this.$message.error("数据重复，请重新选择！");
                      break
                    }
                  }
                }
              }
            }
          },
          {field: 'spcName', title: 'selfTest.spcName', align: 'center',width: 120,
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },
          {field: 'rem', title: 'selfTest.rem', align: 'center',width: 120,
            editRender: {name: 'VxeInput', props: {placeholder: '', align: 'center'}}
          },
        ] ,
        rules: {}
      },
      billData: {
        pgm: 'SJZJSPC',
        formData: {},
        gridData: []
      },
      SubBillData: {
        pgm: 'SJZJSPC',
        formData: {},
        gridData: []
      },
      newData: 0,
      //新增空数据
      newFormData: {},
      newGridData: [],
      //测试数据
      initFormData: {},
      initGridData: [],
      toolbarItems: [
        {label: '编辑',value: 'edit', disabled:true },
        {label: '新增',value: 'create', disabled:true },
        {label: '打印',value: 'print', disabled:true },
        {label: '保存', value: 'save', icon: 'icon-save' },
      ],
      dropSetShow: true,
      inspectData:[],
      inspectDataflag: true,
    }
  },
  mounted() {
    this.formConfig.items = this.formItems
    //单据设计页面用 initItems, initColumns
    this.formConfig.initItems = JSON.parse(JSON.stringify( this.formItems[0].children))
    this.gridConfig.initColumns = JSON.parse(JSON.stringify( this.gridConfig.columns))
    //初始化, 页面栏位信息加载 及动态渲染
    useFieldDefine.init(this.formConfig, this.gridConfig, this.billData)
  },
  activated(){
    let row =this.$route.params.row;
    if (row !== undefined && Object.keys(row).length !== 0) {
      this.billData.formData={}
      this.billData.gridData=[{}]
      this.tableData=[]
      this.SubBillData.gridData=[]
      this.getList(row)
    }
  },
  watch: {
    //自定义栏位设计 关闭, 触发更新栏位数据
    defDialogVisible(newVal) {
      if (newVal === false) {
        useFieldDefine.init(this.formConfig, this.gridConfig, this.billData)
      }
    },
    //单据页面设计 关闭, 触发更新栏位数据
    docDialogVisible(newVal) {
      if (newVal === false) {
        useFieldDefine.init(this.formConfig, this.gridConfig, this.billData)
      }
    },
  },
  methods: {
    createRow(){
      this.SubBillData.gridData=[]
    },
    handCurrentChange(data){
      if (data.newValue.hasOwnProperty('inspectDatatwo')){
        this.SubBillData.gridData=data.newValue.inspectDatatwo
      }else{
        data.newValue.inspectDatatwo=[]
        this.SubBillData.gridData=[]
      }
      data.newValue.inspectDatatwo=this.SubBillData.gridData
      // 找到相同 id 的索引
      const index = this.billData.gridData.findIndex(item => item.qcItm === data.newValue.qcItm);

      if (index !== -1) {
        this.$set(this.billData.gridData, index, data.newValue);
      } else {
        // 如果没找到相同 id，直接 push 新数据
        this.billData.gridData.push(data.newValue);
      }
    },
    getList(row) {
      qcSpcZjyjInfo(
        Object.assign({ zjNo: row.zjNo,
        })
      ).then(response => {
        this.billData.formData = { ...response.data };
        this.$refs.anRef.getList(response.data);
        this.inspectData = response.data.mesQcTy2s
        this.inspectData.forEach((item, i) => {
        item.addId = i
        item.chkId = 'T'
        if (item.qcType == '1') {
          item.chkId = ''
        }
        item.inspectDatatwo = []
        item.inspectDatathree = []
        item.inspectall = []
        let qtylength
        if (item.mesQcTy2List) {
          if (item.mesQcTy2List.length > 0) {
            for (let i = 0; i < item.mesQcTy2List.length; i++) {
              item.mesQcTy2List[i].qcType = item.qcType
              item.inspectDatatwo.push(item.mesQcTy2List[i])
            }
          }
          if (item.mesQcTy2List.length < item.qty) {
            qtylength = item.qty - item.mesQcTy2List.length
            for (let i = 0; i < qtylength; i++) {
              let obj = {
                qcItm: item.qcItm,
                acMax: item.acMax,
                acMin: item.acMin,
                itm: i + 1 + item.mesQcTy2List.length,
                qcRec: item.qcRec,
                chkId: item.chkId,
                spcNo: item.spcNo,
                spcName: item.spcName,
                rem: item.rem,
                qcType: item.qcType,
              };
              item.inspectDatatwo.push(obj)
            }
          }
          item.itm=item.inspectDatatwo[i].itm
          item.qcRec=item.inspectDatatwo[i].qcRec
          }

        })
        this.billData.gridData=this.inspectData
        this.SubBillData.gridData=this.billData.gridData[0].inspectDatatwo
      }).catch(err => this.requestFailed(err))
    },

    getAnnexData(param){
      if (param===undefined){
        param=[]
      }
      this.tableData=param
    },
    handleToolbarClick(params){
      switch (params) {
        case 'create':
          // this.handNewData()
          break;
        case 'remove':
          this.handDelData()
          break;
        case 'query':
          this.handQueryData()
          break;
        case 'save':
          // this.handSaveData()
          this.getSaveData()
          break;
        default:
      }

    },

    //打开单据页面设计窗口
    handOpenDocDia() {
      this.docDialogVisible = true;
    },

    //打开自定义栏位设计页面窗口
    handOpenDefDia() {
      this.defDialogVisible = true;
    },

    //新增  //清空现有单据, 填写新数据
    handNewData() {
      if (this.newData === 0) {
        console.log("this.newData 空数据",this.newData)
        this.billData.formData = JSON.parse(JSON.stringify(this.newFormData));
        this.billData.gridData = JSON.parse(JSON.stringify(this.newGridData));
        this.newData = 1
      } else {
        console.log("this.newData 测试数据",this.newData)
        this.initFormData.pntDt = this.formatDate(new Date())
        this.billData.formData = JSON.parse(JSON.stringify(this.initFormData));
        this.billData.gridData = JSON.parse(JSON.stringify(this.initGridData));
        this.newData = 0
      }
    },

    getSaveData(){
      this.$refs.anRef.sendData()
      this.save()
    },
    async save() {
        let flag=false
        const vxeFo = this.$refs.dfo.$refs.vxeForm
        await vxeFo.validate((valid) => {if (valid) {flag = true}});
        if (flag) {
          this.$message.warning("数据不完整, 请先输入!")
          return;
        }
        this.inspectData=[]
        this.inspectData=this.billData.gridData
        this.billData.formData.mesQcTy1s = []
        this.billData.formData.mesQcTy3s = []
        // 检查不合格原因数据问题
        let errordata = false
        let empy = false
        for (let i = 0; i < this.inspectData.length; i++) {
          if (this.inspectData[i].qcType == 2 && this.inspectData[i].stdValueId == '+-') {
            if (this.inspectData[i].val1 && this.inspectData[i].val2) {
            } else {
              empy = true
              break
            }
          }
        }
        if (empy) {
          this.$message.warning('请填写偏差值')
          return
        }
        for (let i = 0; i < this.inspectData.length; i++) {

          for (let j = 0; j < this.inspectData[i].inspectDatatwo.length; j++) {
            this.inspectData[i].inspectDatatwo[j].qcItm = this.inspectData[i].qcItm
            this.inspectData[i].inspectDatatwo[j].qcTool = this.inspectData[i].qcTool
            this.inspectData[i].inspectDatatwo[j].qcToolName = this.inspectData[i].qcToolName
            this.inspectData[i].inspectDatatwo[j].qty = this.inspectData[i].qty
            this.inspectData[i].inspectDatatwo[j].qcSpc = this.inspectData[i].qcSpc
            this.inspectData[i].inspectDatatwo[j].qtyLost = this.inspectData[i].qtyLost
            this.inspectData[i].inspectDatatwo[j].qcName = this.inspectData[i].qcName

            this.inspectData[i].inspectDatatwo[j].stdValue = this.inspectData[i].stdValue
            this.inspectData[i].inspectDatatwo[j].stdValueId = this.inspectData[i].stdValueId
            this.inspectData[i].inspectDatatwo[j].val1 = this.inspectData[i].val1
            this.inspectData[i].inspectDatatwo[j].val2 = this.inspectData[i].val2

            this.inspectData[i].inspectDatatwo[j].acMax = this.inspectData[i].acMax
            this.inspectData[i].inspectDatatwo[j].acMin = this.inspectData[i].acMin

            this.inspectData[i].inspectDatathree = []
            this.inspectData[i].inspectall = []
            if (this.inspectData[i].inspectDatatwo[j].chkId == 'F') {
              if (this.inspectData[i].inspectDatatwo[j].spcNo == null || this.inspectData[i].inspectDatatwo[j].spcNo == '') {
                errordata = true
                break
              }
            }
          }
        }
        if (errordata) {
          this.$message.warning('请选择不合格原因')
          return
        }
        // 检查不合格原因数据问题结束
        let qcRectrue = false
        for (let i = 0; i < this.inspectData.length; i++) {
          for (let j = 0; j < this.inspectData[i].inspectDatatwo.length; j++) {
            if (this.inspectData[i].qcType == '1') {
            } else {
              if (!this.inspectData[i].inspectDatatwo[j].qcRec && this.inspectData[i].inspectDatatwo[j].chkId == 'F') {
                qcRectrue = true
                break
              }
            }
          }
        }
        if (qcRectrue) {
          this.$message.warning('检验类型测量、合格判定为不合格，检验值必填')
          return
        }

        this.inspectDataflag = true
        for (let i = 0; i < this.inspectData.length; i++) {
          for (let j = 0; j < this.inspectData[i].inspectDatatwo.length; j++) {
            if (this.inspectData[i].qcType == '1') {
              if (this.inspectData[i].inspectDatatwo[j].chkId == 'F') {
                this.inspectData[i].inspectDatathree.push(this.inspectData[i].inspectDatatwo[j])
              }
            } else {
              if (this.inspectData[i].inspectDatatwo[j].qcRec !== null && this.inspectData[i].inspectDatatwo[j].qcRec !== '' && this.inspectData[i].inspectDatatwo[j].chkId == 'F') {
                this.inspectData[i].inspectDatathree.push(this.inspectData[i].inspectDatatwo[j])
              }
            }
            this.inspectData[i].inspectDatatwo[j].reorder = i + 1
            this.billData.formData.mesQcTy3s.push(this.inspectData[i].inspectDatatwo[j])
            if (this.inspectData[i].inspectDatatwo[j].qcRec !== null && this.inspectData[i].inspectDatatwo[j].qcRec !== '') {
              this.inspectData[i].inspectall.push(this.inspectData[i].inspectDatatwo[j])
            }
          }
        }

        for (let i = 0; i < this.inspectData.length; i++) {
          let obj = {};
          this.inspectData[i].inspectDatathree = copare(this.inspectData[i].inspectDatathree).reduce((item, next) => {
            obj[next.spcNo] ? "" : (obj[next.spcNo] = true && item.push(next));
            return item;
          }, []);

        }
        function copare(array) {
          let a = [] //定义一个空数组 a= []
          for (let i = 0; i < array.length; i++) {//for循环遍历每个元素，每遍历一个元素就是arr[i]
            let num = 0//定义一个num统计arr[i]出现的次数，
            for (let j = 0; j < array.length; j++) { //根据这个arr[i]做一次循环遍历这个数组，
              if (array[i].spcNo == array[j].spcNo) {//arr[i]出现一次就会+1
                num++
              }
            }
            if (num > 1) {
              array[i].qtyLost = num
            }
            if (num <= 1) {//如果只出现一次，那么就说明没有重复的，我们就把这个元素push到a数组中，如果出现多次，那么什么都不做，最后得到的a就是我们要的结果。
              array[i].qtyLost = num
              // a.push(array[i])
            }
          }
          return array
        }

        this.billData.formData.mesQcTy2s = this.inspectData
        for (let i = 0; i < this.billData.formData.mesQcTy2s.length; i++) {
          if (this.billData.formData.mesQcTy2s[i].qcType == '1') {
            if (this.billData.formData.mesQcTy2s[i].inspectall.length > 0) {
              for (let j = 0; j < this.billData.formData.mesQcTy2s[i].inspectDatathree.length; j++) {
                if (this.billData.formData.mesQcTy2s[i].inspectDatathree.length > 0) {
                  if (this.billData.formData.mesQcTy2s[i].inspectDatathree[j].chkId) {
                    this.billData.formData.mesQcTy1s.push(this.billData.formData.mesQcTy2s[i].inspectDatathree[j])
                  }
                }
              }
            }
          } else {
            if (this.billData.formData.mesQcTy2s[i].inspectall.length > 0) {

              for (let j = 0; j < this.billData.formData.mesQcTy2s[i].inspectDatathree.length; j++) {
                if (this.billData.formData.mesQcTy2s[i].inspectDatathree.length > 0) {
                  if (this.billData.formData.mesQcTy2s[i].inspectDatathree[j].qcRec) {
                    this.billData.formData.mesQcTy1s.push(this.billData.formData.mesQcTy2s[i].inspectDatathree[j])
                  }
                }
              }
            } else {
              this.inspectDataflag = false
              break
            }
          }
        }
        if (this.tableData){
          this.tableData.forEach(item => {
            if (item.distinguish) {
              delete item.distinguish
            }
          })
        }
        this.billData.formData.closeId = 'F'
        delete this.billData.formData.unitName
        delete this.billData.formData.qcTypeName
        delete this.billData.formData.whName
        if (this.billData.formData.zjNo) {
          qcSpcZjqcUpd({
            ...this.billData.formData,
            sys: this.billData.formData.sys ? 'T' : 'F',
            chkKnd: this.billData.formData.chkKnd ? 'T' : 'F',
            mesTiQcFileList: this.tableData,
          }).then((res) => {
            if (res.code === 0) {
              if (res.msg === 'success') {
                // this.getList()
                this.$message.success(res.data)
              } else {
                this.$message.error(res.data)
                return
              }
            }
          }).catch(err => this.requestFailed(err))
            .finally(() => {

            })

        }
        else {
          delete this.billData.formData.tiDd
          qcSpcZjqcAdd({
            ...this.billData.formData,
            sys: this.billData.formData.sys ? 'T' : 'F',
            chkKnd: this.billData.formData.chkKnd ? 'T' : 'F',
            mesTiQcFileList: this.tableData,
          }).then((res) => {
            if (res.code == 0) {
              if (res.msg === 'success') {
                this.billData.formData.zjDd = res.data.zjDd
                this.billData.formData.zjNo = res.data.zjNo
                // this.getList()
                this.$message.success('保存成功')
              } else {
                this.$message.error(res.data)
                return
              }
              if (res.data) {
                this.billData.formData = res.data
                if (this.billData.formData.sys == 'T') {
                  this.billData.formData.sys = true
                } else {
                  this.billData.formData.sys = false
                }
                if (this.billData.formData.chkKnd == 'T') {
                  this.billData.formData.chkKnd = true
                } else {
                  this.billData.formData.chkKnd = false
                }
              }
            }
          }).catch(err => this.requestFailed(err))
            .finally(() => {
            })
        }
    },

    //删除, 验证有数据才可删
    async handDelData() {
      if (this.billData.formData=== undefined && Object.keys(this.billData.formData).length === 0){
        this.$message.warning("暂无数据!")
        return;
      }
      this.$confirm('确定要该条单据吗？', '提示', {
        confirmButtonText: this.$t('public.sure'),
        cancelButtonText: this.$t('public.cancel'),
        type: 'warning'
      }).then(() => {
        qcSpcZjtyDel({
          zjNo: this.billData.formData.zjNo,
          usr: this.billData.formData.usr
        }).then((res) => {
          if (res.code === 0) {
            if (res.msg === 'success') {
              this.billData.formData={}
              this.billData.gridData=[]
              this.tableData=[]
              this.$router.push({ name: 'selftest' })
              // 确定按钮的回调
              this.$message({type: 'success', message: '删除成功！'});
            } else {
              this.$message.error(res.data)
            }
          }
        }).catch(err => {
          this.requestFailed(err)
        })

      }).catch(() => {
        // 取消按钮的回调
        this.$message({type: 'info', message: '已取消操作'});
      });

    },

    //打开查询窗口
    handQueryData() {
      this.$router.push({ name: 'selftest' })
    },

    //日期格式化 yyyy-MM-dd hh:mm:ss
    formatDate(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
  }
}

</script>

<style lang="less" scoped>
.grip-container{
  display: flex;
  flex: 1;
  gap: 10px;
  padding-left: 5px;
  padding-right: 5px;
}
.el-collapse {
  ::v-deep .el-collapse-item__header.is-active {
    border-bottom: 1px solid darkgrey;
  }

  ::v-deep .el-collapse-item__header {
    border-bottom: 1px solid darkgrey;
    font-size: 17px;
    font-weight: 520;
    padding-left: 20px;
  }

  ::v-deep .el-collapse-item__content {
    padding: 5px 5px;
    //border-bottom: red 5px solid;
  }
}
</style>
