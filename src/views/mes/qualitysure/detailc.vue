<template>
  <div class="consrm compoct">
    <div style="text-align:right;margin-right:16px;position: fixed;top: 90px;right:0px;z-index:999">

      <a-button style="margin-left: 8px" type="primary" @click="deldata" :disabled="isClaim"
                :style="isClaim ? 'background-color: #909399;border-color: #909399;color:#fff;' : 'background-color: #f56c6c;border-color: #f56c6c;color:#fff;'">删除</a-button>

      <a-button style="margin-left: 8px;" type="primary" @click="save" :disabled="isClaim"
                :style="isClaim ? 'background-color: #909399;border-color: #909399;color:#fff;' : ''">保存</a-button>
      <a-button type="primary" style="margin-left: 8px;    color: #909399;
    background: #f4f4f5;
    border-color: #d3d4d6;" @click="cosetiao">关闭</a-button>

    </div>
    <el-tabs type="border-card">
      <el-tab-pane label="首检详情">
        <el-row :gutter="10">
          <el-col :span="16" :xs="24">
            <div class="sup_info_basics_header">
              <div style="float: left;margin-left: 8px;">基础信息</div>
            </div>
            <div ref="leftHeight" style="border:1px solid #e5e5e5;margin:0 auto;padding: 10px;padding-top:5px;padding-bottom:5px;
            background-color:#fff;border-radius:10px;">
              <el-form label-width="100px" :model="entity" :rules="exitRules" ref="addEntityForm">
                <div style="border-bottom: 1px solid #E5E5E5;padding-bottom:10px;">
                  <el-row :gutter="0">
                    <el-col :span="8">
                      <el-form-item label="检验日期:" prop="tyDd">

                        <span>{{ entity.tyDd | formatDate }}</span>

                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="检验单号:" class="tyNo">

                        <span>{{ entity.tyNo }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">

                      <el-form-item label=" 检验方式:" prop="qcTypeName">
                        <div style="font-size:12px;"> {{entity.qcTypeName}}</div>

                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="0">
                    <el-col :span="8">
                      <el-form-item label="客户厂商:" prop="cusNo">

                        <span>{{ entity.cusNo }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('部门')" prop="dep">
                        <my-selectListwo v-if="isEdit" url="mes/basicData/depPage" :read-only="true"
                                         :tableColumn="$Column.salmDep" :form="$Form.salmDep" :data="data" name="dep"
                                         @choose="choose($event)" ref="selectList"
                                         v-decorator="['dep', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                                         :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>

                      </el-form-item>
                    </el-col>


                    <el-col :span="8">
                      <el-form-item label="送检单号:" class="tiNo">

                        <div @click="jumpinspection" style="cursor: pointer;color:#1890FF"><span>{{ entity.tiNo
                          }}</span></div>
                      </el-form-item>
                    </el-col>

                  </el-row>
                  <el-row :gutter="0">
                    <el-col :span="8">
                      <el-form-item label="检验人员:" prop="salNo">

                        <span>{{ entity.salNo }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="单据类别:" prop="bilType">

                        <span>{{ entity.bilType }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="实验室检验:" prop="sys">

                        <a-checkbox v-model="entity.sys" @change="onChangeLaboratory" disabled></a-checkbox>

                      </el-form-item>
                    </el-col>


                  </el-row>
                  <el-row :gutter="0">
                    <el-col :span="8">

                      <el-form-item label="检验结果:" prop="qcId">
                        <span v-if="entity.qcId == 'T'">合格</span>
                        <span v-if="entity.qcId == 'F'">不合格</span>



                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="复检:" prop="chkKnd">

                        <a-checkbox v-model="entity.chkKnd" @change="onChangeclosecase" disabled></a-checkbox>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>

                <div class="sup_info_item" style="border-bottom: 1px solid #E5E5E5;">
                  <el-row :gutter="0">
                    <el-col :span="8">
                      <el-form-item label="品号:" prop="prdNo">

                        <span>{{ entity.prdNo }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="品名:" prop="prdName">

                        <span>{{ entity.prdName }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="特征:">

                        <span>{{ entity.prdMark }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="0">
                    <el-col :span="8">
                      <el-form-item label="单位:">

                        <span>{{ entity.unitName }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="仓库:">

                        <span>{{ entity.whName }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="批号:">

                        <span>{{ entity.batNo }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="8">
                      <el-form-item label="工单号:">

                        <span>{{ entity.moNo }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="派工单号:">

                        <span>{{ entity.pgNo }}</span>
                      </el-form-item>
                    </el-col>

                    <el-col :span="8">
                      <el-form-item label="工序:">

                        <span>{{entity.zcName}}/{{ entity.zcNo }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="已转合格量:">

                        <span>{{ entity.qtyRtn }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="不合格评审单:">

                        <div @click="jumpnonconform" style="cursor: pointer;color:#1890FF"><span>{{ entity.lsNo
                          }}</span></div>
                      </el-form-item>
                    </el-col>

                    <el-col :span="24">
                      <el-form-item label="周转箱号:">

                        <span>{{ entity.boxNo }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label="备注:">
                        <el-input v-if="isEdit" v-model="entity.rem" size="mini" style="width:100%;"></el-input>
                        <span v-else>{{ entity.rem }}</span>
                      </el-form-item>
                    </el-col>

                  </el-row>
                </div>
                <div class="sup_info_item">

                  <el-row :gutter="0">
                    <el-col :span="8">
                      <el-form-item label="检验数量:">

                        <span>{{ entity.qty }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="合格量:">

                        <span>{{ entity.qtyOk }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="不合格量:">

                        <span>{{ entity.qtyLost }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>


                </div>
              </el-form>
            </div>
          </el-col>
          <el-col :span="8" :xs="24">



            <div class="sup_info_basics_header">
              <div style="float: left;margin-left: 8px;">附件</div>
            </div>
            <div class="sup_info_basics_container" :style="{ height: rightHeight }" style="">
              <div style="text-align: right;margin-top:10px;">
                <el-button type="success" @click="handleAddVersion($event)">上传附件</el-button>
              </div>


              <el-table v-loading="loading" stripe :data="tableData" highlight-current-row
                        :header-cell-style="{ backgroundColor: '#F4F5F9', fontWeight: '400' }"
                        style="width: 100%;margin-top:10px;" :height="tablerightHeight">


                <el-table-column label="附件">
                  <template v-slot="{row}">
                    <span class="file-name" @click="handleDown(item)">{{ row.original }}</span>


                  </template>
                </el-table-column>
                <el-table-column prop="materialName" label="操作">
                  <template slot-scope="scope">
                    <el-button :loading="loading" type="primary" size='mini' @click="seepic(scope.$index)">查看
                    </el-button>
                    <el-button size='mini' type="primary" @click="handleDelVersion(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>

            </div>



          </el-col>
        </el-row>
        <a-modal title="不合格处理" destroyOnClose width="50%" :visible.sync="bhgvisible" @cancel="bhghandleCancel"
                 @ok="bhghandleCancel">
          <div class="table-page-search-wrapper">
            <div style="font-size:12px;">不合格评审单：{{entity.lsNo}}</div>
            <div style="text-align: right;">
              <a-button style="margin-left: 8px;background-color: #f56c6c;border-color: #f56c6c;color:#fff;"
                        type="primary" :disabled="!entity.lsNo"
                        :style="!entity.lsNo ? 'background-color: #909399;border-color: #909399;color:#fff;' : 'background-color: #f56c6c;border-color: #f56c6c;color:#fff;'"
                        @click="deldatabhg">删除</a-button>
              <a-button style="margin-left: 8px" type="primary" @click="savebhg">保存</a-button>
            </div>

            <div ref="chuzhiref" style="border:1px solid #e5e5e5;margin:0 auto;padding: 10px;padding-top:5px;padding-bottom:5px;
      background-color:#fff;border-radius:10px;">

              <div class="sup_info_basics_header">
                <div style="float: left;margin-left: 8px;">处置结果:</div>
              </div>
              <div style="padding-top:5px;">
                <el-button size="mini" icon="el-icon-plus" id="add_table" type="primary"
                           @click="addTableRowtwo()"></el-button>
                <el-button size="mini" icon="el-icon-minus" id="add_table" type="primary"
                           :style="'background-color: #f56c6c;border-color: #f56c6c;color:#fff;'"
                           @click="locaDelTableRowtwo()"></el-button>
              </div>
              <el-table ref="multipleTabletwo" stripe border :data="mesLsQc1s"
                        style="margin-top:5px;width:100%;height:300px;overflow: scroll;"
                        @selection-change="handleSelectionresult">
                <el-table-column type="selection" width="48" :selectable="changeSelectable"></el-table-column>
                <el-table-column prop="czNo" align="left" label="处理方式">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.czNo" :disabled="scope.row.bilNo" placeholder="请选择检验类型" size="mini"
                               clearable @input="clfsinput(scope.$index,scope.row, $event)" style="width:100%;">
                      <el-option v-for="item in disposalresults" :key="item.value" :label="item.label"
                                 :value="item.value"></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="处理数量" width="110">
                  <template slot-scope="scope">
                    <el-input type="number" class="cell-input" size="mini" @input="changeprocess(scope)"
                              @wheel.native.prevent="stopScroll($event)" v-model="scope.row.qty"></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="bilNo" width="120" align="left" label="生成单据"></el-table-column>
                <el-table-column prop="rem" align="left" label="备注">
                  <template slot-scope="scope">
                    <el-input class="cell-input" type="textarea" style="width:100%;"
                              :autosize="{ minRows: 1, maxRows: 2}" size="mini" v-model="scope.row.rem"></el-input>
                  </template>
                </el-table-column>
              </el-table>
              <el-form label-width="120px" :model="entity" :rules="exitRules" ref="addEntityForm">
                <el-row :gutter="0" style="margin-top:15px;">
                  <el-col :span="12">
                    <el-form-item label="确认人:" class="lsNo">
                      <my-selectListwo url="mes/basicData/salData" :read-only="true" :tableColumn="$Column.salm"
                                       :form="$Form.salm" :data="dataczSal" name="salNo" @choose="chooseczSal($event)" ref="selectList"
                                       v-decorator="['salNo', { rules: [{ required: true, message:'请选择确认人' } ] }]"
                                       :placeholder="'请选择确认人'"></my-selectListwo>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="日期:" prop="czDate">
                      <a-date-picker style="width:100%" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                                     v-model="entity.czDate" placeholder="日期" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </div>
          <template slot="footer">
            <a-button @click="bhghandleCancel">关闭</a-button>
          </template>
        </a-modal>

        <div class="sup_info_basics_header">
          <div style="float: left;margin-left: 8px;">不合格原因</div>
        </div>
        <el-row>
          <el-col :span="22">
            <div class="sup_info_basics_container" style="padding-bottom:5px;">
              <div style="padding-top:5px;">
                <el-button size="mini" icon="el-icon-plus" id="add_table" @click="addTableRow()"></el-button>
                <el-button size="mini" icon="el-icon-minus" id="add_table" @click="locaDelTableRow()"></el-button>
              </div>
              <el-table ref="multipleTable" border :data="testData" class="tableheight bordervisib" style="width: 100%;"
                        height="150" @row-click="handRowClick" @selection-change="handleSelectionChangereason">

                <el-table-column type="selection" width="48"></el-table-column>
                <el-table-column align="center" type="index" width="38"></el-table-column>
                <el-table-column label="原因代号" width="100" height="30" v-if="colData[0].istrue">
                  <template slot-scope="scope">
                    <el-input @focus="focus(scope.$index,scope.row, $event)" @blur="blurNameInput"
                              @input="inputNameInput" ref="prdt3Input" v-model="scope.row.spcNo"
                              style="border: none;font-size:12px;height:10px;padding:0"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="不合格原因" v-if="colData[1].istrue">
                  <template slot-scope="scope">
                    {{scope.row.spcName}}

                  </template>
                </el-table-column>
                <el-table-column label="不合格量" width="100">
                  <template slot-scope="scope">
                    <el-input type="number" class="cell-input" size="mini" :min="0" @input="changeqtyLost(scope)"
                              v-model="scope.row.qtyLost" :disabled="!scope.row.spcNo"
                              @wheel.native.prevent="stopScroll($event)"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="不合格量(副)" width="100" prop="qty1Lost">
                  <template slot-scope="scope">
                    <el-input type="number" class="cell-input" size="mini" @input="changeqty1Lost(scope)"
                              @change="changeqty1Lost(scope)" v-model="scope.row.qty1Lost" :disabled="!scope.row.spcNo"
                              @wheel.native.prevent="stopScroll($event)"></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="zcNo" width="300" align="left" label="工序">
                  <template slot-scope="scope">
                    <my-selectListwo url="mes/zcNo/curt" :read-only="true" :tableColumn="$Column.zcNotwo"
                                     :form="$Form.zcNotwo" :data="scope.row.zcNo + ' ' + scope.row.zcName" name="zcNotwo"
                                     @choose="choosezcNo($event,scope)" allowClear ref="selectList"
                                     v-decorator="['zcNo', { rules: [{ required: true, message:'请选择工序' } ] }]"
                                     placeholder="请选择工序"></my-selectListwo>
                  </template>
                </el-table-column>
                <el-table-column prop="ygNo" width="300" align="left" label="作业人员">
                  <template slot-scope="scope">
                    <!-- <el-input size="mini" type="textarea" clearable placeholder="作业人员" readonly v-model="scope.row.ygNo"
                      style="width:80%" :autosize="{ minRows: 1, maxRows: 2}">
                      <template slot="append"><a-icon type="database" /></template>
                    </el-input> -->
                    <a-input-search v-model="scope.row.ygNo" @search="selectzyry(scope)" readOnly>
                      <a-button ref="btn" slot="enterButton">
                        <a-icon type="database" />
                      </a-button>
                    </a-input-search>
                    <!-- <my-selectListwo url="mes/salm/query" :read-only="true" :tableColumn="$Column.salmmult"
                      :form="$Form.salm" :data="scope.row.ygNo" multiple name="salNo"
                      @choose="choosescope($event,scope)" allowClear ref="selectList"
                      v-decorator="['salNo', { rules: [{ required: true, message:'请选择作业人员' } ] }]"
                      placeholder="请选择作业人员"></my-selectListwo> -->
                  </template>
                </el-table-column>
                <el-table-column prop="dep" width="180" align="left" label="制造部门">
                  <template slot-scope="scope">
                    <my-selectListwo url="mes/basicData/depPage" :read-only="true" :tableColumn="$Column.salmDep"
                                     :form="$Form.salmDep" :data="scope.row.dep + ' ' + scope.row.depName" name="dep"
                                     @choose="choosedan($event,scope)" allowClear ref="selectList"
                                     v-decorator="['dep', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                                     :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
                  </template>
                </el-table-column>
                <el-table-column label="质量单价" width="100">
                  <template slot-scope="scope">
                    <el-input type="number" class="cell-input" size="mini" v-model="scope.row.qup"
                              :disabled="!scope.row.spcNo" @wheel.native.prevent="stopScroll($event)"></el-input>
                  </template>
                </el-table-column>
              </el-table>

              <!--focus激活选择货品  -->
              <div @mouseleave="prdt3leave">
                <el-popover placement="bottom" :style="objStyle" width="400" style="position: fixed;z-index: 99999;"
                            v-model="visible">
                  <div>
                    <el-table class="el-table" :data="pickerList" stripe max-height="360px" size="mini" height="360px"
                              highlight-current-row :cell-style="{ verticalAlign: 'top' }"
                              :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                              style="width: 100%;">
                      <el-table-column fixed="left" align="center" label="选择" width="80">
                        <template slot-scope="scope">
                          <el-button round type="primary" style="padding: 5px 5px 5px 5px;" size="mini"
                                     icon="el-icon-d-arrow-right" @click="getGoodsPopover(scope.row)">选择</el-button>
                        </template>
                      </el-table-column>
                      <el-table-column fixed="left" prop="spcNo" align="left" label="原因代号" width="70"></el-table-column>

                      <el-table-column prop="name" align="left" label="不合格原因" width="130"></el-table-column>
                    </el-table>
                    <div>
                      <el-pagination background :pager-count="5" :page-sizes="[5,10, 30, 50]" :page-size="10"
                                     @size-change="pageSizeChangespcNo" :current-page="tablePagespcNo.currentPage"
                                     @current-change="currentChangespcNo" layout="prev, pager, next" :total="spcNototal"
                                     style="margin-top: 5px;"></el-pagination>
                    </div>
                  </div>

                </el-popover>
              </div>
            </div>
          </el-col>
          <el-col :span="2" :xs="24">
            <el-button size="mini" type="primary"
                       :disabled="!(this.testData.length>0 && this.entity.tyNo && +this.entity.qtyLost > 0)"
                       @click="bhghandle()">不合格处理</el-button>
          </el-col>
        </el-row>
        <a-card :bordered="false">

          <a-row :gutter="8">
            <a-col :span="24">
              <a-row>

<!--                <uploadFile ref="uploadFile" @seeFile="seeFile" />-->
              </a-row>
            </a-col>

          </a-row>
        </a-card>
<!--        <ModalPic ref="ModalPic" />-->
        <selectall ref="refzyry" @zyList="zyList" />

      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import {
  lsqcupd, lsqcadd, prdZFQty, getItem, datadepPage, qctyDel, qcyjlnfo, qcaddsave, qcdjInfodetail, qcUpdedit, spcLstpage, sysfiledel,
} from '@/api/mes/quality'
import { mapGetters, mapState } from 'vuex'
// import uploadFile from './component/uploadFile'

// import ModalPic from '../inspectionForm/ModalPic'
import moment from 'moment'
import MySelectListwo from '@/components/MySelectListwo'
import selectall from '@/components/selectall'

export default {
  components: {
    // uploadFile,
    // ModalPic,
    MySelectListwo,
    selectall
  },
  data() {
    return {
      spcNo: '',
      isEdit: true,
      mes_quality_query: 'mes_quality_query',
      mes_quality_reset: 'mes_quality_reset',
      mes_quality_newOpen: 'mes_quality_newOpen',
      mes_quality_inspection: 'mes_quality_inspection',
      mes_quality_pause: 'mes_quality_inspection',
      mes_quality_continues: 'mes_quality_continues',
      mes_quality_task: 'mes_quality_task',
      queryParam: {},
      tableData: [],
      tableDatatwo: [{
        qty: '1',
        unit: 'gdfgdf',
        up: '3',
        bDd: '',
        eDd: '',
        qcNo: '',
        curId: null,
        rem: '',
        wyID: '',
        bId: 1
      }, {
        qty: '2',
        unit: 'gdsdsfg',
        up: '7',
        bDd: '',
        eDd: '',
        qcNo: '',
        curId: null,
        rem: '',
        wyID: '',
        bId: 2
      }, {
        qty: '',
        unit: '',
        up: '',
        bDd: '',
        eDd: '',
        qcNo: '',
        curId: null,
        rem: '',
        wyID: '',
        bId: 3
      }, {
        qty: '',
        unit: '',
        up: '',
        bDd: '',
        eDd: '',
        qcNo: '',
        curId: null,
        rem: '',
        wyID: '',
        bId: 4
      }, {
        qty: '',
        unit: '',
        up: '',
        bDd: '',
        eDd: '',
        qcNo: '',
        curId: null,
        rem: '',
        wyID: '',
        bId: 5
      }, {
        qty: '',
        unit: '',
        up: '',
        bDd: '',
        eDd: '',
        qcNo: '',
        curId: null,
        rem: '',
        wyID: '',
        bId: 6
      }, {
        qty: '',
        unit: '',
        up: '',
        bDd: '',
        eDd: '',
        qcNo: '',
        curId: null,
        rem: '',
        wyID: '',
        bId: 7
      }, {
        qty: '',
        unit: '',
        up: '',
        bDd: '',
        eDd: '',
        qcNo: '',
        curId: null,
        rem: '',
        wyID: '',
        bId: 8
      },
      ],
      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 5,
        total: 0
      },
      row: {},
      rowIndex: {},
      // 按钮控制
      btn: {
        newOpen: true, // 首检开工
        open: true, // 开始
        pause: true, // 暂停
        continues: true, // 继续
        report: true, // 报工
        exceptional: true, // 异常
        inspection: true, // 送检
        introspecting: true // 自检
      },
      queryParam: {
        date: [],
      },
      activeName: '1',
      valueone: [],
      visible: false,
      reasondata: [],
      multiplereason: [],
      entity: {
        czDate: '',
        tyDd: '',
        chkKnd: '',
        qcId: '',
        sys: '',
        chktyId: '',
        tiNo: '',
        tyNo: '',
        prdt3Name: "",
        way: 2,
        auxId: "",
        otRemark: "",
        bxId: "",
        qaId: "",
        otId: "",
        bxNo: "",
        bxDd: "",
        status: "",
        statusMsg: "",
        kndId: 3,
        applyDepId: null,
        applyDepNo: "",
        applyDepName: "",
        applyUserId: "",
        applyUserNo: "",
        applyUserName: "",
        prdId: "",
        prdNo: "",
        prdName: "",
        prdt3Id: "",
        prdSpc: "",
        faultRem: "",
        cntId: "",
        cntName: "",
        cntTel: "",
        cntAdr: "",
        dcId: "",
        dcName: "",
        dcLmt: "",
        urgent: 0,
        finId: "",
        finName: "",
        finLmt: "",
        bxPic1: "",
        bxPic2: "",
        bxPic3: "",
        serverDeptId: "",
        otUserId: "",
        qty: null,
        serviceCode: "",
        coDd: null,
        prdUt: "",
        bxType: '1',
        initialTime: '',
        completeTime: ''
      },
      inspectiontype: [
        { value: '1', label: '完工检验' },
        { value: '2', label: '首检检验' },
        { value: '3', label: '托工检验' },
      ],
      multipleSelectiontwo: [],
      sysFiles: [],
      exitRules: {
        dep: [{ required: true, message: "必填:部门", trigger: ["change", "blur"] }],
      },
      colData: [
        { title: "原因代号", istrue: true },
        { title: "不合格原因", istrue: true },
        { title: "不合格量", istrue: true },
        { title: "货品名称", istrue: true },
        { title: "货品代号", istrue: true },
        { title: "规格", istrue: true },
        { title: "现有库存", istrue: true },
        { title: "借出量", istrue: true },
        { title: "单位", istrue: true },
        { title: "单价", istrue: true },
        { title: "数量", istrue: true },
        { title: "已还数量", istrue: true },
        { title: "税率%", istrue: true },
        { title: "未税金额", istrue: true },
        { title: "税额", istrue: true },
        { title: "金额", istrue: true },
      ],
      objStyle: {
        top: "433px",
        left: "",
      },
      pickerList: [],
      testData: [],
      detailIds: [],
      multipleSelection: [],
      pickerIndex: 0,
      isShowPopVel: false,
      rightHeight: 0,
      tablerightHeight: 0,
      tyNotwo: '',
      tyDdtwo: '',
      flag: false,
      ccc: true,
      isClaim: false,
      data: '',
      tableLoading: false,
      bottomWidth: '',
      queryKeyword: '',
      spcNototal: -1,
      tablePagespcNo: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      mesLsQc1s: [],
      bhgvisible: false,
      dataczSal: '',
      disposalresults: [
        { value: '5', label: '放行' },
        { value: '2', label: '报废' },
        { value: '3', label: '返工' },
        { value: '7', label: '复检（全检）' },
        { value: '8', label: '复检（抽检）' },
        { value: '9', label: '重新首检' },
        { value: '1', label: '强制缴库' },
      ],
      multipleSelectionresult: [],
      no: [],
      selectrow: ''

    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    }),
    ...mapGetters(['permissions'])
  },
  created() {

    this.sysFiles = []
    // this.tableAdd()
    var data1 = moment().subtract(30, "days").format('YYYY-MM-DD')
    var data2 = moment(new Date()).format('YYYY-MM-DD')
    this.valueone = [data1, data2]
    if (localStorage.getItem('shoujiantiNo')) {
      this.entity.tiNo = localStorage.getItem('shoujiantiNo')
      this.getListtreat()
    } else {
      this.entity.tyNo = localStorage.getItem('shoujiantyNo')
      this.getList()
    }

    //     this.startTime = this.$moment().subtract(30, "days").format('YYYY-MM-DD');
    // this.endTime = this.$moment(new Date()).format('YYYY-MM-DD');
  },
  mounted() {
    this.pickerSearch()
    this.$nextTick(() => {
      this.rightHeight = this.$refs.leftHeight.offsetHeight + 6 + 'px'
      this.bottomWidth = this.$refs.leftHeight.offsetWidth + 6 + 'px'
      // :height="tablerightHeight"
      this.tablerightHeight = this.$refs.leftHeight.offsetHeight - 65 + 'px'
    })
  },
  methods: {
    zyList(list) {
      this.testData.forEach((item, index) => {
        if (this.selectrow == index) {
          item.ygNo = list
          this.$set(this.testData, index, item);
        }
      })
    },
    selectzyry(scope) {
      this.selectrow = scope.$index
      this.$refs.refzyry.open(this.typeGroupNo)
    },
    choosezcNo(obj, scope, clear) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.testData.forEach((item, index) => {
            if (scope.$index == index) {
              item.zcNo = ''
              item.zcName = ''
            }
            this.$set(this.testData, index, item);
          })
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'zcNotwo') {
        this.testData.forEach((item, index) => {
          if (scope.$index == index) {
            item.zcNo = obj.obj.data.zcNo
            item.zcName = obj.obj.data.zcName
          }
          this.$set(this.testData, index, item);
        })
      }
    },
    chooseczSal(obj) {
      if (obj.obj.name === 'salNo') {
        // this.entity.salName = obj.obj.data.name
        this.entity.czSal = obj.obj.data.salNo
      }
    },
    choosescope(obj, scope, clear) {
      this.no = []
      debugger
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.testData.forEach((item, index) => {
            if (scope.$index == index) {
              item.ygNo = ''
              this.$set(this.testData, index, item);
            }
          })
          return
        }
      }
      if (obj.obj.name === 'salNo') {
        let arr = []
        if (obj.obj.data != null) {
          arr = obj.obj.data
          arr.forEach((i) => {
            this.no.push(i.salNo)
          })
        }
        // this.data = obj.obj.value
        // this.cusNameone = obj.obj.value
        const no = new Set(this.no)
        this.testData.forEach((item, index) => {
          if (scope.$index == index) {
            debugger
            // item.ygNo = obj.obj.data.salNo
            console.log(no, 'btttttttttt')
            item.ygNo = [...no].join(',')
            console.log(item.ygNo, 'hhhhhhhhhhh')
            this.$set(this.testData, index, item);
          }
        })
        console.log(this.testData, 'sshhhhhhhhhh')
      }
    },
    changeqty1Lost(val) {
      if (val.row.qty1Lost < 0) {
        this.$message.warning('不合格量(副)不能为负')
        val.row.qty1Lost = 0
      }
      this.entity.qty1Lost = 0
      this.testData.forEach(i => {
        if (i.spcNo) {
          this.entity.qty1Lost += +i.qty1Lost;
        }
      })
      this.entity.qty1Lost = parseFloat(this.entity.qty1Lost.toFixed(2))
      this.$forceUpdate()
    },
    // choosescope(obj,scope) {
    //   console.log(obj,scope,'9999999999')
    //   if(obj.obj.clear){
    //     if(obj.obj.clear == 1){

    //       // this.queryParam.dep=''
    //       // this.queryParam.depName=''
    //       return
    //     }
    //   }
    //   var map = {}
    //     if (obj.obj.name === 'salNo') {

    //       this.flag = true
    //       this.queryParam.name = obj.obj.data.name
    //       if(obj.obj.data.salNo){
    //         // this.queryParam.usr = obj.obj.data.salNo
    //         // this.data = this.queryParam.ygNo+ '-' + this.queryParam.name
    //         this.testData.forEach((item,index)=>{
    //           if(scope.$index == index){
    //             item.usr = obj.obj.data.salNo
    //             // item.name = obj.obj.data.name
    //           }
    //           this.$set(this.testData, index, item);

    //         })
    //         console.log(this.testData,'gggggggggggg')
    //       }else{
    //         // this.data = ''
    //         // this.queryParam.salNo=''
    //         // this.queryParam.name=''
    //       }
    //     }
    // },
    choosedan(obj, scope) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.testData.forEach((item, index) => {
            if (scope.$index == index) {
              item.dep = ''
              item.depName = ''
            }
            this.$set(this.testData, index, item);
          })
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'dep') {
        this.flag = true
        // this.queryParam.depName = obj.obj.data.name
        if (obj.obj.data.deptCode) {
          // this.queryParam.dep = obj.obj.data.deptCode
          this.datatwo = obj.obj.data.deptCode + '-' + obj.obj.data.name
          // multipleSelectiontwo
          this.testData.forEach((item, index) => {
            if (scope.$index == index) {
              item.dep = obj.obj.data.deptCode
              item.depName = obj.obj.data.name
            }
            this.$set(this.testData, index, item);
          })
        } else {
          // this.datatwo = ''
          // this.queryParam.dep=''
          // this.queryParam.depName=''
        }
      }
    },
    changeprocess(scope) {
      if (scope.row.qty < 0) {
        this.$message.warning('数量不能为负')
        scope.row.qty = 0
      }
      let bhgsl = 0
      this.mesLsQc1s.forEach(i => {
        bhgsl += +i.qty;
      })
      if (bhgsl > this.entity.qtyLost) {
        this.$message.info('不合格量超出')
        scope.row.qty = ''
      }
      this.$forceUpdate()
    },
    clfsinput(index, row, e) {
      let clfsdata = JSON.parse(JSON.stringify(this.mesLsQc1s))
      const result = clfsdata.filter((item, index2) => {
        if (index2 !== index) {
          if (item.czNo == row.czNo) {
            return true
          }
        }
      })
      console.log(result, 'w1sgggggggggg')
      if (result.length) {
        row.czNo = ''
        this.$message.warning('不允许重复选择')
      }
    },
    handleSelectionresult(rows) {
      this.multipleSelectionresult = rows;
    },
    changeSelectable(row, index) {
      return !row.bilNo
    },
    savebhg() {
      let errordata = false
      for (let j = 0; j < this.mesLsQc1s.length; j++) {
        if (this.mesLsQc1s[j].czNo == '2' || this.mesLsQc1s[j].czNo == '3') {
          if (this.mesLsQc1s[j].qty == null || this.mesLsQc1s[j].qty == '') {
            errordata = true
            break
          }
        }
      }
      if (errordata) {
        this.$message.warning('处置结果为报废返修栏位，数量必填')
        return
      }
      //处置结果
      // this.mesLsQc1s
      this.mesLsQc1s = this.mesLsQc1s.filter((item) => {
        return item.czNo !== '' && item.czNo !== null
      })
      debugger
      let mesLsQc1svalue = false
      if (this.mesLsQc1s.length) {
        for (let j = 0; j < this.mesLsQc1s.length; j++) {
          if (this.mesLsQc1s[j].qty) {
            mesLsQc1svalue = true
            break
          }
        }
      } else {
        this.$message.warning('请填写处置结果表格')
        return
      }
      if (mesLsQc1svalue) {
        if (this.entity.czSal) { } else {
          this.$message.warning('请选择处置结果确认人')
        }
        if (this.entity.czDate) { } else {
          this.$message.warning('请选择处置结果日期')
        }
        if (this.entity.czSal && this.entity.czDate) {
        } else {
          return
        }
      } else {
        this.$message.warning('请填写处置结果表格')
        return
      }

      let mesQcTy1s = []
      if (this.testData) {
        mesQcTy1s = this.testData.filter((item) => {
          return item.spcNo !== "" && item.spcNo !== null;
        })
      }
      if (this.entity.lsNo) {
        lsqcupd({
          ...this.entity,
          mesLsQc1s: this.mesLsQc1s,
          sys: this.entity.sys ? 'T' : 'F',
          chkKnd: this.entity.chkKnd ? 'T' : 'F',
          mesQcTy1s: mesQcTy1s
          // mesTiQcFileList:this.tableData,
        }).then((res) => {
          if (res.code === 0) {
            if (res.msg === 'success') {
              this.$message.success(res.data)
              localStorage.setItem("shoujiantyNo", res.data.tyNo)
              this.getList()
              this.bhgvisible = false
            } else {
              this.$message.error(res.data)
              return
            }
          }
        }).catch(err => this.requestFailed(err))
          .finally(() => {
            this.loading = false
          })

      } else {

        // delete this.entity.tiDd
        lsqcadd({
          ...this.entity,
          mesLsQc1s: this.mesLsQc1s,
          sys: this.entity.sys ? 'T' : 'F',
          chkKnd: this.entity.chkKnd ? 'T' : 'F',
          mesQcTy1s: mesQcTy1s
          // mesTiQcFileList:this.tableData,
        }).then((res) => {
          if (res.code == 0) {
            if (res.msg === 'success') {
              this.entity.lsNo = res.data.lsNo
              localStorage.setItem("shoujiantyNo", res.data.tyNo)
              localStorage.removeItem('shoujiantiNo')
              this.getList()
              this.$message.success('保存成功')
              // if(localStorage.getItem('completiontwotiNo')){
              //   this.entity.tiNo = localStorage.getItem('completiontwotiNo')
              //   this.getListtreat()
              // }else{
              //   this.entity.tyNo = localStorage.getItem('completiontwotyNo')
              //   this.getList()
              // }
              this.bhgvisible = false
            } else {
              this.$message.error(res.data)
              return
            }
          }
        }).catch(err => this.requestFailed(err))
          .finally(() => {
            this.loading = false
          })
      }
    },
    deldatabhg() {
      const that = this
      if (that.entity.lsNo) {
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('identifier.delcont'),
          okText: this.$t('public.sure'),
          okType: 'warn',
          cancelText: this.$t('public.cancel'),
          onOk() {
            that.loading = true
            lsqcdel({
              lsNo: that.entity.lsNo,
              usr: that.entity.usr
            }).then((res) => {
              if (res.code === 0) {
                if (res.msg === 'success') {
                  that.bhgvisible = false
                  if (localStorage.getItem('completiontwotiNo')) {
                    that.entity.tiNo = localStorage.getItem('completiontwotiNo')
                    that.getListtreat()
                  } else {
                    that.entity.tyNo = localStorage.getItem('completiontwotyNo')
                    that.getList()
                  }
                  that.$message.success(res.data)
                } else {
                  that.$message.error(res.data)
                }
              }
            }).catch(err => that.requestFailed(err))
              .finally(() => {
                that.loading = false
              })
          },
          onCancel() { }
        })
      }
    },
    addTableRowtwo() {
      this.tableAddtwo();
    },
    tableAddtwo() {
      for (let i = 0; i < 3; i++) {
        let obj = {
          czNo: "",
          qty: "",
          rem: "",
          id: null,
          bilNo: null
        };
        this.mesLsQc1s.push(obj);
      }
    },
    bhghandleCancel() {
      this.mesLsQc1s = []
      this.bhgvisible = false
    },
    bhghandle() {
      if (this.testData.length > 0 && this.entity.tyNo && +this.entity.qtyLost > 0) {
        if (JSON.parse(JSON.stringify(this.entity.mesLsQc1s))) {
          this.mesLsQc1s = []
          this.mesLsQc1s = JSON.parse(JSON.stringify(this.entity.mesLsQc1s))
        } else {
          this.mesLsQc1s = []
        }
        this.bhgvisible = true
        this.addTableRowtwo()
        console.log(this.$store.state.user.info.username, 'vvvghhhhhhhhhhhh')
        this.entity.czSal = this.$store.state.user.info.username
        this.dataczSal = this.$store.state.user.info.username
        this.entity.czDate = moment(new Date()).format('YYYY-MM-DD')
      } else {
        this.$message.warning('不符合条件')
      }
    },
    currentChangespcNo(currentPage) {
      this.tablePagespcNo.currentPage = currentPage;
      this.pickerSearch();
    },
    pageSizeChangespcNo(pageSize) {
      this.tablePagespcNo.pageSize = pageSize;
      this.pickerSearch();
    },
    jumpnonconform() {
      this.$router.push({
        path: '/mes/nonconform/detail',
        query: {
          lsNo: this.entity.lsNo
        }
      })
    },
    jumpinspection() {
      localStorage.setItem("soujiansong", this.entity.tiNo);
      this.$router.push({
        path: '/mes/inspectionForm/check',
        query: {
          tiNo: this.entity.tiNo
        }
      })
    },
    stopScroll(evt) {
      evt = evt || window.event;
      if (evt.preventDefault) {
        // Firefox
        evt.preventDefault();
        evt.stopPropagation();
      } else {
        // IE
        evt.cancelBubble = true;
        evt.returnValue = false;
      }
      return false;
    },
    cosetiao() {
      if (this.entity.tyNo) {
        this.$router.push({
          name: 'qualitysure',
          params: {
            tab: 'second'
          }
        })
      } else {
        this.$router.push({
          name: 'qualitysure',
          params: {
            tab: 'first'
          }
        })
      }
    },
    handleDelVersion(row) {
      var list = []
      //  { id, fileName,bucketName }
      if (row.distinguish) {
        this.tableData = this.tableData.filter(i => i.id !== row.id)
      } else {
        sysfiledel(
          {
            id: row.id,
            // fileName:row.fileName,
            // bucketName:row.bucketName,
            // idList:list
          }
        )
          .then(res => {
            if (res.msg == 'success') {
              this.tableData = this.tableData.filter(i => i.id !== row.id)
              this.$message.success('删除成功')
            } else {
              this.$message.success('删除失败')
            }
            // if(localStorage.getItem('shoujiantiNo')){
            //   this.getListtreat()
            // }else{
            //   this.getList()
            // }
            this.loading = false
            // this.getList()
          })
          .catch(err => {
            this.loading = false
            this.requestFailed(err)
          })

      }
      //  this.tableData.forEach((item, index) => {
      //     if(item.id === id){
      //       item.contraFileUrlList.forEach((item2, index2) => {
      //       list.push(item2.id)
      //       })
      //     }
      //  })
    },
    handleAddVersion() {
      // this.$refs.uploadFile.create({ title: '上传' }, 'oooooooooo')
      // this.$refs.AddVersion.create(true, true)
    },
    deldata() {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('identifier.delcont'),
        okText: this.$t('public.sure'),
        okType: 'warn',
        cancelText: this.$t('public.cancel'),
        onOk() {
          that.loading = true
          qctyDel({
            tyNo: that.entity.tyNo,
            usr: that.entity.usr
          }).then((res) => {
            if (res.code === 0) {
              if (res.msg === 'success') {
                that.cosetiao()
                that.$message.success(res.data)
              } else {
                that.$message.error(res.data)
              }
            }
          }).catch(err => that.requestFailed(err))
            .finally(() => {
              that.loading = false
            })
        },
        onCancel() { }
      })


    },
    // 渲染组件
    choose(obj) {

      var map = {}
      // if (obj.obj.name === 'upSalNo') {
      //   this.salName = obj.obj.data.name
      //   map[obj.obj.name] = obj.obj.data.salNo
      // }
      // if (obj.obj.name === 'salNo') {
      //   this.salName = obj.obj.data.name
      //   map[obj.obj.name] = obj.obj.data.salNo
      // }
      if (obj.obj.name === 'dep') {
        this.flag = true
        this.entity.depName = obj.obj.data.name
        this.entity.dep = obj.obj.data.deptCode
        this.data = this.entity.dep + '-' + this.entity.depName
      }
      // this.form.setFieldsValue(map)
    },
    changeqtyLost(val) {
      if (val.row.qtyLost < 0) {
        this.$message.warning('不合格量不能为负')
        val.row.qtyLost = ''
        return
      }
      if (val.row.qtyLost == 0) {
        this.$message.warning('不合格量不能为零')
        val.row.qtyLost = ''
        return
      }
      this.entity.qtyLost = 0
      let getQtys = 0;
      if (!this.entity.qtyLost) {
        this.entity.qtyLost = 0
      }
      this.testData.forEach(i => {
        if (i.spcNo) {
          this.entity.qtyLost += +i.qtyLost;
        }
      })
      if (this.entity.qtyLost > 0) {
        this.entity.qcId = 'F'
        if (+this.entity.qty - +this.entity.qtyLost < 0) {

          this.$message.info('不合格量超出')
          val.row.qtyLost = ''
          this.entity.qtyLost = 0
          this.testData.forEach(i => {
            if (i.spcNo) {
              this.entity.qtyLost += +i.qtyLost;
            }
          })
        } else {
          this.entity.qtyOk = +this.entity.qty - +this.entity.qtyLost
        }
      } else {
        this.entity.qcId = 'T'
      }
      this.$forceUpdate()
      prdZFQty({
        prdNo: this.entity.prdNo,
        type: 'z',
        qty: val.row.qtyLost
      }).then((res) => {
        if (res) {
          val.row.qty1Lost = res.data.qty1
          this.$forceUpdate()
          this.changeqty1Lost(val)
          console.log(this.testData, '66666')
        }
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 货品关键字搜索回显数据
    getGoodsPopover({
                      spcNo,
                      name,
                      prd3No,
                      spc,
                      qtyNow,
                      qtyLrn,
                      ut,
                      price,
                      qty,
                      rtoTax,
                      amtnNet,
                      tax,
                      amt,
                      itm,
                      id,
                      qty1Lost,
                      ygNo,
                      dep,
                      qup,
                      qtyLost,
                    }) {
      let newRow = {
        spcNo,
        spcName: name,
        prdNo: prd3No,
        spc,
        qtyNow,
        qtyLrn,
        ut,
        price,
        qty: "1",
        lastQty: 0,
        rtoTax: rtoTax,
        amtnNet: "",
        tax: "",
        amt: "",
        itm: -1,
        prdtId: id,
        id: null,
        qtyRtn: null,
        qty1Lost,
        ygNo,
        dep,
        qup,
        qtyLost,
      };
      newRow.show = false;
      // this.$set(this.testData, this.pickerIndex, newRow);
      this.testData[this.pickerIndex].spcNo = spcNo
      this.testData[this.pickerIndex].spcName = name
      this.isShowPopVel = false;
      // let getQtys = 0;
      // this.testData.forEach((item) => {
      //   if (item.qty) {
      //     getQtys += +item.qty;
      //     this.qtys = getQtys;
      //   }
      // });
      this.visible = false;
      for (let i = 0; i < this.testData.length; i++) {//for循环遍历每个元素，每遍历一个元素就是arr[i]
        let num = 0//定义一个num统计arr[i]出现的次数，
        for (let j = 0; j < this.testData.length; j++) { //根据这个arr[i]做一次循环遍历这个数组，
          if (this.testData[i].spcNo) {
            if (this.testData[i].spcNo == this.testData[j].spcNo) {//arr[i]出现一次就会+1
              num++
            }
          }
        }
        if (num > 1) {
          this.testData[this.pickerIndex].spcNo = ''
          this.testData[this.pickerIndex].spcName = ''
          this.$message.warning('原因代号不能重复')
          this.$forceUpdate()
        }
      }
    },
    prdt3leave() {
      setTimeout(() => {
        this.visible = false;
      }, 100);
    },
    pickerSearch() {
      // {
      //     ...this.entity,
      //     sysFiles:this.sysFiles
      //   }
      spcLstpage({
        spcNo: this.queryKeyword,
        name: this.queryKeyword,
        current: this.tablePagespcNo.currentPage,
        size: this.tablePagespcNo.pageSize,
      }).then((res) => {
        if (res.code === 0) {

          this.pickerList = res.data.records
          this.spcNototal = res.data.total
        }
      }).catch(err => this.requestFailed(err))
      //    .finally(() => {
      //         this.loading = false
      // })
    },
    // input聚焦
    focus(index, row, e) {
      this.spcNo = row.spcNo
      // this.queryKeyword = ''

      this.pickerSearch();
      this.visible = true;
      // this.isSaveColor = "danger";
      this.pickerIndex = index;
      let getTop = e.target.getBoundingClientRect().top + 35;

      if (e.target.getBoundingClientRect().top - 380 < 0) {
        this.objStyle.top = getTop.toString() + "px";
      } else {
        this.objStyle.top = (e.target.getBoundingClientRect().top - 390).toString() + "px";
      }
      this.objStyle.left = e.target.getBoundingClientRect().left + "px";



      // let getTop = e.target.getBoundingClientRect().top + 35;
      // this.objStyle.left = e.target.getBoundingClientRect().left + "px";
      // this.objStyle.top = getTop.toString() + "px";


    },
    blurNameInput() {
      // this.visible = false;
      setTimeout(() => {
        this.isShowPopVel = false;
      }, 100);
    },
    inputNameInput(val) {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        this.queryKeyword = val;
        this.goods_currentPage = 1;

        this.pickerSearch();
      }, 500);
    },
    handleSelectionChangereason(rows) {
      this.multipleSelection = rows;
    },
    handleSelectionChange(rows) {
      this.multipleSelection = rows;
    },
    // 添加行
    addTableRow() {
      this.tableAdd();
    },
    // 本地删除
    locaDelTableRow() {
      let that = this

      if (this.multipleSelection.length === 0 || this.multipleSelection === []) {
        this.$message.error("请勾选要操作的数据！");
        return false;
      }
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('public.del.content'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk() {
          let selectRows = that.multipleSelection
          selectRows.forEach((item) => {
            if (item.id === null || item.id === "" || item.id === 0) {
            } else {
              that.detailIds.push(
                Object.assign(
                  {},
                  {
                    id: item.id,
                    prdNo: item.prdNo,
                    itm: item.itm,
                    lastQty: item.lastQty,
                    prdtId: item.prdtId,
                  }
                )
              );
            }
          });

          that.entity.qtyLost = 0
          selectRows.forEach((item) => {
            that.testData.splice(that.testData.indexOf(item), 1);
          });


          that.testData.forEach(i => {
            that.entity.qtyLost += +i.qtyLost;
          })
          if (that.entity.qtyLost > 0) {
            that.entity.qcId = 'F'
          } else {
            that.entity.qcId = 'T'
          }
          that.entity.qtyOk = +that.entity.qty - +that.entity.qtyLost
          that.$forceUpdate()
        },
        onCancel() {

          // that.loading = false
          that.$refs.multipleTable.clearSelection();
          that.$message.info("已取消删除");
        }
      })

      // this.$confirm("此操作将删除, 是否继续?", "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning",
      // })
      // .then(() => {
      // })
      // .catch(() => {
      //   this.$refs.multipleTable.clearSelection();
      //   this.$message.info("已取消删除");
      // });
    },
    // 表格初始化，往数组里面添加50个对象
    tableAdd() {

      for (let i = 0; i < 3; i++) {
        let obj = {
          spcNo: "",
          spcName: "",
          qtyLost: "",
          name: "", // 品名
          id: null,
          itm: i,
          qty1Lost: "",
          ygNo: "",
          dep: this.entity.dep,
          qup: "",
          ygName: "",
          depName: this.entity.depName,
          zcName: this.entity.zcName,
          zcNo: this.entity.zcNo
        };
        this.testData.push(obj);
      }
    },
    //行点击
    handRowClick(row, column, event) {
      var index;
      this.testData.map((c, i) => {
        if (c == row) {
          index = i;
        }
      });
      this.$set(this.testData, index, row);
      event.stopPropagation();
    },
    // this.entity.chktyId: this.entity.chktyId ? 'T' : 'F'
    onChangeinspect(e) {

      // this.checked=e
      //  this.entity.qcId = e.target.checked
    },
    onChange(checked) {
      this.checked = checked;

    },
    onChangeclosecase(e) {


      this.entity.chkKnd = e.target.checked
      this.$forceUpdate()
    },
    onChangeLaboratory(e) {
      this.entity.sys = e.target.checked
    },
    seeFile(obj, formD) {
      this.sysFiles = obj
      this.sysFiles.forEach((item, index) => {
        this.tableData.push(item)
      })

      // this.objpic = obj
      // this.formD = formD
      // this.tableData.forEach((item, index) => {
      //   if (this.formD.wyID === item.wyID) {
      //     if (item.imageUrlList == null) {
      //       item.imageUrlList = []
      //       this.objpic.forEach((item2, index2) => {
      //         item.imageUrlList.push(item2)
      //       })
      //     } else {
      //       this.objpic.forEach((item2, index2) => {
      //         item.imageUrlList.push(item2)
      //       })
      //     }
      //   }
      // })
    },
    save() {
      let validflag
      this.$refs.addEntityForm.validate(valid => {
        console.log(valid, 'ghhhhhhhhhh')
        validflag = valid
        console.log(validflag, '33333')
        if (!valid) {
          this.$message.warning('请选择部门')
          return
        }
      })
      if (validflag) {
        //  this.entity.chkKnd = this.entity.chkKnd ? 'T' : 'F'
        //  this.entity.sys = this. entity.sys ? 'T' : 'F'
        let mesQcTy1s = []
        if (this.testData) {
          mesQcTy1s = this.testData.filter((item) => {
            return item.spcNo !== "" && item.spcNo !== null;
          })
        }
        let flagqtyLost = false
        if (mesQcTy1s.length) {
          for (let i = 0; i < mesQcTy1s.length; i++) {
            if (+mesQcTy1s[i].qtyLost > 0) { } else {
              flagqtyLost = true
              break
            }
          }
        }
        if (flagqtyLost) {
          this.$message.warning('请输入不合格量')
          return
        }
        this.tableData.forEach(item => {
          if (item.distinguish) {
            delete item.distinguish
          }
        })
        // if(this.entity.chktyId==true){
        //   this.entity.chktyId = 'T'
        // }else{
        //   this.entity.chktyId = 'F'
        // }qc/qcAdd

        delete this.entity.unitName
        delete this.entity.qcTypeName
        delete this.entity.whName
        if (this.entity.tyNo) {
          qcUpdedit({
            ...this.entity,
            sys: this.entity.sys ? 'T' : 'F',
            chkKnd: this.entity.chkKnd ? 'T' : 'F',
            mesTiQcFileList: this.tableData,
            mesQcTy1s: mesQcTy1s,
          }).then((res) => {
            if (res.code === 0) {
              if (res.msg === 'success') {
                this.getList()
                this.$message.success(res.data)
              } else {
                this.$message.error(res.data)
                return
              }



            }
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })

        }
        else {
          delete this.entity.tiDd
          this.entity.closeId = 'F'
          qcaddsave({
            ...this.entity,
            sys: this.entity.sys ? 'T' : 'F',
            chkKnd: this.entity.chkKnd ? 'T' : 'F',
            mesTiQcFileList: this.tableData,
            mesQcTy1s: mesQcTy1s,
          }).then((res) => {
            if (res.code == 0) {
              if (res.msg === 'success') {
                this.entity.tyDd = res.data.tyDd
                this.entity.tyNo = res.data.tyNo
                localStorage.setItem("shoujiantyNo", res.data.tyNo)
                localStorage.removeItem('shoujiantiNo')
                this.getList()
                this.$message.success('保存成功')
              } else {
                this.$message.error(res.data)
                return
              }



            }
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        }
      } else {
        this.$message.warning('请选择部门')
      }
    },
    selectChangeEvent({ checked, records, reserves, row, rowIndex }) {

      this.multipleSelectiontwo = records
    },
    selectChangeAll({ records, checked }) {

      this.multipleSelectiontwo = records
    },
    insertEvent() {
      const $table = this.$refs.xTable2
      // if (this.tableData.length > 8) {
      //   this.$message.info('最多8行！')
      // } else {
      if (this.tableDatatwo == null) {
        this.tableDatatwo = []
      }
      this.tableDatatwo.push({
        qty: '',
        unit: '',
        up: '',
        bDd: '',
        eDd: '',
        qcNo: this.row.qcNo,
        curId: null,
        rem: '',
        wyID: this.flag,
        bId: this.tableData.length
      })
      // }
      // }
      // this.tableData.forEach((item, index) => {
      //   item.id = index
      // })
      // this.$refs.xTable2.insertAt({
      //   qty: '',
      //   unit: '',
      //   up: '',
      //   bDd: '',
      //   eDd: '',
      //   qcNo: '',
      //   curId: null,
      //   rem: '',
      //   wyID: '',
      // }, -1) // 从最后新增
      // $table.insertAt(
      //   {
      //     qty: '',
      //     unit: '',
      //     up: '',
      //     bDd: '',
      //     eDd: '',
      //     qcNo: '',
      //     curId: null,
      //     rem: '',
      //     wyID: '',
      //   }, -1).then(({ row }) => {
      //     $table.setActiveCell(row, 'name')
      //   })
    },
    removeEvent() {

      const $table = this.$refs.xTable2

      const selectRecords = this.multipleSelectiontwo
      let that = this
      //获取table
      // (function () {
      //   var len = this.tableData.length - 1;
      //   //start from the top
      //   for (var i = len; i >= 0; i--) {

      //     if (arr[i] == 2) {
      //       arr.splice(i, 1);
      //     }
      //   }

      // })();
      let arr = []
      // selectRecords.forEach((item, index) => {
      //   arr.push(item.id)
      //   for (var i = 0; i < that.tableData.length; i++) {

      //     if (arr.indexOf(that.tableData[i].id) > -1) {
      //       that.tableData.splice(that.tableData[i].id, 1) // 将使后面的元素依次前移，数组长度减1
      //       i--; // 如果不减，将漏掉一个元素
      //     }
      //   }
      // })
      selectRecords.forEach(item => {
        this.tableDatatwo.splice(this.tableDatatwo.indexOf(item), 1);
      })
      // selectRecords.forEach((item, index) => {
      //   arr.push(item.bId)
      //   that.tableDatatwo.forEach((item2, index2) => {
      //     if (arr.indexOf(item2.bId) > -1) {
      //       // delete that.tableData[item2.id]
      //       that.tableData.splice(index2, 1)
      //     }
      //   })
      // })
    },
    editclick() {
      this.isEdit = true
    },
    closeclick() {
      this.isEdit = false
    },
    reset() {
      this.entity = {}
    },
    seepic(index) {
      let arry = []
      arry.push(this.tableData[index])

      // this.$refs.ModalPic.create({ title: '查看' }, arry)
    },
    uploadpic(row) {
      // this.$refs.uploadFile.create({ title: '上传' }, row)
      // this.$refs.Upload.create(
      //   {
      //     title: '上传'
      //   },
      // )
      // this.$refs.Upload.create({ title: '上传' }, row)
    },
    // check(){
    //   this.$router.push('')
    // },
    handleChangereson(val) {

      this.multiplereason = val
    },
    cancelGetData() {
      this.visible = false
    },
    otngetData() {

      if (this.multiplereason.length) {
        this.reasondata = this.multiplereason
      }
      this.visible = false
    },
    reasonclick(val) {
      this.visible = true
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSelectionChangetwo(val) {
      this.multipleSelectiontwo = val
    },
    handleTabClick(key) {
      if (key == '1') {
        this.getList()
      } else if (key == '2') {
        this.getListtwo()
      }
    },
    onChange(data, dateString) {

      this.queryParam.date = dateString

    },
    depme(row) {
      datadepPage(
        Object.assign({
          current: 1,
          size: 10,
          deptCode: row
        })
      ).then(response => {
        this.entity.depName = response.data.records[0].name
        this.data = this.entity.dep + '-' + this.entity.depName
        this.testData.forEach(item => {
          if (item.spcNo) { } else {
            item.dep = this.data
          }
        })
        this.tableAdd()

      }).catch(err => this.requestFailed(err))
    },
    getListtreat() {
      this.loading = true
      qcdjInfodetail(
        Object.assign({
          // total: this.tablePage.currentPage,
          // current: this.tablePage.currentPage,
          // size: this.tablePage.pageSize,
          // staDd:this.queryParam.staDd,
          // endDd:this.queryParam.endDd,
          tiNo: this.entity.tiNo,
          // zcNo:this.queryParam.zcNo,
          // moNo:this.queryParam.moNo,
          // prdNo:this.queryParam.prdNo,
          // prdName:this.queryParam.prdName,
          // bilNo:this.queryParam.bilNo
        })
      ).then(response => {
        this.loading = false
        this.entity = response.data
        if (this.entity.lsNo) {
          this.isClaim = true
        } else {
          this.isClaim = false
        }
        if (this.entity.dep) {
          this.depme(this.entity.dep)
        }
        this.entity.qcId = ''
        if (this.entity.qtyLost) {
          if (this.entity.qtyLost > 0) {
            this.entity.qcId = 'F'
          } else {
            this.entity.qcId = 'T'
          }
        } else {
          this.entity.qtyLost = 0
          this.entity.qcId = 'T'
        }
        this.entity.qtyOk = this.entity.qty - +this.entity.qtyLost
        // this.data = this.entity.dep + '-' + this.entity.depName
        this.tableData = response.data.mesTiQcFileList


        if (this.entity.chkKnd == 'T') {
          this.entity.chkKnd = true
        } else {
          this.entity.chkKnd = false
        }
        if (this.entity.sys == 'T') {
          this.entity.sys = true
        } else {
          this.entity.sys = false
        }

        // this.tablePage.total = response.data.total
        // this.tablePage.currentPage = response.data.current
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    getList() {
      this.loading = true
      qcyjlnfo(
        Object.assign({
          // total: this.tablePage.currentPage,
          // current: this.tablePage.currentPage,
          // size: this.tablePage.pageSize,
          // staDd:this.queryParam.staDd,
          // endDd:this.queryParam.endDd,
          tyNo: this.entity.tyNo,
          // zcNo:this.queryParam.zcNo,
          // moNo:this.queryParam.moNo,
          // prdNo:this.queryParam.prdNo,
          // prdName:this.queryParam.prdName,
          // bilNo:this.queryParam.bilNo
        })
      ).then(response => {
        this.loading = false
        this.entity = response.data
        if (this.entity.lsNo) {
          this.isClaim = true
        } else {
          this.isClaim = false
        }
        if (this.entity.dep) {
          this.depme(this.entity.dep)
        }
        this.testData.forEach(item => {
          if (item.dep == null) {
            item.dep = ''
          }
          if (item.depName == null) {
            item.depName = ''
          }
          if (item.zcName == null) {
            item.zcName = ''
          }
          if (item.zcNo == null) {
            item.zcNo = ''
          }
          if (item.spcNo) { } else {
            item.zcNo = this.entity.zcNo
            item.zcName = this.entity.zcName
          }
        })
        if (this.entity.qtyLost) {
          // if(this.entity.qtyLost>0){
          //     this.entity.qcId = 'F'
          // }else{
          //   this.entity.qcId = 'T'
          // }
        } else {
          this.entity.qtyLost = 0
        }
        // this.testData=response.data.mesQcTy1List
        this.testData = response.data.mesQcTy1s
        // this.testData.forEach(item=>{
        //   item.
        // })
        //
        // this.data = this.entity.dep + '-' + this.entity.depName
        // 不合格评审单


        this.tableData = response.data.mesTiQcFileList
        if (this.entity.chkKnd == 'T') {
          this.entity.chkKnd = true
        } else {
          this.entity.chkKnd = false
        }

        if (this.entity.sys == 'T') {
          this.entity.sys = true
        } else {
          this.entity.sys = false
        }


        // this.tablePage.total = response.data.total
        // this.tablePage.currentPage = response.data.current
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 获取列表数据
    // getList () {
    //   this.loading = true
    //   fetchList(
    //     Object.assign({
    //       current: this.tablePage.currentPage,
    //       size: this.tablePage.pageSize,
    //       pgNo: this.queryParam.pgNo
    //     })
    //   ).then(response => {
    //     this.loading = false
    //     this.tableData = response.data.records

    //     this.tablePage.total = response.data.total
    //     this.tablePage.currentPage = response.data.current
    //   }).catch(e => {
    //     this.loading = false
    //   })
    // },
  },
}
</script>

<style lang='scss' scoped>
.ant-input-wrapper .ant-input-group-addon .ant-btn {
  height: 28px;
}

.sup_info_item {
  padding: 10px 0;
}



.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 0px
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  -o-appearance: none !important;
  -ms-appearance: none !important;
  appearance: none !important;
  margin: 0;
}

input[type="number"] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  -o-appearance: textfield;
  -ms-appearance: textfield;
  appearance: textfield;
}

.sup_info_basics_container {
  border: 1px solid #E5E5E5;
  border-radius: 10px;
  padding: 0px 20px 0px 20px;
}

.sup_info_basics_header {
  font-size: 15px;
  color: #1F2A3F;
  height: 41px;
  font-weight: 500;
  line-height: 41px;
  overflow: hidden;
}

.el-table__fixed,
.el-table__fixed-right {
  height: 100% !important;
}

.ant-input {
  height: 28px;
}

.bordervisib .el-input__inner {
  -webkit-appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 0px;
  width: 100%;
  /* height:20px; */
}

/* .el-table tbody tr:hover > td {
  background-color: #fff!important;
} */
.compoct .el-form-item--small .el-form-item__content,
.el-form-item--small .el-form-item__label {
  line-height: 24px;
}

/* .bordervisib .el-input__inner{
height:20px;
} */
.tableheight .el-table__cell {
  padding: 1px !important;
}
</style>