<template>
  <div class="consrm compoct boxoperat">
    <el-dialog title='打印选择' style='padding: 0px;' width='45%' :before-close='cancelEidt' :visible.sync='isPrintone'
      v-if='isPrintone'>
      <!-- :close-on-click-modal="false" -->
      <div style='display:flex;'>
        <el-form label-width='85px' :model='entity' :rules='rules' ref='addEntityForm'
          style='margin: 0px;padding: 0px;flex:1'>
          <el-form-item label='打印套版:' prop='id'>
            <el-select v-model='taoban.id' size='mini' @change='selectChange' style='width: 150px' placeholder=''>
              <el-option v-for='item in printmodal' :key='item.id' :label='item.name' :value='item.id' />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot='footer' class='dialog-footer'>
        <el-button size='mini' @click='cancelEidt'>取 消</el-button>
        <el-button size='mini' type='primary' @click="printaddEntity('addEntityForm')">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 打印 -->
    <el-dialog :visible.sync="isPrint" :close-on-click-modal="false" v-loading="loading" width="400px" top="8vh">
      <div style="height: 65px;">
        <div id="print_icon" style="font-size:22px;color: red;">
          <!-- <el-icon type="printer" /> -->
          <span style="color: rgba(0, 0, 0, 0.85);margin-left:8px;">确定要打印吗？</span>
        </div>
        <div style="float: right;margin-top:10px;">
          <el-button type="primary" @click="printClose">取消</el-button>
          <el-button v-print="printObj2" type="primary" :disabled="isPrintButton" style="margin-left: 8px"
            @click="printSure">确定
          </el-button>

        </div>
      </div>
    </el-dialog>
    <!-- <div v-show="true">
      <div id="formbashi" style="color:#000;">
        <div
          v-for="(item, index) in boxNoprintbashi"
          id="gfvrghtwo"
          style="color:#000;overflow:hidden;color:#000;width: 40mm;padding: 0mm 2mm 0 2mm;height:60mm;background-color: pink;margin:0 auto;position:relative"
        >
        <div style="display: table;width:100%;color:#000">
          <div style="color:#000;display:inline-block;background-color: pink;overflow: hidden;; width:40mm; display: table-cell;">
            <div>
              <div style="margin:0 auto;font-weight: bold;text-align: center;font-size:16px;padding-top:4mm;">周转箱格式</div>
              <div class="form_one">
                <div style="font-size: 12px;display: flex;margin-bottom:2px;">
                  <div style="flex:1">
                    <div style="display:inline-block;">日期：</div>
                    <div style="display:inline-block;">{{ datebashi }}</div>
                  </div>
                </div>
              </div>
              <div class="form_one">
                <div style="font-size: 12px;display: flex;margin-bottom:2px;">
                  <div style="flex:1">
                    <div style="display:inline-block;">周转箱：</div>
                    <div style="display:inline-block;">{{ item.boxNo }}</div>
                  </div>
                </div>
              </div>
              <div class="form_one">
                <div style="font-size: 12px;display: flex;margin-bottom:2px;">
                  <div style="flex:1">
                    <div style="display:inline-block;">工单号：</div>
                    <div style="display:inline-block;">{{ item.moNo }}</div>
                  </div>
                </div>
              </div>

              <div style="display:-webkit-flex;display:-ms-flexbox;display:flex;">
                <div style="flex-grow:1;">
                  <div style="font-size: 12px;display: flex;margin-bottom:2px;">
                    <div style="flex:1">
                      <div style="display:inline-block;">品名：</div>
                      <div style="display:inline-block;">{{ item.prdName }}</div>
                    </div>
                  </div>
                  <div style="font-size: 12px;display: flex;margin-bottom:2px;">
                    <div style="flex:1">
                      <div style="display:inline-block;">数量：</div>
                      <div style="display:inline-block;">{{ item.qty }}</div>
                    </div>
                  </div>
                  <div style="font-size: 12px;display: flex;margin-bottom:2px;">
                    <div style="flex:1">
                      <div style="display:inline-block;">作业人员：</div>
                      <div style="display:inline-block;">{{ $store.state.user.info.name }}</div>
                    </div>
                  </div>
                </div>
                <div style="width:60px">
                  <div :id="`erweimabashi${index}`"></div>
                </div>
              </div>

            </div>
          </div>
        </div>

        </div>
      </div>
    </div> -->
    <div v-show="false">
      <div id="formbashi" style="color:#000;">
        <div v-for="(item, index) in boxNoprintbashi" id="gfvrghtwo"
          style="color:#000;overflow:hidden;color:#000;width: 60mm;padding: 0mm 1mm 0 1mm;height:40mm;background-color: pink;margin:0 auto;position:relative">
          <div style="display: table;width:100%;color:#000">
            <div
              style="color:#000;display:inline-block;background-color: pink;overflow: hidden;; width:60mm; display: table-cell;">
              <div>
                <div style="margin:0 auto;font-weight: bold;text-align: center;font-size:16px;padding-top:2mm;">周转箱格式
                </div>
                <div class="form_one">
                  <div style="font-size: 10px;display: flex;margin-bottom:1px;">
                    <div style="flex:1">
                      <div style="display:inline-block;width:30px">日期:</div>
                      <div style="display:inline-block;">{{ datebashi }}</div>
                    </div>
                  </div>
                </div>
                <div class="form_one">
                  <div style="font-size: 10px;display: flex;margin-bottom:1px;">
                    <div style="flex:1">
                      <div style="display:inline-block;">周转箱:</div>
                      <div style="display:inline-block;">{{ item.boxNo }}</div>
                    </div>
                  </div>
                </div>
                <div class="form_one">
                  <div style="font-size: 10px;display: flex;margin-bottom:1px;">
                    <div style="flex:1">
                      <div style="display:inline-block;">工单号:</div>
                      <div style="display:inline-block;">{{ item.moNo }}</div>
                    </div>
                  </div>
                </div>

                <div style="display:-webkit-flex;display:-ms-flexbox;display:flex; justify-content:space-between">
                  <div style="flex-grow:1;">
                    <div style="font-size: 10px;display: flex;margin-bottom:1px;">
                      <div style="flex:1">
                        <div style="display:inline-block;">品名:</div>
                        <div style="display:inline-block;">{{ item.prdName }}</div>
                      </div>
                    </div>
                    <div style="font-size: 10px;display: flex;margin-bottom:1px;">
                      <div style="flex:1">
                        <div style="display:inline-block;">数量:</div>
                        <div style="display:inline-block;">{{ item.qty }}</div>
                      </div>
                    </div>
                    <div style="font-size: 10px;display: flex;">
                      <div style="flex:1">
                        <div style="display:inline-block;">作业人员:</div>
                        <div style="display:inline-block;">{{ $store.state.user.info.name }}</div>
                      </div>
                    </div>
                  </div>
                  <div style="width:60px;margin-top:5px;">
                    <div :id="`erweimabashi${index}`"></div>
                  </div>
                </div>

              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
    <div v-show="false">
      <div id="form1" style="color:#000;">
        <!-- <div
        :key="i"
        v-for="(i, index) in Len"
        id="gfvrgh"
        style="width: 880px;padding: 10mm 10mm 0 10mm;"
      > -->
        <div v-for="(itemlist, indexlist) in boxNoList" style="color:#000">
          <!-- width: 880px; -->
          <!-- v-for="(itematm, i) in curidList" -->
          <div v-for="(itematm, i) in itemlist[0].curidList" id="gfvrgh" :key="i"
            style="overflow:hidden;color:#000;width: 100mm;padding: 0mm 2mm 0 2mm;height:150mm;background-color: pink;margin:0 auto;position:relative">
            <div
              style="margin: 0px auto;display: flex;flex-direction: row;align-items: center;justify-content: center;">
              <div style="position:absolute;bottom:2px;">
                <span style="font-size:10px;margin-right:10px;">{{$store.state.user.info.name}}</span><span
                  style="font-size:10px;">{{newday}}</span>
              </div>
            </div>
            <!-- <div ></div> -->
            <img src="../../.././assets/print.png" class="watermark" v-if="showFlag" alt />
            <div style="display: table;width:100%;color:#000">
              <div
                style="color:#000;display:inline-block;background-color: pink;overflow: hidden;; width:100mm; display: table-cell;">
                <!-- width: -moz-calc(94% - 4px);width: -webkit-calc(94% - 4px);width: calc(94% - 4px); -->
                <div>
                  <div style="margin:0 auto;font-weight: bold;text-align: center;font-size:16px;padding-top:5mm;">工艺流转卡
                  </div>
                  <!-- {{itematm}} -->
                  <table border="1" cellspacing="0" width="100%" align="center" cellpadding="0"
                    style="font-size:10px;border-collapse:collapse;" class="confluenceTable">
                    <tr>
                      <td colspan="4" rowspan="5" style="height:80px;padding-top:2px;" align="center">
                        <!-- <div
                    id="yuxin"
                    style="margin-bottom:10px;"
                    ></div> -->
                        <!-- {{itemlist[0].addid}}/ {{i}} -->
                        <div :id="`yuxin${itemlist[0].addid}${i}`"></div>
                        <div style="font-size: 10px;">{{itemlist[0].boxNo}}</div>
                        <div style="font-size: 10px;">{{itemlist[0].moNo}}</div>
                        <!-- boxNoList -->
                      </td>
                      <td colspan="6" style="font-size:10px;">
                        <div style="display: inline-block">
                          <div style="font-size:10px;">预入仓库:{{itemlist[0].whName}}
                          </div>
                          <div style="font-size:10px;"><span
                              style="margin-right:9px;">日期：{{itemlist[0].today}}</span><span>页{{i + 1}} /
                              {{itemlist[0].curidList.length}}</span></div>
                        </div>
                        <div :id="`yuxinright${itemlist[0].addid}${i}`"
                          style="display:inline-block;float:right;padding-right:2px;padding-top:2px;padding-bottom:2px">
                        </div>

                      </td>
                      <!-- <span style="margin-right:2px;font-size:10px;">第{{itemlist[0].qtyRsv}}桶</span> -->
                      <!-- <td colspan="4" style="text-align:left">222222222</td> -->
                    </tr>
                    <tr style="height:16px;">
                      <td style="width:40px;" align="center">产品规格</td>
                      <td colspan="6" style="text-align:left">{{itemlist[0].spc}}</td>
                      <!-- <td>联系电话</td>
                  <td colspan="2" style="text-align:left;position:relative">
                    <span style="position:absolute;left:0px;top:5px;"></span>
                  </td> -->
                    </tr>
                    <tr style="height:16px;">
                      <td align="center" style="width:40px;">品号</td>
                      <td colspan="6" style="text-align:left">{{itemlist[0].prdNo}}</td>
                    </tr>
                    <tr style="height:16px;">
                      <td style="width:40px;" align="center">图纸设计号</td>
                      <td style="text-align:left;font-size:11px;" colspan="3">{{itemlist[0].prdName}}</td>
                      <td style="width:30px;">应生产量</td>
                      <td style="text-align:left">{{itemlist[0].qty}}</td>
                    </tr>
                    <tr style="height:16px;">
                      <td align="center" style="width:40px;">材料规格</td>
                      <td colspan="6" style="text-align:left;font-size: 10px;"><span
                          style="margin-right:2px;">{{itemlist[0].tmSpc}}</span>{{itemlist[0].bomRem}}
                      </td>
                    </tr>
                    <tr style="height:16px;">
                      <td align="center" style="width:65px">材料炉批号</td>
                      <td style="width:65px" colspan="3">&nbsp;&nbsp;&nbsp;</td>
                      <!-- recTypeName -->
                      <td style="width:50px;">应领材料</td>
                      <td colspan="2" style="min-width:30px;">{{itemlist[0].qtyRsv}}</td>
                      <!-- wxAdviceName -->
                      <td>单重g</td>
                      <td colspan="2" style="width:40px;">{{itemlist[0].dz.toFixed(2)}}</td>
                    </tr>
                    <tr style="height:16px;">
                      <!-- <td style="width:20px"></td> -->
                      <td colspan="10" style="text-align:left;font-size:8px;">备注:<span style="font-size:8px"
                          v-if="itemlist[0].twRem.length<100">{{itemlist[0].twRem}}</span>
                        <span style="font-size:8px" v-else>{{itemlist[0].twRem.substring(0,100)}}...</span>
                      </td>
                    </tr>
                  </table>
                  <!-- tou -->


                  <!-- <a-row>
            <a-col
              :span="8"
              class="code"
            >
              <div :id="`qrcode${index}`"></div>
            </a-col>
            <a-col :span="16">
              <a-row>
                <a-col
                  :span="12"
                  style="text-align: center"
                >
                  <h3>
                    {{data.length == 0
                     || data[0].compName == null
                     || data[0].compName == ''
                     || data[0].compName == undefined ? '' : data[0].compName
                      }}
                  </h3>
                </a-col>
                <a-col
                  :span="12"
                  style="text-align: center"
                >
                  <h3>{{ i }}/{{ Len }}</h3>
                </a-col>
              </a-row>
              <a-row>
                <a-col
                  :span="12"
                  style="text-align: center"
                >
                  <h4 style="font-weight: 700">{{ name }}</h4>
                </a-col>
                <a-col
                  :span="12"
                  style="text-align: center"
                >
                  <h4 v-if="data.length > 0">NO.{{ data[0].prnNo }}</h4>
                </a-col>
              </a-row>
            </a-col>
          </a-row> -->
                  <!-- <a-row style="margin-bottom: 5px">
            <a-col
              :span="6"
              style="font-size: 10px; font-weight: 700"
            > 报交日期：{{ bjDd }} </a-col>
            <a-col
              :span="9"
              style="font-size: 10px; font-weight: 700"
            > 供应商：{{ cusName }} </a-col>
            <a-col
              :span="4"
              style="font-size: 10px; font-weight: 700"
            > 检验否：{{ check }} </a-col>
            <a-col
              :span="5"
              style="font-size: 10px; font-weight: 700"
            >
              {{ sqlTable }}
            </a-col>
          </a-row> -->
                </div>
                <!-- 表格 -->
                <table id="table" border="1" align="center" style="width: 100%; border-collapse: collapse"
                  class="confluenceTable">
                  <thead id="table2">
                    <tr>
                      <th align="center" style="text-align: center; font-size: 10px">序号</th>

                      <th align="center" style="text-align: center; font-size: 10px;width:100px;">名称</th>
                      <th align="center" style="text-align: center; font-size: 10px">机台</th>
                      <th align="center" style="text-align: center; font-size: 10px">检验</th>
                      <th align="center" style="text-align: center; font-size: 10px">转移</th>
                      <th align="center" style="text-align: center; font-size: 8px;height:16px;">作业描述</th>
                      <th align="center" style="text-align: center; font-size: 10px;">摘要</th>
                      <th align="center" style="text-align: center; font-size: 10px;height:16px;">确认</th>
                      <th align="center" style="text-align: center; font-size: 10px">日期</th>
                      <!-- <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >备注</th> -->

                      <!-- <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >物料编码</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >货品名称</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >型号规格</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >单位</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >数量</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >单价</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >金额</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >合同号</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >备注</th> -->
                      <!-- <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >{{ numb }}</th> -->
                    </tr>
                  </thead>
                  <tbody class="form1body">
                    <tr v-for="(item, index) in 8" :key="item" style="min-height:26px;">
                      <td v-if="itemlist[index + 8 * (i)]" style="text-align: center; font-size: 10px;width:20px;">
                        <!-- {{ index + 1 }} -->
                        <div>{{itemlist[index + 8 * (i)].zcItm}}</div>
                      </td>
                      <td v-else />

                      <!-- ${index} -->
                      <td v-else />
                      <td v-if="itemlist[index + 8 * (i)]" style="text-align: left;width:100px;font-size: 8px;">
                        <!-- <div>{{itemlist[index + 5 * (i)].zcName}}</div> -->
                        <div v-if="itemlist[index + 8 * (i)].zcName.length<10">{{itemlist[index + 8 * (i)].zcName}}
                        </div>
                        <div v-else>{{itemlist[index + 8 * (i)].zcName.substring(0,10)}}...</div>
                      </td>
                      <td v-else />
                      <td v-if="itemlist[index + 8 * (i)]" style="text-align: left; font-size: 8px;width:45px;">
                        <div>{{itemlist[index + 8 * (i)].dep}}</div>
                      </td>
                      <td v-else />
                      <td v-if="itemlist[index + 8 * (i)]" style="text-align: left; font-size: 10px;width:20px;">
                        <div>{{itemlist[index + 8 * (i)].xmj}}</div>
                      </td>
                      <td v-else />
                      <td v-if="itemlist[index + 8 * (i)]" style="text-align: left; font-size: 10px;width:20px;">
                        <div>{{itemlist[index + 8 * (i)].mvId}}</div>
                      </td>
                      <td v-else />


                      <td v-if="itemlist[index + 8 * (i)]" style="text-align: left; font-size: 8px;width:300px;">
                        <div>
                          <div style="font-size:8px" v-if="itemlist[index + 8 * (i)].rem.length<56">{{itemlist[index + 8
                            * (i)].rem}}</div>
                          <div style="font-size:8px;;" v-else>{{itemlist[index + 8 * (i)].rem.substring(0,56)}}...</div>
                        </div>
                      </td>
                      <td v-else />
                      <td v-if="itemlist[index + 8 * (i)]" style="text-align: left; font-size: 10px;width:35px;">
                        <!-- {{ printdata[index + 10 * (i)].boxNo }} -->
                        <!-- <div
                    id="scNoerwei"
                    style="margin-bottom:10px;"
                    ></div> -->
                        <!-- scNoerwei{{itemlist[index + 10 * (i)].addid}} -->
                        <!-- {{itemlist[index + 4 * (i)].addid}}
                     {{itemlist[index + 4 * (i)].scNo}} -->
                        <div style="width:35px;">
                          <!-- <div :id="`scNoerwei${itemlist[index + 4 * (i)].addid}`"></div> -->
                        </div>
                      </td>
                      <td v-if="itemlist[index + 8 * (i)]" style="text-align: left; font-size: 10px;width:40px;">
                        <div style="width:40px;min-height:26px;"></div>
                        <!-- {{itemlist[index + 3 * (i)].rem}} -->
                      </td>
                      <td v-else />
                      <td v-if="itemlist[index + 8 * (i)]" style="text-align: left; font-size: 10px;width:35px;">
                        <div></div>
                      </td>
                      <td v-else />

                      <!-- <td
                    v-if="printdata[index + 10 * (i)]"
                    style="text-align: left; font-size: 12px;width:90px;"
                  >{{ printdata[index + 10 * (i)].moNo }}</td>
                  <td v-else /> -->
                      <!-- <td
                    v-if="printdata[index + 10 * (i)]"
                    style="text-align: left; font-size: 12px;width:90px;"
                  >{{ printdata[index + 10 * (i)].qty }}</td>
                  <td v-else />
                  <td
                    v-if="printdata[index + 10 * (i)]"
                    style="text-align: left; font-size: 12px;width:90px;"
                  >{{ printdata[index + 10 * (i)].qtyFin }}</td>
                  <td v-else />
                  <td
                    v-if="printdata[index + 10 * (i)]"
                    style="text-align: left; font-size: 12px;width:90px;"
                  >{{ printdata[index + 10 * (i)].qtyFin }}</td>
                  <td v-else /> -->

                      <!-- 暂时 -->
                      <!-- <td
                    v-if="printdata[index + 10 * (i)]"
                    style="text-align: left; font-size: 12px;width:100px;"
                  >{{ printdata[index + 10 * (i)].prdNo.slice(0,20) }}</td>
                  <td v-else />
                  <td
                    v-if="printdata[index + 10 * (i)]"
                    style="text-align: left; font-size: 12px;width:130px;"
                  >
                    <div v-if="printdata[index + 10 * (i)].prdName">
                      {{ printdata[index + 10 * (i)].prdName.slice(0,14) }}
                    </div>
                    <div v-else>
                      {{ printdata[index + 10 * (i)].prdName }}
                    </div>
                  </td>
                  <td v-else />
                   <td
                    v-if="printdata[index + 10 * (i)]"
                    style="text-align: left; font-size: 12px;width:140px;"
                  >{{ printdata[index + 10 * (i)].spc }}</td>
                  <td v-else />
                  <td
                    v-if="data[index + 10 * (i)]"
                    style="text-align: left; font-size: 12px;width:80px;"
                  >{{ printdata[index + 10 * (i)].qtyBj }}</td>
                  <td v-else />
                  <td
                    v-if="printdata[index + 10 * (i)]"
                    style="text-align: left; font-size: 12px;width:40px;"
                  >{{ printdata[index + 10 * (i)].unitName }}</td>
                  <td v-else />
                   <td
                    v-if="printdata[index + 10 * (i)]"
                    style="text-align: left; font-size: 12px;width:100px;"
                  >{{ printdata[index + 10 * (i)].rem }} </td>
                  <td
                    v-else
                    style="height: 24px"
                  /> -->
                      <!-- 暂时 -->



                      <!-- <td
                    v-if="data[index + 12 * (i)]"
                    style="text-align: left; font-size: 12px;width:100px;"
                  >{{ data[index + 12 * (i)].prdNo.slice(0,20) }}</td>
                  <td v-else />
                  <td
                    v-if="data[index + 12 * (i)]"
                    style="text-align: left; font-size: 12px;width:130px;"
                  >
                    <div v-if="data[index + 12 * (i)].prdName">
                      {{ data[index + 12 * (i)].prdName.slice(0,14) }}
                    </div>
                    <div v-else>
                      {{ data[index + 12 * (i)].prdName }}
                    </div>
                    {{ data[index + 12 * (i)].prdName.slice(0,14) }}
                  </td>
                  <td v-else />
                  <td
                    v-if="data[index + 12 * (i)]"
                    style="text-align: left; font-size: 12px;width:140px;"
                  >{{ data[index + 12 * (i)].spc }}</td>
                  <td v-else />
                  <td
                    v-if="data[index + 12 * (i)]"
                    style="text-align: left; font-size: 12px;width:40px;"
                  >{{ data[index + 12 * (i)].unitName }}</td>
                  <td v-else />
                  <td
                    v-if="data[index + 12 * (i)]"
                    style="text-align: left; font-size: 12px;width:80px;"
                  >{{ data[index + 12 * (i)].qtyBj }}</td>
                  <td v-else />
                  <td
                    v-if="data[index + 12 * (i)]"
                    style="text-align: left; font-size: 12px;width:80px;"
                  >
                    {{ data[index + 12 * (i)].up }}
                  </td>
                  <td v-else />
                  <td
                    v-if="data[index + 12 * (i)]"
                    style="text-align: left; font-size: 12px;width:80px;"
                  >
                    {{ data[index + 12 * (i)].amtn }}
                  </td>
                  <td v-else />
                  <td
                    v-if="data[index + 12 * (i)]"
                    style="text-align: left; font-size: 12px;width:90px;"
                  >{{ data[index + 12 * (i)].bilNo }}</td>
                  <td v-else />
                  <td
                    v-if="data[index + 12 * (i)]"
                    style="text-align: left; font-size: 12px;width:100px;"
                  >{{ data[index + 12 * (i)].rem }} </td>
                  <td
                    v-else
                    style="height: 24px"
                  /> -->
                      <!-- 备注 -->
                      <!-- <td style="height: 24px"></td> -->
                    </tr>
                    <!-- <tr>
                  <th colspan="10">备注:<span style="font-size:12px;margin-left:2px;font-weight: 100;"></span></th>
                </tr>
                <tr style="height: 40px">
                  <th style="width:25%;" colspan="2.5">客户签收:</th>
                  <th style="width:25%;" colspan="2">送货人:</th>
                  <th style="width:25%;" colspan="2.5">核准:</th>
                  <th style="width:25%;" colspan="2.5">制表:</th>
                </tr> -->
                    <!-- <tr> -->
                    <!-- <th colspan="5">数量合计： {{ numTotal }}</th> -->
                    <!-- <th colspan="5">数量合计： {{ itematm.qty }}</th> -->
                    <!-- <th colspan="5">金额合计： </th> -->
                    <!-- <th colspan="5">金额合计： {{ itematm.value }}</th> -->
                    <!-- </tr> -->
                    <!-- <tr>
                  <th colspan="5">数量合计： {{ amtnTotal }}</th>
                  <th colspan="5">金额合计： {{ amtnTotal }}</th>
                </tr> -->
                  </tbody>
                </table>
              </div>
              <!-- <div style="padding-top:200px;padding-left:5px;display:inline-block;width:6%;display: table-cell;font-size:8px;">
            <div>(一)</div>
            <div>存根白</div>
            <div>(二)</div>
            <div>客户红</div>
            <div>(三)</div>
            <div>回单黄</div>
          </div> -->
            </div>

            <!-- <div>
          <a-row style="margin-bottom: 5px">
            <a-col
              :span="6"
              style="font-size: 10px; font-weight: 700"
            >质量检验</a-col>
            <a-col
              :span="6"
              style="font-size: 10px; font-weight: 700"
            >仓库收货</a-col>
            <a-col
              :span="6"
              style="font-size: 10px; font-weight: 700"
            >报交经办</a-col>
            <a-col
              :span="5"
              style="font-size: 10px; font-weight: 700"
            >金额合计： {{ amtnTotal }}</a-col>
          </a-row>
        </div> -->
            <!-- <div style="border-left:1px solid #2c2c2c;border-right:1px solid #2c2c2c;border-bottom:1px solid #2c2c2c;border-top:1px solid #2c2c2c;">
          <span style="border-right:1px solid #808080;display:inline-block;width:50%">金额合计： {{ amtnTotal }}</span>
          <span style="display:inline-block;width:50%;">金额合计： {{ amtnTotal }}</span>
        </div> -->
            <!-- <div>
          <el-row style="margin-bottom: 5px;margin-top:10px;">
            <el-col
              :span="8"
              style="font-size: 13px; font-weight: 700;"
            >收货单位及经手人：</el-col>
            <el-col
              :span="8"
              style="font-size: 13px; font-weight: 700"
            >检验员：</el-col>
            <el-col
              :span="8"
              style="font-size: 13px; font-weight: 700"
            >送货单位及经手人：</el-col> -->
            <!-- <a-col
              :span="6"
              style="font-size: 10px; font-weight: 700"
            >数量合计： {{ amtnTotal }}</a-col>
            <a-col
              :span="6"
              style="font-size: 10px; font-weight: 700"
            >金额合计： {{ amtnTotal }}</a-col> -->
            <!-- </el-row>
        </div> -->
          </div>
        </div>
      </div>
    </div>
    <!-- 打印 -->
    <div style="text-align:right;margin-right:16px;position: fixed;top: 80px;right:0px;z-index:999">
      <!-- <a-button
      style="margin-left: 8px"
      type="primary"
      @click="reset"
      v-if="$route.query.tyNo"
    >新增</a-button> -->
      <!-- <a-button
      style="margin-left: 8px"
      type="primary"
       :disabled="isClaim"
      :style="isClaim ? 'background-color: #909399;border-color: #909399;' : ''"
      @click="editclick"
    >编辑</a-button> -->
      <a-button style="margin-left: 8px;background-color:#3CB371;border-color:#3CB371" type="primary"
        @click="unpackboxmeth">开箱</a-button>
      <a-button style="margin-left: 8px;background-color:#67c23a;border-color: #67c23a" type="primary"
        @click="tearopenmedth">拆箱</a-button>
      <a-button style="margin-left: 8px;background-color:#E6A23C;border-color:#E6A23C" type="primary"
        @click="abnormalmeth">异常拆箱</a-button>
      <a-button style="margin-left: 8px;background-color:#4a89c8;border-color:#4a89c8" type="primary"
        @click="mouldassembmedth">合箱</a-button>
      <a-button type="primary" @click="delradio"
        style="margin-left: 8px;background-color: #f56c6c;border-color: #f56c6c;color:#fff;">删除</a-button>
      <a-button style="margin-left: 8px;color: #303133;background: rgb(220, 223, 230);border-color:rgb(220, 223, 230)"
        type="primary" @click="printone" v-loading="loading">打印</a-button>
      <!-- <a-button
      style="margin-left: 8px;color: #303133;background: rgb(220, 223, 230);border-color:rgb(220, 223, 230)"
      type="primary"
      @click="printtwo"
      v-loading="loading"
    >套版打印</a-button> -->
      <!-- <a-button
      style="margin-left: 8px"
      type="primary"
      @click="save"
        :disabled="isClaim"
      :style="isClaim ? 'background-color: #909399;border-color: #909399;color:#fff;' : ''"
    >保存</a-button> -->
      <a-button style="margin-left: 8px;color: #909399;
    background: #f4f4f5;
    border-color: #d3d4d6;" type="primary" @click="cosetiao">关闭</a-button>

      <!-- 新增	编辑	删除	保存	关闭 -->
    </div>
    <el-dialog title="信息" style="padding: 0px;" :close-on-click-modal="false" :visible.sync="hexiangtwo" width="30%">
      <div style="center='center'">
        <el-form status-icon inline-message label-width="85px" :model="entity" ref="addEntityForm"
          style="margin: 0px;padding: 0px;">
          <span>已做转移，无法合箱！箱号：【</span><span>{{mvboNostring}}</span><span>】转移单【</span><span>{{mvNostring}}</span>】
          <!-- <el-form-item  label="箱数:" prop="boxCount" style="margin-bottom:8px;">
                  <el-input prefix-icon="el-icon-edit" type="number" @wheel.native.prevent="stopScroll($event)" :disabled="isEdit" v-model="entity.boxCount" size="mini" style="width: 200px;"
                  placeholder="请输入箱数"></el-input>   
                </el-form-item>
                <el-form-item  label="数量:" prop="pk3Qty" v-if="isEdit" style="margin-bottom:8px;">
                  <el-input prefix-icon="el-icon-edit" type="number" @wheel.native.prevent="stopScroll($event)" :disabled="isEdit"  v-model="entity.pk3Qty" size="mini" style="width: 200px;"
                  placeholder="请输入数量"></el-input>
                </el-form-item> -->
        </el-form>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="hexiangtwo=!hexiangtwo">确 定</el-button>
        <!-- <el-button size="mini" @click="unpackbox=!unpackbox">取 消</el-button> -->
      </span>
    </el-dialog>
    <el-dialog title="信息" style="padding: 0px;" :close-on-click-modal="false" :visible.sync="hexiang" width="30%">
      <div style="center='center'">
        <el-form status-icon inline-message label-width="85px" :model="entity" ref="addEntityForm"
          style="margin: 0px;padding: 0px;">
          <span>周转箱：【</span><span>{{qtyFinstring}}</span><span>】未报工! 不能合箱</span>
          <!-- <el-form-item  label="箱数:" prop="boxCount" style="margin-bottom:8px;">
                  <el-input prefix-icon="el-icon-edit" type="number" @wheel.native.prevent="stopScroll($event)" :disabled="isEdit" v-model="entity.boxCount" size="mini" style="width: 200px;"
                  placeholder="请输入箱数"></el-input>   
                </el-form-item>
                <el-form-item  label="数量:" prop="pk3Qty" v-if="isEdit" style="margin-bottom:8px;">
                  <el-input prefix-icon="el-icon-edit" type="number" @wheel.native.prevent="stopScroll($event)" :disabled="isEdit"  v-model="entity.pk3Qty" size="mini" style="width: 200px;"
                  placeholder="请输入数量"></el-input>
                </el-form-item> -->
        </el-form>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="hexiang=!hexiang">确 定</el-button>
        <!-- <el-button size="mini" @click="unpackbox=!unpackbox">取 消</el-button> -->
      </span>
    </el-dialog>
    <el-dialog title="开箱" style="padding: 0px;" :close-on-click-modal="false" :visible.sync="unpackbox" width="30%">
      <div style="center='center'">
        <el-form status-icon inline-message label-width="85px" :model="entity" ref="addEntityForm"
          style="margin: 0px;padding: 0px;">
          <el-form-item label="箱数:" prop="boxCount" style="margin-bottom:8px;">
            <el-input prefix-icon="el-icon-edit" type="number" @wheel.native.prevent="stopScroll($event)"
              :disabled="isEdit" v-model="entity.boxCount" size="mini" style="width: 200px;"
              placeholder="请输入箱数"></el-input>
          </el-form-item>
          <el-form-item label="数量:" prop="pk3Qty" v-if="isEdit" style="margin-bottom:8px;">
            <el-input prefix-icon="el-icon-edit" type="number" @wheel.native.prevent="stopScroll($event)"
              :disabled="isEdit" v-model="entity.pk3Qty" size="mini" style="width: 200px;"
              placeholder="请输入数量"></el-input>
          </el-form-item>
        </el-form>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="unpackboxadd('addEntityForm')">确 定</el-button>
        <el-button size="mini" @click="unpackbox=!unpackbox">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="异常拆箱" style="padding: 0px;" :close-on-click-modal="false" :visible.sync="abnormal" width="30%">
      <div style="center='center'">
        <el-form status-icon inline-message label-width="85px" :model="entity" ref="addEntityForm"
          style="margin: 0px;padding: 0px;">
          <el-form-item label="原转箱号:" prop="boxNo" style="margin-bottom:8px;">
            <div style="font-size:12px;">{{entity2.boxNo}}</div>
            <!-- <el-input prefix-icon="el-icon-edit" v-model="entity.boxNo" size="mini" style="width: 200px;"
                            placeholder="请输入原转箱号"></el-input> -->
          </el-form-item>
          <el-form-item label="拆箱总量:" prop="wrQtyMax" style="margin-bottom:8px;">
            <div style="font-size:12px;">{{entity2.wrQtyMax}}</div>
          </el-form-item>
          <!-- <el-form-item v-if="tgFlatyc"   label="拆箱总量:" prop="wrQtyMax" style="margin-bottom:8px;">
                  <div style="font-size:12px;">{{entity2.wrQtyMax}}</div>
                </el-form-item>
                <el-form-item v-else  label="拆箱总量:" prop="qtyFin" style="margin-bottom:8px;">
                  <div style="font-size:12px;">{{entity2.qtyFin}}</div>
                </el-form-item> -->
          <el-form-item label="完工量:" prop="qtyFin" style="margin-bottom:8px;">
            <div style="font-size:12px;">{{entity2.qtyFin}}</div>
            <!-- <el-input prefix-icon="el-icon-edit" v-model="entity.qtyFin" size="mini" style="width: 200px"
                            placeholder="请输入完工量"></el-input> -->
          </el-form-item>
          <el-form-item label="原箱数:" prop="boxQty1" style="margin-bottom:8px;">
            <el-input prefix-icon="el-icon-edit" type="number" @input="pastboxqty2"
              @wheel.native.prevent="stopScroll($event)" v-model="entity2.boxQty1" size="mini" style="width: 200px"
              placeholder="请输入原箱数"></el-input>
          </el-form-item>
          <el-form-item label="新箱数:" prop="boxQty2" style="margin-bottom:8px;">
            <el-input prefix-icon="el-icon-edit" type="number" @input="newboxqty2"
              @wheel.native.prevent="stopScroll($event)" v-model="entity2.boxQty2" size="mini" style="width: 200px"
              placeholder="请输入新箱数"></el-input>
          </el-form-item>
          <el-form-item label="副数量:" prop="name" type="number" style="margin-bottom:8px;">
            <el-input prefix-icon="el-icon-edit" v-model="entity2.name" size="mini" style="width: 200px"
              placeholder="请输入副数量"></el-input>
          </el-form-item>
        </el-form>

      </div>
      <span slot="footer" class="dialog-footer">

        <el-button size="mini" type="primary" @click="abnormalunboxmeth('addEntityForm')">确 定</el-button>
        <el-button size="mini" @click="abnormal=!abnormal">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="拆箱" style="padding: 0px;" :close-on-click-modal="false" :visible.sync="tearopen" width="30%">
      <div style="center='center'">
        <el-form status-icon inline-message label-width="85px" :model="entity" ref="addEntityForm"
          style="margin: 0px;padding: 0px;">
          <el-form-item label="原转箱号:" prop="boxNo" style="margin-bottom:8px;">
            <div style="font-size:12px;">{{entity.boxNo}}</div>
            <!-- <el-input prefix-icon="el-icon-edit" v-model="entity.boxNo" size="mini" style="width: 200px;"
                            placeholder="请输入原转箱号"></el-input> -->
          </el-form-item>
          <el-form-item v-if="tgFlatflag" label="拆箱总量:" prop="wrQtyMax" style="margin-bottom:8px;">
            <!--wrQtyMax  -->
            <div style="font-size:12px;">{{entity.wrQtyMax}}</div>
          </el-form-item>
          <el-form-item v-else label="拆箱总量:" prop="qtyFin" style="margin-bottom:8px;">
            <div style="font-size:12px;">{{entity.qtyFin}}</div>
          </el-form-item>
          <el-form-item label="完工量:" prop="qtyFin" style="margin-bottom:8px;">
            <div style="font-size:12px;">{{entity.qtyFin}}</div>
            <!-- <el-input prefix-icon="el-icon-edit" v-model="entity.qtyFin" size="mini" style="width: 200px"
                            placeholder="请输入完工量"></el-input> -->
          </el-form-item>
          <!-- disabled -->
          <el-form-item label="原箱数:" prop="boxQty1" style="margin-bottom:8px;">
            <el-input prefix-icon="el-icon-edit" type="number" @input="pastboxqty"
              @wheel.native.prevent="stopScroll($event)" v-model="entity.boxQty1" size="mini" style="width: 200px"
              placeholder="请输入原箱数"></el-input>
          </el-form-item>
          <el-form-item label="新箱数:" prop="boxQty2" style="margin-bottom:8px;">
            <el-input prefix-icon="el-icon-edit" type="number" @input="newboxqty"
              @wheel.native.prevent="stopScroll($event)" v-model="entity.boxQty2" size="mini" style="width: 200px"
              placeholder="请输入新箱数"></el-input>
          </el-form-item>
          <el-form-item label="副数量:" prop="name" type="number" style="margin-bottom:8px;">
            <el-input prefix-icon="el-icon-edit" v-model="entity.name" size="mini" style="width: 200px"
              placeholder="请输入副数量"></el-input>
          </el-form-item>
        </el-form>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="chaisure('addEntityForm')">确 定</el-button>
        <el-button size="mini" @click="tearopen=!tearopen">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="合箱" :visible.sync="mouldassemb" width="55%" style="height:100%">
      <div class="table-page-search-wrapper" style="height:420px;">
        <!-- <el-input  v-model="queryKeywordzhi" @input="inputNameInputzhi" size="mini" style="width: 150px"
        placeholder="请输入"></el-input> -->
        <el-table :data="multipleSelection" border stripe v-loading="loading" @row-click="mfBxHanddlezhi" style
          height="380"
          :header-cell-style="{fontSize:'14px',color: '#606266',backgroundColor: '#F4F5F9', fontWeight: '700' }">
          <!-- <el-table-column fixed="left" align="center" label="选择" width="80">
              <template slot-scope="scope">
                <el-button
                  round
                  type="primary"
                  style="padding: 5px 5px 5px 5px;"
                  size="mini"
                  icon="el-icon-d-arrow-right"
                  @click="getGoodsPopoverzhi(scope.row)"
                >选择</el-button>
              </template>
            </el-table-column> -->
          <el-table-column width="60px" label="选择" align="center">
            <template slot-scope="scope">
              <el-radio v-model="radio" :label="scope.$index" class="radio"
                @change.native="raidchange(scope.row)">&nbsp;</el-radio>
            </template>
          </el-table-column>
          <el-table-column prop="boxNo" align="left" label="周转箱号"></el-table-column>
          <el-table-column prop="moNo" align="left" label="工单号"></el-table-column>
          <el-table-column prop="qty" align="left" width="90" label="箱数量"></el-table-column>
          <el-table-column prop="qtyFin" align="left" width="90" label="完工量"></el-table-column>
          <el-table-column prop="qty1Fin" align="left" width="90" label="完工量(副)"></el-table-column>
          <el-table-column prop="rqNo" align="left" label="容器号"></el-table-column>
          <el-table-column prop="scNo" align="left" label="生产单号"></el-table-column>
        </el-table>

      </div>
      <span slot="footer" class="dialog-footer">

        <el-button size="mini" type="primary" @click="mergemethod('addEntityForm')">确 定</el-button>
        <el-button size="mini" @click="mergecancel">取 消</el-button>
      </span>
    </el-dialog>
    <el-tabs type="border-card">
      <el-tab-pane label="周转箱作业详情">

        <div ref="leftHeight" v-if="turnoverdata.length>0" style="border:1px solid #e5e5e5;margin:0 auto;margin-right:5px;padding: 10px;padding-top:9px;padding-bottom:9px;
            background-color:#fff;border-radius:10px;">
          <div class="overdata" style="margin-bottom:5px;">
            <span class="overbold">工单号：</span><span style="margin-right:10px;">{{turnoverdata[0].moNo}}</span>
            <span class="overbold">货品：</span><span
              style="margin-right:10px;">{{turnoverdata[0].prdNo}}/{{turnoverdata[0].prdName}}</span>
          </div>
          <div class="overdata">
            <span class="overbold" style="margin-right:10px;">工单数量：</span><span
              style="margin-right:10px;">{{turnoverdata[0].moQty}}</span>
            <span class="overbold">规格：</span><span>{{turnoverdata[0].spc}}</span>
          </div>
        </div>
        <el-row :gutter="20">
          <el-col :span="13">
            <!-- <div class="sup_info_basics_container" style="padding-bottom:5px;"> -->
            <!-- :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }" -->
            <el-table stripe :data="turnoverdata" class="checkdata" highlight-current-row
              style="margin-top:10px;font-weight: 700;" height="400px" @row-click="mfBxHanddle"
              :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '700' }"
              :row-class-name="tableRowClassName">
              <!-- :row-style="selectedstyle" @selection-change="handleSelectionChange" -->
              <!-- @row-dblclick="mfBxHanddle" -->
              <!-- :header-cell-style="{fontSize:'14px',color: '#606266', verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '700' }" -->
              <!-- @sort-change="sortChange" -->
              <!-- <el-table-column type="selection" align="center" width="50"></el-table-column> -->
              <!-- <el-table-column
  width="60px"
  label="选择"
  align="center"
>
  <template slot-scope="scope">
    <el-radio
      v-model="radio"
      :label="scope.$index"
      class="radio"
      @change.native="raidchange(scope.row)"
    >&nbsp;</el-radio>
  </template>
</el-table-column> -->
              <el-table-column prop="zcItm" label="加工顺序" width="49" align="center"></el-table-column>
              <!-- <el-table-column prop="moNo" width="90"  label="工单号"></el-table-column> -->
              <el-table-column prop="scNo" label="生产单" align="center" width="130"></el-table-column>
              <!-- <el-table-column prop="prdNo" width="90"  label="品号"></el-table-column>
                      <el-table-column prop="prdName" width="90"  label="品名"></el-table-column>
                      <el-table-column prop="spc" width="90"  label="规格"></el-table-column> -->
              <el-table-column prop="pgNo" label="派工单" align="center" width="150"></el-table-column>
              <el-table-column prop="qtyPrc" label="在制量" align="center" width="80"></el-table-column>
              <el-table-column prop="zcNo" label="工序" align="center" width="100"></el-table-column>
              <el-table-column prop="zcName" label="工序名称" align="center" width="130"></el-table-column>
            </el-table>
            <el-dialog title="检验项目" :visible.sync="visibleqcItm" width="40%" style="height:100%">
              <div class="table-page-search-wrapper" style="height:400px;">
                <el-input v-model="queryKeyword" @input="inputNameInputtwo" size="mini" style="width: 150px"
                  placeholder="请输入"></el-input>
                <el-table :data="pickerListtwo" border stripe v-loading="loading" @row-click="mfBxHanddleqcItm" style
                  height="360"
                  :header-cell-style="{fontSize:'14px',color: '#606266',backgroundColor: '#F4F5F9', fontWeight: '700' }">
                  <!-- @row-dblclick="mfBxHanddleqcItm" -->
                  <!-- @selection-change="handleChangereson" -->
                  <!-- <el-table-column fixed="left" align="center" label="选择" width="80">
              <template slot-scope="scope">
                <el-button
                  round
                  type="primary"
                  style="padding: 5px 5px 5px 5px;"
                  size="mini"
                  icon="el-icon-d-arrow-right"
                  @click="getGoodsPopover(scope.row)"
                >选择</el-button>
              </template>
            </el-table-column> -->
                  <el-table-column fixed="left" prop="qcItm" align="left" label="检验项目"></el-table-column>
                  <!-- :show-overflow-tooltip="true" -->
                  <el-table-column prop="name" align="left" label="检验名称"></el-table-column>
                </el-table>
                <!-- <div slot="footer" style="float: right;margin-top:20px;">
            <el-button size="mini" @click="cancelGetData">取 消</el-button>
            <el-button size="mini" type="primary" @click="otngetData">确 定</el-button>
      </div> -->
              </div>
            </el-dialog>
            <!-- <div style="display: flex;justify-content: space-between;margin: 2px">
                <el-pagination
                  background
                  :page-sizes="[5, 10, 30, 50]"
                  :page-size="30"
                  :pager-count="5"
                  @size-change="pageSizeChange"
                  :current-page="tablePage.currentPage"
                  @current-change="currentChange"
                  layout="total,sizes,prev, pager, next"
                  :total="totalCount"
                ></el-pagination>
              </div> -->
            <!--focus激活选择货品  -->
            <div @mouseleave="prdt3leave">
              <el-popover placement="bottom" :style="objStyle" width="400" style="position: fixed;z-index: 99999;"
                v-model="visible">
                <div>
                  <el-table class="el-table" :data="pickerList" stripe max-height="360px" size="mini" height="360px"
                    highlight-current-row :cell-style="{ verticalAlign: 'top' }"
                    :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                    style="width: 100%;">
                    <el-table-column fixed="left" align="center" label="选择" width="80">
                      <template slot-scope="scope">
                        <el-button round type="primary" style="padding: 5px 5px 5px 5px;" size="mini"
                          icon="el-icon-d-arrow-right" @click="getGoodsPopover(scope.row)">选择</el-button>
                      </template>
                    </el-table-column>
                    <el-table-column fixed="left" prop="spcNo" align="left" label="原因代号" width="70"></el-table-column>
                    <!-- :show-overflow-tooltip="true" -->
                    <el-table-column prop="name" align="left" label="不合格原因" width="130"></el-table-column>
                  </el-table>
                </div>
                <!-- <div>
          <el-pagination
            background
            :pager-count="5"
            :page-sizes="[5,10, 30, 50]"
            :page-size="10"
            @size-change="goods_pageSizeChange"
            :current-page="goods_currentPage"
            @current-change="goods_currentChange"
            layout="prev, pager, next"
            :total="goods_pickerCount"
            style="margin-top: 5px;"
          ></el-pagination>
        </div> -->
              </el-popover>
            </div>
          </el-col>

          <el-col :span="11">
            <!-- turnoverdata[workItem].mesTfBoxList -->
            <div v-if="turnoverdata.length>0">
              <el-table stripe :data="turnoverdata[workItem].mesTfBoxList" highlight-current-row
                :cell-style="{ verticalAlign: 'top',fontWeight: '700' }"
                :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '700' }"
                style="width: 100%;margin-top:10px;font-weight: 700;" @selection-change="handleSelectionChange"
                height="570" v-loading="loadingtwo">
                <!-- :header-cell-style="{fontSize:'14px',color: '#606266', verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '700' }" -->
                <!-- @sort-change="sortChange" -->
                <!-- <el-table-column type="selection" align="center" width="50"></el-table-column> -->
                <!-- <el-table-column
                  width="60px"
                  label="选择"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-radio
                      v-model="radio"
                      :label="scope.$index"
                      class="radio"
                      @change.native="raidchange(scope.row)"
                    >&nbsp;</el-radio>
                  </template>
                </el-table-column> -->
                <el-table-column type="selection" width="45"></el-table-column>
                <!-- <el-table-column width="30" align="left" prop="itm" label="项次">
                   <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                </el-table-column> -->
                <el-table-column prop="boxNo" width="148" label="周转箱"></el-table-column>
                <!-- <el-table-column prop="moNo" width="148"  label="工单号"></el-table-column> -->
                <!-- <el-table-column prop="moNo" width="90"  label="工单号"></el-table-column> -->
                <el-table-column prop="qty" label="箱数量"></el-table-column>
                <el-table-column prop="qtyFin" label="完工量"></el-table-column>
                <el-table-column prop="qty1Fin" label="副完工量"></el-table-column>
                <!-- <el-table-column   label="操作">
                  <template slot-scope="scope">
                    <el-button type="primary" @click="deldata(scope.row)" style="background-color: #f56c6c;border-color: #f56c6c;color:#fff;">删除</el-button>
                  </template>
                </el-table-column> -->
                <!-- <el-table-column prop="scNo" width="90"  label="scNo"></el-table-column> -->
              </el-table>
            </div>
            <!-- <div style="display: flex;justify-content: space-between;margin: 2px">
                <el-pagination
                  background
                  :page-sizes="[5, 10, 30, 50]"
                  :page-size="30"
                  :pager-count="5"
                  @size-change="pageSizeChange"
                  :current-page="tablePage.currentPage"
                  @current-change="currentChange"
                  layout="total,sizes,prev, pager, next"
                  :total="totalCount"
                ></el-pagination>
              </div> -->

          </el-col>
        </el-row>
        <!-- <uploadFile
      ref="uploadFile"
      @seeFile="seeFile"
    />
   <ModalPic ref="ModalPic" /> -->
      </el-tab-pane>
    </el-tabs>
    <print-dialog ref="printDialog" />
  </div>
</template>
<script>
  import {
    datadepPage, qcSpctyDel, qcSpcqcAdd, sptMerCheck, qcSpcdjInfo, qcSpcqcUpd, spcLstpage, sysfiledel, basicDataqcItm, basicDatatool, tfBoxinfo,
    getBoxCount, tfBoxaddBox, tfBoxmerge, tfBoxsptBox, errSptBoxPc, tfBoxdel, boxPrintAll, boxInfoByScNo, sptBoxPc, getPlate
  } from '@/api/mes/quality'
  import { mapGetters, mapState } from 'vuex'
  import printDialog from '@/components/printDialog/index'
  import qs from 'qs'
  // import uploadFile from './uploadFile'
  // import ModalPic from './ModalPic'
  // import ModalPic from '../inspectionForm/ModalPic'
  import moment from 'moment'
  import QRCode from 'qrcodejs2'
  import MySelectListwo from '@/components/MySelectListwo'
  // import formList from './formList'
  // import inspection from './inspection'

  export default {
    components: {
      // uploadFile,
      //  ModalPic,
      printDialog,
      MySelectListwo
    },
    data() {
      return {
        tablePagefour: {
          currentPage: 1,
          pageSize: 20,
          total: 0
        },
        totalCountfour: '',
        exitRules: {
          dep: [{ required: true, message: "必填:部门", trigger: ["change", "blur"] }],
        },
        isEdit: true,
        mes_quality_query: 'mes_quality_query',
        mes_quality_reset: 'mes_quality_reset',
        mes_quality_newOpen: 'mes_quality_newOpen',
        mes_quality_inspection: 'mes_quality_inspection',
        mes_quality_pause: 'mes_quality_inspection',
        mes_quality_continues: 'mes_quality_continues',
        mes_quality_task: 'mes_quality_task',
        tableData: [],

        loading: false,
        tablePage: {
          currentPage: 1,
          pageSize: 5,
          total: 0
        },
        row: {},
        rowIndex: {},
        // 按钮控制
        btn: {
          newOpen: true, // 首检开工
          open: true, // 开始
          pause: true, // 暂停
          continues: true, // 继续
          report: true, // 报工
          exceptional: true, // 异常
          inspection: true, // 送检
          introspecting: true // 自检
        },
        queryParam: {
          date: [],
        },
        activeName: '1',
        visible: false,
        reasondata: [],
        multiplereason: [],
        entity: {
          qty: null,
          boxCount: '',
          pk3Qty: '',
          qtyFin: '',
          boxNo: '',
          boxQty1: '',
          boxQty2: '',
          qtyLost: '',
          tyDd: '',
          chkKnd: '',
          qcId: '',
          sys: '',
          chktyId: '',
          tiNo: '',
          tyNo: '',
          prdt3Name: "",
          way: 2,
          auxId: "",
          otRemark: "",
          bxId: "",
          qaId: "",
          otId: "",
          bxNo: "",
          bxDd: "",
          status: "",
          statusMsg: "",
          kndId: 3,
          applyDepId: null,
          applyDepNo: "",
          applyDepName: "",
          applyUserId: "",
          applyUserNo: "",
          applyUserName: "",
          prdId: "",
          prdNo: "",
          prdName: "",
          prdt3Id: "",
          prdSpc: "",
          faultRem: "",
          cntId: "",
          cntName: "",
          cntTel: "",
          cntAdr: "",
          dcId: "",
          dcName: "",
          dcLmt: "",
          urgent: 0,
          finId: "",
          finName: "",
          finLmt: "",
          bxPic1: "",
          bxPic2: "",
          bxPic3: "",
          serverDeptId: "",
          otUserId: "",

          serviceCode: "",
          coDd: null,
          prdUt: "",
          bxType: '1',
          initialTime: '',
          completeTime: '',
          name: ''
        },
        entity2: {
          boxQty1: '',
          boxQty2: '',
          qtyFin: '',
          boxNo: '',
          name: ''
        },
        qualifijudgment: [
          { value: 'T', label: '合格' },
          { value: 'F', label: '不合格' },
        ],
        inspectiontype: [
          { value: '1', label: '完工检验' },
          { value: '2', label: '首检检验' },
          { value: '3', label: '托工检验' },
        ],
        multipleSelectiontwo: [],
        sysFiles: [],
        colData: [
          { title: "原因代号", istrue: true },
          { title: "不合格原因", istrue: true },
          { title: "不合格量", istrue: true },
          { title: "货品名称", istrue: true },
          { title: "货品代号", istrue: true },
          { title: "规格", istrue: true },
          { title: "现有库存", istrue: true },
          { title: "借出量", istrue: true },
          { title: "单位", istrue: true },
          { title: "单价", istrue: true },
          { title: "数量", istrue: true },
          { title: "已还数量", istrue: true },
          { title: "税率%", istrue: true },
          { title: "未税金额", istrue: true },
          { title: "税额", istrue: true },
          { title: "金额", istrue: true },
        ],
        objStyle: {
          top: "433px",
          left: "",
        },
        pickerList: [],
        testData: [],
        detailIds: [],
        multipleSelection: [],
        pickerIndex: 0,
        isShowPopVel: false,
        rightHeight: '',
        tablerightHeight: '',
        tyNotwo: '',
        tyDdtwo: '',
        flag: false,
        ccc: true,
        isClaim: false,
        data: '',
        tableLoading: false,
        bottomWidth: '',
        inspectData: [],
        inspectDatatwo: [],
        chkIdqty: '',
        qcItmflag: 0,
        inspectDataflag: true,
        getIndex: null,
        queryKeyword: '',
        isClaimtwo: false,
        queryKeywordtwo: [],
        visibleqcItm: false,
        pickerListtwo: [],
        pickerIndextwo: '',
        toolLIst: [],
        typetwo: [
          { value: '1', label: '计数' },
          { value: '2', label: '测量' },
        ],
        stdValueLIst: [
          { value: '±', label: '±' },
          { value: '>=', label: '>=' },
          { value: '<=', label: '<=' },
          { value: '=', label: '=' },
          { value: '+-', label: '+-' },
          { value: '~~', label: '~~' },
        ],
        mouldassemb: false,
        mouldassembList: [],
        unpackList: [],
        unpackbox: false,
        abnormal: false,
        tearopen: false,
        turnoverdata: [],
        workItem: 0,
        selBox: '',
        radio: '',
        printObj2: {
          id: "",
        },
        isPrint: false,
        isPrintButton: false,
        printdata: [],
        curidList: [],
        Len: null,
        timeKey: '',
        tenantId: '',
        boxNoList: [],
        printdatatwo: [],
        tgFlatflag: false,
        isPrintone: false,
        printmodal: [
          { name: '01A套版1(100mm*150mm)', id: 1 },
          { name: '01B套版2(100mm*150mm)', id: 2 },
          { name: '02A套版1(80mm*60mm)', id: 3 },
        ],
        taoban: { id: 1 },
        showFlag: false,
        loadingtwo: false,
        qtyFinstring: '',
        hexiang: false,
        mvboNostring: '',
        mvNostring: '',
        hexiangtwo: false,
        newday: '',
        idmoNo: '',
        typrorder: '',
        boxNoprintbashi: [],
        datebashi: ''
      }
    },
    computed: {
      ...mapState({
        userInfo: state => state.user.userInfo
      }),
      ...mapGetters(['permissions'])
    },
    created() {
      this.tenantId = this.$store.state.user.userInfo.tenantId
      this.entity.tyDd = moment(new Date()).format('YYYY-MM-DD')
      console.log(this.entity.tyDd, 'nnnnnnnnnnnn')
      this.sysFiles = []
      this.tableAdd()
      if (localStorage.getItem('zhouzhuanmoNo')) {
        this.idmoNo = localStorage.getItem('zhouzhuanmoNo')
      } else {
        this.idmoNo = JSON.parse(localStorage.getItem('zhouzhuanmosecond')).moNo
        this.typrorder = JSON.parse(localStorage.getItem('zhouzhuanmosecond')).typrorder
      }
      // if(this.$route.params.id){
      //    this.entity.tiNo = this.$route.params.id
      //    this.getListtreat()
      // }else{
      //   this.entity.tyNo = this.$route.params.tyNo
      //   this.getList()
      // }
      this.getList()

      //     this.startTime = this.$moment().subtract(30, "days").format('YYYY-MM-DD');
      // this.endTime = this.$moment(new Date()).format('YYYY-MM-DD');
    },
    activated() {
      if (localStorage.getItem('zhouzhuanmoNo')) {
        this.idmoNo = localStorage.getItem('zhouzhuanmoNo')
      } else {
        this.idmoNo = JSON.parse(localStorage.getItem('zhouzhuanmosecond')).moNo
        this.typrorder = JSON.parse(localStorage.getItem('zhouzhuanmosecond')).typrorder
      }
      this.getList()
    },
    mounted() {

      //  var array = [
      //     {id:1,name:'Alice'},
      //     {id:2,name:'Alice'},
      //     {id:1,name:'Alice'},
      //     {id:3,name:'Alice'},
      //   ]
      //   var mergedArray = array.reduce(function(result,obj){ 
      //     var target = result.find(function(item){
      //       return item.id === obj.id
      //     })
      //     if(target){
      //       Object.assign(target,obj)
      //     }else{ 
      //       result.push(obj)
      //     }
      //     return result
      //   },[])

      getBoxCount({
        moNo: this.idmoNo,
      }).then((res) => {
        if (res.code === 0) {
          if (res.msg == 'success') {
            this.entity.boxCount = res.data.boxCount
            this.entity.pk3Qty = res.data.pk3Qty
            if (res.data.openFirstWr == "true") {
              this.isEdit = false
              // that.$message.success(res.data)
            } else {
              this.isEdit = true
              // that.$message.error(res.data)
            }
          } else {
            this.$message.warning(res.data.msg)
          }
        }
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
      this.pickerSearch()
      // this.$nextTick(() => {
      //   this.rightHeight = this.$refs.leftHeight.offsetHeight + 6 + 'px'
      //   this.bottomWidth = this.$refs.leftHeight.offsetWidth + 6 + 'px'
      //   // :height="tablerightHeight"
      //   this.tablerightHeight = this.$refs.leftHeight.offsetHeight - 65 + 'px'
      // })
      basicDatatool(
        {
          current: 1,
          size: 1000,
          qcTool: ''
        }).then(response => {
          this.toolLIst = response.data
        }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    watch: {
      // inspectData (val) {
      //   const newlocale = val
      //   const calendarApi = this.$refs.fullCalendar.getApi()
      //   calendarApi.setOption('locale', newlocale)
      // },
    },
    methods: {
      printbashi() {
        if (this.multipleSelection.length < 1) {
          this.$message.warning('请选择至少一条数据打印')
          return
        }
        this.datebashi = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        this.boxNoprintbashi = this.multipleSelection
        console.log(this.boxNoprintbashi, '8888888899999')
        this.isPrint = true
        this.printObj2.id = 'formbashi'

      },
      cancelEidt() { this.isPrintone = false },

      delradio() {
        if (this.multipleSelection.length == 1) {
          let boxNonum = this.multipleSelection[0].boxNo
          const that = this

          this.$confirm(
            this.$t('identifier.delcont'),   // 内容
            this.$t('public.del.title'),      // 标题
            {
              confirmButtonText: this.$t('public.sure'),
              cancelButtonText: this.$t('public.cancel'),
              type: 'warning'  // 等同于 `okType: 'warn'`
            }
          ).then(() => {
              // 在这里处理确认后的逻辑
              that.loading = true
              tfBoxdel({
                boxNos: [boxNonum],
              }).then((res) => {
                if (res.code === 0) {
                  if (res.msg === 'success') {
                    that.$message.success(res.data)
                    that.getList()
                  } else {
                    that.$message.error(res.data)
                  }
                }
              }).catch(err => that.requestFailed(err))
                .finally(() => {
                  that.loading = false
                })
            })
            .catch(() => {
              this.$message.error('删除失败！')
            });
        } else {
          this.$message.warning('请选择一箱进行删除，不能勾选多箱！')
        }
      },
      printClose() {
        this.isPrint = false
      },
      printSure() {
        this.isPrint = false
        if (this.taoban.id == 2 || this.taoban.id == 1) {
          this.toperwei()
          this.erweimatwo()
          if (this.printdatatwo.length <= 0) {
            this.Len = 1
          } else {
            this.Len = Math.ceil(this.printdatatwo.length / 10.0)
          }
          var amtnTotal = 0
          var numTotal = 0
          // this.printdata.forEach((i) => {
          //   i.qtyBj = parseFloat(i.qtyBj).toFixed(1)
          //   let amtn = +i.qtyBj * +i.up
          //   i.amtn = this.toDecimal2(amtn)
          //   amtnTotal += +i.amtn
          //   numTotal += +i.qtyBj
          // })
          // this.amtnTotal = amtnTotal.toFixed(2)
          // this.numTotal = numTotal.toFixed(2)
          for (var i = 0; i < this.boxNoList.length; i++) {
            this.boxNoList[i][0].curidList = []
          }

          this.curidListtwo = []
          for (var i = 0; i < this.boxNoList.length; i++) {
            for (var d = 0; d < this.boxNoList[i].length; d = d + 8) {
              let curidarrytwo = {}
              curidarrytwo = { label: d / 8 }
              this.boxNoList[i][0].curidList.push(curidarrytwo)
              // this.curidListtwo.push(curidarrytwo)
            }
          }
          this.curidList = []
          for (var d = 0; d < this.printdatatwo.length; d = d + 10) {
            // let amtnTotal2 = 0
            // let qtyTotal2 = 0
            // for (var f = d; f < this.printdata.length; f++) {
            //   if (f < d + 10) {
            //     let amtn2 = +this.printdata[f].qtyBj * +this.printdata[f].up
            //     this.printdata[f].amtn = this.toDecimal2(amtn2)
            //     amtnTotal2 += +this.printdata[f].amtn
            //     qtyTotal2 += +this.printdata[f].qtyBj
            //   }
            // }
            // amtnTotal2 = amtnTotal2.toFixed(2)
            // qtyTotal2 = qtyTotal2.toFixed(2)

            let curidarry = {}
            // curidarry = { label: d / 10, value: amtnTotal2, qty: qtyTotal2 }
            curidarry = { label: d / 10 }
            this.curidList.push(curidarry)
          }
          // 自己写打印备注
        } else if (this.taoban.id == 3) {
          this.methodserweima()
        }
      },

      qrcodeerweima() {
        this.$nextTick(() => {
          const dom = 'yuxin'
          if (document.getElementById(dom).innerHTML === null || document.getElementById(dom).innerHTML === '') {
          } else {
            document.getElementById(dom).innerHTML = ''
          }
        })
        this.$nextTick(() => {
          new QRCode('yuxin', {
            width: 50,
            height: 50,
            text: this.printdata[0].boxNo // 二维码地址
          })
        })
      },
      erweima() {
        this.$nextTick(() => {
          // for (let i = 0; i < this.tableerweima.length; i++) {
          //   const dom = 'yuxin' + i
          //   if (document.getElementById(dom).innerHTML === null || document.getElementById(dom).innerHTML === '') {
          //   } else {
          //     document.getElementById(dom).innerHTML = ''
          //   }
          // }
          const dom = 'scNoerwei'
          if (document.getElementById(dom).innerHTML === null || document.getElementById(dom).innerHTML === '') {
          } else {
            document.getElementById(dom).innerHTML = ''
          }
        })
        this.$nextTick(() => {
          // for (let i = 0; i < this.tableerweima.length; i++) {
          //   new QRCode('yuxin'+ i, {
          //     width: 90,
          //     height: 90,
          //     text: this.tableerweima[i].prnCode // 二维码地址
          //   })
          // }
          new QRCode('scNoerwei', {
            width: 90,
            height: 90,
            text: this.printdatatwo[0].scNo // 二维码地址
          })
        })
      },
      methodserweima() {
        this.$nextTick(() => {
          for (let j = 0; j < this.boxNoprintbashi.length; j++) {
            const dom = 'erweimabashi' + j
            if (document.getElementById(dom).innerHTML === null || document.getElementById(dom).innerHTML === '') {
            } else {
              document.getElementById(dom).innerHTML = ''
            }
          }

        })
        this.$nextTick(() => {
          for (let j = 0; j < this.boxNoprintbashi.length; j++) {
            new QRCode('erweimabashi' + j, {
              width: 50,
              height: 50,
              text: this.boxNoprintbashi[j].boxNo // 二维码地址
            })
          }
        })
      },
      toperwei() {
        // this.bodyObj2.forEach((item, index) => {
        this.$nextTick(() => {
          for (let j = 0; j < this.boxNoList.length; j++) {
            for (let i = 0; i < this.boxNoList[j][0].curidList.length; i++) {
              const dom8 = 'yuxin' + this.boxNoList[j][0].addid + i
              const dom10 = 'yuxinright' + this.boxNoList[j][0].addid + i
              // const dom9 = 'yuxin' + i
              if (document.getElementById(dom8).innerHTML === null || document.getElementById(dom8).innerHTML === '') {
              } else {
                document.getElementById(dom8).innerHTML = ''
              }

              if (document.getElementById(dom10).innerHTML === null || document.getElementById(dom10).innerHTML === '') {
              } else {
                document.getElementById(dom10).innerHTML = ''
              }
              // if (document.getElementById(dom9).innerHTML === null || document.getElementById(dom9).innerHTML === '') {
              // } else {
              //   document.getElementById(dom9).innerHTML = ''
              // }
            }
          }
        })
        // })
        // this.bodyObj2.forEach((item, index) => {
        this.$nextTick(() => {
          // for (let i = 0; i < this.printdatatwo.length; i++) {
          //   // this.tableDatapopup[i].qrcodeLeftForm1 = 'qrcodeLeftForm1' + i
          //   // let indexForm1 = i.toString()
          //   new QRCode('yuxin' + this.printdatatwo[i].addid, {
          //     width: 50,
          //     height: 50,
          //     text: this.printdatatwo[i].scNo // 二维码地址
          //   })
          // }
          for (let j = 0; j < this.boxNoList.length; j++) {
            for (let i = 0; i < this.boxNoList[j][0].curidList.length; i++) {
              // for (let i = 0; i < this.boxNoList[j].length; i++) {

              new QRCode('yuxin' + this.boxNoList[j][0].addid + i, {
                width: 65,
                height: 65,
                text: this.boxNoList[j][0].boxNo // 二维码地址
              })

              new QRCode('yuxinright' + this.boxNoList[j][0].addid + i, {
                width: 42,
                height: 42,
                text: this.boxNoList[j][0].prdName // 二维码地址
              })
              // }
            }
          }
        })
        // })
      },
      erweimatwo() {
        // this.bodyObj2.forEach((item, index) => {
        this.$nextTick(() => {
          for (let j = 0; j < this.boxNoList.length; j++) {
            for (let i = 0; i < this.boxNoList[j].length; i++) {
              // for (let i = 0; i < this.printdatatwo.length; i++) {
              const dom8 = 'scNoerwei' + this.boxNoList[j][i].addid
              // const dom9 = 'scNoerwei' + i
              if (document.getElementById(dom8).innerHTML === null || document.getElementById(dom8).innerHTML === '') {
              } else {
                document.getElementById(dom8).innerHTML = ''
              }
              // if (document.getElementById(dom9).innerHTML === null || document.getElementById(dom9).innerHTML === '') {
              // } else {
              //   document.getElementById(dom9).innerHTML = ''
              // }
            }
          }
        })
        // })
        // this.bodyObj2.forEach((item, index) => {
        this.$nextTick(() => {
          for (let j = 0; j < this.boxNoList.length; j++) {
            for (let i = 0; i < this.boxNoList[j].length; i++) {
              // this.tableDatapopup[i].qrcodeLeftForm1 = 'qrcodeLeftForm1' + i
              // let indexForm1 = i.toString()
              new QRCode('scNoerwei' + this.boxNoList[j][i].addid, {
                width: 46,
                height: 46,
                text: this.boxNoList[j][i].scNo // 二维码地址
              })
            }
          }
        })

        // })
      },
      printtwo() {
        if (this.multipleSelection.length < 1) {
          this.$message.warning('请选择至少一条数据打印')
          return
        }
        this.timeKey = new Date().getTime()
        let boxNoList = []
        this.multipleSelection.forEach(item => {
          boxNoList.push(item.boxNo)
        })
        let printData = { 'timeKey': this.timeKey, bill_type: 'boxPrint', tenantId: this.tenantId, boxNos: boxNoList }
        this.$refs.printDialog.create(printData)
      },
      printone() {
        if (this.multipleSelection.length < 1) {
          this.$message.warning('请选择至少一条数据打印')
          return
        }
        getPlate().then((res) => {
          if (res.data) {
            this.taoban.id = res.data
          }
        }).catch(err => this.requestFailed(err))
        this.isPrintone = true
      },
      printaddEntity() {
        if (this.taoban.id == 2) {
          this.print()
          this.showFlag = true
          // document.getElementById('watermark').style.display='block'
        } else if (this.taoban.id == 3) {
          this.printbashi()
        } else {
          this.print()
          this.showFlag = false
          // document.getElementById('watermark').style.display='none'
        }
        this.isPrintone = false
      },
      print() {
        // this.newday = moment(new Date()).format("YYYY-MM-DD hh:mm:ss")
        var d = new Date();
        var year = d.getFullYear();
        var month = d.getMonth();
        month = month + 1 > 12 ? 1 : month + 1;
        month = month > 9 ? month : "0" + month.toString();
        var day = d.getDate();
        var hour = d.getHours();
        hour = hour > 9 ? hour : "0" + hour.toString();
        var minute = d.getMinutes();
        minute = minute > 9 ? minute : "0" + minute.toString();
        var second = d.getSeconds();
        this.newday = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
        // moment(new Date()).format("YYYY-MM-DD hh:mm:ss”)
        if (this.multipleSelection.length < 1) {
          this.$message.warning('请选择至少一条数据打印')
          return
        }
        this.timeKey = new Date().getTime()
        let boxNoList = []
        this.boxNoList = []
        this.multipleSelection.forEach(item => {
          boxNoList.push(item.boxNo)
        })
        // let printData = { 'timeKey': this.timeKey, bill_type: 'boxPrint', tenantId: this.tenantId,boxNos:boxNoList}
        // this.$refs.printDialog.create(printData)

        this.loading = true
        boxPrintAll(
          // Object.assign(
          {
            plateId: this.taoban.id,
            boxNos: qs.parse(boxNoList),
          }
          // )
        ).then((res) => {
          this.loading = false
          if (res.code === 0) {
            this.printdatatwo = res.data
            this.printdatatwo.forEach((item, index) => {
              item.addid = index
            })


            //  var arr=[{name:2,id:3},{name:2,id:4},{name:3,id:5},{name:3,id:6},{name:1,id:1},{name:1,id:2}];
            var arr = this.printdatatwo
            // 传入一个数组
            // 按照特定方式格式化
            function sortArr(arr, str) {
              var _arr = [],
                _t = [],
                // 临时的变量
                _tmp;

              // 按照特定的参数将数组排序将具有相同值得排在一起
              arr = arr.sort(function (a, b) {
                var s = a[str],
                  t = b[str];

                return s < t ? -1 : 1;
              });

              if (arr.length) {
                _tmp = arr[0][str];
              }

              // 将相同类别的对象添加到统一个数组
              for (var i in arr) {

                if (arr[i][str] === _tmp) {

                  _t.push(arr[i]);
                } else {
                  _tmp = arr[i][str];
                  _arr.push(_t);
                  _t = [arr[i]];
                }
              }
              // 将最后的内容推出新数组
              _arr.push(_t);

              return _arr;
            }
            sortArr(arr, 'boxNo');
            this.boxNoList = sortArr(arr, 'boxNo')

            // this.boxNoList.forEach((item,index)=>{
            //   item.addid = index
            // })
            this.isPrint = true
            this.printObj2.id = 'form1'
          }
        }).catch(err => {
          this.loading = false
          this.requestFailed(err)
        }).finally(() => {
          this.loading = false
        })
        this.isPrintButton = false

      },
      abnormalunboxmeth() {

        if (this.entity2.boxQty1 == '') {
          this.entity2.boxQty1 = 0
        }
        if (this.entity2.boxQty2 == '') {
          this.entity2.boxQty2 = 0
        }
        errSptBoxPc({
          // qty:this.entity.qty,
          boxNo: this.entity2.boxNo,
          boxQty1: this.entity2.boxQty1,
          boxQty2: this.entity2.boxQty2,
          scNo: this.multipleSelection[0].scNo,
          pgNo: this.multipleSelection[0].pgNo,
          // pk3Qty:this.entity.pk3Qty,
        }).then((res) => {

          if (res.code === 0) {
            this.abnormal = false
            if (res.msg == 'success') {
              this.getList()
              this.$message.success(res.data)
            } else {
              this.$message.warning(res.data)
            }
          }
        }).catch(err => this.requestFailed(err))
          .finally(() => {
            this.abnormal = false
            this.loading = false
          })
      },
      abnormalmeth() {
        this.entity2.name = ''
        if (this.multipleSelection.length == 1) {
          sptMerCheck({
            pgNo: this.multipleSelection[0].pgNo,
            ckType: 2,
            scNo: this.multipleSelection[0].scNo,
            boxNo: this.multipleSelection[0].boxNo
            // pk3Qty:this.entity.pk3Qty,
          }).then((res) => {
            if (res.code === 0) {
              // this.unpackbox = false
              if (res.msg == 'success') {
                // this.getList()
                // this.$message.success(res.data)
                // if(this.multipleSelection[0].bilType == 'TW'){
                if (this.multipleSelection[0].wrQtyMax === 0) {
                  this.$message.warning('没有可拆箱量!')
                  return
                }
                this.entity2.wrQtyMax = this.multipleSelection[0].wrQtyMax
                this.entity2.boxQty2 = this.entity2.wrQtyMax

                // }
                // else{
                //   if(this.multipleSelection[0].qtyFin === 0){
                //     this.$message.warning('没有可拆箱量!')
                //     debugger
                //     return
                //   }
                //   this.entity2.qtyFin = this.multipleSelection[0].qtyFin
                //   this.entity2.boxQty2=this.entity2.qtyFin
                // }
                this.entity2.boxNo = this.multipleSelection[0].boxNo
                this.entity2.qtyFin = this.multipleSelection[0].qtyFin
                this.abnormal = true
                this.entity2.boxQty1 = 0
              } else {
                this.$message.warning(res.data)
              }
            }
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.unpackbox = false
              this.loading = false
            })


          // if(this.multipleSelection[0].wrQtyMax==0){
          //   this.$message.warning('没有可报量不能拆箱！')
          //   return
          // }
          // if(this.multipleSelection[0].qtyFin==0){
          //   this.$message.warning('没有完工量，不能拆箱！')
          //   return
          // }



          // this.entity2.wrQtyMax = this.multipleSelection[0].wrQtyMax
          // this.entity2.boxNo = this.multipleSelection[0].boxNo
          // this.entity2.qtyFin = this.multipleSelection[0].qtyFin

          // this.abnormal = true
          // this.entity2.boxQty1=0
          // this.entity2.boxQty2=this.entity2.wrQtyMax
        } else {
          this.$message.warning('拆箱选择一条数据')
        }
      },
      chaisure() {
        if (this.entity.boxQty1 == '') {
          this.entity.boxQty1 = 0
        }
        if (this.entity.boxQty2 == '') {
          this.entity.boxQty2 = 0
        }
        sptBoxPc({
          // qty:this.entity.qty,
          boxNo: this.entity.boxNo,
          boxQty1: this.entity.boxQty1,
          boxQty2: +this.entity.boxQty2,
          scNo: this.multipleSelection[0].scNo,
          pgNo: this.multipleSelection[0].pgNo,
          // pk3Qty:this.entity.pk3Qty,
        }).then((res) => {

          if (res.code === 0) {
            this.tearopen = false
            if (res.msg == 'success') {
              this.getList()
              this.$message.success(res.data)
            } else {
              this.$message.warning(res.data)
            }
          }
        }).catch(err => this.requestFailed(err))
          .finally(() => {
            this.tearopen = false
            this.loading = false
          })

      },
      mergecancel() {
        this.mouldassemb = false
        this.radio = ''
      },
      raidchange(row) {

        this.selBox = row.boxNo
      },
      mergemethod() {
        if (this.radio !== '' && this.radio !== null) { } else {
          this.$message.warning('请选择一条数据')
          return
        }
        let boxNoList = []
        this.multipleSelection.forEach(item => {
          boxNoList.push(item.boxNo)
        })
        console.log(this.multipleSelection, boxNoList, 'hjklllllllllll')
        tfBoxmerge({
          scNo: this.multipleSelection[0].scNo,
          selBox: this.selBox,
          boxNos: boxNoList,
          pgNo: this.multipleSelection[0].pgNo
          // pk3Qty:this.entity.pk3Qty,
        }).then((res) => {

          if (res.code === 0) {
            this.mouldassemb = false
            if (res.msg == 'success') {
              this.getList()
              this.$message.success(res.data)
            } else {
              this.$message.warning(res.data)
            }
          }
        }).catch(err => this.requestFailed(err))
          .finally(() => {
            this.mouldassemb = false
            this.loading = false
          })
      },
      mouldassembmedth() {
        this.radio = ''
        let boxNoList = []
        if (this.multipleSelection.length > 1) {
          this.multipleSelection.forEach(item => {
            boxNoList.push(item.boxNo)
          })
          sptMerCheck({
            pgNo: this.multipleSelection[0].pgNo,
            ckType: 3,
            scNo: this.multipleSelection[0].scNo,
            boxNos: boxNoList
            // pk3Qty:this.entity.pk3Qty,
          }).then((res) => {
            if (res.code === 0) {
              // this.unpackbox = false
              if (res.msg == 'success') {
                // this.getList()
                // this.$message.success(res.data)




                let qtyFinarry = []
                let mvNoarry = []
                let mvboNoarry = []
                for (let i = 0; i < this.multipleSelection.length; i++) {
                  if (this.multipleSelection[i].qtyFin == 0 || this.multipleSelection[i].qtyFin == '' || this.multipleSelection[i].qtyFin == null) {
                    qtyFinarry.push(this.multipleSelection[i].boxNo)
                  }
                  if (this.multipleSelection[i].mvNo) {
                    mvNoarry.push(this.multipleSelection[i].mvNo)
                    mvboNoarry.push(this.multipleSelection[i].boxNo)
                  }
                }
                if (qtyFinarry.length > 0) {
                  this.hexiang = true
                  this.qtyFinstring = qtyFinarry.join()
                  return
                }
                if (mvNoarry.length > 0) {
                  this.mvboNostring = mvboNoarry.join()
                  this.mvNostring = mvNoarry.join()
                  this.hexiangtwo = true
                  return
                }
                this.mouldassemb = true

              } else {
                this.$message.warning(res.data)
              }
            }
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.unpackbox = false
              this.loading = false
            })

        } else {
          this.$message.warning('请至少选择两条数据')
        }

      },
      tearopenmedth() {
        this.entity.name = ''
        this.tgFlatflag = false
        if (this.multipleSelection.length == 1) {
          sptMerCheck({
            pgNo: this.multipleSelection[0].pgNo,
            ckType: 1,
            scNo: this.multipleSelection[0].scNo,
            boxNo: this.multipleSelection[0].boxNo
            // pk3Qty:this.entity.pk3Qty,
          }).then((res) => {
            if (res.code === 0) {
              // this.unpackbox = false
              if (res.msg == 'success') {
                // this.getList()
                // this.$message.success(res.data)
                console.log(this.multipleSelection[0], 'rrrrrrrrrrrrr')
                if (this.multipleSelection[0].bilType == 'TW') {
                  this.tgFlatflag = true
                  if (this.multipleSelection[0].wrQtyMax === 0) {
                    this.$message.warning('没有可拆箱量!')
                    return
                  }
                  this.entity.wrQtyMax = this.multipleSelection[0].wrQtyMax
                  this.entity.boxQty2 = this.entity.wrQtyMax
                } else {
                  if (this.multipleSelection[0].qtyFin === 0) {
                    this.$message.warning('没有可拆箱量!')
                    return
                  }
                  this.tgFlatflag = false
                  this.entity.qtyFin = this.multipleSelection[0].qtyFin
                  this.entity.boxQty2 = this.entity.qtyFin
                }
                this.entity.boxNo = this.multipleSelection[0].boxNo
                // this.entity.wrQtyMax = this.multipleSelection[0].wrQtyMax
                this.tearopen = true
                this.entity.boxQty1 = 0
                // if(this.multipleSelection[0].tgFlat == 'T'){
                //   this.entity.boxQty2=this.entity.wrQtyMax
                // }else{
                //   this.entity.boxQty2=this.entity.qtyFin
                // }
              } else {
                this.$message.warning(res.data)
              }
            }
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.unpackbox = false
              this.loading = false
            })





          // if(this.multipleSelection[0].tgFlat == 'T'&&this.multipleSelection[0].wrQtyMax==0){
          //     this.$message.warning('没有可报量不能拆箱！')
          //       return
          // }
          // if(this.multipleSelection[0].wrQtyMax>0&&this.multipleSelection[0].qty>0){
          //   if(this.multipleSelection[0].tgFlat == 'T'){
          //     this.tgFlatflag= true
          //   }else{
          //     this.tgFlatflag= false

          //     if(+this.multipleSelection[0].qtyFin>0){

          //     }else{
          //       this.$message.warning('完工量大于0才可以拆箱')
          //       return
          //     }
          //   }
          // }else{
          //   this.$message.warning('无可报量或无箱数量')
          //   return
          // }




        } else {
          this.$message.warning('拆箱选择一条数据')
        }
      },
      unpackboxadd() {
        tfBoxaddBox({
          boxCount: this.entity.boxCount,
          moNo: this.idmoNo,
          // pk3Qty:this.entity.pk3Qty,
        }).then((res) => {
          if (res.code === 0) {
            this.unpackbox = false
            if (res.msg == 'success') {
              this.getList()
              this.$message.success(res.data)
            } else {
              this.$message.warning(res.data)
            }
          }
        }).catch(err => this.requestFailed(err))
          .finally(() => {
            this.unpackbox = false
            this.loading = false
          })
      },
      unpackboxmeth() {
        this.unpackbox = true
      },
      mfBxHanddlezhi() { },
      currentChangefour(currentPage) {
        this.tablePagefour.currentPage = currentPage;
        this.pickerSearchzhi()
      },
      pageSizeChangefour(pageSize) {
        this.tablePagefour.pageSize = pageSize;
        this.pickerSearchzhi()
      },
      qcRecchangebiao(scope) {
        if (scope.row.stdValueId) {
          if (scope.row.stdValue && scope.row.val1) {

            if (scope.row.stdValueId == '±') {
              scope.row.acMin = (+scope.row.stdValue - +scope.row.val1).toFixed(2)
              scope.row.acMax = (+scope.row.stdValue + +scope.row.val1).toFixed(2)
            } else if (scope.row.stdValueId == '=') {
              scope.row.acMin = parseFloat(scope.row.val1).toFixed(2)
              scope.row.acMax = parseFloat(scope.row.val1).toFixed(2)
            } else if (scope.row.stdValueId == '+-') {
              if (scope.row.val1 && scope.row.stdValue) {
                scope.row.acMax = (+scope.row.stdValue + +scope.row.val1).toFixed(2)
              } else {
                scope.row.acMax = ''
              }
              if (scope.row.val2 && scope.row.stdValue) {
                scope.row.acMin = (+scope.row.stdValue - +scope.row.val2).toFixed(2)
              } else {
                scope.row.acMin = ''
              }
            }
          } else {
            scope.row.acMin = ''
            scope.row.acMax = ''
          }
          if (scope.row.stdValue) {

            if (scope.row.stdValueId == '>=') {
              scope.row.acMin = scope.row.stdValue.toFixed(2)
              scope.row.val1 = scope.row.stdValue.toFixed(2)
              scope.row.acMax = ''
            } else if (scope.row.stdValueId == '<=') {
              scope.row.acMin = ''
              scope.row.acMax = scope.row.stdValue.toFixed(2)
              scope.row.val1 = scope.row.stdValue.toFixed(2)
            }
          } else {
            scope.row.acMin = ''
            scope.row.acMax = ''
          }
        } else {
          scope.row.acMin = ''
          scope.row.acMax = ''
          scope.row.val1 = ''
          scope.row.val2 = ''
        }
      },
      blurstdValue(scope) {
        scope.row.stdValue = parseFloat(scope.row.stdValue).toFixed(2)
      },
      rangechange(scope) {

        if (scope.row.val1) {

          scope.row.acMin = (+scope.row.stdValue - +scope.row.val1).toFixed(2)
          scope.row.acMax = (+scope.row.stdValue + +scope.row.val1).toFixed(2)
        } else {
          scope.row.acMin = ''
          scope.row.acMax = ''
        }
      },
      blurval1(scope) {
        scope.row.val1 = parseFloat(scope.row.val1).toFixed(2)
      },
      blurval2(scope) {

        scope.row.val2 = parseFloat(scope.row.val2).toFixed(2)
      },
      topchange(scope) {
        if (scope.row.val1) {
          scope.row.acMin = scope.row.val1.toFixed(2)
        } else {
          scope.row.acMin = ''
          scope.row.acMax = ''
        }
      },
      bottomchange(scope) {
        if (scope.row.val1) {
          scope.row.acMax = scope.row.val1
        } else {
          scope.row.acMin = ''
          scope.row.acMax = ''
        }
      },
      equalchange(scope) {
        if (scope.row.val1) {
          scope.row.acMax = parseFloat(scope.row.val1).toFixed(2)
          scope.row.acMin = parseFloat(scope.row.val1).toFixed(2)
        } else {
          scope.row.acMin = ''
          scope.row.acMax = ''
        }
      },
      addchange(scope) {
        if (scope.row.val1 && scope.row.stdValue) {
          scope.row.acMax = (+scope.row.stdValue + +scope.row.val1).toFixed(2)
        } else {
          scope.row.acMax = ''
        }
      },
      reducechange(scope) {
        if (scope.row.val2 && scope.row.stdValue) {
          scope.row.acMin = (+scope.row.stdValue - +scope.row.val2).toFixed(2)
        } else {
          scope.row.acMin = ''
        }
      },
      stdValueIdchange(scope) {
        if (scope.row.stdValueId) {
          if (scope.row.stdValue && scope.row.val1) {
            if (scope.row.stdValueId == '±') {
              scope.row.acMin = (+scope.row.stdValue - +scope.row.val1).toFixed(2)
              scope.row.acMax = (+scope.row.stdValue + +scope.row.val1).toFixed(2)
              scope.row.val2 = ''
            } else if (scope.row.stdValueId == '=') {

              scope.row.val1 = parseFloat(scope.row.stdValue).toFixed(2)
              scope.row.acMin = parseFloat(scope.row.stdValue).toFixed(2)
              scope.row.acMax = parseFloat(scope.row.stdValue).toFixed(2)
              scope.row.val2 = ''
            } else if (scope.row.stdValueId == '+-') {
              if (scope.row.val1 && scope.row.stdValue) {
                scope.row.acMax = (+scope.row.stdValue + +scope.row.val1).toFixed(2)
              } else {
                scope.row.acMax = ''
              }
              if (scope.row.val2 && scope.row.stdValue) {
                scope.row.acMin = (+scope.row.stdValue - +scope.row.val2).toFixed(2)
              } else {
                scope.row.acMin = ''
              }
            }
          } else {
            scope.row.acMin = ''
            scope.row.acMax = ''
          }
          if (scope.row.stdValue) {
            if (scope.row.stdValueId == '>=') {

              scope.row.acMin = parseFloat(scope.row.stdValue).toFixed(2)
              scope.row.acMax = ''
              scope.row.val2 = ''
            } else if (scope.row.stdValueId == '<=') {
              scope.row.acMin = ''
              scope.row.acMax = parseFloat(scope.row.stdValue).toFixed(2)
              scope.row.val2 = ''
            }
          }
        } else {
          scope.row.acMin = ''
          scope.row.acMax = ''
          scope.row.val1 = ''
          scope.row.val2 = ''
        }
      },
      qcToolinput(scope) {
        scope.row.qcToolName = this.toolLIst.filter(i => i.qcTool === scope.row.qcTool)[0].qcToolName
      },
      mfBxHanddleqcItm({ qcItm, name, qcMth, rem }) {
        let newRow = {
          qcItm,
          name,
          qcMth,
          rem,
        };
        newRow.show = false;
        // this.inspectData[this.qcItmflag].inspectDatatwo[this.pickerIndex].spcNo = spcNo
        this.inspectData[this.pickerIndextwo].qcItm = newRow.qcItm
        this.inspectData[this.pickerIndextwo].name = newRow.name
        this.inspectData[this.pickerIndextwo].qcName = newRow.name
        this.inspectData.forEach((item, i) => {
          item.inspectDatatwo.forEach((item2) => {
            item2.qcItm = item.qcItm
          })
        })
        //  this.tableData[this.pickerIndex].qcItm = newRow.qcItm
        //  this.tableData[this.pickerIndex].name = newRow.name
        // this.$set(this.tableData, this.pickerIndex, this.tableData[this.pickerIndex])

        // this.$set(this.tableData, this.pickerIndex, newRow);
        this.isShowPopVel = false;
        this.visibleqcItm = false;

        for (let i = 0; i < this.inspectData.length; i++) {//for循环遍历每个元素，每遍历一个元素就是arr[i]
          let num = 0//定义一个num统计arr[i]出现的次数，
          for (let j = 0; j < this.inspectData.length; j++) { //根据这个arr[i]做一次循环遍历这个数组，
            if (this.inspectData[i].qcItm) {
              if (this.inspectData[i].qcItm == this.inspectData[j].qcItm) {//arr[i]出现一次就会+1
                num++
              }
            }
          }
          if (num > 1) {
            this.inspectData[this.pickerIndextwo].qcItm = ''
            this.inspectData[this.pickerIndextwo].qcName = ''
            this.$message.warning('检验项目不能重复')
            this.$forceUpdate()
          }
        }
      },
      pickerSearchtwo() {
        this.loading = true
        basicDataqcItm({
          qcItm: this.queryKeywordtwo,
          name: this.queryKeywordtwo
        }).then((res) => {
          this.loading = false
          if (res.code === 0) {
            this.pickerListtwo = res.data

          }
        }).catch(err => {
          this.loading = false
          this.requestFailed(err)
        })
        //    .finally(() => {
        //         this.loading = false
        // })
      },
      // input聚焦
      focustwo(index, row, e) {
        this.qcItm = row.qcItm
        this.queryKeywordtwo = ''
        this.pickerSearchtwo();
        this.visibleqcItm = true;
        // this.isSaveColor = "danger";
        this.pickerIndextwo = index;
        let getTop = e.target.getBoundingClientRect().top + 35;
        if (e.target.getBoundingClientRect().top - 380 < 0) {
          this.objStyle.top = getTop.toString() + "px";
        } else {
          this.objStyle.top = (e.target.getBoundingClientRect().top - 390).toString() + "px";
        }
        this.objStyle.left = e.target.getBoundingClientRect().left + "px";
      },
      locaDelTableRow() {

        let that = this
        if (this.multipleSelection.length === 0 || this.multipleSelection === []) {
          this.$message.error("请勾选要操作的数据！");
          return false;
        }
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('public.del.content'),
          okText: this.$t('public.sure'),
          okType: 'danger',
          cancelText: this.$t('public.cancel'),
          onOk() {


            let selectRows = that.multipleSelection
            // selectRows.forEach((item) => {
            //   if (item.id === null || item.id === "" || item.id === 0) {
            //   } else {
            //     that.detailIds.push(
            //       Object.assign(
            //         {},
            //         {
            //           id: item.id,
            //           prdNo: item.prdNo,
            //           itm: item.itm,
            //           lastQty: item.lastQty,
            //           prdtId: item.prdtId,
            //         }
            //       )
            //     );
            //   }
            // });
            //  this.zcNodetailDatatwo[this.zcNoIndex].tableData.push(obj)
            selectRows.forEach((item) => {
              // that.tableData.splice(that.tableData.indexOf(item), 1);
              that.inspectData.splice(that.inspectData.indexOf(item), 1)
            });
          },
          onCancel() {
            //  that.$refs.multipleSelection.clearSelection();
            that.$message.info("已取消删除");
          }
        })
      },
      addTableRow() {
        this.tableAdd2()
      },
      tableAdd2() {
        for (let i = 0; i < 3; i++) {
          let obj = {
            qcItm: '',
            qty: 3,
            qcSpc: '',
            qcToolName: '',
            qcType: '2',
            qtyLost: '',
            id: null,
            addId: this.inspectData.length,
            inspectDatatwo: [],
            inspectDatathree: [],
            inspectall: [],

            name: '',
            qcName: '',
            stdValue: '',
            stdValueId: '',
            acMax: '',
            acMin: '',
            qcTool: '',
            val1: "",
            val2: "",
          };
          this.inspectData.push(obj)

          // if(item.qty){
          //     for (let i = 0; i < item.qty; i++) {
          //       let obj = {
          //       qcItm:item.qcItm,
          //       acMax:item.acMax,
          //       acMin:item.acMin,
          //       itm:i+1,
          //       qcRec:item.qcRec,
          //       chkId:item.chkId,
          //       spcNo:item.spcNo,
          //       spcName:item.spcName,
          //       rem:item.rem,
          //       qcType:item.qcType,
          //       // id: null,
          //       };
          //       item.inspectDatatwo.push(obj)
          //     }
          //  }
        }
        this.inspectData.forEach((item, i) => {
          if (item.id) { } else {
            item.chkId = 'T'
            if (item.qcType == '1') {
              item.chkId = ''
            }
            if (item.qty) {
              for (let i = 0; i < item.qty; i++) {
                let obj = {
                  qcItm: item.qcItm,
                  acMax: '',
                  acMin: '',
                  itm: i + 1,
                  qcRec: '',
                  chkId: '',
                  spcNo: '',
                  spcName: '',
                  rem: '',
                  qcType: item.qcType,
                };
                item.inspectDatatwo.push(obj)
              }
            }
          }
        })

        // this.inspectDatatwo = this.zcNodetailData
      },
      jumpnonconform() {
        // this.$router.push({path: '/mes/nonconform/detail',
        //     params:{
        //       lsNo: this.entity.lsNo
        //     }
        // })
        this.$router.push({
          name: 'nonconformdetail', params: {
            lsNo: this.entity.lsNo
          }
        })
      },
      jumpinspection() {
        this.$router.push({
          name: 'inspectionFormcheck', params: {
            tiNo: this.entity.tiNo
          }
        })
        // this.$router.push({path: '/mes/inspectionForm/check',
        //     params:{
        //       tiNo: this.entity.tiNo
        //     }
        //   })
      },
      stopScroll(evt) {
        evt = evt || window.event;
        if (evt.preventDefault) {
          // Firefox
          evt.preventDefault();
          evt.stopPropagation();
        } else {
          // IE
          evt.cancelBubble = true;
          evt.returnValue = false;
        }
        return false;
      },
      pastboxqty(val) {
        if (this.multipleSelection[0].bilType == 'TW') {
          console.log(val, this.entity.boxQty1, this.entity.wrQtyMax, 'vvvvvvvvvvvv')
          if (this.entity.boxQty1 > this.entity.wrQtyMax) {
            val = 0
            this.entity.boxQty1 = 0
            this.$message.warning('原箱数不能大于拆箱总量')
          }
        } else {
          if (this.entity.boxQty1 > this.entity.qtyFin) {
            val = 0
            this.entity.boxQty1 = 0
            this.$message.warning('原箱数不能大于拆箱总量')
          }
        }
        this.entity.boxQty2 = 0
        if (+this.entity.boxQty1 < 0) {
          this.$message.warning('原箱数必须大于0')
          val = ''
          this.entity.boxQty1 = ''
        } else {
          if (this.multipleSelection[0].bilType == 'TW') {
            this.entity.boxQty2 = +this.entity.wrQtyMax - +this.entity.boxQty1
          } else {
            this.entity.boxQty2 = +this.entity.qtyFin - +this.entity.boxQty1
          }
        }
      },
      newboxqty(val) {
        if (this.multipleSelection[0].bilType == 'TW') {
          console.log(val, this.entity.boxQty2, this.entity.wrQtyMax, 'vvvvvvvvvvvv')
          if (this.entity.boxQty2 > this.entity.wrQtyMax) {
            val = 0
            this.entity.boxQty2 = 0
            this.$message.warning('新箱数不能大于拆箱总量')
          }
        } else {
          if (this.entity.boxQty2 > this.entity.qtyFin) {
            val = 0
            this.entity.boxQty2 = 0
            this.$message.warning('新箱数不能大于拆箱总量')
          }
        }
        this.entity.boxQty1 = 0
        if (+this.entity.boxQty2 < 0) {
          this.$message.warning('新箱数必须大于0')
          val = ''
          this.entity.boxQty2 = ''
        }
        // else if(+this.entity.boxQty2>+this.entity.wrQtyMax){
        //   val = ''
        //   this.entity.boxQty2 = ''
        //   this.$message.warning('原箱数不能大于拆箱总量')
        // }
        else {
          if (this.multipleSelection[0].bilType == 'TW') {
            this.entity.boxQty1 = +this.entity.wrQtyMax - +this.entity.boxQty2
          } else {
            this.entity.boxQty1 = +this.entity.qtyFin - +this.entity.boxQty2
          }
        }
      },
      newboxqty2(val) {
        // if(this.multipleSelection[0].bilType == 'TW'){
        if (this.entity2.boxQty2 > this.entity2.wrQtyMax) {
          val = 0
          this.entity2.boxQty2 = 0
          this.$message.warning('新箱数不能大于拆箱总量')
        }
        // }
        // else{
        //     if(this.entity2.boxQty2 > this.entity2.qtyFin){
        //         val = 0
        //         this.entity2.boxQty2 = 0
        //         this.$message.warning('新箱数不能大于拆箱总量')
        //     }
        // }
        this.entity2.boxQty1 = 0
        if (+this.entity2.boxQty2 < 0) {
          this.$message.warning('新箱数必须大于0')
          val = ''
          this.entity2.boxQty2 = ''
        } else {
          // if(this.multipleSelection[0].bilType == 'TW'){
          this.entity2.boxQty1 = +this.entity2.wrQtyMax - +this.entity2.boxQty2
          // }
          // else{
          // this.entity2.boxQty1 =+this.entity2.qtyFin - +this.entity2.boxQty2
          // }
        }
      },
      pastboxqty2(val) {
        // if(this.multipleSelection[0].bilType == 'TW'){
        if (this.entity2.boxQty1 > this.entity2.wrQtyMax) {
          val = 0
          this.entity2.boxQty1 = 0
          this.$message.warning('新箱数不能大于拆箱总量')
        }
        // }
        // else{
        //     if(this.entity2.boxQty1 > this.entity2.qtyFin){
        //         val = 0
        //         this.entity2.boxQty1 = 0
        //         this.$message.warning('新箱数不能大于拆箱总量')
        //     }
        // }
        this.entity2.boxQty2 = 0
        if (+this.entity2.boxQty1 < 0) {
          this.$message.warning('新箱数必须大于0')
          val = ''
          this.entity2.boxQty1 = ''
        } else {
          // if(this.multipleSelection[0].bilType == 'TW'){
          this.entity2.boxQty2 = +this.entity2.wrQtyMax - +this.entity2.boxQty1
          // }
          // else{
          // this.entity2.boxQty2 =+this.entity2.qtyFin - +this.entity2.boxQty1
          // }
        }
        // if(+this.entity2.boxQty1<0){
        //   this.$message.warning('原箱数必须大于0')
        //   val = ''
        //   this.entity2.boxQty1 = ''
        // }else{
        //   this.entity2.boxQty2 =+this.entity2.wrQtyMax - +this.entity2.boxQty1
        // }
      },
      // newboxqty(val){
      //   if(this.multipleSelection[0].bilType == 'TW'){
      //     if(this.entity.boxQty2 > this.entity.qty){
      //       val = 0
      //       this.entity.boxQty2 = 0
      //       this.$message.warning('新箱数不能大于拆箱总量')
      //     } 
      //   }else{
      //     if(this.entity.boxQty2 > this.entity.qtyFin){
      //       val = 0
      //       this.entity.boxQty2 = 0
      //       this.$message.warning('新箱数不能大于拆箱总量')
      //     } 
      //   }
      //   this.entity.boxQty1 = 0
      //   if(+this.entity.boxQty2<0){
      //     this.$message.warning('新箱数必须大于0')
      //     val = ''
      //     this.entity.boxQty2 = ''
      //   }
      //   else{
      //     if(this.multipleSelection[0].bilType == 'TW'){
      //       this.entity.boxQty1 =+this.entity.qty - +this.entity.boxQty2
      //     }else{
      //       this.entity.boxQty1 =+this.entity.qtyFin - +this.entity.boxQty2
      //     }
      //   }
      // },

      changeqty(val) {
        if (val == 0) {
          this.entity.qtyOk = +this.entity.qty
        }
        if (val > 0) {
          if (+this.entity.qty - +this.entity.qtyLost < 0) {
            this.$message.info('不合格量超出')
            val = ''
            this.entity.qtyLost = 0
            this.entity.qcId = 'T'
          } else {
            this.entity.qtyOk = +this.entity.qty - +this.entity.qtyLost
            this.entity.qcId = 'F'
          }
        } else {
          this.entity.qcId = 'T'
        }
        if (val < 0) {
          val = ''
          this.entity.qtyLost = 0
          this.$message.info('不合格量不能为负数')
        }
        this.$forceUpdate()
      },
      qcRecchange(val) {

        if (+val.row.qcRec < 0) {
          this.$message.warning('检验值必须大于0')
          val.row.qcRec = ''
          return
        }
        if (val.row.acMax === 0 && val.row.acMin === 0) {
          if (+val.row.qcRec === 0) {

            val.row.chkId = 'T'
          } else {
            val.row.chkId = 'F'
          }
        }
        if (val.row.qcRec === '' || val.row.qcRec === null) {
          val.row.chkId = 'T'
          this.$set(this.inspectData[this.qcItmflag].inspectDatatwo, val.$index, val.row)
        } else {
          if (+val.row.acMax && +val.row.acMin) {
            if (+val.row.qcRec <= val.row.acMax && +val.row.qcRec >= val.row.acMin) {
              val.row.chkId = 'T'
            } else {
              val.row.chkId = 'F'
            }
          } else if (!val.row.acMax && val.row.acMin) {
            if (+val.row.qcRec >= val.row.acMin) {
              val.row.chkId = 'T'
            } else {
              val.row.chkId = 'F'
            }
          } else if (val.row.acMax && !val.row.acMin) {
            if (+val.row.qcRec <= val.row.acMax) {
              val.row.chkId = 'T'
            } else {
              val.row.chkId = 'F'
            }
          }
        }
        this.noqualified(val)
      },
      //  @input="changeqtyLost(scope)"
      noqualified(val) {

        let that = this

        this.chkIdqty = 0
        // this.testData.forEach(i=>{
        //   if(i.spcNo){
        //     this.entity.qtyLost += +i.qtyLost;
        //   }
        // })
        this.inspectData[this.qcItmflag].inspectDatatwo.forEach(item => {
          if (item.chkId == 'F') {
            this.chkIdqty = this.chkIdqty + 1
          } else {
            val.row.spcNo = ''
            val.row.spcName = ''
          }
        })

        //  this.entity.qtyLost =  this.chkIdqty

        this.inspectData.forEach((item, i) => {
          if (item.qtyLost == '' || item.qtyLost == null) {
            item.qtyLost = 0
          }
          if (item.qcItm == val.row.qcItm) {
            item.qtyLost = that.chkIdqty
          }
          this.$forceUpdate()
        })

        this.inspectData.forEach((item, index) => {
          this.$set(this.inspectData, index, this.inspectData[index]);
        })

        let inspectval = this.inspectData
        this.entity.qtyLost = Math.max.apply(null, inspectval.map((item) => item.qtyLost));
        this.entity.qtyOk = +this.entity.qty - +this.entity.qtyLost
        if (this.entity.qtyLost > 0) {
          this.entity.qcId = 'F'
        } else {
          this.entity.qcId = 'T'
        }
        this.$forceUpdate()
      },
      inspectDatamethod() {
        inspectData
      },
      judgmentAdd(row) {
        for (let i = 0; i < 4; i++) {
          let obj = {
            qcItm: row.qcItm,
            acMax: row.acMax,
            acMin: row.acMin,
            itm: '',
            qcRec: '',
            chkId: 'T',
            spcNo: '',
            spcName: '',
            rem: '',
            // id: null,
          };
          this.inspectDatatwo.push(obj);
        }

        this.inspectData.forEach(item => {
          if (item.qcItm == row.qcItm) {
            if (item.inspectDatatwo.length > 0) {
            } else {
              item.inspectDatatwo = this.inspectDatatwo
            }
          }
        })
        this.$forceUpdate()


      },

      // 表格双击事件
      mfBxHanddle(row, column, event) {
        this.workItem = row.addId
        console.log(row, 'ffffffffffffffffff')
        this.getIndex = row.index
        this.qcItmflag = row.addId
        // tableRowClassName({ row, rowIndex }) {

        //   row.index = rowIndex;
        // }

        // this.printdata = this.turnoverdata[this.workItem].mesTfBoxList
        this.$forceUpdate()
        this.loadingtwo = true
        // 
        boxInfoByScNo(
          {
            scNo: row.scNo,
            moNo: row.moNo,
            // fileName:row.fileName,
            // bucketName:row.bucketName,
            // idList:list
          }
        )
          .then(res => {
            if (res.data) {
              res.data.forEach(item => {
                item.pgNo = row.pgNo
                item.scNo = row.scNo
                item.bilType = row.bilType
              })
              this.turnoverdata[this.workItem].mesTfBoxList = res.data
              // this.turnoverdata = res.data
              this.$forceUpdate()
            }
            // this.$message.success(res.msg)
            this.loadingtwo = false
            // this.getList()
          })
          .catch(err => {
            this.loadingtwo = false
            this.requestFailed(err)
          })

      },




      tableRowClassName({ row, rowIndex }) {
        row.index = rowIndex;
        if (rowIndex == this.workItem) {
          return 'current-row'
        }
      },
      selectedstyle({ row, rowIndex }) {

        if (this.getIndex == rowIndex) {
          return {
            "background-color": "#f00",
          };
        }
      },
      cosetiao() {
        if (this.typrorder == 'second') {
          this.$router.push({
            name: 'boxoperation',
            params: {
              tab: 'second'
            }
          })
        } else {
          this.$router.push({
            name: 'boxoperation',
            params: {
              tab: 'first'
            }
          })
        }
      },
      handleDelVersion(row) {
        var list = []
        //  { id, fileName,bucketName }
        if (row.distinguish) {
          this.tableData = this.tableData.filter(i => i.id !== row.id)
        } else {
          sysfiledel(
            {
              id: row.id,
              // fileName:row.fileName,
              // bucketName:row.bucketName,
              // idList:list
            }
          )
            .then(res => {
              this.$message.success(res.msg)
              this.loading = false
              // this.getList()
            })
            .catch(err => {
              this.loading = false
              this.requestFailed(err)
            })

        }
        //  this.tableData.forEach((item, index) => {
        //     if(item.id === id){
        //       item.contraFileUrlList.forEach((item2, index2) => {
        //       list.push(item2.id)
        //       })
        //     }
        //  })
      },
      handleAddVersion() {
        this.$refs.uploadFile.create({ title: '上传' }, 'oooooooooo')
        // this.$refs.AddVersion.create(true, true)
      },
      deldata(row) {
        const that = this
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('identifier.delcont'),
          okText: this.$t('public.sure'),
          okType: 'warn',
          cancelText: this.$t('public.cancel'),
          onOk() {
            that.loading = true
            tfBoxdel({
              boxNos: [row.boxNo],
            }).then((res) => {
              if (res.code === 0) {
                if (res.msg === 'success') {
                  that.$message.success(res.data)
                  that.getList()
                } else {
                  that.$message.error(res.data)
                }
              }
            }).catch(err => that.requestFailed(err))
              .finally(() => {
                that.loading = false
              })
          },
          onCancel() { }
        })


      },
      // 渲染组件
      choose(obj) {

        var map = {}
        // if (obj.obj.name === 'upSalNo') {
        //   this.salName = obj.obj.data.name
        //   map[obj.obj.name] = obj.obj.data.salNo
        // }
        // if (obj.obj.name === 'salNo') {
        //   this.salName = obj.obj.data.name
        //   map[obj.obj.name] = obj.obj.data.salNo
        // }
        if (obj.obj.name === 'dep') {
          this.flag = true
          this.entity.depName = obj.obj.data.name
          this.entity.dep = obj.obj.data.deptCode
          this.data = this.entity.dep + '-' + this.entity.depName
        }
        // this.form.setFieldsValue(map)
      },
      changeqtyLost(val) {
        this.entity.qtyLost = 0
        let getQtys = 0;
        if (!this.entity.qtyLost) {
          this.entity.qtyLost = 0
        }
        this.testData.forEach(i => {
          if (i.spcNo) {
            this.entity.qtyLost += +i.qtyLost;
          }
        })
        if (this.entity.qtyLost > 0) {
          this.entity.qcId = 'F'
          if (+this.entity.qty - +this.entity.qtyLost < 0) {

            this.$message.info('不合格量超出')
            val.row.qtyLost = ''
            this.entity.qtyLost = 0
            this.testData.forEach(i => {
              if (i.spcNo) {
                this.entity.qtyLost += +i.qtyLost;
              }
            })
          } else {
            this.entity.qtyOk = +this.entity.qty - +this.entity.qtyLost
          }
        } else {
          this.entity.qcId = 'T'
        }
        //  if(this.entity.qtyOk < 0){
        //   this.entity.qtyOk = 0
        //  }
        // this.entity.qtyLost += +val;
        this.$forceUpdate()
        // this.testData.forEach((item) => {
        //   if (item.qty) {
        //     getQtys += +item.qty;

      },
      // 货品关键字搜索回显数据
      getGoodsPopover({
        // qcItm,
        // itm,
        // qcRec,
        // chkId,
        // rem,
        spcNo,
        name,
      }) {

        let newRow = {
          // qcItm,
          // itm,
          // qcRec,
          // chkId,
          // rem,
          spcNo,
          spcName: name,
        };
        newRow.show = false;

        this.inspectData[this.qcItmflag].inspectDatatwo[this.pickerIndex].spcNo = spcNo
        this.inspectData[this.qcItmflag].inspectDatatwo[this.pickerIndex].spcName = name
        //  this.inspectData[this.qcItmflag].inspectDatatwo
        // this.$set(this.inspectData[this.qcItmflag].inspectDatatwo, this.pickerIndex, newRow);
        this.isShowPopVel = false;
        // let getQtys = 0;
        // this.testData.forEach((item) => {
        //   if (item.qty) {
        //     getQtys += +item.qty;
        //     this.qtys = getQtys;
        //   }
        // });
        this.visible = false;

        // for (let i = 0; i < this.inspectData[this.qcItmflag].inspectDatatwo.length; i++) {//for循环遍历每个元素，每遍历一个元素就是arr[i]
        //   let num = 0//定义一个num统计arr[i]出现的次数，
        //   for (let j = 0; j < this.inspectData[this.qcItmflag].inspectDatatwo.length; j++) { //根据这个arr[i]做一次循环遍历这个数组，
        //     if(this.inspectData[this.qcItmflag].inspectDatatwo[i].spcNo){
        //       if(this.inspectData[this.qcItmflag].inspectDatatwo[i].spcNo==this.inspectData[this.qcItmflag].inspectDatatwo[j].spcNo){//arr[i]出现一次就会+1
        //         num++
        //       }
        //     }
        //   }
        //   if(num>1){
        //     this.inspectData[this.qcItmflag].inspectDatatwo[this.pickerIndex].spcNo = ''
        //     this.inspectData[this.qcItmflag].inspectDatatwo[this.pickerIndex].spcName = ''
        //     this.$message.warning('原因代号不能重复')
        //     this.$forceUpdate()
        //   }
        // }
      },
      prdt3leave() {
        setTimeout(() => {
          this.visible = false;
        }, 100);
      },
      pickerSearch() {
        // {
        //     ...this.entity,
        //     sysFiles:this.sysFiles
        //   }
        spcLstpage({
          spcNo: this.queryKeyword,
          name: this.queryKeyword
        }).then((res) => {
          if (res.code === 0) {

            this.pickerList = res.data.records
          }
        }).catch(err => this.requestFailed(err))
        //    .finally(() => {
        //         this.loading = false
        // })
      },
      // input聚焦
      focus(index, row, e) {

        this.pickerSearch();
        this.visible = true;
        // this.isSaveColor = "danger";
        this.pickerIndex = index;
        let getTop = e.target.getBoundingClientRect().top + 35;

        if (e.target.getBoundingClientRect().top - 380 < 0) {
          this.objStyle.top = getTop.toString() + "px";
        } else {
          this.objStyle.top = (e.target.getBoundingClientRect().top - 390).toString() + "px";
        }
        this.objStyle.left = e.target.getBoundingClientRect().left + "px";



        // let getTop = e.target.getBoundingClientRect().top + 35;
        // this.objStyle.left = e.target.getBoundingClientRect().left + "px";
        // this.objStyle.top = getTop.toString() + "px";


      },
      blurNameInput() {
        this.visible = false;
        setTimeout(() => {
          this.isShowPopVel = false;
        }, 100);
      },
      inputNameInputtwo(val) {
        if (this.timer) {
          clearTimeout(this.timer);
        }
        this.timer = setTimeout(() => {
          this.queryKeywordtwo = val;
          this.goods_currentPage = 1;

          this.pickerSearchtwo();
        }, 500);
      },
      inputNameInput(val) {
        if (this.timer) {
          clearTimeout(this.timer);
        }
        this.timer = setTimeout(() => {
          this.queryKeyword = val;
          this.goods_currentPage = 1;
          this.pickerSearch();
        }, 500);
      },
      handleSelectionChangereason(rows) {
        this.multipleSelection = rows;
      },
      handleSelectionChange(rows) {
        this.multipleSelection = rows;
      },
      //  // 添加行
      // addTableRow() {
      //   this.tableAdd();
      // },
      // // 本地删除
      // locaDelTableRow() {
      //   let that = this

      //   if (this.multipleSelection.length === 0 || this.multipleSelection === []) {
      //     this.$message.error("请勾选要操作的数据！");
      //     return false;
      //   }
      //   this.$confirm({
      //     title: this.$t('public.del.title'),
      //     content: this.$t('public.del.content'),
      //     okText: this.$t('public.sure'),
      //     okType: 'danger',
      //     cancelText: this.$t('public.cancel'),
      //     onOk () {


      //       let selectRows = that.multipleSelection
      //       // selectRows.forEach((item) => {
      //       //   if (item.id === null || item.id === "" || item.id === 0) {
      //       //   } else {
      //       //     that.detailIds.push(
      //       //       Object.assign(
      //       //         {},
      //       //         {
      //       //           id: item.id,
      //       //           prdNo: item.prdNo,
      //       //           itm: item.itm,
      //       //           lastQty: item.lastQty,
      //       //           prdtId: item.prdtId,
      //       //         }
      //       //       )
      //       //     );
      //       //   }
      //       // });
      //       selectRows.forEach((item) => {
      //         that.testData.splice(that.testData.indexOf(item), 1);
      //       });
      //     },
      //   onCancel () {

      //     // that.loading = false
      //      that.$refs.multipleTable.clearSelection();
      //     that.$message.info("已取消删除");
      //   }
      // })

      // this.$confirm("此操作将删除, 是否继续?", "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning",
      // })
      // .then(() => {
      // })
      // .catch(() => {
      //   this.$refs.multipleTable.clearSelection();
      //   this.$message.info("已取消删除");
      // });
      // },
      // 表格初始化，往数组里面添加50个对象
      tableAdd() {
        for (let i = 0; i < 3; i++) {
          let obj = {
            spcNo: "",
            spcName: "",
            qtyLost: "",
            name: "", // 品名
            id: null,
          };
          this.testData.push(obj);
        }
      },
      //行点击
      handRowClick(row, column, event) {
        var index;
        this.testData.map((c, i) => {
          if (c == row) {
            index = i;
          }
        });
        this.$set(this.testData, index, row);
        event.stopPropagation();
      },
      // this.entity.chktyId: this.entity.chktyId ? 'T' : 'F'
      onChangeinspect(e) {

        // this.checked=e
        //  this.entity.qcId = e.target.checked
      },
      onChange(checked) {
        this.checked = checked;

      },
      onChangeclosecase(e) {


        this.entity.chkKnd = e.target.checked
        this.$forceUpdate()
      },
      onChangeLaboratory(e) {
        this.entity.sys = e.target.checked
      },
      seeFile(obj, formD) {
        this.sysFiles = obj
        this.sysFiles.forEach((item, index) => {
          this.tableData.push(item)
        })
        // this.objpic = obj
        // this.formD = formD
        // this.tableData.forEach((item, index) => {
        //   if (this.formD.wyID === item.wyID) {
        //     if (item.imageUrlList == null) {
        //       item.imageUrlList = []
        //       this.objpic.forEach((item2, index2) => {
        //         item.imageUrlList.push(item2)
        //       })
        //     } else {
        //       this.objpic.forEach((item2, index2) => {
        //         item.imageUrlList.push(item2)
        //       })
        //     }
        //   }
        // })
      },
      save() {
        let validflag
        this.$refs.addEntityForm.validate(valid => {
          validflag = valid
          if (!valid) {
            this.$message.warning('请选择部门')
            return
          }
        })
        if (validflag) {
          this.inspectData = this.inspectData.filter(i => i.qcItm)
          if (this.inspectData.length) { } else {
            this.$message.warning('无检验数据，不能保存')
            return
          }

          this.entity.mesQcTy1s = []
          this.entity.mesQcTy3s = []
          // 检查不合格原因数据问题
          let errordata = false
          let empy = false
          for (let i = 0; i < this.inspectData.length; i++) {
            if (this.inspectData[i].qcType == 2 && this.inspectData[i].stdValueId == '+-') {
              if (this.inspectData[i].val1 && this.inspectData[i].val2) {
              } else {
                empy = true
                break
              }
            }
          }
          if (empy) {
            this.$message.warning('请填写偏差值')
            return
          }
          for (let i = 0; i < this.inspectData.length; i++) {

            for (let j = 0; j < this.inspectData[i].inspectDatatwo.length; j++) {
              this.inspectData[i].inspectDatatwo[j].qcTool = this.inspectData[i].qcTool
              this.inspectData[i].inspectDatatwo[j].qcToolName = this.inspectData[i].qcToolName
              this.inspectData[i].inspectDatatwo[j].qty = this.inspectData[i].qty
              this.inspectData[i].inspectDatatwo[j].qcSpc = this.inspectData[i].qcSpc
              // this.inspectData[i].inspectDatatwo[j].qcType = this.inspectData[i].qcType
              this.inspectData[i].inspectDatatwo[j].qtyLost = this.inspectData[i].qtyLost

              this.inspectData[i].inspectDatathree = []
              this.inspectData[i].inspectall = []
              if (this.inspectData[i].inspectDatatwo[j].chkId == 'F') {
                if (this.inspectData[i].inspectDatatwo[j].spcNo == null || this.inspectData[i].inspectDatatwo[j].spcNo == '') {
                  errordata = true
                  break
                }
              }
            }
          }
          if (errordata) {
            this.$message.warning('请选择不合格原因')
            return
          }
          // 检查不合格原因数据问题结束
          // this.inspectData[0].inspectDatatwo = this.inspectData[0].inspectDatatwo.filter((item) => {
          //       return item.qcRec !== ''&&item.qcRec !== null
          // })
          let qcRectrue = false
          for (let i = 0; i < this.inspectData.length; i++) {
            for (let j = 0; j < this.inspectData[i].inspectDatatwo.length; j++) {
              if (this.inspectData[i].qcType == '1') {
              } else {
                if (!this.inspectData[i].inspectDatatwo[j].qcRec && this.inspectData[i].inspectDatatwo[j].chkId == 'F') {
                  qcRectrue = true
                  break
                }
              }
            }
          }
          if (qcRectrue) {
            this.$message.warning('检验类型测量、合格判定为不合格，检验值必填')
            return
          }

          this.inspectDataflag = true
          for (let i = 0; i < this.inspectData.length; i++) {
            for (let j = 0; j < this.inspectData[i].inspectDatatwo.length; j++) {
              if (this.inspectData[i].qcType == '1') {
                if (this.inspectData[i].inspectDatatwo[j].chkId == 'F') {
                  this.inspectData[i].inspectDatathree.push(this.inspectData[i].inspectDatatwo[j])
                }
              } else {
                if (this.inspectData[i].inspectDatatwo[j].qcRec !== null && this.inspectData[i].inspectDatatwo[j].qcRec !== '' && this.inspectData[i].inspectDatatwo[j].chkId == 'F') {
                  this.inspectData[i].inspectDatathree.push(this.inspectData[i].inspectDatatwo[j])
                }
              }
              // if(this.inspectData[i].qcType == '1'){
              //   if(this.inspectData[i].inspectDatatwo[j].chkId!== null && this.inspectData[i].inspectDatatwo[j].chkId!== ''){
              //     this.entity.mesQcTy3s.push(this.inspectData[i].inspectDatatwo[j])
              //   }
              // }else{
              //   if(this.inspectData[i].inspectDatatwo[j].qcRec!== null && this.inspectData[i].inspectDatatwo[j].qcRec!== ''){
              //     this.entity.mesQcTy3s.push(this.inspectData[i].inspectDatatwo[j])
              //   }
              // }
              this.entity.mesQcTy3s.push(this.inspectData[i].inspectDatatwo[j])
              if (this.inspectData[i].inspectDatatwo[j].qcRec !== null && this.inspectData[i].inspectDatatwo[j].qcRec !== '') {
                this.inspectData[i].inspectall.push(this.inspectData[i].inspectDatatwo[j])
              }
              // this.inspectData[i].inspectDatatwo = this.inspectData[i].inspectDatatwo.map(item=> { 
              //   if(item.qcRec !== ''&&item.qcRec !== null ){
              //   }
              // })
              // this.inspectData[i].inspectDatatwo = this.inspectData[i].inspectDatatwo.filter((item) => {
              //   return item.qcRec !== ''&&item.qcRec !== null
              // })
            }
          }

          // let arr = [1,2,2,3,4,4,4,6]
          // 去重并且记录出现次数 拿到qtyLost值
          for (let i = 0; i < this.inspectData.length; i++) {
            // let arr = copare(this.inspectData[i].inspectDatathree)
            let obj = {};
            this.inspectData[i].inspectDatathree = copare(this.inspectData[i].inspectDatathree).reduce((item, next) => {

              obj[next.spcNo] ? "" : (obj[next.spcNo] = true && item.push(next));
              return item;
            }, []);

          }
          // let arr = this.inspectData[this.qcItmflag].inspectDatathree
          // copare(arr)
          function copare(array) {
            let a = [] //定义一个空数组 a= []
            for (let i = 0; i < array.length; i++) {//for循环遍历每个元素，每遍历一个元素就是arr[i]
              let num = 0//定义一个num统计arr[i]出现的次数，
              for (let j = 0; j < array.length; j++) { //根据这个arr[i]做一次循环遍历这个数组，
                if (array[i].spcNo == array[j].spcNo) {//arr[i]出现一次就会+1
                  num++
                }
              }
              if (num > 1) {
                array[i].qtyLost = num
              }
              if (num <= 1) {//如果只出现一次，那么就说明没有重复的，我们就把这个元素push到a数组中，如果出现多次，那么什么都不做，最后得到的a就是我们要的结果。
                array[i].qtyLost = num
                // a.push(array[i])
              }
            }
            return array
          }

          // 记录出现次数 拿到qtyLost值
          //  let obj = {};
          //   let arrays = arr.reduce((item, next) => {

          //     obj[next.spcNo] ? "" : (obj[next.spcNo] = true && item.push(next));
          //     return item;
          //   }, []);
          // this.inspectData[this.qcItmflag].inspectDatathree = arrays

          //  let arrays = getmaxArr.reduce((item, next) => {


          //     obj[next.mlNo] ? "" : (obj[next.mlNo] = true && item.push(next));
          //     return item;
          //   }, []);
          //   this.entity = response.data
          // this.inspectData = this.entity.mesQcTy2s
          this.entity.mesQcTy2s = this.inspectData
          for (let i = 0; i < this.entity.mesQcTy2s.length; i++) {
            if (this.entity.mesQcTy2s[i].qcType == '1') {
              if (this.entity.mesQcTy2s[i].inspectall.length > 0) {
                for (let j = 0; j < this.entity.mesQcTy2s[i].inspectDatathree.length; j++) {
                  if (this.entity.mesQcTy2s[i].inspectDatathree.length > 0) {
                    if (this.entity.mesQcTy2s[i].inspectDatathree[j].chkId) {
                      this.entity.mesQcTy1s.push(this.entity.mesQcTy2s[i].inspectDatathree[j])
                    }
                  }
                }
              }
            } else {
              if (this.entity.mesQcTy2s[i].inspectall.length > 0) {

                for (let j = 0; j < this.entity.mesQcTy2s[i].inspectDatathree.length; j++) {
                  if (this.entity.mesQcTy2s[i].inspectDatathree.length > 0) {
                    if (this.entity.mesQcTy2s[i].inspectDatathree[j].qcRec) {
                      this.entity.mesQcTy1s.push(this.entity.mesQcTy2s[i].inspectDatathree[j])
                    }
                  }
                }
              } else {
                this.inspectDataflag = false
                break
              }
            }
          }
          let mesQcTy1s = []
          mesQcTy1s = this.testData.filter((item) => {
            return item.spcNo !== "" && item.spcNo !== null;
          })
          this.tableData.forEach(item => {
            if (item.distinguish) {
              delete item.distinguish
            }
          })
          // if(this.entity.chktyId==true){
          //   this.entity.chktyId = 'T'
          // }else{
          //   this.entity.chktyId = 'F'
          // }
          this.entity.closeId = 'F'
          delete this.entity.unitName
          delete this.entity.qcTypeName
          delete this.entity.whName
          // if(this.inspectDataflag){
          // this.entity.chkKnd = this.entity.chkKnd ? 'T' : 'F'
          // this.entity.sys = this.entity.sys ? 'T' : 'F'
          if (this.entity.tyNo) {
            qcSpcqcUpd({
              ...this.entity,
              sys: this.entity.sys ? 'T' : 'F',
              chkKnd: this.entity.chkKnd ? 'T' : 'F',
              mesTiQcFileList: this.tableData,
              // mesQcTy1s:mesQcTy1s
            }).then((res) => {
              if (res.code === 0) {

                if (res.msg === 'success') {
                  this.$message.success(res.data)
                } else {
                  this.$message.error(res.data)
                  return
                }

              }
            }).catch(err => this.requestFailed(err))
              .finally(() => {

                this.loading = false
              })

          } else {
            delete this.entity.tiDd
            delete this.entity.tyDd
            qcSpcqcAdd({
              ...this.entity,
              sys: this.entity.sys ? 'T' : 'F',
              chkKnd: this.entity.chkKnd ? 'T' : 'F',
              mesTiQcFileList: this.tableData,
              // mesQcTy1s:mesQcTy1s,
            }).then((res) => {
              if (res.code == 0) {

                if (res.msg === 'success') {
                  this.entity.tyDd = res.data.tyDd
                  this.entity.tyNo = res.data.tyNo
                  if (this.entity.tyNo) {
                    this.isClaimtwo = false
                  } else {
                    this.isClaimtwo = true
                  }
                  this.$forceUpdate()
                  this.$message.success('保存成功')
                } else {
                  this.$message.error(res.data)
                  return
                }
                if (res.data) {
                  this.entity = res.data
                  this.$forceUpdate()
                  if (this.entity.sys == 'T') {
                    this.entity.sys = true
                  } else {
                    this.entity.sys = false
                  }
                  if (this.entity.chkKnd == 'T') {
                    this.entity.chkKnd = true
                  } else {
                    this.entity.chkKnd = false
                  }
                }
              }
            }).catch(err => this.requestFailed(err))
              .finally(() => {

                this.loading = false
              })
          }

          // }else{
          //   this.$message.warning('每个检验项目至少填写一条数据')
          // }
        }
      },
      selectChangeEvent({ checked, records, reserves, row, rowIndex }) {

        this.multipleSelectiontwo = records
      },
      selectChangeAll({ records, checked }) {

        this.multipleSelectiontwo = records
      },

      editclick() {
        this.isEdit = true
      },
      closeclick() {
        this.isEdit = false
      },
      reset() {
        this.entity = {}
      },
      seepic(index) {
        let arry = []
        arry.push(this.tableData[index])

        this.$refs.ModalPic.create({ title: '查看' }, arry)
      },
      uploadpic(row) {
        this.$refs.uploadFile.create({ title: '上传' }, row)
        // this.$refs.Upload.create(
        //   {
        //     title: '上传'
        //   },
        // )
        // this.$refs.Upload.create({ title: '上传' }, row)
      },
      // check(){
      //   this.$router.push('')
      // },
      handleChangereson(val) {

        this.multiplereason = val
      },
      cancelGetData() {
        this.visible = false
      },
      otngetData() {

        if (this.multiplereason.length) {
          this.reasondata = this.multiplereason
        }
        this.visible = false
      },
      reasonclick(val) {
        this.visible = true
      },
      handleSelectionChange(val) {
        this.multipleSelection = val
      },
      handleSelectionChangetwo(val) {
        this.multipleSelectiontwo = val
      },
      handleTabClick(key) {
        if (key == '1') {
          this.getList()
        } else if (key == '2') {
          this.getListtwo()
        }
      },
      onChange(data, dateString) {

        this.queryParam.date = dateString

      },
      depme(row) {
        datadepPage(
          Object.assign({
            current: 1,
            size: 10,
            deptCode: row
          })
        ).then(response => {
          this.entity.depName = response.data.records[0].name
          this.data = this.entity.dep + '-' + this.entity.depName

        }).catch(err => this.requestFailed(err))
      },
      getListtreat() {
        this.loading = true
        qcSpcdjInfo(
          Object.assign({
            tiNo: this.entity.tiNo,
          })
        ).then(response => {
          this.loading = false
          this.entity = response.data
          if (this.entity.lsNo) {
            this.isClaim = true
          } else {
            this.isClaim = false
          }
          if (this.entity.tyNo) {
            this.isClaimtwo = false
          } else {
            this.isClaimtwo = true
          }
          this.entity.mesQcTy1s = []
          this.entity.mesQcTy3s = []
          this.inspectData = response.data.mesQcTy2s
          this.inspectData.forEach((item, i) => {
            item.addId = i
            item.chkId = 'T'
            if (item.qcType == '1') {
              item.chkId = ''
            }
            item.inspectDatatwo = []
            item.inspectDatathree = []
            item.inspectall = []
            if (item.qty) {
              for (let i = 0; i < item.qty; i++) {
                let obj = {
                  qcItm: item.qcItm,
                  acMax: item.acMax,
                  acMin: item.acMin,
                  itm: i + 1,
                  qcRec: item.qcRec,
                  chkId: item.chkId,
                  spcNo: item.spcNo,
                  spcName: item.spcName,
                  rem: item.rem,
                  qcType: item.qcType,
                  // id: null,
                };
                item.inspectDatatwo.push(obj)
              }
            }
          })
          this.entity.qcId = ''
          if (this.entity.qtyLost) {
            if (this.entity.qtyLost > 0) {
              this.entity.qcId = 'F'
            } else {
              this.entity.qcId = 'T'
            }
          } else {

            this.entity.qtyLost = null

            this.$forceUpdate()
            this.entity.qcId = 'T'
          }

          this.entity.qtyOk = this.entity.qty
          this.entity.chkKnd = false

          // this.data = this.entity.dep + '-' + this.entity.depName
          this.tableData = response.data.mesTiQcFileList
          this.entity.tyDd = moment(new Date()).format('YYYY-MM-DD')
          // if(this.entity.chkKnd == 'T'){
          //   this.entity.chkKnd = true
          // }else{
          //   this.entity.chkKnd = false
          // }
          if (this.entity.sys == 'T') {
            this.entity.sys = true
          } else {
            this.entity.sys = false
          }

          if (this.entity.dep) {
            this.depme(this.entity.dep)
          }

          // this.tablePage.total = response.data.total
          // this.tablePage.currentPage = response.data.current
        }).catch(err => this.requestFailed(err))
          .finally(() => {
            this.loading = false
          })
      },
      getList() {
        this.loading = true
        tfBoxinfo(
          Object.assign({
            // total: this.tablePage.currentPage,
            // current: this.tablePage.currentPage,
            // size: this.tablePage.pageSize,
            // staDd:this.queryParam.staDd,
            // endDd:this.queryParam.endDd,
            moNo: this.idmoNo,
            // zcNo:this.queryParam.zcNo,
            // moNo:this.queryParam.moNo,
            // prdNo:this.queryParam.prdNo,
            // prdName:this.queryParam.prdName,
            // bilNo:this.queryParam.bilNo
          })
        ).then(response => {
          this.loading = false
          this.turnoverdata = response.data
          if (this.turnoverdata.length > 0) {
            this.turnoverdata.forEach((item, i) => {
              item.addId = i
            })
          }


          this.loadingtwo = true
          if (this.turnoverdata.length > 0) {
            boxInfoByScNo(
              {
                scNo: this.turnoverdata[this.workItem].scNo,
                moNo: this.turnoverdata[this.workItem].moNo,
                // fileName:row.fileName,
                // bucketName:row.bucketName,
                // idList:list
              }
            )
              .then(res => {
                if (res.data) {
                  this.turnoverdata[this.workItem].mesTfBoxList = res.data
                  this.turnoverdata[this.workItem].mesTfBoxList.forEach(item => {
                    item.scNo = this.turnoverdata[this.workItem].scNo
                    item.pgNo = this.turnoverdata[this.workItem].pgNo
                    item.prdName = this.turnoverdata[this.workItem].prdName
                    item.prdNo = this.turnoverdata[this.workItem].prdNo
                    item.bilType = this.turnoverdata[this.workItem].bilType
                  })
                  // this.turnoverdata = res.data
                  this.$forceUpdate()
                }
                // this.$message.success(res.msg)
                this.loadingtwo = false
                // this.getList()
              })
              .catch(err => {
                this.loadingtwo = false
                this.requestFailed(err)
              })
          }



          // this.entity = response.data
          // if(this.entity.lsNo){
          //   this.isClaim = true
          // }else{
          //    this.isClaim = false
          // }
          // if(this.entity.qtyLost){
          // }else{
          //   this.entity.qtyLost = 0
          // }
          // this.entity.mesQcTy1s = []
          // this.entity.mesQcTy3s = []
          // this.inspectData = response.data.mesQcTy2s

          // this.inspectData.forEach((item,i)=>{
          // item.addId = i
          // item.chkId = 'T'
          // if(item.qcType=='1'){
          //   item.chkId = ''
          // }
          // item.inspectDatatwo = []
          //  item.inspectDatathree = []
          //  item.inspectall = []
          //  let qtylength
          //  if(item.mesQcTy2List){
          //   if(item.mesQcTy2List.length >0){
          //     for (let i = 0; i < item.mesQcTy2List.length; i++) {
          //       item.mesQcTy2List[i].qcType = item.qcType
          //       item.inspectDatatwo.push(item.mesQcTy2List[i])
          //     }
          //   }
          //   if(item.mesQcTy2List.length <item.qty){
          //     qtylength = item.qty - item.mesQcTy2List.length
          //     for (let i = 0; i < qtylength; i++) {
          //       let obj = {
          //         qcItm:item.qcItm,
          //         acMax:item.acMax,
          //         acMin:item.acMin,
          //         itm:i+1+item.mesQcTy2List.length,
          //         qcRec:item.qcRec,
          //         chkId:item.chkId,
          //         spcNo:item.spcNo,
          //         spcName:item.spcName,
          //         rem:item.rem,
          //         qcType:item.qcType,
          //       };
          //       item.inspectDatatwo.push(obj)
          //     }
          //   }
          //  }
          // if(item.qty){
          //   for (let i = 0; i < item.qty; i++) {
          //     let obj = {
          //     qcItm:item.qcItm,
          //     acMax:item.acMax,
          //     acMin:item.acMin,
          //     itm:i+1,
          //     qcRec:item.qcRec,
          //     chkId:item.chkId,
          //     spcNo:item.spcNo,
          //     spcName:item.spcName,
          //     rem:item.rem,
          //     };
          //     item.inspectDatatwo.push(obj)
          //   }
          // }
          // }) 
          // this.entity.chkKnd = false

          // this.tableData = response.data.mesTiQcFileList
          // if(this.entity.chkKnd == 'T'){
          //   this.entity.chkKnd = true
          // }else{
          //   this.entity.chkKnd = false
          // }
          // if(this.entity.sys == 'T'){
          //   this.entity.sys = true
          // }else{
          //   this.entity.sys = false
          // }
          // if(this.entity.dep){
          // this.depme(this.entity.dep)
          // }
        })
          .catch(err => this.requestFailed(err))
          .finally(() => {
            this.loading = false
          })
      },
      // 获取列表数据
      // getList () {
      //   this.loading = true
      //   fetchList(
      //     Object.assign({
      //       current: this.tablePage.currentPage,
      //       size: this.tablePage.pageSize,
      //       pgNo: this.queryParam.pgNo
      //     })
      //   ).then(response => {
      //     this.loading = false
      //     this.tableData = response.data.records

      //     this.tablePage.total = response.data.total
      //     this.tablePage.currentPage = response.data.current
      //   }).catch(e => {
      //     this.loading = false
      //   })
      // },
    },
  }
</script>

<style lang='scss' scoped>
  .watermark {
    display: block;
    position: fixed;
    top: calc(50% - 100px);
    left: calc(50% - 60px);
    /* right:0;
    bottom: 0; */
    /* opacity:0.8; */
    width: 150px;
    height: 150px;
    /* background: url('../../.././assets/print.png'); */
    /* background-size:auto 100%; */
    /* border-radius:14px 0 0 14px; */
  }

  #showPrint {
    page-break-before: always;
  }

  @page {
    margin: 0;
    size: auto
  }

  #gfvrgh {
    page-break-before: always;
    page-break-after: always;
  }

  #gfvrghtwo {
    page-break-before: always;
    page-break-after: always;
  }

  @media print {
    #gfvrgh {
      page-break-before: always;
      page-break-after: always;
    }

    #gfvrghtwo {
      page-break-before: always;
      page-break-after: always;
    }

    .watermark {
      display: block;
    }
  }

  .overdata span {
    font-weight: bold;
  }

  .overdata span {
    font-size: 12px
  }

  .ant-input-wrapper .ant-input-group-addon .ant-btn {
    height: 28px;
  }

  .sup_info_item {
    padding: 10px 0;
  }



  .el-form-item--mini.el-form-item,
  .el-form-item--small.el-form-item {
    margin-bottom: 0px
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    -o-appearance: none !important;
    -ms-appearance: none !important;
    appearance: none !important;
    margin: 0;
  }

  input[type="number"] {
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    -o-appearance: textfield;
    -ms-appearance: textfield;
    appearance: textfield;
  }

  .sup_info_basics_container {
    border: 1px solid #E5E5E5;
    border-radius: 10px;
    padding: 0px 20px 0px 20px;
  }

  .sup_info_basics_header {
    font-size: 15px;
    color: #1F2A3F;
    height: 41px;
    font-weight: 500;
    line-height: 41px;
    overflow: hidden;
  }

  .el-table__fixed,
  .el-table__fixed-right {
    height: 100% !important;
  }

  .ant-input {
    height: 28px;
  }



  .compoct .el-form-item--small .el-form-item__content,
  .el-form-item--small .el-form-item__label {
    line-height: 24px;
  }

  .tableheight .el-table__cell {
    padding: 1px
  }

  /* .el-table__body tr.current-row>td.el-table__cell {
  background-color: #98e4ff;
}
.el-table--striped .el-table__body tr.el-table__row--striped.current-row td.el-table__cell {
    background-color: #98e4ff;
}
.el-table--enable-row-transition .el-table__body td.el-table__cell{
  transition:no;
}
.checkdata .el-table tbody tr:hover > td {
    background-color: #98e4ff!important;
} */
  /* .checkdata .el-table__body tr.current-row>td{background-color: #98e4ff;}
el-table--striped .el-table__body tr.el-table__row--striped.current-row td.el-table__cell {
    background-color: #f00;
} */

  /* 用来设置当前页面element全局table 选中某行时的背景色*/
  .checkdata .el-table__body tr.current-row>td {
    background-color: #98e4ff !important;
    /* color: #f19944; */
    /* 设置文字颜色，可以选择不设置 */
  }

  /* 用来设置当前页面element全局table 鼠标移入某行时的背景色*/
  .checkdata .el-table--enable-row-hover .el-table__body tr:hover>td {
    background-color: #98e4ff;
    /* color: #f19944; */
    /* 设置文字颜色，可以选择不设置 */
  }

  .checkdata th.el-table__cell>.cell {
    padding-left: 1px !important;
    padding-right: 1px !important;
  }

  .checkdata .cell {
    padding-left: 1px !important;
    padding-right: 1px !important;
  }

  .boxoperat .el-table th.el-table__cell>.cell {
    font-weight: bold;
  }

  .boxoperat .el-checkbox__inner {
    border: 1px solid #000;
  }

  .confluenceTable {
    border: 1px solid #000;
  }
</style>

<style media="print">
  @page {
    margin: 0;
    size: auto
  }

  #gfvrgh {
    page-break-before: always;
    page-break-after: always;
  }

  #gfvrghtwo {
    page-break-before: always;
    page-break-after: always;
  }

  @media print {
    #gfvrgh {
      page-break-before: always;
      page-break-after: always;
    }

    #gfvrghtwo {
      page-break-before: always;
      page-break-after: always;
    }

    .watermark {
      display: block;
    }

    body {
      height: auto;
    }

    html {
      height: auto;
      margin: 0;
    }
  }
</style>