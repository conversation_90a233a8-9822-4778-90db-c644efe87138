
<template>
  <div>
    <el-dialog class="JustMake-dialog" width="25%" title="快速录入" :before-close="handleClose" :visible.sync="dialogFormVisible">
      <el-form label-position="right" label-width="100px" ref="form" :rules="rules" :model="form">
        <el-form-item :label="this.$t('mesMoLine.dept')" prop="deptName">
          <el-input v-model="form.deptName" class="input-centered" >
            <!-- 自定义后缀图标 -->
            <template #suffix>
              <i class="el-icon-search" @click="openComponents(1)" style="padding-right: 8px"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item :label="this.$t('mesMoLine.moNo')" prop="moNo" >
          <el-input v-model="form.moNo" class="input-centered">
            <template #suffix>
              <i class="el-icon-search" @click="openComponents(2)" style="padding-right: 8px"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item :label="this.$t('mesMoLine.pgNo')" prop="pgNo" >
          <el-input v-model="form.pgNo" class="input-centered">
            <template #suffix>
              <i class="el-icon-search" @click="openComponents(3)" style="padding-right: 8px"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item :label="this.$t('mesMoLine.begDd')" prop="begDd">
          <el-date-picker
            v-model="form.begDd"
            type="datetime"
            placeholder="选择日期时间"
            class="input-centered">
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="this.$t('mesMoLine.endDd')" prop="endDd">
          <el-date-picker
            v-model="form.endDd"
            type="datetime"
            placeholder="选择日期时间"
            class="input-centered">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{this.$t('public.cancel')}}</el-button>
        <el-button type="primary" @click="submit('form')">{{this.$t('public.sure')}}</el-button>
      </div>
    </el-dialog>
    <dept ref="dept" @getDept="getDept"></dept>
    <moNo ref="moNo" @getMoNo="getMoNo"></moNo>
    <pgNo ref="pgNo" @getPgNo="getPgNo"></pgNo>
  </div>
</template>
<script>
import dept from '@/views/mes/basicdata/mesMoLine/components/dept.vue'
import moNo from '@/views/mes/basicdata/mesMoLine/components/moNo.vue'
import pgNo from '@/views/mes/basicdata/mesMoLine/components/pgNo.vue'
import Vue from 'vue'
import { getPrdStep, getStationName, pageMesMoLine, saveMesMoLine, updateMesMoLine } from '@/api/mes/mesMoLine'
import { mapState } from 'vuex'
import moment from 'moment/moment'
export default {
  components:{
    dept,moNo,pgNo
  },
  data(){
    return{
      dialogFormVisible:false,
      formLabelWidth:180,
      form:{
        deptName: '',
        moNo: '',
        pgNo: '',
        begDd: '',
        endDd: null,
        prdName: '',
        prdNo: '',
        zcNo:''
      },
      rules: {
        deptName: [
          { required: true, message: '请选择产线', trigger: 'change' }
        ],
        moNo: [
          { required: true, message: '请选择生产单号', trigger: 'change' }
        ],
        pgNo: [
          { required: true, message: '请选择派工单号', trigger: 'change' }
        ],
        begDd: [
          { type: 'date', required: true, message: '请选择开始日期', trigger: 'change' }
        ],
      }
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    })
  },
  methods:{
    openComponents(param){
      switch (param) {
        case 1:
          this.$refs.dept.open()
          break;
        case 2:
          this.$refs.moNo.open()
          break;
        case 3:
          this.$refs.pgNo.open(this.form.moNo)
          break;
        case 4:
          break;
        default:
          break;
      }
    },
    open(){
      this.dialogFormVisible=true
    },
    getDept(row){
      Vue.set(this.form, 'dept', row.deptCode);
      Vue.set(this.form, 'deptName',row.name);
    },
    getMoNo(row){
      Vue.set(this.form, 'moNo', row.moNo);
      Vue.set(this.form, 'prdNo',row.mrpNo);
      Vue.set(this.form, 'prdName', row.prdName);
      Vue.set(this.form, 'pgNo', '');

    },
    getPgNo(row){
      Vue.set(this.form, 'pgNo',row.pgNo);
      Vue.set(this.form, 'zcNo', row.zcNo);
    },

    submit(form){
          this.$refs[form].validate((valid) => {
            if (!valid) {
              this.$message.error("请输入完整数据！");
              return false;
          } });
          getPrdStep(
            {
              dept:this.form.dept,
              moNo:this.form.moNo,
              prdNo:this.form.prdNo,
              zcNo:this.form.zcNo,
            }
          ).then(res=>{
            if (res.msg==="success"){
              let prdArray=[]
              if(res.data.length===0){
                this.$message.error("该生产单号还没绑定工步！");
                return
              }
              prdArray = res.data.map(record => (
                {
                  wsNo: record.wsNo,
                  stationNo: record.stationNo||null,
                  stationName: record.stationName||null,
                  prdNo: record.prdNo,
                  pgNo:this.form.pgNo||null,
                  moNo: this.form.moNo,
                  dept: this.form.dept,
                  begDd: moment(this.form.begDd).format('YYYY-MM-DD HH:mm:ss'),
                  endDd:this.form.endDd,
                  prdName: this.form.prdName,
                  state: 2,
                  usrRs: this.userInfo.username
                }));
              this.getPageMesMoLine(prdArray)

            }
          }).catch(res=>{
            this.$message.error(res);
          })
    },

    getPageMesMoLine(prdArray){
      pageMesMoLine(
        {
          dept: this.form.dept,
          size: 1000000
        }
      ).then(res=>{
        if (res.msg==="success"){

          //相同产线相同工位相同工步改变状态，不同工位新增
          const savedRecords = res.data.records
          let existingArray
          let filteredArray
          if (savedRecords.length>0){
            // 已存在的数据
            existingArray = prdArray.filter((newItem) =>
              savedRecords.some((savedItem) =>
                savedItem.stationNo === newItem.stationNo && savedItem.wsNo === newItem.wsNo)
            );

            // 新增的数据
            filteredArray = prdArray.filter((newItem) =>
              !savedRecords.some((savedItem) => savedItem.stationNo === newItem.stationNo && savedItem.wsNo === newItem.wsNo)
            );
          }else{
            filteredArray=[...prdArray]
            existingArray=[]
          }

          try {

            if (existingArray.length>0){
              updateMesMoLine(existingArray).then(res=>{
                if (res.msg==="success"){
                  this.$message.success('更新产线成功！');
                  this.$emit('getTable',this.form.dept)
                  this.handleClose()
                }
              }).catch(res=>{
                this.$message.error(res);
              })
            }
            if (filteredArray.length>0){
              this.saveNewMesMoLin(filteredArray)
            }

          } catch (error) {
            this.requestFailed(error)
            return
          }
        }
      }).catch(res=>{
        this.requestFailed(res)
      })
    },
    saveNewMesMoLin(prdArray){
      saveMesMoLine(
        prdArray
      ).then(res=>{
        if (res.msg==="success"){
          this.$message.success('录入成功！');
          this.$emit('getTable',this.form.dept)
          this.$refs.form.resetFields();
          this.dialogFormVisible=false
        }
      }).catch(res=>{
        this.$message.error(res);
      })
    },
    cancel(){
      this.handleClose()
    },
    handleClose(){
      this.$refs.form.resetFields();
      this.dialogFormVisible=false
    }
  }
}
</script>

<style scoped lang="less">
.input-centered {
  width: 85%;  /* 设定输入框宽度 */
}
</style>