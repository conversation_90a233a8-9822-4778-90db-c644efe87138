<template>
  <div>
    <a-drawer
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :destroyOnClose="true"
      width="70%"
    >
      <template slot="title">
        <span class="title-name">{{ title }}</span>
        <span v-if="this.modeType !== '0'" class="title-age">
          <a-dropdown v-permission="barcode_Furnace_No_del">
            <a-button class="ant-dropdown-link">
              {{ $t('public.action') }}
              <a-icon type="down" />
            </a-button>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="delet()">{{ $t('public.delete') }}</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </template>
      <div :style="{ height: heights }" style="overflow:auto">
        <a-form-model layout="horizontal" ref="ruleForm" :rules="rules" :model="form">
          <a-row>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlRllNo')" prop="rlRllNo">
                <a-input
                  v-model="form.rlRllNo"
                  :disabled="disabled"
                  :placeholder="$t('Furnace_No.placeholder.rlRllNo')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlRllLk')">
                <a-input
                  v-model="form.rlRllLk"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlRllLk')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlGbxh')">
                <a-input
                  v-model="form.rlGbxh"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlGbxh')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlKszj')">
                <a-input
                  v-model="form.rlKszj"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlKszj')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlTyqcy')">
                <a-input
                  v-model="form.rlTyqcy"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlTyqcy')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlTyhcy')">
                <a-input
                  v-model="form.rlTyhcy"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlTyhcy')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlKlsj')">
                <a-input
                  v-model="form.rlKlsj"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlKlsj')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlGbwd')">
                <a-input
                  v-model="form.rlGbwd"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlGbwd')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlGbngswd')">
                <a-input
                  v-model="form.rlGbngswd"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlGbngswd')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlCksj')">
                <a-input
                  v-model="form.rlCksj"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlCksj')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlCkwd')">
                <a-input
                  v-model="form.rlCkwd"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlCkwd')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlZdsj')">
                <a-input
                  v-model="form.rlZdsj"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlZdsj')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlSlys')">
                <a-input
                  v-model="form.rlSlys"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlSlys')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlTyclzl')">
                <a-input
                  v-model="form.rlTyclzl"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlTyclzl')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlXglzl')">
                <a-input
                  v-model="form.rlXglzl"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlXglzl')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlHlglzl')">
                <a-input
                  v-model="form.rlHlglzl"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlHlglzl')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlCysj')">
                <a-input
                  v-model="form.rlCysj"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlCysj')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlSi')">
                <a-input v-model="form.rlSi" :disabled="formStatus" :placeholder="$t('Furnace_No.placeholder.rlSi')" />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlGmn')">
                <a-input
                  v-model="form.rlGmn"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlGmn')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlDjmn')">
                <a-input
                  v-model="form.rlDjmn"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlDjmn')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlGcr')">
                <a-input
                  v-model="form.rlGcr"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlGcr')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlGxm')">
                <a-input
                  v-model="form.rlGxm"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlGxm')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlAl')">
                <a-input v-model="form.rlAl" :disabled="formStatus" :placeholder="$t('Furnace_No.placeholder.rlAl')" />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlWcr')">
                <a-input
                  v-model="form.rlWcr"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlWcr')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlNi')">
                <a-input v-model="form.rlNi" :disabled="formStatus" :placeholder="$t('Furnace_No.placeholder.rlNi')" />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlMu')">
                <a-input v-model="form.rlMu" :disabled="formStatus" :placeholder="$t('Furnace_No.placeholder.rlMu')" />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlV')">
                <a-input v-model="form.rlV" :disabled="formStatus" :placeholder="$t('Furnace_No.placeholder.rlV')" />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlQtys')">
                <a-input
                  v-model="form.rlQtys"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.rlQtys')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.creatDate')">
                <a-input
                  v-model="form.creatDate"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.creatDate')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.valid')">
                <a-input
                  v-model="form.valid"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.valid')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.moNoRem')">
                <a-input
                  v-model="form.moNoRem"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.moNoRem')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.hxcfC')">
                <a-input
                  v-model="form.hxcfC"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.hxcfC')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.hxcfSi')">
                <a-input
                  v-model="form.hxcfSi"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.hxcfSi')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.hxcfMn')">
                <a-input
                  v-model="form.hxcfMn"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.hxcfMn')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.hxcfP')">
                <a-input
                  v-model="form.hxcfP"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.hxcfP')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.hxcfS')">
                <a-input
                  v-model="form.hxcfS"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.hxcfS')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.hxcfCr')">
                <a-input
                  v-model="form.hxcfCr"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.hxcfCr')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.hxcfAL')">
                <a-input
                  v-model="form.hxcfAL"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.hxcfAL')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.hxcfNi')">
                <a-input
                  v-model="form.hxcfNi"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.hxcfNi')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.hxcfMo')">
                <a-input
                  v-model="form.hxcfMo"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.hxcfMo')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.hxcfV')">
                <a-input
                  v-model="form.hxcfV"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.hxcfV')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.hxcfCu')">
                <a-input
                  v-model="form.hxcfCu"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.hxcfCu')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.hxcfQt')">
                <a-input
                  v-model="form.hxcfQt"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.hxcfQt')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.rlTp')">
                <a-input v-model="form.rlTp" :disabled="formStatus" :placeholder="$t('Furnace_No.placeholder.rlTp')" />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.tlRem')">
                <a-input
                  v-model="form.tlRem"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.tlRem')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :xxl="8" :xl="8" :lg="12">
              <a-form-model-item v-bind="formItemLayout" :label="$t('Furnace_No.tlRem')">
                <a-input
                  v-model="form.tlRem"
                  :disabled="formStatus"
                  :placeholder="$t('Furnace_No.placeholder.tlRem')"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>

      <a-row :gutter="16">
        <a-col class="gutter-row" :span="12" style="text-align:right">
          <a-button type="primary" :loading="loading" v-if="modeType === '0'" @click="handleOK()" v-permission="barcode_Furnace_No_save">{{
            $t('public.save')
          }}</a-button>
          <a-button type="primary" v-if="modeType === '1'" @click="handleMenuClick()" v-permission="barcode_Furnace_No_save">{{ $t('public.edit') }}</a-button>
          <a-button type="primary" :loading="loading" v-if="modeType === '2'" @click="handleEdit()" v-permission="barcode_Furnace_No_save">{{
            $t('public.save')
          }}</a-button>
        </a-col>
        <a-col class="gutter-row" :span="12" style="text-align:left">
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script>
import { add, edit } from '@/api/barcode/paramterSetting/Furnace_No'
import { check } from '@/api/barcode/paramterSetting/Parameter_definition'

export default {
  data() {
    return {
      barcode_Furnace_No_del: 'barcode_Furnace_No_del',
      barcode_Furnace_No_save: 'barcode_Furnace_No_save',
      title: '',
      disabled: false,
      visible: false,
      formStatus: false,
      loading: false,
      modeType: '',
      dep: '',
      row: {},
      form: {
        stopId: 'F'
      },
      rule: false,
      rules: {
        rlRllNo: [{ required: true, validator: this.handlePass, trigger: 'blur' }]
      },
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 7 },
          md: { span: 8 },
          lg: { span: 7 }
        },
        wrapperCol: {
          xs: { span: 5 },
          sm: { span: 16 },
          md: { span: 17 },
          lg: { span: 16 }
        }
      }
    }
  },
  computed: {
    // 滚动区高度
    // (业务需求：屏幕高度减去头部标题和底部tabbar的高度，当然这2个高度也是可以动态获取的)
    heights: function() {
      return window.innerHeight - 120 + 'px'
    }
  },
  methods: {
    // 校验
    handlePass(rule, value, callback) {
      if (this.rule) {
        if (value) {
          check({
            type: 'rl',
            id: value
          }).then(response => {
            const result = response.msg
            if (result == 'fail') {
              callback(new Error('该熔炼炉号已重复 请重新输入！'))
            } else {
              callback()
            }
          }).catch(err => this.requestFailed(err))
        } else {
          callback(new Error('请输入熔炼炉号！'))
        }
      }
    },
    // 取消
    onClose() {
      this.rule = false
      this.loading = false
      this.visible = false
      this.form = { stopId: 'F' }
    },
    create(model, row) {
      this.rule = true
      this.title = model.title
      this.modeType = '0'
      this.visible = true
      this.formStatus = false
      this.disabled = false
    },
    // 点击编辑按钮
    handleMenuClick() {
      this.rule = false
      this.title = this.$t('public.edit')
      this.title = this.$t('public.edit')
      this.formStatus = false
      this.modeType = '2'
      this.disabled = true
    },
    edit(model, row) {
      this.rule = false
      this.disabled = true
      this.title = model.title
      this.modeType = '1'
      this.row = row
      this.formStatus = true
      this.visible = true
      this.form = {
        rlRllNo: row.rlRllNo,
        rlRllLk: row.rlRllLk,
        rlGbxh: row.rlGbxh,
        rlKszj: row.rlKszj,
        rlTyqcy: row.rlTyqcy,
        rlTyhcy: row.rlTyhcy,
        rlKlsj: row.rlKlsj,
        rlGbwd: row.rlGbwd,
        rlGbngswd: row.rlGbngswd,
        rlCksj: row.rlCksj,
        rlCkwd: row.rlCkwd,
        rlZdsj: row.rlZdsj,
        rlSlys: row.rlSlys,
        rlTyclzl: row.rlTyclzl,
        rlXglzl: row.rlXglzl,
        rlHlglzl: row.rlHlglzl,
        rlCysj: row.rlCysj,
        rlSi: row.rlSi,
        rlGmn: row.rlGmn,
        rlDjmn: row.rlDjmn,
        rlGcr: row.rlGcr,
        rlGxm: row.rlGxm,
        rlAl: row.rlAl,
        rlWcr: row.rlWcr,
        rlNi: row.rlNi,
        rlMu: row.rlMu,
        rlV: row.rlV,
        rlQtys: row.rlQtys,
        createTime: row.createTime,
        valid: row.valid,
        moNoRem: row.moNoRem,
        hxcfC: row.hxcfC,
        hxcfSi: row.hxcfSi,
        hxcfMn: row.hxcfMn,
        hxcfP: row.hxcfP,
        hxcfS: row.hxcfS,
        hxcfCr: row.hxcfCr,
        hxcfAl: row.hxcfAl,
        hxcfNi: row.hxcfNi,
        hxcfMo: row.hxcfMo,
        hxcfV: row.hxcfV,
        hxcfCu: row.hxcfCu,
        hxcfQt: row.hxcfQt,
        rlTp: row.rlTp,
        ycTp: row.ycTp,
        rlTyclzlpc: row.rlTyclzlpc,
        rlXglzlpc: row.rlXglzlpc,
        tlRem: row.tlRem,
        man: row.man
      }
    },
    // 添加确认
    handleOK() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          add(this.form)
            .then(res => {
              this.loading = false
              this.onClose()
              this.$emit('getList')
              this.$message.success(this.$t('public.success'))
            })
            .catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
          return false
        }
      })
    },
    delet() {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('public.del.content'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk() {
          that.loading = true
          del({ id: that.row.id, verNo: that.row.verNo })
            .then(() => {
              that.loading = false
              that.onClose()
              that.$emit('getList')
              that.$message.success(that.$t('public.success'))
            })
            .catch(err => that.requestFailed(err))
            .finally(() => {
              that.loading = false
            })
        },
        onCancel() {
          that.loading = false
        }
      })
    },
    // 确认编辑
    handleEdit() {
      this.loading = true
      this.form.id = this.row.id


      edit(this.form)
        .then(res => {
          this.loading = false
          this.onClose()
          this.$emit('getList')
          this.$message.success(this.$t('public.success'))
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
      // this.$refs.ruleForm.validate(valid => {
      //   if (valid) {
      //     this.loading = true
      //     this.form.id = this.row.id


      //     edit(this.form).then((res) => {
      //       this.loading = false
      //       this.onClose()
      //       this.$emit('getList')
      //       this.$message.success(this.$t('public.success'))
      //     })
      //   } else {
      //     this.loading = false
      //     this.$message.error(this.$t('public.error'))
      //     return false
      //   }
      // })
    }
  }
}
</script>
