<template>
  <div class="bill-container">
    <div class="bill-header">
      <!-- 工具栏 操作按钮 -->
      <dToolbar :data="toolbarItems" @toolbarClick="handleToolbarClick"
                :dropSetShow="dropSetShow"
                @handOpenDocDia = "handOpenDocDia"
                @handOpenDefDia = "handOpenDefDia"
      ></dToolbar>
    </div>
    <div class="bill-body">
      <!-- 单据页面设计-视窗 -->
      <docDialog
        :docDialogVisible.sync="docDialogVisible"
        :form-config="formConfig"
        :grid-config="gridConfig"
      ></docDialog>
      <!-- 自定义栏位设计-视窗 -->
      <defDialog
        :defDialogVisible.sync="defDialogVisible"
        :form-config="formConfig"
        :grid-config="gridConfig"
      ></defDialog>

      <!-- 数据 表头vxe-form及表身vxe-grid -->
      <div>
        <el-collapse v-model="activeNames">
          <el-collapse-item style="margin-bottom: 10px" name="1">
            <template #title>
              基础信息
            </template>
            <dForm
              ref = "dfo"
              :formConfig="formConfig"
              :billData.sync="billData"
            >
            </dForm>
          </el-collapse-item>

          <el-collapse-item style="margin-bottom: 10px" name="2">
            <template #title>
              附件
            </template>
            <annex ref="anRef" @getAnnexData="getAnnexData" ></annex>
          </el-collapse-item>
          <el-collapse-item style="margin-bottom: 10px" name="3">
            <template #title>
              <div class="title-container">
                <template class="title-text">检验数据</template>
                <div class="title-actions" v-show="activeNames.includes('3')">
                  <el-button size="mini" type="primary" icon="el-icon-circle-plus" @click.stop="addTableRow" circle></el-button>
                  <el-button size="mini" type="danger" icon="el-icon-delete" @click.stop="locaDelTableRow" circle></el-button>
                </div>
              </div>
            </template>

<!--            <div style="padding-top:5px; display: flex; justify-content: flex-end;">-->
<!--              <el-button size="mini" icon="el-icon-plus" id="add_table" type="primary" @click="addTableRow()"></el-button>-->
<!--              <el-button size="mini" icon="el-icon-minus" type="primary"-->
<!--                         :style="'background-color: #f56c6c;border-color: #f56c6c;color:#fff;'" id="add_table"-->
<!--                         @click="locaDelTableRow()"></el-button>-->
<!--            </div>-->
            <el-row :gutter="10">
              <el-col :span="24">
                <el-table stripe :data="inspectData" class="checkdata" highlight-current-row
                          style="width: 100%;margin-top:10px;" @selection-change="handleSelectionChange" height="320px"
                          @row-click="mfBxHanddle" :header-cell-style="{backgroundColor: '#F4F5F9', fontWeight: '400' }"
                          :row-class-name="tableRowClassName" :row-style="selectedstyle">


                  <el-table-column type="selection" ></el-table-column>
                  <el-table-column label="检验项目" >
                    <template slot-scope="scope">
                      <el-input @focus="focustwo(scope.$index,scope.row, $event)" @input="inputNameInputtwo"
                                ref="prdt3Input" readonly v-model="scope.row.qcItm"></el-input>

                    </template>
                  </el-table-column>
                  <el-table-column prop="qcName" label="检验名称"></el-table-column>
                  <el-table-column prop="qty"  align="left" label="检验数量"></el-table-column>

                  <el-table-column prop="qcSpc"  align="left" label="检验标准">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.qcSpc" size="mini" style="max-width: 90px;width:100%;"></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column prop="stdValue"  align="left" label="标准值">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.stdValue" size="mini" type="number" :disabled="scope.row.qcType == 1"
                                @input="qcRecchangebiao(scope)" @blur="blurstdValue(scope)" style="max-width: 200px;width:100%;"
                                @wheel.native.prevent="stopScroll($event)"></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column prop="stdValueId"  align="left" label="逻辑符">
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.stdValueId" placeholder="请选择" size="mini"
                                 :disabled="scope.row.qcType == 1" @input="stdValueIdchange(scope)" clearable
                                 style="max-width:200px; width:100%;">
                        <el-option v-for="item in stdValueLIst" :key="item.value" :label="item.value"
                                   :value="item.value"></el-option>
                      </el-select>
                    </template>
                  </el-table-column>

                  <el-table-column width="160" align="left" label="偏差值">
                    <template slot-scope="scope">
                      <el-input v-if="scope.row.stdValueId=='±'" v-model="scope.row.val1" size="mini" type="number"
                                :disabled="scope.row.qcType == 1" @input="rangechange(scope)" @blur="blurval1(scope)"
                                style="max-width: 60px;width:100%;" @wheel.native.prevent="stopScroll($event)"></el-input>
                      <div v-if="scope.row.stdValueId=='>='">
                        <span>>=</span> {{scope.row.stdValue}}
                      </div>

                      <div v-if="scope.row.stdValueId=='<='">
                    <span>
                      <= </span> {{scope.row.stdValue}}
                      </div>

                      <el-input v-if="scope.row.stdValueId=='='" v-model="scope.row.val1" size="mini"
                                :disabled="scope.row.qcType == 1" type="number" @input="equalchange(scope)" @blur="blurval1(scope)"
                                style="max-width: 60px;width:100%;" @wheel.native.prevent="stopScroll($event)"></el-input>

                      <div v-if="scope.row.stdValueId=='+-'">
                        <span style="font-size:16px;">-</span>
                        <el-input v-model="scope.row.val2" size="mini" :disabled="scope.row.qcType == 1" type="number"
                                  @input="reducechange(scope)" @blur="blurval2(scope)" style="max-width: 60px;width:100%;"
                                  @wheel.native.prevent="stopScroll($event)"></el-input>

                        <span style="font-size:16px;">+</span>
                        <el-input v-model="scope.row.val1" @blur="blurval1(scope)" size="mini"
                                  :disabled="scope.row.qcType == 1" type="number" @input="addchange(scope)"
                                  style="max-width: 60px;width:100%;" @wheel.native.prevent="stopScroll($event)"></el-input>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="acMin"  align="left" label="允收下限">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.acMin" size="mini" type="number"
                                :disabled="scope.row.stdValueId !== '~~'" style="max-width: 200px;width:100%;"
                                @wheel.native.prevent="stopScroll($event)"></el-input>

                    </template>
                  </el-table-column>
                  <el-table-column prop="acMax" align="left" label="允收上限">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.acMax" size="mini" type="number"
                                :disabled="scope.row.stdValueId !== '~~'" style="max-width: 200px;width:100%;"
                                @wheel.native.prevent="stopScroll($event)"></el-input>

                    </template>
                  </el-table-column>
                  <el-table-column prop="qcType" align="left" label="检验类型">
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.qcType" placeholder="请选择检验类型" size="mini"
                                 style="max-width:200px; width:100%;">
                        <el-option v-for="item in typetwo" :key="item.value" :label="item.label"
                                   :value="item.value"></el-option>
                      </el-select>
                    </template>
                  </el-table-column>

                  <el-table-column prop="qcTool"  align="left" label="测量工具">
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.qcTool" placeholder="请选择" size="mini" @input="qcToolinput(scope)"
                                 clearable filterable style="max-width:200px; width:100%;">
                        <el-option v-for="item in toolLIst" :key="item.qcTool" :label="item.qcToolName"
                                   :value="item.qcTool"></el-option>
                      </el-select>
                    </template>
                  </el-table-column>

                  <el-table-column prop="qtyLost"align="left" label="不合格量"></el-table-column>

                </el-table>
                <el-dialog title="检验项目" :visible.sync="visibleqcItm" width="40%" style="height:100%">
                  <div class="table-page-search-wrapper" style="height:400px;">
                    <el-input v-model="queryKeyword" @input="inputNameInputtwo" size="mini" style="width: 150px"
                              placeholder="请输入"></el-input>
                    <el-table :data="pickerListtwo" border stripe v-loading="loading" @row-click="mfBxHanddleqcItm" style
                              height="360"
                              :header-cell-style="{fontSize:'14px',color: '#606266',backgroundColor: '#F4F5F9', fontWeight: '700' }">


                      <el-table-column fixed="left" prop="qcItm" align="left" label="检验项目"></el-table-column>

                      <el-table-column prop="name" align="left" label="检验名称"></el-table-column>
                    </el-table>

                  </div>
                </el-dialog>

                <!--focus激活选择货品  -->
                <div @mouseleave="prdt3leave">
                  <el-popover placement="bottom" :style="objStyle" width="400" style="position: fixed;z-index: 99999;"
                              v-model="visible">
                    <div>
                      <el-table class="el-table" :data="pickerList" stripe max-height="360px" size="mini" height="360px"
                                highlight-current-row :cell-style="{ verticalAlign: 'top' }"
                                :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                                style="width: 100%;">
                        <el-table-column fixed="left" align="center" label="选择" width="80">
                          <template slot-scope="scope">
                            <el-button round type="primary" style="padding: 5px 5px 5px 5px;" size="mini"
                                       icon="el-icon-d-arrow-right" @click="getGoodsPopover(scope.row)">选择</el-button>
                          </template>
                        </el-table-column>
                        <el-table-column fixed="left" prop="spcNo" align="left" label="原因代号" width="70"></el-table-column>

                        <el-table-column prop="name" align="left" label="不合格原因" width="130"></el-table-column>
                      </el-table>
                      <div>
                        <el-pagination background :pager-count="5" :page-sizes="[5,10, 30, 50]" :page-size="10"
                                       @size-change="pageSizeChangespcNo" :current-page="tablePagespcNo.currentPage"
                                       @current-change="currentChangespcNo" layout="prev, pager, next" :total="spcNototal"
                                       style="margin-top: 5px;"></el-pagination>
                      </div>
                    </div>

                  </el-popover>
                </div>
              </el-col>
              <el-col :span="24">
                <div v-if="inspectData.length">
                  <el-table stripe :data="inspectData[qcItmflag].inspectDatatwo" highlight-current-row
                            :cell-style="{ verticalAlign: 'top' }"
                            :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                            style="width: 100%;margin-top:10px;" @selection-change="handleSelectionChange" height="400px">

                    <el-table-column width="60"  align="left" prop="itm" label="项次">
                      <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                    </el-table-column>
                    <el-table-column prop="qcRec" align="left" label="检验值">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.qcRec" size="mini" type="number" @input="qcRecchange(scope)"
                                  style="max-width: 200px;width:100%;" @wheel.native.prevent="stopScroll($event)"></el-input>
                      </template>
                    </el-table-column>
                    <el-table-column prop="chkId" align="left" label="合格判定">
                      <template slot-scope="scope">
                        <el-select v-model="scope.row.chkId" placeholder="合格判定" size="mini" clearable
                                   @input="noqualified(scope)" style="max-width:200px; width:100%;">
                          <el-option v-for="item in qualifijudgment" :key="item.value" :label="item.label"
                                     :value="item.value"></el-option>
                        </el-select>
                      </template>
                    </el-table-column>

                    <el-table-column prop="spcName" align="left" label="不合格原因">
                      <template slot-scope="scope">
                        <el-input @focus="focus(scope.$index,scope.row, $event)" @blur="blurNameInput"
                                  @input="inputNameInput" ref="prdt3Input" :disabled="scope.row.chkId == 'T'"
                                  v-model="scope.row.spcName" style="border: none;font-size:12px;height:10px;padding:0"></el-input>
                      </template>
                    </el-table-column>
                    <el-table-column prop="rem" align="left" label="备注">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.rem" size="mini" style="max-width: 200px;width:100%;"></el-input>
                      </template>
                    </el-table-column>
                  </el-table>

                </div>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>

      </div>
    </div>
  </div>
</template>

<script>
import docDialog from "@/components/def/DocDialog.vue";
import defDialog from "@/components/def/DefDialog.vue";
import dGrid from "@/components/def/DGrid.vue"
import dForm from "@/components/def/DForm.vue"
import vToolbar from "@/components/amtxts/vTable/vToolbar.vue"
import dToolbar from "@/components/def/DToolbar.vue"
import useFieldDefine from "@/utils/def/useFieldDefine";

import {
  datadepPage, gctgspctyDel, gctgspcyjInfo, gctgspcqcAdd, gctgspcdjInfo, gctgspcqcUpd, spcLstpage, sysfiledel, basicDataqcItm, basicDatatool
} from '@/api/mes/quality'
import { mapGetters, mapState } from 'vuex'
import annex from '@/components/Upload/annex.vue'

import moment from 'moment'
import MySelectListwo from '@/components/MySelectListwo'

export default {
  components: {
    docDialog, defDialog,
    dGrid, dForm,
    vToolbar, dToolbar,
    annex,
    MySelectListwo
  },
  data() {
    return {
      defDialogVisible: false,
      docDialogVisible: false,
      activeNames: ['1', '2', '3'],

      formConfig: {
        //托工检（录数据）
        PGM: "TGTYSPC",
        MENU_NAME: "TGTYSPC",
        TABLE_NAME: "MES_QC_TI",

        initItems: null,
        items: [
          {
            span: 24,
            children: []
          }
        ],
        rules: {
          dep: [{ required: true, message: "必填:部门", trigger: ["change", "blur"] }],
        },
      },

      formItems: [
        {
          span: 24,
          children: [
            {
              field: 'tyDd',
              title: '检验日期',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'tyNo',
              title: '检验单号',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'qcTypeName',
              title: '检验方式',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'cusNo',
              title: '客户厂商',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'dep', title: '责任部门', span: 6,
              itemRender: {
                name: 'MySelectList',
                props: {
                  placeholder: '',
                  url: '/mes/basicData/depPage',
                  tableColumn: this.$Column.salmDep,
                  form: this.$Form.salmDep,
                },
              }
            },
            {
              field: 'tiNo',
              title: '送检单号',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'salNo',
              title: '检验人员',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'bilType',
              title: '单据类别',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'sys',
              title: '实验室检验',
              span: 6,
              itemRender: {
                name: 'MyElCheckBox',
                props: {
                  placeholder: '',
                  disabled: true,
                  trueLable: 'T',
                  falseLable: 'F',
                },
                events: {
                  change: this.onChangeLaboratory,
                }
              }
            },
            {
              field: 'qcIds',
              title: '检验结果',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'chkKnd',
              title: '复检',
              span: 6,
              itemRender: {
                name: 'MyElCheckBox',
                props: {
                  placeholder: '',
                  disabled: true,
                  trueLable: 'T',
                  falseLable: 'F',
                },
                events: {
                  change: this.onChangeclosecase,
                }
              }
            },

            {
              field: 'prdNo',
              title: '品号',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'prdName',
              title: '品名',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'prdMark',
              title: '特征',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true, } }
            },
            {
              field: 'unitName',
              title: '单位',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },

            {
              field: 'batNo',
              title: '批号',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'qtySps',
              title: '可疑数量',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'qty',
              title: '检验数量',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'qtyLostRto',
              title: '不合格率',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'whName',
              title: '仓库',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'batNo',
              title: '批号',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'moNo',
              title: '工单号',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'pgNo',
              title: '派工单号',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'zcName',
              title: '工序',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'qtyRtn',
              title: '已转合格量',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'lsNo',
              title: '不合格评审单',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'boxNo',
              title: '周转箱号',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'rem',
              title: '备注',
              span: 24,
              itemRender: { name: 'MyElInput', props: { placeholder: '' } }
            },
            {
              field: 'qty',
              title: '检验数量',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'qtyOk',
              title: '合格量',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'qtyLost',
              title: '不合格量',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '',  } }
            },
          ]
        }
      ],

      gridConfig: {
        TABLE_NAME: "",
        height: '180',
        columns: [],
        rules: {
        }
      },
      billData: {
        pgm: 'MES_QC_TI',
        formData: {},
        gridData: []
      },
      toolbarItems: [
        {label: '删除', value: 'del', icon: 'icon-delete' },
        {label: '保存', value: 'save', icon: 'icon-save' },
        {label: '关闭', value: 'close', icon: 'icon-close' },
        {value: 'edit', visible: false },
      ],
      dropSetShow: true,



      isEdit: true,
      mes_quality_query: 'mes_quality_query',
      mes_quality_reset: 'mes_quality_reset',
      mes_quality_newOpen: 'mes_quality_newOpen',
      mes_quality_inspection: 'mes_quality_inspection',
      mes_quality_pause: 'mes_quality_inspection',
      mes_quality_continues: 'mes_quality_continues',
      mes_quality_task: 'mes_quality_task',
      tableData: [],

      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 5,
        total: 0
      },
      row: {},
      rowIndex: {},
      // 按钮控制
      btn: {
        newOpen: true, // 首检开工
        open: true, // 开始
        pause: true, // 暂停
        continues: true, // 继续
        report: true, // 报工
        exceptional: true, // 异常
        inspection: true, // 送检
        introspecting: true // 自检
      },
      queryParam: {
        date: [],
      },
      activeName: '1',
      visible: false,
      reasondata: [],
      multiplereason: [],
      entity: {
        tyDd: '',
        chkKnd: '',
        qcId: '',
        qcIds: '',
        sys: '',
        chktyId: '',
        tiNo: '',
        tyNo: '',
        prdt3Name: "",
        way: 2,
        auxId: "",
        otRemark: "",
        bxId: "",
        qaId: "",
        otId: "",
        bxNo: "",
        bxDd: "",
        status: "",
        statusMsg: "",
        kndId: 3,
        applyDepId: null,
        applyDepNo: "",
        applyDepName: "",
        applyUserId: "",
        applyUserNo: "",
        applyUserName: "",
        prdId: "",
        prdNo: "",
        prdName: "",
        prdt3Id: "",
        prdSpc: "",
        faultRem: "",
        cntId: "",
        cntName: "",
        cntTel: "",
        cntAdr: "",
        dcId: "",
        dcName: "",
        dcLmt: "",
        urgent: 0,
        finId: "",
        finName: "",
        finLmt: "",
        bxPic1: "",
        bxPic2: "",
        bxPic3: "",
        serverDeptId: "",
        otUserId: "",
        qty: null,
        serviceCode: "",
        coDd: null,
        prdUt: "",
        bxType: '1',
        initialTime: '',
        completeTime: ''
      },
      qualifijudgment: [
        { value: 'T', label: '合格' },
        { value: 'F', label: '不合格' },
      ],
      inspectiontype: [
        { value: '1', label: '完工检验' },
        { value: '2', label: '首检检验' },
        { value: '3', label: '托工检验' },
      ],
      multipleSelectiontwo: [],
      sysFiles: [],
      colData: [
        { title: "原因代号", istrue: true },
        { title: "不合格原因", istrue: true },
        { title: "不合格量", istrue: true },
        { title: "货品名称", istrue: true },
        { title: "货品代号", istrue: true },
        { title: "规格", istrue: true },
        { title: "现有库存", istrue: true },
        { title: "借出量", istrue: true },
        { title: "单位", istrue: true },
        { title: "单价", istrue: true },
        { title: "数量", istrue: true },
        { title: "已还数量", istrue: true },
        { title: "税率%", istrue: true },
        { title: "未税金额", istrue: true },
        { title: "税额", istrue: true },
        { title: "金额", istrue: true },
      ],
      objStyle: {
        top: "433px",
        left: "",
      },
      pickerList: [],
      testData: [],
      detailIds: [],
      multipleSelection: [],
      pickerIndex: 0,
      isShowPopVel: false,
      rightHeight: 0,
      tablerightHeight: 0,
      tyNotwo: '',
      tyDdtwo: '',
      flag: false,
      ccc: true,
      isClaim: false,
      data: '',
      tableLoading: false,
      bottomWidth: '',
      inspectData: [],
      inspectDatatwo: [],
      chkIdqty: '',
      qcItmflag: 0,
      inspectDataflag: true,
      getIndex: null,
      queryKeyword: '',
      queryKeywordtwo: [],
      visibleqcItm: false,
      pickerListtwo: [],
      pickerIndextwo: '',
      toolLIst: [],
      typetwo: [
        { value: '1', label: '计数' },
        { value: '2', label: '测量' },
      ],
      stdValueLIst: [
        { value: '±', label: '±' },
        { value: '>=', label: '>=' },
        { value: '<=', label: '<=' },
        { value: '=', label: '=' },
        { value: '+-', label: '+-' },
        { value: '~~', label: '~~' },
      ],
      spcNototal: -1,
      tablePagespcNo: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
    }
  },

  mounted() {
    console.log("mounted")
    basicDatatool(
      {
        current: 1,
        size: 1000,
        qcTool: ''
      }).then(response => {
      this.toolLIst = response.data
    }).catch(err => this.requestFailed(err))
      .finally(() => {
        this.loading = false
      })
    this.formConfig.items = this.formItems
    //单据设计页面用 initItems, initColumns
    this.formConfig.initItems = JSON.parse(JSON.stringify( this.formItems[0].children))
    this.gridConfig.initColumns = JSON.parse(JSON.stringify( this.gridConfig.columns))
    //初始化, 页面栏位信息加载 及动态渲染
    useFieldDefine.init(this.formConfig, this.gridConfig, this.billData)

    this.pickerSearch()
    this.$nextTick(() => {
      // this.rightHeight = this.$refs.leftHeight.offsetHeight + 6 + 'px'
      // this.bottomWidth = this.$refs.leftHeight.offsetWidth + 6 + 'px'
      //
      // this.tablerightHeight = this.$refs.leftHeight.offsetHeight - 65 + 'px'
    })
  },

  activated(){
    if (localStorage.getItem('tuogonglutiNo')) {
      this.billData.formData.tiNo = localStorage.getItem('tuogonglutiNo')
      this.getListtreat()
    } else {
      this.billData.formData.tyNo = localStorage.getItem('tuogonglutyNo')
      this.getList()
    }
  },

  watch: {
    //自定义栏位设计 关闭, 触发更新栏位数据
    defDialogVisible(newVal) {
      if (newVal === false) {
        useFieldDefine.init(this.formConfig, this.gridConfig, this.billData)
      }
    },
    //单据页面设计 关闭, 触发更新栏位数据
    docDialogVisible(newVal) {
      if (newVal === false) {
        useFieldDefine.init(this.formConfig, this.gridConfig, this.billData)
      }
    },
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    }),
    ...mapGetters(['permissions'])
  },
  created() {
    this.sysFiles = []
    // if (localStorage.getItem('tuogonglutiNo')) {
    //   this.billData.formData.tiNo = localStorage.getItem('tuogonglutiNo')
    //   this.getListtreat()
    // } else {
    //   this.billData.formData.tyNo = localStorage.getItem('tuogonglutyNo')
    //   this.getList()
    // }
  },
  methods: {
    getAnnexData(param){
      this.tableData=param
    },
    handleToolbarClick(params){
      switch (params) {
        case 'del':
          this.deldata()
          break;
        case 'close':
          this.cosetiao()
          break;
        case 'save':
          this.save()
          break;
        default:
      }

    },

    //打开单据页面设计窗口
    handOpenDocDia() {
      this.docDialogVisible = true;
    },

    //打开自定义栏位设计页面窗口
    handOpenDefDia() {
      this.defDialogVisible = true;
    },

    currentChangespcNo(currentPage) {
      this.tablePagespcNo.currentPage = currentPage;
      this.pickerSearch();
    },
    pageSizeChangespcNo(pageSize) {
      this.tablePagespcNo.pageSize = pageSize;
      this.pickerSearch();
    },
    qcRecchangebiao(scope) {
      if (scope.row.stdValueId) {
        if (scope.row.stdValue && scope.row.val1) {

          if (scope.row.stdValueId == '±') {
            scope.row.acMin = (+scope.row.stdValue - +scope.row.val1).toFixed(2)
            scope.row.acMax = (+scope.row.stdValue + +scope.row.val1).toFixed(2)
          } else if (scope.row.stdValueId == '=') {
            scope.row.acMin = parseFloat(scope.row.val1).toFixed(2)
            scope.row.acMax = parseFloat(scope.row.val1).toFixed(2)
          } else if (scope.row.stdValueId == '+-') {
            if (scope.row.val1 && scope.row.stdValue) {
              scope.row.acMax = (+scope.row.stdValue + +scope.row.val1).toFixed(2)
            } else {
              scope.row.acMax = ''
            }
            if (scope.row.val2 && scope.row.stdValue) {
              scope.row.acMin = (+scope.row.stdValue - +scope.row.val2).toFixed(2)
            } else {
              scope.row.acMin = ''
            }
          }
        } else {
          scope.row.acMin = ''
          scope.row.acMax = ''
        }
        if (scope.row.stdValue) {

          if (scope.row.stdValueId == '>=') {
            scope.row.acMin = scope.row.stdValue.toFixed(2)
            scope.row.val1 = scope.row.stdValue.toFixed(2)
            scope.row.acMax = ''
          } else if (scope.row.stdValueId == '<=') {
            scope.row.acMin = ''
            scope.row.acMax = scope.row.stdValue.toFixed(2)
            scope.row.val1 = scope.row.stdValue.toFixed(2)
          }
        } else {
          scope.row.acMin = ''
          scope.row.acMax = ''
        }
      } else {
        scope.row.acMin = ''
        scope.row.acMax = ''
        scope.row.val1 = ''
        scope.row.val2 = ''
      }
    },
    blurstdValue(scope) {
      scope.row.stdValue = parseFloat(scope.row.stdValue).toFixed(2)
    },
    rangechange(scope) {

      if (scope.row.val1) {

        scope.row.acMin = (+scope.row.stdValue - +scope.row.val1).toFixed(2)
        scope.row.acMax = (+scope.row.stdValue + +scope.row.val1).toFixed(2)
      } else {
        scope.row.acMin = ''
        scope.row.acMax = ''
      }
      for (let i = 0; i < this.inspectData.length; i++) {
        for (let j = 0; j < this.inspectData[i].inspectDatatwo.length; j++) {
          this.inspectData[i].inspectDatatwo[j].acMax = this.inspectData[i].acMax
          this.inspectData[i].inspectDatatwo[j].acMin = this.inspectData[i].acMin
        }
      }
    },
    blurval1(scope) {
      scope.row.val1 = parseFloat(scope.row.val1).toFixed(2)
    },
    blurval2(scope) {

      scope.row.val2 = parseFloat(scope.row.val2).toFixed(2)
    },
    topchange(scope) {
      if (scope.row.val1) {
        scope.row.acMin = scope.row.val1.toFixed(2)
      } else {
        scope.row.acMin = ''
        scope.row.acMax = ''
      }
    },
    bottomchange(scope) {
      if (scope.row.val1) {
        scope.row.acMax = scope.row.val1
      } else {
        scope.row.acMin = ''
        scope.row.acMax = ''
      }
    },
    equalchange(scope) {
      if (scope.row.val1) {
        scope.row.acMax = parseFloat(scope.row.val1).toFixed(2)
        scope.row.acMin = parseFloat(scope.row.val1).toFixed(2)
      } else {
        scope.row.acMin = ''
        scope.row.acMax = ''
      }
      for (let i = 0; i < this.inspectData.length; i++) {
        for (let j = 0; j < this.inspectData[i].inspectDatatwo.length; j++) {
          this.inspectData[i].inspectDatatwo[j].acMax = this.inspectData[i].acMax
          this.inspectData[i].inspectDatatwo[j].acMin = this.inspectData[i].acMin
        }
      }
    },
    addchange(scope) {
      if (scope.row.val1 && scope.row.stdValue) {
        scope.row.acMax = (+scope.row.stdValue + +scope.row.val1).toFixed(2)
      } else {
        scope.row.acMax = ''
      }
      for (let i = 0; i < this.inspectData.length; i++) {
        for (let j = 0; j < this.inspectData[i].inspectDatatwo.length; j++) {
          this.inspectData[i].inspectDatatwo[j].acMax = this.inspectData[i].acMax
          this.inspectData[i].inspectDatatwo[j].acMin = this.inspectData[i].acMin
        }
      }
    },
    reducechange(scope) {
      if (scope.row.val2 && scope.row.stdValue) {
        scope.row.acMin = (+scope.row.stdValue - +scope.row.val2).toFixed(2)
      } else {
        scope.row.acMin = ''
      }
      for (let i = 0; i < this.inspectData.length; i++) {
        for (let j = 0; j < this.inspectData[i].inspectDatatwo.length; j++) {
          this.inspectData[i].inspectDatatwo[j].acMax = this.inspectData[i].acMax
          this.inspectData[i].inspectDatatwo[j].acMin = this.inspectData[i].acMin
        }
      }
    },
    stdValueIdchange(scope) {
      if (scope.row.stdValueId) {
        if (scope.row.stdValue && scope.row.val1) {
          if (scope.row.stdValueId == '±') {
            scope.row.acMin = (+scope.row.stdValue - +scope.row.val1).toFixed(2)
            scope.row.acMax = (+scope.row.stdValue + +scope.row.val1).toFixed(2)
            scope.row.val2 = ''
          } else if (scope.row.stdValueId == '=') {

            scope.row.val1 = parseFloat(scope.row.stdValue).toFixed(2)
            scope.row.acMin = parseFloat(scope.row.stdValue).toFixed(2)
            scope.row.acMax = parseFloat(scope.row.stdValue).toFixed(2)
            scope.row.val2 = ''
          } else if (scope.row.stdValueId == '+-') {
            if (scope.row.val1 && scope.row.stdValue) {
              scope.row.acMax = (+scope.row.stdValue + +scope.row.val1).toFixed(2)
            } else {
              scope.row.acMax = ''
            }
            if (scope.row.val2 && scope.row.stdValue) {
              scope.row.acMin = (+scope.row.stdValue - +scope.row.val2).toFixed(2)
            } else {
              scope.row.acMin = ''
            }
          }
        } else {
          scope.row.acMin = ''
          scope.row.acMax = ''
        }
        if (scope.row.stdValue) {
          if (scope.row.stdValueId == '>=') {

            scope.row.acMin = parseFloat(scope.row.stdValue).toFixed(2)
            scope.row.acMax = ''
            scope.row.val2 = ''
          } else if (scope.row.stdValueId == '<=') {
            scope.row.acMin = ''
            scope.row.acMax = parseFloat(scope.row.stdValue).toFixed(2)
            scope.row.val2 = ''
          }
        }
        for (let i = 0; i < this.inspectData.length; i++) {
          for (let j = 0; j < this.inspectData[i].inspectDatatwo.length; j++) {
            this.inspectData[i].inspectDatatwo[j].acMax = this.inspectData[i].acMax
            this.inspectData[i].inspectDatatwo[j].acMin = this.inspectData[i].acMin
          }
        }
      } else {
        scope.row.acMin = ''
        scope.row.acMax = ''
        scope.row.val1 = ''
        scope.row.val2 = ''
      }
    },
    inputNameInputtwo(val) {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        this.queryKeywordtwo = val;
        this.goods_currentPage = 1;

        this.pickerSearchtwo();
      }, 500);
    },
    qcToolinput(scope) {
      scope.row.qcToolName = this.toolLIst.filter(i => i.qcTool === scope.row.qcTool)[0].qcToolName
    },
    mfBxHanddleqcItm({ qcItm, name, qcMth, rem }) {
      let newRow = {
        qcItm,
        name,
        qcMth,
        rem,
      };
      newRow.show = false;
      // this.inspectData[this.qcItmflag].inspectDatatwo[this.pickerIndex].spcNo = spcNo
      this.inspectData[this.pickerIndextwo].qcItm = newRow.qcItm
      this.inspectData[this.pickerIndextwo].name = newRow.name
      this.inspectData[this.pickerIndextwo].qcName = newRow.name
      this.inspectData.forEach((item, i) => {
        item.inspectDatatwo.forEach((item2) => {
          item2.qcItm = item.qcItm
        })
      })
      //  this.tableData[this.pickerIndex].qcItm = newRow.qcItm
      //  this.tableData[this.pickerIndex].name = newRow.name
      // this.$set(this.tableData, this.pickerIndex, this.tableData[this.pickerIndex])

      // this.$set(this.tableData, this.pickerIndex, newRow);
      this.isShowPopVel = false;
      this.visibleqcItm = false;

      for (let i = 0; i < this.inspectData.length; i++) {//for循环遍历每个元素，每遍历一个元素就是arr[i]
        let num = 0//定义一个num统计arr[i]出现的次数，
        for (let j = 0; j < this.inspectData.length; j++) { //根据这个arr[i]做一次循环遍历这个数组，
          if (this.inspectData[i].qcItm) {
            if (this.inspectData[i].qcItm == this.inspectData[j].qcItm) {//arr[i]出现一次就会+1
              num++
            }
          }
        }
        if (num > 1) {
          this.inspectData[this.pickerIndextwo].qcItm = ''
          this.inspectData[this.pickerIndextwo].qcName = ''
          this.$message.warning('检验项目不能重复')
          this.$forceUpdate()
        }
      }
    },
    pickerSearchtwo() {
      this.loading = true
      basicDataqcItm({
        qcItm: this.queryKeywordtwo,
        name: this.queryKeywordtwo
      }).then((res) => {
        this.loading = false
        if (res.code === 0) {
          this.pickerListtwo = res.data

        }
      }).catch(err => {
        this.loading = false
        this.requestFailed(err)
      })
      //    .finally(() => {
      //         this.loading = false
      // })
    },
    // input聚焦
    focustwo(index, row, e) {
      this.qcItm = row.qcItm
      this.queryKeywordtwo = ''
      this.pickerSearchtwo();
      this.visibleqcItm = true;
      // this.isSaveColor = "danger";
      this.pickerIndextwo = index;
      let getTop = e.target.getBoundingClientRect().top + 35;
      if (e.target.getBoundingClientRect().top - 380 < 0) {
        this.objStyle.top = getTop.toString() + "px";
      } else {
        this.objStyle.top = (e.target.getBoundingClientRect().top - 390).toString() + "px";
      }
      this.objStyle.left = e.target.getBoundingClientRect().left + "px";
    },
    locaDelTableRow() {

      let that = this
      if (this.multipleSelection.length === 0 || this.multipleSelection === []) {
        this.$message.error("请勾选要操作的数据！");
        return false;
      }
      this.$confirm(this.$t('public.del.content'), this.$t('public.del.title'), {
        confirmButtonText: this.$t('public.sure'),
        cancelButtonText: this.$t('public.cancel'),
        type: 'warning'
      }).then(async () => {
        let selectRows = that.multipleSelection
        // selectRows.forEach((item) => {
        //   if (item.id === null || item.id === "" || item.id === 0) {
        //   } else {
        //     that.detailIds.push(
        //       Object.assign(
        //         {},
        //         {
        //           id: item.id,
        //           prdNo: item.prdNo,
        //           itm: item.itm,
        //           lastQty: item.lastQty,
        //           prdtId: item.prdtId,
        //         }
        //       )
        //     );
        //   }
        // });
        //  this.zcNodetailDatatwo[this.zcNoIndex].tableData.push(obj)
        selectRows.forEach((item) => {
          // that.tableData.splice(that.tableData.indexOf(item), 1);
          that.inspectData.splice(that.inspectData.indexOf(item), 1)
        });
      }).catch(() => {
        that.$message.info("已取消删除");
      });
    },
    addTableRow() {
      this.tableAdd2()
    },
    tableAdd2() {
      for (let i = 0; i < 1; i++) {
        let obj = {
          qcItm: '',
          qty: 3,
          qcSpc: '',
          qcToolName: '',
          qcType: '',
          qtyLost: '',
          id: null,
          addId: this.inspectData.length,
          inspectDatatwo: [],
          inspectDatathree: [],
          inspectall: [],

          name: '',
          qcName: '',
          stdValue: '',
          stdValueId: '',
          acMax: '',
          acMin: '',
          qcTool: '',
          val1: "",
          val2: "",
        };
        this.inspectData.push(obj)

        // if(item.qty){
        //     for (let i = 0; i < item.qty; i++) {
        //       let obj = {
        //       qcItm:item.qcItm,
        //       acMax:item.acMax,
        //       acMin:item.acMin,
        //       itm:i+1,
        //       qcRec:item.qcRec,
        //       chkId:item.chkId,
        //       spcNo:item.spcNo,
        //       spcName:item.spcName,
        //       rem:item.rem,
        //       qcType:item.qcType,
        //       // id: null,
        //       };
        //       item.inspectDatatwo.push(obj)
        //     }
        //  }
      }
      this.inspectData.forEach((item, i) => {
        debugger
        if (item.id) {

        } else {
          // item.addId = i
          item.chkId = 'T'
          if (item.qcType == '1') {
            item.chkId = ''
          }
          if (item.qty) {
            for (let i = 0; i < item.qty; i++) {
              let obj = {
                qcItm: item.qcItm,
                acMax: '',
                acMin: '',
                itm: i + 1,
                qcRec: '',
                chkId: '',
                spcNo: '',
                spcName: '',
                rem: '',
                qcType: item.qcType,

                qcName: item.qcName,
                stdValue: null,
                stdValueId: null,
                tyNo: "",
                val1: null,
                val2: null,
              };
              item.inspectDatatwo.push(obj)
            }
          }
        }
      })

    },
    jumpnonconform() {
      // this.$router.push({path: '/mes/nonconform/detail',
      //     params:{
      //       lsNo: this.billData.formData.lsNo
      //     }
      // })
      this.$router.push({
        name: 'nonconformdetail', params: {
          lsNo: this.billData.formData.lsNo
        }
      })
    },
    jumpentrust() {
      // this.$router.push({path: '/mes/entrustworker/check',
      //     params:{
      //       tiNo: this.billData.formData.tiNo
      //     }
      // })
      localStorage.setItem("tuogongtyNo", this.billData.formData.tiNo);
      this.$router.push({
        name: 'entrustworkercheck', params: {
          tiNo: this.billData.formData.tiNo
        }
      })
    },
    stopScroll(evt) {
      evt = evt || window.event;
      if (evt.preventDefault) {
        // Firefox
        evt.preventDefault();
        evt.stopPropagation();
      } else {
        // IE
        evt.cancelBubble = true;
        evt.returnValue = false;
      }
      return false;
    },
    changeqty(val) {
      if (val == 0) {
        this.billData.formData.qtyOk = +this.billData.formData.qty
      }
      if (val > 0) {
        if (+this.billData.formData.qty - +this.billData.formData.qtyLost < 0) {
          this.$message.info('不合格量超出')
          val = ''
          this.billData.formData.qtyLost = 0
          this.billData.formData.qcId = 'T'
        } else {
          this.billData.formData.qtyOk = +this.billData.formData.qty - +this.billData.formData.qtyLost
          this.billData.formData.qcId = 'F'
        }
      } else {
        this.billData.formData.qcId = 'T'
      }
      if (val < 0) {
        val = ''
        this.billData.formData.qtyLost = 0
        this.$message.info('不合格量不能为负数')
      }
      this.$forceUpdate()
    },
    qcRecchange(val) {

      if (+val.row.qcRec < 0) {
        this.$message.warning('检验值必须大于0')
        val.row.qcRec = ''
        return
      }
      if (val.row.acMax === 0 && val.row.acMin === 0) {
        if (+val.row.qcRec === 0) {

          val.row.chkId = 'T'
        } else {
          val.row.chkId = 'F'
        }
      }
      if (val.row.qcRec === '' || val.row.qcRec === null) {
        val.row.chkId = 'T'
        this.$set(this.inspectData[this.qcItmflag].inspectDatatwo, val.$index, val.row)
      } else {
        if (val.row.acMax && val.row.acMin) {
          if (+val.row.qcRec <= val.row.acMax && +val.row.qcRec >= val.row.acMin) {
            val.row.chkId = 'T'
          } else {
            val.row.chkId = 'F'
          }
        } else if (!val.row.acMax && val.row.acMin) {
          if (+val.row.qcRec >= val.row.acMin) {
            val.row.chkId = 'T'
          } else {
            val.row.chkId = 'F'
          }
        } else if (val.row.acMax && !val.row.acMin) {
          if (+val.row.qcRec <= val.row.acMax) {
            val.row.chkId = 'T'
          } else {
            val.row.chkId = 'F'
          }
        }
      }
      this.noqualified(val)
    },
    //  @input="changeqtyLost(scope)"
    noqualified(val) {
      let that = this

      this.chkIdqty = 0
      // this.testData.forEach(i=>{
      //   if(i.spcNo){
      //     this.billData.formData.qtyLost += +i.qtyLost;
      //   }
      // })
      this.inspectData[this.qcItmflag].inspectDatatwo.forEach(item => {
        if (item.chkId == 'F') {
          this.chkIdqty = this.chkIdqty + 1
        } else {
          val.row.spcNo = ''
          val.row.spcName = ''
        }
      })

      //  this.billData.formData.qtyLost =  this.chkIdqty
      this.inspectData.forEach(item => {
        if (item.qtyLost == '' || item.qtyLost == null) {
          item.qtyLost = 0
        }
        if (item.qcItm == val.row.qcItm) {
          item.qtyLost = that.chkIdqty
        }
      })
      let inspectval = this.inspectData
      this.billData.formData.qtyLost = Math.max.apply(null, inspectval.map((item) => item.qtyLost));
      this.billData.formData.qtyOk = +this.billData.formData.qty - +this.billData.formData.qtyLost
      if (this.billData.formData.qtyLost > 0) {
        this.billData.formData.qcId = 'F'
      } else {
        this.billData.formData.qcId = 'T'
      }
      this.$forceUpdate()
    },
    inspectDatamethod() {
      inspectData
    },
    judgmentAdd(row) {
      for (let i = 0; i < 4; i++) {
        let obj = {
          qcItm: row.qcItm,
          acMax: row.acMax,
          acMin: row.acMin,
          itm: '',
          qcRec: '',
          chkId: 'T',
          spcNo: '',
          spcName: '',
          rem: '',
          // id: null,
        };
        this.inspectDatatwo.push(obj);
      }

      this.inspectData.forEach(item => {
        if (item.qcItm == row.qcItm) {
          if (item.inspectDatatwo.length > 0) {
          } else {
            item.inspectDatatwo = this.inspectDatatwo
          }
        }
      })
      this.$forceUpdate()


    },
    // 表格双击事件
    mfBxHanddle(row, column, event) {
      this.getIndex = row.index
      this.qcItmflag = row.addId
      if (row.qcItm) {
      } else {
        this.$message.warning('请先输入检验项目')
      }
    },
    tableRowClassName({ row, rowIndex }) {
      row.index = rowIndex;
    },
    selectedstyle({ row, rowIndex }) {

      if (this.getIndex == rowIndex) {
        return {
          "background-color": "#cde1dc",
        };
      }
    },
    cosetiao() {
      if (this.billData.formData.tyNo) {
        this.$router.push({
          name: 'entrustinspecdata',
          params: {
            tab: 'second'
          }
        })
      } else {
        this.$router.push({
          name: 'entrustinspecdata',
          params: {
            tab: 'first'
          }
        })
      }
    },
    handleDelVersion(row) {
      var list = []
      //  { id, fileName,bucketName }
      if (row.distinguish) {
        this.tableData = this.tableData.filter(i => i.id !== row.id)
      } else {
        sysfiledel(
          {
            id: row.id,
            // fileName:row.fileName,
            // bucketName:row.bucketName,
            // idList:list
          }
        )
          .then(res => {
            if (res.msg == 'success') {
              this.tableData = this.tableData.filter(i => i.id !== row.id)
              this.$message.success('删除成功')
            } else {
              this.$message.success('删除失败')
            }
            this.loading = false
            // this.getList()
          })
          .catch(err => {
            this.loading = false
            this.requestFailed(err)
          })

      }
      //  this.tableData.forEach((item, index) => {
      //     if(item.id === id){
      //       item.contraFileUrlList.forEach((item2, index2) => {
      //       list.push(item2.id)
      //       })
      //     }
      //  })
    },
    handleAddVersion() {
      this.$refs.uploadFile.create({ title: '上传' }, 'oooooooooo')
      // this.$refs.AddVersion.create(true, true)
    },
    deldata() {
      const that = this
      this.$confirm(this.$t('public.del.content'), this.$t('public.del.title'), {
        confirmButtonText: this.$t('public.sure'),
        cancelButtonText: this.$t('public.cancel'),
        type: 'warning'
      }).then(async () => {
        that.loading = true
        gctgspctyDel({
          tyNo: that.billData.formData.tyNo,
          usr: that.billData.formData.usr
        }).then((res) => {
          if (res.code === 0) {
            if (res.msg === 'success') {
              that.$message.success(res.data)
              this.$bus.$emit('closeTab',this.$route.path)
            } else {
              that.$message.error(res.data)
            }
          }
        }).catch(err => that.requestFailed(err))
          .finally(() => {
            that.loading = false
          })
      }).catch(() => {
        this.loading = false
      });
    },
    // 渲染组件
    choose(obj) {

      var map = {}
      // if (obj.obj.name === 'upSalNo') {
      //   this.salName = obj.obj.data.name
      //   map[obj.obj.name] = obj.obj.data.salNo
      // }
      // if (obj.obj.name === 'salNo') {
      //   this.salName = obj.obj.data.name
      //   map[obj.obj.name] = obj.obj.data.salNo
      // }
      if (obj.obj.name === 'dep') {
        this.flag = true
        this.billData.formData.depName = obj.obj.data.name
        this.billData.formData.dep = obj.obj.data.deptCode
        this.data = this.billData.formData.dep + '-' + this.billData.formData.depName
      }
      // this.form.setFieldsValue(map)
    },
    changeqtyLost(val) {
      this.billData.formData.qtyLost = 0
      let getQtys = 0;
      if (!this.billData.formData.qtyLost) {
        this.billData.formData.qtyLost = 0
      }
      this.testData.forEach(i => {
        if (i.spcNo) {
          this.billData.formData.qtyLost += +i.qtyLost;
        }
      })
      if (this.billData.formData.qtyLost > 0) {
        this.billData.formData.qcId = 'F'
        if (+this.billData.formData.qty - +this.billData.formData.qtyLost < 0) {

          this.$message.info('不合格量超出')
          val.row.qtyLost = ''
          this.billData.formData.qtyLost = 0
          this.testData.forEach(i => {
            if (i.spcNo) {
              this.billData.formData.qtyLost += +i.qtyLost;
            }
          })
        } else {
          this.billData.formData.qtyOk = +this.billData.formData.qty - +this.billData.formData.qtyLost
        }
      } else {
        this.billData.formData.qcId = 'T'
      }
      //  if(this.billData.formData.qtyOk < 0){
      //   this.billData.formData.qtyOk = 0
      //  }
      // this.billData.formData.qtyLost += +val;
      this.$forceUpdate()
      // this.testData.forEach((item) => {
      //   if (item.qty) {
      //     getQtys += +item.qty;

    },
    // 货品关键字搜索回显数据
    getGoodsPopover({
                      // qcItm,
                      // itm,
                      // qcRec,
                      // chkId,
                      // rem,
                      spcNo,
                      name,
                    }) {

      let newRow = {
        // qcItm,
        // itm,
        // qcRec,
        // chkId,
        // rem,
        spcNo,
        spcName: name,
      };
      newRow.show = false;

      this.inspectData[this.qcItmflag].inspectDatatwo[this.pickerIndex].spcNo = spcNo
      this.inspectData[this.qcItmflag].inspectDatatwo[this.pickerIndex].spcName = name
      //  this.inspectData[this.qcItmflag].inspectDatatwo
      // this.$set(this.inspectData[this.qcItmflag].inspectDatatwo, this.pickerIndex, newRow);
      this.isShowPopVel = false;
      // let getQtys = 0;
      // this.testData.forEach((item) => {
      //   if (item.qty) {
      //     getQtys += +item.qty;
      //     this.qtys = getQtys;
      //   }
      // });
      this.visible = false;

      // for (let i = 0; i < this.inspectData[this.qcItmflag].inspectDatatwo.length; i++) {//for循环遍历每个元素，每遍历一个元素就是arr[i]
      //   let num = 0//定义一个num统计arr[i]出现的次数，
      //   for (let j = 0; j < this.inspectData[this.qcItmflag].inspectDatatwo.length; j++) { //根据这个arr[i]做一次循环遍历这个数组，
      //     if(this.inspectData[this.qcItmflag].inspectDatatwo[i].spcNo){
      //       if(this.inspectData[this.qcItmflag].inspectDatatwo[i].spcNo==this.inspectData[this.qcItmflag].inspectDatatwo[j].spcNo){//arr[i]出现一次就会+1
      //         num++
      //       }
      //     }
      //   }
      //   if(num>1){
      //     this.inspectData[this.qcItmflag].inspectDatatwo[this.pickerIndex].spcNo = ''
      //     this.inspectData[this.qcItmflag].inspectDatatwo[this.pickerIndex].spcName = ''
      //     this.$message.warning('原因代号不能重复')
      //     this.$forceUpdate()
      //   }
      // }
    },
    prdt3leave() {
      setTimeout(() => {
        this.visible = false;
      }, 100);
    },
    pickerSearch() {
      // {
      //     ...this.billData.formData,
      //     sysFiles:this.sysFiles
      //   }
      spcLstpage({
        spcNo: this.queryKeyword,
        name: this.queryKeyword,
        current: this.tablePagespcNo.currentPage,
        size: this.tablePagespcNo.pageSize,

      }).then((res) => {
        if (res.code === 0) {
          this.pickerList = res.data.records
          this.spcNototal = res.data.total
        }
      }).catch(err => this.requestFailed(err))
      //    .finally(() => {
      //         this.loading = false
      // })
    },
    // input聚焦
    focus(index, row, e) {
      this.pickerSearch();
      this.visible = true;
      // this.isSaveColor = "danger";
      this.pickerIndex = index;
      let getTop = e.target.getBoundingClientRect().top + 35;

      if (e.target.getBoundingClientRect().top - 380 < 0) {
        this.objStyle.top = getTop.toString() + "px";
      } else {
        this.objStyle.top = (e.target.getBoundingClientRect().top - 390).toString() + "px";
      }
      this.objStyle.left = e.target.getBoundingClientRect().left + "px";



      // let getTop = e.target.getBoundingClientRect().top + 35;
      // this.objStyle.left = e.target.getBoundingClientRect().left + "px";
      // this.objStyle.top = getTop.toString() + "px";


    },
    blurNameInput() {
      // this.visible = false;
      setTimeout(() => {
        this.isShowPopVel = false;
      }, 100);
    },
    inputNameInput(val) {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        this.queryKeyword = val;
        this.goods_currentPage = 1;
        this.pickerSearch();
      }, 500);
    },
    handleSelectionChangereason(rows) {
      this.multipleSelection = rows;
    },
    handleSelectionChange(rows) {
      this.multipleSelection = rows;
    },
    //  // 添加行
    // addTableRow() {
    //   this.tableAdd();
    // },
    // // 本地删除
    // locaDelTableRow() {
    //   let that = this

    //   if (this.multipleSelection.length === 0 || this.multipleSelection === []) {
    //     this.$message.error("请勾选要操作的数据！");
    //     return false;
    //   }
    //   this.$confirm({
    //     title: this.$t('public.del.title'),
    //     content: this.$t('public.del.content'),
    //     okText: this.$t('public.sure'),
    //     okType: 'danger',
    //     cancelText: this.$t('public.cancel'),
    //     onOk () {


    //       let selectRows = that.multipleSelection
    //       selectRows.forEach((item) => {
    //         if (item.id === null || item.id === "" || item.id === 0) {
    //         } else {
    //           that.detailIds.push(
    //             Object.assign(
    //               {},
    //               {
    //                 id: item.id,
    //                 prdNo: item.prdNo,
    //                 itm: item.itm,
    //                 lastQty: item.lastQty,
    //                 prdtId: item.prdtId,
    //               }
    //             )
    //           );
    //         }
    //       });
    //       selectRows.forEach((item) => {
    //         that.testData.splice(that.testData.indexOf(item), 1);
    //       });
    //     },
    //     onCancel () {

    //       // that.loading = false
    //        that.$refs.multipleTable.clearSelection();
    //       that.$message.info("已取消删除");
    //     }
    //   })

    //   // this.$confirm("此操作将删除, 是否继续?", "提示", {
    //   //   confirmButtonText: "确定",
    //   //   cancelButtonText: "取消",
    //   //   type: "warning",
    //   // })
    //   // .then(() => {
    //   // })
    //   // .catch(() => {
    //   //   this.$refs.multipleTable.clearSelection();
    //   //   this.$message.info("已取消删除");
    //   // });
    // },
    // 表格初始化，往数组里面添加50个对象
    tableAdd() {
      for (let i = 0; i < 3; i++) {
        let obj = {
          spcNo: "",
          spcName: "",
          qtyLost: "",
          name: "", // 品名
          id: null,
        };
        this.testData.push(obj);
      }
    },
    //行点击
    handRowClick(row, column, event) {
      var index;
      this.testData.map((c, i) => {
        if (c == row) {
          index = i;
        }
      });
      this.$set(this.testData, index, row);
      event.stopPropagation();
    },
    // this.billData.formData.chktyId: this.billData.formData.chktyId ? 'T' : 'F'
    onChangeinspect(e) {

      // this.checked=e
      //  this.billData.formData.qcId = e.target.checked
    },
    onChange(checked) {
      this.checked = checked;

    },
    onChangeclosecase(e) {


      this.billData.formData.chkKnd = e.target.checked
      this.$forceUpdate()
    },
    onChangeLaboratory(e) {
      this.billData.formData.sys = e.target.checked
    },

    async save() {
      this.$refs.anRef.sendData()

      let validflag
      const vxeFo = this.$refs.dfo.$refs.vxeForm
      await vxeFo.validate(valid => {
        validflag = ((valid === undefined) ? true : false);
        if (valid) {
          this.$message.warning('请选择部门')
          return
        }
      })
      if (validflag) {
        this.billData.formData = this.billData.formData
        this.billData.formData.mesQcTy1s = []
        this.billData.formData.mesQcTy3s = []
        this.inspectData = this.inspectData.filter(i => i.qcItm)
        if (this.inspectData.length) { } else {
          this.$message.warning('无检验数据，不能保存')
          return
        }
        // 检查不合格原因数据问题
        let errordata = false
        for (let i = 0; i < this.inspectData.length; i++) {

          for (let j = 0; j < this.inspectData[i].inspectDatatwo.length; j++) {
            this.inspectData[i].inspectDatatwo[j].qcTool = this.inspectData[i].qcTool
            this.inspectData[i].inspectDatatwo[j].qcToolName = this.inspectData[i].qcToolName
            this.inspectData[i].inspectDatatwo[j].qty = this.inspectData[i].qty
            this.inspectData[i].inspectDatatwo[j].qcSpc = this.inspectData[i].qcSpc
            // this.inspectData[i].inspectDatatwo[j].qcType = this.inspectData[i].qcType
            this.inspectData[i].inspectDatatwo[j].qtyLost = this.inspectData[i].qtyLost

            this.inspectData[i].inspectDatatwo[j].qcName = this.inspectData[i].qcName
            this.inspectData[i].inspectDatatwo[j].stdValue = this.inspectData[i].stdValue
            this.inspectData[i].inspectDatatwo[j].stdValueId = this.inspectData[i].stdValueId
            this.inspectData[i].inspectDatatwo[j].val1 = this.inspectData[i].val1
            this.inspectData[i].inspectDatatwo[j].val2 = this.inspectData[i].val2
            this.inspectData[i].inspectDatatwo[j].acMax = this.inspectData[i].acMax
            this.inspectData[i].inspectDatatwo[j].acMin = this.inspectData[i].acMin

            this.inspectData[i].inspectDatathree = []
            this.inspectData[i].inspectall = []
            if (this.inspectData[i].inspectDatatwo[j].chkId == 'F') {
              if (this.inspectData[i].inspectDatatwo[j].spcNo == null || this.inspectData[i].inspectDatatwo[j].spcNo == '') {
                errordata = true
                break
              }
            }
          }
        }
        if (errordata) {
          this.$message.warning('请选择不合格原因')
          return
        }
        let qcRectrue = false
        for (let i = 0; i < this.inspectData.length; i++) {
          for (let j = 0; j < this.inspectData[i].inspectDatatwo.length; j++) {
            if (this.inspectData[i].qcType == '1') {
            } else {
              if (!this.inspectData[i].inspectDatatwo[j].qcRec && this.inspectData[i].inspectDatatwo[j].chkId == 'F') {
                qcRectrue = true
                break
              }
            }
          }
        }
        if (qcRectrue) {
          this.$message.warning('检验类型测量、合格判定为不合格，检验值必填')
          return
        }
        // 检查不合格原因数据问题结束
        // this.inspectData[0].inspectDatatwo = this.inspectData[0].inspectDatatwo.filter((item) => {
        //       return item.qcRec !== ''&&item.qcRec !== null
        // })
        this.inspectDataflag = true
        for (let i = 0; i < this.inspectData.length; i++) {
          for (let j = 0; j < this.inspectData[i].inspectDatatwo.length; j++) {
            if (this.billData.formData.mesQcTy2s[i].qcType == '1') {
              if (this.inspectData[i].inspectDatatwo[j].chkId == 'F') {
                this.inspectData[i].inspectDatathree.push(this.inspectData[i].inspectDatatwo[j])
              }
            } else {
              if (this.inspectData[i].inspectDatatwo[j].qcRec !== null && this.inspectData[i].inspectDatatwo[j].qcRec !== '' && this.inspectData[i].inspectDatatwo[j].chkId == 'F') {
                this.inspectData[i].inspectDatathree.push(this.inspectData[i].inspectDatatwo[j])
              }
            }
            // if(this.billData.formData.mesQcTy2s[i].qcType == '1'){
            //   if(this.inspectData[i].inspectDatatwo[j].chkId!== null && this.inspectData[i].inspectDatatwo[j].chkId!== ''){
            //     this.billData.formData.mesQcTy3s.push(this.inspectData[i].inspectDatatwo[j])
            //   }
            // }else{
            //   if(this.inspectData[i].inspectDatatwo[j].qcRec!== null && this.inspectData[i].inspectDatatwo[j].qcRec!== ''){
            //     this.billData.formData.mesQcTy3s.push(this.inspectData[i].inspectDatatwo[j])
            //   }
            // }
            this.inspectData[i].inspectDatatwo[j].reorder = i + 1
            this.billData.formData.mesQcTy3s.push(this.inspectData[i].inspectDatatwo[j])

            if (this.inspectData[i].inspectDatatwo[j].qcRec !== null && this.inspectData[i].inspectDatatwo[j].qcRec !== '') {
              this.inspectData[i].inspectall.push(this.inspectData[i].inspectDatatwo[j])
            }
            // this.inspectData[i].inspectDatatwo = this.inspectData[i].inspectDatatwo.map(item=> {
            //   if(item.qcRec !== ''&&item.qcRec !== null ){
            //   }
            // })
            // this.inspectData[i].inspectDatatwo = this.inspectData[i].inspectDatatwo.filter((item) => {
            //   return item.qcRec !== ''&&item.qcRec !== null
            // })
          }
        }

        // let arr = [1,2,2,3,4,4,4,6]
        // 去重并且记录出现次数 拿到qtyLost值
        for (let i = 0; i < this.inspectData.length; i++) {
          // let arr = copare(this.inspectData[i].inspectDatathree)
          let obj = {};
          this.inspectData[i].inspectDatathree = copare(this.inspectData[i].inspectDatathree).reduce((item, next) => {

            obj[next.spcNo] ? "" : (obj[next.spcNo] = true && item.push(next));
            return item;
          }, []);

        }
        // let arr = this.inspectData[this.qcItmflag].inspectDatathree
        // copare(arr)
        function copare(array) {
          let a = [] //定义一个空数组 a= []
          for (let i = 0; i < array.length; i++) {//for循环遍历每个元素，每遍历一个元素就是arr[i]
            let num = 0//定义一个num统计arr[i]出现的次数，
            for (let j = 0; j < array.length; j++) { //根据这个arr[i]做一次循环遍历这个数组，
              if (array[i].spcNo == array[j].spcNo) {//arr[i]出现一次就会+1
                num++
              }
            }
            if (num > 1) {
              array[i].qtyLost = num
            }
            if (num <= 1) {//如果只出现一次，那么就说明没有重复的，我们就把这个元素push到a数组中，如果出现多次，那么什么都不做，最后得到的a就是我们要的结果。
              array[i].qtyLost = num
              // a.push(array[i])
            }
          }
          return array
        }

        // 记录出现次数 拿到qtyLost值
        //  let obj = {};
        //   let arrays = arr.reduce((item, next) => {

        //     obj[next.spcNo] ? "" : (obj[next.spcNo] = true && item.push(next));
        //     return item;
        //   }, []);
        // this.inspectData[this.qcItmflag].inspectDatathree = arrays

        //  let arrays = getmaxArr.reduce((item, next) => {


        //     obj[next.mlNo] ? "" : (obj[next.mlNo] = true && item.push(next));
        //     return item;
        //   }, []);
        //   this.billData.formData = response.data
        // this.inspectData = this.billData.formData.mesQcTy2s
        this.billData.formData.mesQcTy2s = this.inspectData
        for (let i = 0; i < this.billData.formData.mesQcTy2s.length; i++) {
          if (this.billData.formData.mesQcTy2s[i].qcType == '1') {
            if (this.billData.formData.mesQcTy2s[i].inspectall.length > 0) {
              for (let j = 0; j < this.billData.formData.mesQcTy2s[i].inspectDatathree.length; j++) {
                if (this.billData.formData.mesQcTy2s[i].inspectDatathree.length > 0) {
                  if (this.billData.formData.mesQcTy2s[i].inspectDatathree[j].chkId) {
                    this.billData.formData.mesQcTy1s.push(this.billData.formData.mesQcTy2s[i].inspectDatathree[j])
                  }
                }
              }
            }
          } else {
            if (this.billData.formData.mesQcTy2s[i].inspectall.length > 0) {
              for (let j = 0; j < this.billData.formData.mesQcTy2s[i].inspectDatathree.length; j++) {
                if (this.billData.formData.mesQcTy2s[i].inspectDatathree.length > 0) {
                  if (this.billData.formData.mesQcTy2s[i].inspectDatathree[j].qcRec) {
                    this.billData.formData.mesQcTy1s.push(this.billData.formData.mesQcTy2s[i].inspectDatathree[j])
                  }
                }
              }
            } else {
              this.inspectDataflag = false
              break
            }
          }
        }


        let mesQcTy1s = []
        mesQcTy1s = this.testData.filter((item) => {
          return item.spcNo !== "" && item.spcNo !== null;
        })
        this.tableData.forEach(item => {
          if (item.distinguish) {
            delete item.distinguish
          }
        })
        // if(this.billData.formData.chktyId==true){
        //   this.billData.formData.chktyId = 'T'
        // }else{
        //   this.billData.formData.chktyId = 'F'
        // }
        this.billData.formData.closeId = 'F'
        delete this.billData.formData.unitName
        delete this.billData.formData.qcTypeName
        delete this.billData.formData.whName
        // if(this.inspectDataflag){
        //     this.billData.formData.chkKnd = this.billData.formData.chkKnd ? 'T' : 'F'
        //  this.billData.formData.sys = this.billData.formData.sys ? 'T' : 'F'
        if (this.billData.formData.tyNo) {
          gctgspcqcUpd({
            ...this.billData.formData,
            sys: this.billData.formData.sys ? 'T' : 'F',
            chkKnd: this.billData.formData.chkKnd ? 'T' : 'F',
            mesTiQcFileList: this.tableData,
            // mesQcTy1s:mesQcTy1s
          }).then((res) => {
            if (res.code === 0) {

              if (res.msg === 'success') {
                this.getList()
                this.$message.success(res.data)
              } else {
                this.$message.error(res.data)
                return
              }
              //         if(this.billData.formData.sys == 'T'){
              //           this.billData.formData.sys = true
              //         }else{
              //           this.billData.formData.sys = false
              //         }
              //         if(this.billData.formData.chkKnd == 'T'){
              //           this.billData.formData.chkKnd = true
              //         }else{
              //           this.billData.formData.chkKnd = false
              //         }
              // if(this.$route.query.id){
              //   this.getListtreat()
              // }else{
              //   this.getList()
              // }
            }
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              //       if(this.billData.formData.sys == 'T'){
              //           this.billData.formData.sys = true
              //         }else{
              //           this.billData.formData.sys = false
              //         }
              // if(this.billData.formData.chkKnd == 'T'){
              //           this.billData.formData.chkKnd = true
              //         }else{
              //           this.billData.formData.chkKnd = false
              //         }
              this.loading = false
            })

        } else {
          delete this.billData.formData.tiDd
          delete this.billData.formData.tyDd
          gctgspcqcAdd({
            ...this.billData.formData,
            sys: this.billData.formData.sys ? 'T' : 'F',
            chkKnd: this.billData.formData.chkKnd ? 'T' : 'F',
            mesTiQcFileList: this.tableData,
            // mesQcTy1s:mesQcTy1s,
          }).then((res) => {
            if (res.code == 0) {
              if (res.msg === 'success') {
                this.billData.formData.tyDd = res.data.tyDd
                this.billData.formData.tyNo = res.data.tyNo
                localStorage.setItem("tuogonglutyNo", res.data.tyNo)
                localStorage.removeItem('tuogonglutiNo')
                this.getList()
                this.$message.success('保存成功')
              } else {
                this.$message.error(res.data)
                return
              }
              if (res.data) {
                this.billData.formData = res.data
                if (this.billData.formData.sys == 'T') {
                  this.billData.formData.sys = true
                } else {
                  this.billData.formData.sys = false
                }
                if (this.billData.formData.chkKnd == 'T') {
                  this.billData.formData.chkKnd = true
                } else {
                  this.billData.formData.chkKnd = false
                }
              }
            }
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              //       if(this.billData.formData.sys == 'T'){
              //           this.billData.formData.sys = true
              //         }else{
              //           this.billData.formData.sys = false
              //         }
              // if(this.billData.formData.chkKnd == 'T'){
              //           this.billData.formData.chkKnd = true
              //         }else{
              //           this.billData.formData.chkKnd = false
              //         }
              this.loading = false
            })
        }
        // }else{
        //   this.$message.warning('每个检验项目至少填写一条数据')
        // }
      }
    },
    selectChangeEvent({ checked, records, reserves, row, rowIndex }) {

      this.multipleSelectiontwo = records
    },
    selectChangeAll({ records, checked }) {

      this.multipleSelectiontwo = records
    },

    editclick() {
      this.isEdit = true
    },
    closeclick() {
      this.isEdit = false
    },
    reset() {
      this.billData.formData = {}
      this.billData.formData = {}
    },
    // seepic(index) {
    //   let arry = []
    //   arry.push(this.tableData[index])
    //
    //   this.$refs.ModalPic.create({ title: '查看' }, arry)
    // },
    // uploadpic(row) {
    //   this.$refs.uploadFile.create({ title: '上传' }, row)
    //   // this.$refs.Upload.create(
    //   //   {
    //   //     title: '上传'
    //   //   },
    //   // )
    //   // this.$refs.Upload.create({ title: '上传' }, row)
    // },
    // check(){
    //   this.$router.push('')
    // },
    handleChangereson(val) {

      this.multiplereason = val
    },
    cancelGetData() {
      this.visible = false
    },
    otngetData() {

      if (this.multiplereason.length) {
        this.reasondata = this.multiplereason
      }
      this.visible = false
    },
    reasonclick(val) {
      this.visible = true
    },
    // handleSelectionChange(val) {
    //   this.multipleSelection = val
    // },
    handleSelectionChangetwo(val) {
      this.multipleSelectiontwo = val
    },
    handleTabClick(key) {
      if (key == '1') {
        this.getList()
      } else if (key == '2') {
        this.getListtwo()
      }
    },
    // onChange(data, dateString) {
    //
    //   this.queryParam.date = dateString
    //
    // },
    depme(row) {
      datadepPage(
        Object.assign({
          current: 1,
          size: 10,
          deptCode: row
        })
      ).then(response => {
        this.billData.formData.depName = response.data.records[0].name
        this.data = this.billData.formData.dep + '-' + this.billData.formData.depName

      }).catch(err => this.requestFailed(err))
    },
    getListtreat() {
      this.loading = true
      gctgspcdjInfo(
        Object.assign({
          tiNo: this.billData.formData.tiNo,
        })
      ).then(response => {
        this.loading = false
        this.billData.formData = response.data
        if (this.billData.formData.lsNo) {
          this.isClaim = true
        } else {
          this.isClaim = false
        }

        this.billData.formData.mesQcTy1s = []
        this.billData.formData.mesQcTy3s = []
        this.inspectData = response.data.mesQcTy2s
        this.inspectData.forEach((item, i) => {
          item.addId = i
          item.chkId = 'T'
          if (item.qcType == '1') {
            item.chkId = ''
          }
          item.inspectDatatwo = []
          item.inspectDatathree = []
          item.inspectall = []
          if (item.qty) {
            for (let i = 0; i < item.qty; i++) {
              let obj = {
                qcItm: item.qcItm,
                acMax: item.acMax,
                acMin: item.acMin,
                itm: i + 1,
                qcRec: item.qcRec,
                chkId: item.chkId,
                spcNo: item.spcNo,
                spcName: item.spcName,
                rem: item.rem,
                qcType: item.qcType,
                // id: null,
              };
              item.inspectDatatwo.push(obj)
            }
          }

        })
        this.billData.formData.qcId = ''
        if (this.billData.formData.qtyLost) {
          if (this.billData.formData.qtyLost > 0) {
            this.billData.formData.qcId = 'F'
          } else {
            this.billData.formData.qcId = 'T'
          }
        } else {
          this.billData.formData.qtyLost = 0
          this.billData.formData.qcId = 'T'
        }
        this.billData.formData.qtyOk = this.billData.formData.qty
        this.billData.formData.chkKnd = false

        // this.data = this.billData.formData.dep + '-' + this.billData.formData.depName
        // this.tableData = response.data.mesTiQcFileList
        this.$refs.anRef.getList(response.data.mesTiQcFileList,1);

        // if(this.billData.formData.chkKnd == 'T'){
        //   this.billData.formData.chkKnd = true
        // }else{
        //   this.billData.formData.chkKnd = false
        // }
        if (this.billData.formData.sys == 'T') {
          this.billData.formData.sys = true
        } else {
          this.billData.formData.sys = false
        }

        if (this.billData.formData.dep) {
          this.depme(this.billData.formData.dep)
        }
        // this.tablePage.total = response.data.total
        // this.tablePage.currentPage = response.data.current
        if (this.billData.formData.qcId == 'T') {
          this.billData.formData.qcIds = '合格'
        }else if (this.billData.formData.qcId == 'F') {
          this.billData.formData.qcIds = '不合格'
        }
        this.billData.formData.tyDd = moment(this.billData.formData.tyDd).format('YYYY-MM-DD')
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    getList() {
      this.loading = true
      gctgspcyjInfo(
        Object.assign({
          // total: this.tablePage.currentPage,
          // current: this.tablePage.currentPage,
          // size: this.tablePage.pageSize,
          // staDd:this.queryParam.staDd,
          // endDd:this.queryParam.endDd,
          tyNo: this.billData.formData.tyNo,
          // zcNo:this.queryParam.zcNo,
          // moNo:this.queryParam.moNo,
          // prdNo:this.queryParam.prdNo,
          // prdName:this.queryParam.prdName,
          // bilNo:this.queryParam.bilNo
        })
      ).then(response => {
        this.loading = false
        this.billData.formData = response.data
        if (this.billData.formData.lsNo) {
          this.isClaim = true
        } else {
          this.isClaim = false
        }
        if (this.billData.formData.qtyLost) {
        } else {
          this.billData.formData.qtyLost = 0
        }
        this.billData.formData.mesQcTy1s = []
        this.billData.formData.mesQcTy3s = []
        this.inspectData = response.data.mesQcTy2s

        this.inspectData.forEach((item, i) => {
          item.addId = i
          item.chkId = 'T'
          if (item.qcType == '1') {
            item.chkId = ''
          }
          item.inspectDatatwo = []
          item.inspectDatathree = []
          item.inspectall = []
          let qtylength


          if (item.mesQcTy2List) {
            if (item.mesQcTy2List.length > 0) {
              for (let i = 0; i < item.mesQcTy2List.length; i++) {
                item.mesQcTy2List[i].qcType = item.qcType
                item.inspectDatatwo.push(item.mesQcTy2List[i])
              }
            }

            if (item.mesQcTy2List.length < item.qty) {
              qtylength = item.qty - item.mesQcTy2List.length
              for (let i = 0; i < qtylength; i++) {
                let obj = {
                  qcItm: item.qcItm,
                  acMax: item.acMax,
                  acMin: item.acMin,
                  itm: i + 1 + item.mesQcTy2List.length,
                  qcRec: item.qcRec,
                  chkId: item.chkId,
                  spcNo: item.spcNo,
                  spcName: item.spcName,
                  rem: item.rem,
                  qcType: item.qcType,
                };
                item.inspectDatatwo.push(obj)
              }
            }
          }
          // if(item.qty){
          //   for (let i = 0; i < item.qty; i++) {
          //     let obj = {
          //     qcItm:item.qcItm,
          //     acMax:item.acMax,
          //     acMin:item.acMin,
          //     itm:i+1,
          //     qcRec:item.qcRec,
          //     chkId:item.chkId,
          //     spcNo:item.spcNo,
          //     spcName:item.spcName,
          //     rem:item.rem,
          //     };
          //     item.inspectDatatwo.push(obj)
          //   }
          // }
        })

        this.billData.formData.chkKnd = false
        // this.testData=response.data[0].mesQcTy1List
        // this.data = this.billData.formData.dep + '-' + this.billData.formData.depName
        this.$refs.anRef.getList(response.data.mesTiQcFileList,1);

        // this.tableData = response.data.mesTiQcFileList
        if (this.billData.formData.chkKnd == 'T') {
          this.billData.formData.chkKnd = true
        } else {
          this.billData.formData.chkKnd = false
        }
        if (this.billData.formData.sys == 'T') {
          this.billData.formData.sys = true
        } else {
          this.billData.formData.sys = false
        }
        if (this.billData.formData.dep) {
          this.depme(this.billData.formData.dep)
        }

        if (this.billData.formData.qcId == 'T') {
          this.billData.formData.qcIds = '合格'
        }else if (this.billData.formData.qcId == 'F') {
          this.billData.formData.qcIds = '不合格'
        }
        this.billData.formData.tyDd = moment(this.billData.formData.tyDd).format('YYYY-MM-DD')
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
  }
}

</script lang='scss' scoped>

<style lang="less" scoped>
.title-container {
  display: flex;
  align-items: center;
  justify-content: left;
  width: 100%;
}

.title-text {
  color: #303133;
  font-weight: 600;
}

.title-actions {
  margin-left: 10px;
  display: flex;
  gap: 5px;
}
.el-collapse {
  ::v-deep .el-collapse-item__header.is-active {
    border-bottom: 1px solid darkgrey;
  }

  ::v-deep .el-collapse-item__header {
    border-bottom: 1px solid darkgrey;
    font-size: 17px;
    font-weight: 520;
    padding-left: 20px;
  }

  ::v-deep .el-collapse-item__content {
    padding: 5px 5px;
    //border-bottom: red 5px solid;
  }
}
::v-deep .grid-wrapper {
  height: 180px;
  overflow: auto;
}
.ant-input-wrapper .ant-input-group-addon .ant-btn {
  height: 28px;
}

.sup_info_item {
  padding: 10px 0;
}



.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 0px
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  -o-appearance: none !important;
  -ms-appearance: none !important;
  appearance: none !important;
  margin: 0;
}

input[type="number"] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  -o-appearance: textfield;
  -ms-appearance: textfield;
  appearance: textfield;
}

.sup_info_basics_container {
  border: 1px solid #E5E5E5;
  border-radius: 10px;
  padding: 0px 20px 0px 20px;
}

.sup_info_basics_header {
  font-size: 15px;
  color: #1F2A3F;
  height: 41px;
  font-weight: 500;
  line-height: 41px;
  overflow: hidden;
}

.el-table__fixed,
.el-table__fixed-right {
  height: 100% !important;
}

.ant-input {
  height: 28px;
}

/* .bordervisib .el-input__inner {
-webkit-appearance: none;
background-color: #fff;
background-image: none;
border-radius: 4px;
border: 0px;
width: 100%;
height:20px;
} */


.compoct .el-form-item--small .el-form-item__content,
.el-form-item--small .el-form-item__label {
  line-height: 24px;
}

/* .bordervisib .el-input__inner{
height:20px;
} */
.tableheight .el-table__cell {
  padding: 1px
}

/* .el-table__body tr.current-row>td.el-table__cell {
background-color: #98e4ff;
}
.el-table--striped .el-table__body tr.el-table__row--striped.current-row td.el-table__cell {
  background-color: #98e4ff;
}
.el-table--enable-row-transition .el-table__body td.el-table__cell{
transition:no;
}
.checkdata .el-table tbody tr:hover > td {
  background-color: #98e4ff!important;
} */
/* .checkdata .el-table__body tr.current-row>td{background-color: #98e4ff;}
el-table--striped .el-table__body tr.el-table__row--striped.current-row td.el-table__cell {
  background-color: #f00;
} */

/* 用来设置当前页面element全局table 选中某行时的背景色*/
.checkdata .el-table__body tr.current-row>td {
  background-color: #98e4ff !important;
  /* color: #f19944; */
  /* 设置文字颜色，可以选择不设置 */
}

/* 用来设置当前页面element全局table 鼠标移入某行时的背景色*/
.checkdata .el-table--enable-row-hover .el-table__body tr:hover>td {
  background-color: #98e4ff;
  /* color: #f19944; */
  /* 设置文字颜色，可以选择不设置 */
}

.checkdata th.el-table__cell>.cell {
  padding-left: 1px !important;
  padding-right: 1px !important;
}

.checkdata .cell {
  padding-left: 1px !important;
  padding-right: 1px !important;
}
</style>
