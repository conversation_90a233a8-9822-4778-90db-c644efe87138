<template>
  <div class="consrm handoverjiao" style="position:relative">
    <div style="position: absolute;top: 5px;left:195px;z-index:1">
      <a-button icon="delete" style="margin-left: 8px;background-color: #f56c6c;border-color: #f56c6c;color:#fff;"
                type="primary" @click="cleardelthree" size="small" v-if="activeNameMain == 'three'">删除</a-button>
      <a-button style="margin-left: 8px" type="primary" icon="search" size="small" @click="queryonetwo"
                v-if="activeNameMain == 'first' || activeNameMain == 'three'">{{ $t('public.query') }}</a-button>
      <a-button size="small" type="dashed" icon="reload" style="margin-left: 8px;color: #909399;
                  background: #f4f4f5;
                  border-color: #d3d4d6;" @click="reset">{{ $t('public.reset') }}</a-button>
      <a-button style="margin-left: 8px" type="primary" @click="save" size="small"
                v-if="activeNameMain == 'first'">派工</a-button>
      <a-button icon="printer" style="margin-left: 8px" type="primary" @click="pgprint" size="small"
                v-if="activeNameMain == 'three'">打印</a-button>



    </div>
    <el-dialog class="JustMake-dialog" :visible.sync="isPrint" :close-on-click-modal="false" v-loading="loading" width="400px" top="8vh">
      <div style="height: 65px;">
        <div id="print_icon" style="font-size:22px;color: red;">
          <span style="color: rgba(0, 0, 0, 0.85);margin-left:8px;">确定要打印吗？</span>
        </div>
        <div style="float: right;margin-top:10px;">
          <el-button type="primary" @click="printClose">取消</el-button>
          <el-button v-print="printObj2" type="primary" :disabled="isPrintButton" style="margin-left: 8px"
                     @click="printSure">确定
          </el-button>

        </div>
      </div>
    </el-dialog>
    <div v-show="false">
      <div id="form1" style="color:#000">
        <div v-for="(itematm, i) in curidList" id="gfvrgh" :key="i"
             style="width: 210mm;height:297mm;padding: 5mm 5mm 0 5mm;background-color: pink;">
          <!-- 148mm×210mm -->
          第{{i+1}}页
          <div style="text-align: center;margin-bottom:3px;font-size:20px;font-weight: bold;">南京优耐特精密机械制造有限公司</div>
          <div style="text-align: center;margin-bottom:5px;">(产品型号名称)MES二维码</div>
          <div style="display: table;width:100%;">
            <div
              style="display:inline-block; width: -moz-calc(94% - 4px);width: -webkit-calc(94% - 4px);width: calc(94% - 4px); display: table-cell;">
              <div style="text-align: center;">
                <span style="margin-right:15px;">派工日期:{{dataprint[0].pgDd}}</span><span
                style="margin-right:15px;">车间名称:{{dataprint[0].zcName}}</span><span>打印日期:{{todaydata}}</span>
              </div>
              <div style="text-align: center;">
                <span v-for="(item, j) in itematm.dataprint" :key="j" style="margin-right: -1px;margin-bottom: -1px;border: 1px solid black;
                box-sizing: border-box;display: inline-block;padding:25px 25px;">
                  <span style="display: inline-block;" :id="`yuxin${i}${j}`"></span>
                  <div>型号名称:{{item.prdName}}</div>
                  <!-- <div :id="`yuxin${itemlist[0].addid}${i}`"></div> -->
                </span>
              </div>

              <!-- 表格 -->
              <!-- <table
              id="table"
              border="1"
              align="center"
              style="width: 100%; border-collapse: collapse"
            >
              <thead id="table2">
                <tr>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px;"
                  >序号</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >生产单号</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >图号</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >规格</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >生产数量(K)</th>

                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >重量(KG)</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >件数</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >材质</th>

                  <th
                    align="center"
                    style="text-align: center; font-size: 12px;width:180px;"
                  >加工内容</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >下工序通知单号</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px"
                  >单重</th>
                  <th
                  align="center"
                  style="text-align: center; font-size: 12px;width:100px;"
                >去氢工艺</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 12px;width:100px;"
                  >备注</th>
                </tr>
              </thead>
              <tbody class="form1body">
                <tr
                  v-for="(item, index) in 20"
                  :key="item"
                  style="height:20px;"
                >
                  <td
                    v-if="dataprint[index + 20 * (i)]"
                    style="text-align: center; font-size: 12px;width:40px;"
                  >{{ index + 1 }}</td>
                  <td v-else />
                  <td
                    v-if="dataprint[index + 20 * (i)]"
                    style="text-align: left; font-size: 12px;width:90px;"
                  >{{ dataprint[index + 20 * (i)].boxNo }}</td>
                  <td v-else />
                  <td
                    v-if="dataprint[index + 20 * (i)]"
                    style="text-align: left; font-size: 12px;width:100px;"
                  >
                  <div v-if="dataprint[index + 20 * (i)].prdName">
                    {{ dataprint[index + 20 * (i)].prdName.slice(0,20) }}
                  </div>
                </td>
                  <td v-else />
                  <td
                    v-if="dataprint[index + 20 * (i)]"
                    style="text-align: left; font-size: 10px;width:150px;"
                  >
                  <div v-if="dataprint[index + 20 * (i)].spc"></div>
                  {{ dataprint[index + 20 * (i)].spc }}
                  </td>
                  </td>
                  <td v-else />
                  <td
                  v-if="dataprint[index + 20 * (i)]"
                  style="text-align: left; font-size: 12px;width:50px;"
                  >{{ dataprint[index + 20 * (i)].qty }}</td>
                  <td v-else />
                  <td
                  v-if="dataprint[index + 20 * (i)]"
                  style="text-align: left; font-size: 12px;width:50px;"
                  >{{ dataprint[index + 20 * (i)].qty1 }}</td>
                  <td v-else />
                  <td
                  v-if="dataprint[index + 20 * (i)]"
                  style="text-align: left; font-size: 12px;width:40px;"
                  >{{ dataprint[index + 20 * (i)].jsQty }}T</td>
                  <td v-else />
                  <td
                  v-if="dataprint[index + 20 * (i)]"
                  style="text-align: left; font-size: 10px;width:100px;"
                  >{{ dataprint[index + 20 * (i)].bomRem }}</td>
                  <td v-else />
                  <td
                  v-if="dataprint[index + 20 * (i)]"
                  style="text-align: left; font-size: 8px;width:180px;"
                  ><div style="width:120px;">{{ dataprint[index + 20 * (i)].remJg }}</div></td>
                  <td v-else />
                  <td
                  v-if="dataprint[index + 20 * (i)]"
                  style="text-align: left; font-size: 12px;width:180px;"
                  >{{ dataprint[index + 20 * (i)].tzNo2 }}</td>
                  <td v-else />
                  <td
                  v-if="dataprint[index + 20 * (i)]"
                  style="text-align: left; font-size: 12px;width:50px;"
                  >{{ dataprint[index + 20 * (i)].cpdz }}</td>
                  <td v-else />
                  <td
                  v-if="dataprint[index + 20 * (i)]"
                  style="text-align: left; font-size: 10px;width:150px;"
                  ><div style="width:120px;">{{ dataprint[index + 20 * (i)].ptRem }}</div></td>
                  <td v-else />
                  <td
                  v-if="dataprint[index + 20 * (i)]"
                  style="text-align: left; font-size: 10px;width:150px;"
                  ><div style="width:120px;">{{ dataprint[index + 20 * (i)].rem }}</div></td>
                  <td v-else />
                </tr>
                <tr>
                    <th colspan="2">合计：</th>
                    <th colspan="2"></th>
                    <th colspan="1"> {{ itematm.qtynum }}</th>
                    <th colspan="1"> {{ itematm.qty1num }}</th>
                    <th colspan="7"> </th>
                 </tr>
                 <tr v-if="i+1==curidList.length">
                  <th colspan="2">总合计：</th>
                  <th colspan="2"></th>
                  <th colspan="1"> {{ qtynumzong}}</th>
                  <th colspan="1"> {{ qtynum1zong }}</th>
                  <th colspan="7"> </th>
               </tr>
                <tr style="height: 40px">
                  <th style="width:25%;" colspan="3">收货单位: <span>(</span><span>{{queryParam.depName}}</span><span>)</span>车间</th>
                  <th style="width:25%;" colspan="3">发货人:</th>
                  <th style="width:25%;" colspan="3">收货人:</th>
                  <th style="width:25%;" colspan="3"><div style="text-align: right;">{{queryParam.endDd}}</div></th>
                </tr>
              </tbody>
            </table> -->
            </div>
            <!-- <div style="padding-top:200px;padding-left:5px;display:inline-block;width:6%;display: table-cell;font-size:8px;">
            <div>(一)</div>
            <div>存根白</div>
            <div>(二)</div>
            <div>客户红</div>
            <div>(三)</div>
            <div>回单黄</div>
          </div> -->
          </div>

          <!-- <div>
          <a-row style="margin-bottom: 5px">
            <a-col
              :span="6"
              style="font-size: 10px; font-weight: 700"
            >质量检验</a-col>
            <a-col
              :span="6"
              style="font-size: 10px; font-weight: 700"
            >仓库收货</a-col>
            <a-col
              :span="6"
              style="font-size: 10px; font-weight: 700"
            >报交经办</a-col>
            <a-col
              :span="5"
              style="font-size: 10px; font-weight: 700"
            >金额合计： {{ amtnTotal }}</a-col>
          </a-row>
        </div> -->
          <!-- <div style="border-left:1px solid #2c2c2c;border-right:1px solid #2c2c2c;border-bottom:1px solid #2c2c2c;border-top:1px solid #2c2c2c;">
          <span style="border-right:1px solid #808080;display:inline-block;width:50%">金额合计： {{ amtnTotal }}</span>
          <span style="display:inline-block;width:50%;">金额合计： {{ amtnTotal }}</span>
        </div> -->
          <!-- <div>
          <el-row style="margin-bottom: 5px;margin-top:10px;">
            <el-col
              :span="8"
              style="font-size: 13px; font-weight: 700;"
            >收货单位及经手人：</el-col>
            <el-col
              :span="8"
              style="font-size: 13px; font-weight: 700"
            >检验员：</el-col>
            <el-col
              :span="8"
              style="font-size: 13px; font-weight: 700"
            >送货单位及经手人：</el-col> -->
          <!-- <a-col
              :span="6"
              style="font-size: 10px; font-weight: 700"
            >数量合计： {{ amtnTotal }}</a-col>
            <a-col
              :span="6"
              style="font-size: 10px; font-weight: 700"
            >金额合计： {{ amtnTotal }}</a-col> -->
          <!-- </el-row>
        </div> -->
        </div>





      </div>
    </div>

    <el-dialog class="JustMake-dialog" title="派工" :visible.sync="visiblejiaojie" width="95%" style="height:100%;" :before-close='cancelpaigong'
               :close-on-click-modal='false'>
      <div style="text-align: right;">
        <a-button style="margin-left: 8px;background-color:#3CB371;border-color:#3CB371" type="primary"
                  @click="batchset" size="small">批次设置</a-button>
        <a-button style="margin-left: 8px;background-color:#3CB371;border-color:#3CB371" type="primary" @click="split"
                  size="small">拆分</a-button>
        <a-button style="margin-left: 8px;background-color:#3CB371;border-color:#3CB371" type="primary" @click="savetwo"
                  size="small">派工</a-button>
        <a-button type="primary" @click="savezan" size="small"
                  style="margin-left: 8px;background-color:#4a89c8;border-color:#4a89c8">暂存</a-button>
        <!-- <a-button
      style="margin-left: 8px;background-color: #f56c6c;border-color: #f56c6c;color:#fff;"
        type="primary"
        @click="cleardeltwo"
        size="small"
      >删除</a-button> -->


        <el-table :data="tableDatatwo" stripe row-key="mvNo" highlight-current-row
                  :cell-style="{ verticalAlign: 'top' }"
                  :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                  style="width: 100%;" height="480" ref="table2" @selection-change="handleSelectionChangetwo"
                  :row-class-name="tableRowClassName">
          <!-- v-loading="loading" -->
          <!-- :header-cell-style="{fontSize:'14px',color: '#606266', verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '700' }" -->
          <el-table-column type="selection" align="center" width="50"></el-table-column>

          <el-table-column prop="zcNo" width="100" align="left" label="工序"></el-table-column>
          <el-table-column prop="zcName" width="130" align="left" label="工序名称"></el-table-column>
          <el-table-column prop="prdNo" width="100" align="left" label="品号"></el-table-column>
          <el-table-column prop="prdName" width="120" align="left" label="品名"></el-table-column>
          <el-table-column prop="prdMark" width="100" align="left" label="特征"></el-table-column>

          <!--  -->
          <el-table-column prop="qty" width="100" align="left" label="派工数量">
            <template slot-scope="scope">
              <el-input v-model="scope.row.qty" oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleqty(scope)"
                        @wheel.native.prevent="stopScroll($event)" size="mini" placeholder="请输入派工数量" />
            </template>
          </el-table-column>

          <el-table-column prop="qty1" width="100" align="left" label="派工副数量">
            <template slot-scope="scope">
              <el-input v-model="scope.row.qty1" oninput="value=value.replace(/[^0-9.]/g,'')"
                        @input="handleqtyfu(scope)" @wheel.native.prevent="stopScroll($event)" size="mini"
                        placeholder="请输入派工副数量" />
            </template>
          </el-table-column>
          <el-table-column prop="pk3Unit" width="100" align="left" label="包装单位2">
            <template slot-scope="scope">
              <el-input v-model="scope.row.pk3Unit" oninput="value=value.replace(/[^0-9.]/g,'')"
                        @input="handlebao(scope)" @wheel.native.prevent="stopScroll($event)" size="mini"
                        placeholder="请输入包装单位2" />
            </template>
          </el-table-column>
          <el-table-column prop="preStaDd" width="170" align="left" label="预开工时间">
            <template slot-scope="scope">
              <a-date-picker style="width:100%" format="YYYY-MM-DD HH:mm:ss" valueFormat="YYYY-MM-DD HH:mm:ss"
                             v-model="scope.row.preStaDd" placeholder="请输入预开工日" />
              <!-- <el-date-picker
                      v-model="scope.row.preStaDd"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      format="yyyy-MM-dd HH:mm:ss"
                      style="max-width: 200px;width:100%;"
                      size="mini"
                      type="datetime"
                      placeholder="请输入预开工日"
                    ></el-date-picker> -->
            </template>
          </el-table-column>
          <el-table-column prop="preEndDd" width="170" align="left" label="预完工时间">
            <template slot-scope="scope">
              <a-date-picker style="width:100%" format="YYYY-MM-DD HH:mm:ss" valueFormat="YYYY-MM-DD HH:mm:ss"
                             v-model="scope.row.preEndDd" placeholder="请输入预完工日" />
              <!-- <el-date-picker
                      v-model="scope.row.preEndDd"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      format="yyyy-MM-dd HH:mm:ss"
                      style="max-width: 200px;width:100%;"
                      size="mini"
                      type="datetime"
                      placeholder="请输入预完工日"
                    ></el-date-picker> -->
            </template>
          </el-table-column>
          <el-table-column prop="sebNo" width="130" align="left" label="设备">
            <template slot-scope="scope">
              <el-input v-model="scope.row.sebNo" type="number" @wheel.native.prevent="stopScroll($event)" size="mini"
                        placeholder="请输入设备" />
            </template>
          </el-table-column>
          <el-table-column prop="ygNo" width="180" align="left" label="作业人员">
            <template slot-scope="scope">
              <my-selectListwo url="mes/salm/query" :read-only="true" :tableColumn="$Column.salm2" :form="$Form.salm"
                               :data="scope.row.ygNo" name="salNo" @choose="choosescope($event,scope)" allowClear ref="selectList"
                               v-decorator="['salNo', { rules: [{ required: true, message:'请选择作业人员' } ] }]"
                               placeholder="请选择作业人员"></my-selectListwo>
              <!-- <el-input v-model="scope.row.ygNo" type="number" @wheel.native.prevent="stopScroll($event)" size="mini" placeholder="请输入作业人员" /> -->
            </template>
          </el-table-column>
          <el-table-column prop="dep" width="180" align="left" label="制造部门">
            <template slot-scope="scope">
              <my-selectListwo url="mes/basicData/depPage" :read-only="true" :tableColumn="$Column.salmDep"
                               :form="$Form.salmDep" :data="scope.row.dep" name="dep" @choose="choosedan($event,scope)" allowClear
                               ref="selectList"
                               v-decorator="['dep', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                               :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
            </template>
          </el-table-column>
          <el-table-column prop="rem" width="200" align="left" label="备注" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-input v-model="scope.row.rem" size="mini" placeholder="请输入设备" />
            </template>
          </el-table-column>
          <el-table-column prop="qtyFin" width="100" align="left" label="已完工量"></el-table-column>
          <!-- <template slot-scope="scope">
                    <el-input v-model="scope.row.rem" @wheel.native.prevent="stopScroll($event)" size="mini" placeholder="请输入备注" />
                  </template> -->
          </el-table-column>
          <el-table-column prop="bomRem" width="150" align="left" label="材质"></el-table-column>
          <el-table-column prop="moNo" width="120" align="left" label="工单号"></el-table-column>
          <el-table-column prop="moDd" width="130" align="left" label="工单日期">
            <template slot-scope="scope">
              {{scope.row.moDd | formatDate}}
            </template>
          </el-table-column>
          <el-table-column prop="batNo" width="150" align="left" label="批号"></el-table-column>
          <!-- <el-table-column prop="batNo" width="130" align="left" label="批号"></el-table-column> -->
          <el-table-column prop="pgNo" width="130" align="left" label="派工单号"></el-table-column>
          <el-table-column prop="prdMark" width="100" align="left" label="批次单号否">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.isBaNo" @change="selectcheck($event,scope.row.row_index,'checked1')"
                           placeholder="请输入批次单号否"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column prop="baNo" width="130" align="left" label="批次单号"></el-table-column>
          <el-table-column prop="tzNo" width="130" align="left" label="工序单号"></el-table-column>
          <!-- <el-table-column prop="endDd" width="100" align="left" label="预交日期"> -->
          <el-table-column prop="tzDd" width="130" align="left" label="通知日期">
            <template slot-scope="scope">
              {{scope.row.tzDd | formatDate}}
            </template>
          </el-table-column>

          <!-- <template slot-scope="scope">
                    {{scope.row.endDd | formatDate}}
                </template>
                </el-table-column> -->

          <!--
                <el-table-column prop="mdNo" width="150" align="left" label="模具"></el-table-column>
                <el-table-column prop="soNo" width="130" align="left" label="受订计划" :show-overflow-tooltip="true"></el-table-column>
                <el-table-column prop="spc" width="130" align="left" label="规格"></el-table-column>
                <el-table-column prop="ypgQty" width="130" align="left" label="已派工量"></el-table-column>
               -->
          <!-- <el-table-column prop="usr" width="130" align="left" label="制单人"></el-table-column>-->
        </el-table>
        <div style="display: flex;justify-content: space-between;margin: 2px">
          <el-pagination background :page-sizes="[5, 10,20, 30, 50]" :page-size="20" @size-change="pageSizeChangetwo"
                         :current-page="tablePagetwo.currentPage" @current-change="currentChangetwo"
                         layout="total,sizes,prev, pager, next" :total="totalCounttwo"></el-pagination>
        </div>

      </div>
    </el-dialog>

    <el-dialog class="JustMake-dialog" title="缓存" :visible.sync="visiblechuhuan" width="95%" style="height:100%;"
               :before-close='cancelpaigonghuan' :close-on-click-modal='false'>
      <div style="text-align: right;">
        <a-button style="margin-left: 8px;background-color:#3CB371;border-color:#3CB371" type="primary"
                  @click="batchset" size="small">批次设置</a-button>
        <a-button style="margin-left: 8px;background-color:#3CB371;border-color:#3CB371" type="primary" @click="split"
                  size="small">拆分</a-button>
        <a-button style="margin-left: 8px;background-color:#3CB371;border-color:#3CB371" type="primary" @click="savetwo"
                  size="small">派工</a-button>
        <!-- <a-button
        type="primary"
        @click="savezan"
        size="small"
        style="margin-left: 8px;background-color:#4a89c8;border-color:#4a89c8"
      >暂存</a-button> -->
        <a-button style="margin-left: 8px;background-color: #f56c6c;border-color: #f56c6c;color:#fff;" type="primary"
                  @click="cleardel" size="small">删除</a-button>
        <el-table :data="tableDatatwo" stripe row-key="mvNo" highlight-current-row
                  :cell-style="{ verticalAlign: 'top' }"
                  :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                  style="width: 100%;" height="480" ref="table2" @selection-change="handleSelectionChangetwo"
                  :row-class-name="tableRowClassName">
          <!-- v-loading="loading" -->
          <!-- :header-cell-style="{fontSize:'14px',color: '#606266', verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '700' }" -->
          <el-table-column type="selection" align="center" width="50"></el-table-column>

          <el-table-column prop="zcNo" width="100" align="left" label="工序"></el-table-column>
          <el-table-column prop="zcName" width="130" align="left" label="工序名称"></el-table-column>
          <el-table-column prop="prdNo" width="100" align="left" label="品号"></el-table-column>
          <el-table-column prop="prdName" width="120" align="left" label="品名"></el-table-column>
          <el-table-column prop="prdMark" width="100" align="left" label="特征"></el-table-column>


          <el-table-column prop="qty" width="100" align="left" label="派工数量">
            <template slot-scope="scope">
              <el-input v-model="scope.row.qty" oninput="value=value.replace(/[^0-9.]/g,'')" @input="handleqty(scope)"
                        @wheel.native.prevent="stopScroll($event)" size="mini" placeholder="请输入派工数量" />
            </template>
          </el-table-column>

          <el-table-column prop="qty1" width="100" align="left" label="派工副数量">
            <template slot-scope="scope">
              <el-input v-model="scope.row.qty1" oninput="value=value.replace(/[^0-9.]/g,'')"
                        @input="handleqtyfu(scope)" @wheel.native.prevent="stopScroll($event)" size="mini"
                        placeholder="请输入派工副数量" />
            </template>
          </el-table-column>
          <el-table-column prop="pk3Unit" width="100" align="left" label="包装单位2">
            <template slot-scope="scope">
              <el-input v-model="scope.row.pk3Unit" oninput="value=value.replace(/[^0-9.]/g,'')"
                        @input="handlebao(scope)" @wheel.native.prevent="stopScroll($event)" size="mini"
                        placeholder="请输入包装单位2" />
            </template>
          </el-table-column>
          <el-table-column prop="preStaDd" width="170" align="left" label="预开工时间">
            <template slot-scope="scope">
              <a-date-picker style="width:100%" format="YYYY-MM-DD HH:mm:ss" valueFormat="YYYY-MM-DD HH:mm:ss"
                             v-model="scope.row.preStaDd" placeholder="请输入预开工日" />
              <!-- <el-date-picker
                      v-model="scope.row.preStaDd"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      format="yyyy-MM-dd HH:mm:ss"
                      style="max-width: 200px;width:100%;"
                      size="mini"
                      type="datetime"
                      placeholder="请输入预开工日"
                    ></el-date-picker> -->
            </template>
          </el-table-column>
          <el-table-column prop="preEndDd" width="170" align="left" label="预完工时间">
            <template slot-scope="scope">
              <a-date-picker style="width:100%" format="YYYY-MM-DD HH:mm:ss" valueFormat="YYYY-MM-DD HH:mm:ss"
                             v-model="scope.row.preEndDd" placeholder="请输入预完工日" />
              <!-- <el-date-picker
                      v-model="scope.row.preEndDd"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      format="yyyy-MM-dd HH:mm:ss"
                      style="max-width: 200px;width:100%;"
                      size="mini"
                      type="datetime"
                      placeholder="请输入预完工日"
                    ></el-date-picker> -->
            </template>
          </el-table-column>
          <el-table-column prop="sebNo" width="130" align="left" label="设备">
            <template slot-scope="scope">
              <el-input v-model="scope.row.sebNo" type="number" @wheel.native.prevent="stopScroll($event)" size="mini"
                        placeholder="请输入设备" />
            </template>
          </el-table-column>
          <el-table-column prop="ygNo" width="180" align="left" label="作业人员">
            <template slot-scope="scope">
              <my-selectListwo url="mes/salm/query" :read-only="true" :tableColumn="$Column.salm2" :form="$Form.salm"
                               :data="scope.row.ygNo" name="salNo" @choose="choosescope($event,scope)" allowClear ref="selectList"
                               v-decorator="['salNo', { rules: [{ required: true, message:'请选择作业人员' } ] }]"
                               placeholder="请选择作业人员"></my-selectListwo>
              <!-- <el-input v-model="scope.row.ygNo" type="number" @wheel.native.prevent="stopScroll($event)" size="mini" placeholder="请输入作业人员" /> -->
            </template>
          </el-table-column>
          <el-table-column prop="dep" width="180" align="left" label="制造部门">
            <template slot-scope="scope">
              <my-selectListwo url="mes/basicData/depPage" :read-only="true" :tableColumn="$Column.salmDep"
                               :form="$Form.salmDep" :data="scope.row.dep" name="dep" @choose="choosedan($event,scope)" allowClear
                               ref="selectList"
                               v-decorator="['dep', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                               :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
            </template>
          </el-table-column>
          <el-table-column prop="rem" width="200" align="left" label="备注" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-input v-model="scope.row.rem" size="mini" placeholder="请输入设备" />
            </template>
          </el-table-column>
          <el-table-column prop="qtyFin" width="100" align="left" label="已完工量"></el-table-column>
          <!-- <template slot-scope="scope">
                    <el-input v-model="scope.row.rem" @wheel.native.prevent="stopScroll($event)" size="mini" placeholder="请输入备注" />
                  </template> -->
          <el-table-column prop="bomRem" width="120" align="left" label="材质"></el-table-column>
          <el-table-column prop="moNo" width="130" align="left" label="工单号"></el-table-column>
          <el-table-column prop="moDd" width="130" align="left" label="工单日期">
            <template slot-scope="scope">
              {{scope.row.moDd | formatDate}}
            </template>
          </el-table-column>
          <el-table-column prop="batNo" width="150" align="left" label="批号"></el-table-column>
          <!-- <el-table-column prop="batNo" width="130" align="left" label="批号"></el-table-column> -->
          <el-table-column prop="pgNo" width="130" align="left" label="派工单号"></el-table-column>
          <el-table-column prop="prdMark" width="100" align="left" label="批次单号否">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.isBaNo" @change="selectcheck($event,scope.row.row_index,'checked1')"
                           placeholder="请输入批次单号否"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column prop="baNo" width="120" align="left" label="批次单号"></el-table-column>
          <el-table-column prop="tzNo" width="120" align="left" label="工序单号"></el-table-column>

          <!-- <el-table-column prop="endDd" width="100" align="left" label="预交日期"> -->
          <el-table-column prop="tzDd" width="130" align="left" label="通知日期">
            <template slot-scope="scope">
              {{scope.row.tzDd | formatDate}}
            </template>
          </el-table-column>
          <!-- <el-table-column prop="rem" width="200" align="left" label="备注"></el-table-column> -->
          <!-- <template slot-scope="scope">
                    {{scope.row.endDd | formatDate}}
                </template>
                </el-table-column> -->

          <!--
                <el-table-column prop="mdNo" width="150" align="left" label="模具"></el-table-column>
                <el-table-column prop="soNo" width="130" align="left" label="受订计划" :show-overflow-tooltip="true"></el-table-column>
                <el-table-column prop="spc" width="130" align="left" label="规格"></el-table-column>
                <el-table-column prop="ypgQty" width="130" align="left" label="已派工量"></el-table-column>

               -->

          <!-- <el-table-column prop="usr" width="130" align="left" label="制单人"></el-table-column>-->
        </el-table>
        <div style="display: flex;justify-content: space-between;margin: 2px">
          <el-pagination background :page-sizes="[5, 10,20, 30, 50]" :page-size="20" @size-change="pageSizeChangetwo"
                         :current-page="tablePagetwo.currentPage" @current-change="currentChangetwo"
                         layout="total,sizes,prev, pager, next" :total="totalCounttwo"></el-pagination>
        </div>

      </div>
    </el-dialog>


    <el-dialog class="JustMake-dialog" title="已交接修改" :visible.sync="visibleyijiao" width="40%"  style="height:100%">
      <div style="center='center'">

        <el-form status-icon inline-message label-width="85px" :model="handover" ref="addEntityForm"
                 style="margin: 0px;padding: 0px;">
          <el-form-item label="转移单号:" prop="mvNo" style="margin-bottom:8px;">
            {{handover.mvNo}}
            <!-- <el-input v-model="handover.mvNo size="mini" placeholder="请输入数量(副)" /> -->
          </el-form-item>
          <el-form-item label="交接单号:" prop="qrNo" style="margin-bottom:8px;">
            {{handover.qrNo}}
            <!-- <el-input v-model="handover.qrNo size="mini" placeholder="请输入数量(副)" /> -->
          </el-form-item>
          <el-form-item label="数量:" prop="qty" style="margin-bottom:8px;">
            <el-input v-model="handover.qty" type="number" @wheel.native.prevent="stopScroll($event)" size="mini"
                      placeholder="请输入请输入数量" />
            <!-- <el-input prefix-icon="el-icon-edit" type="number" @wheel.native.prevent="stopScroll($event)" :disabled="isEdit" v-model="entity.boxCount" size="mini" style="width: 200px;"
          placeholder="请输入箱数"></el-input>  -->
          </el-form-item>
          <!-- <el-form-item  label="数量(副):" prop="pk3Qty" style="margin-bottom:8px;"> -->
          <!-- <el-input prefix-icon="el-icon-edit" type="number" @wheel.native.prevent="stopScroll($event)" :disabled="isEdit"  v-model="entity.pk3Qty" size="mini" style="width: 200px;"
          placeholder="请输入数量"></el-input> -->
          <!-- </el-form-item> -->
          <el-form-item label="数量(副):" prop="qty1" style="margin-bottom:8px;">
            <el-input v-model="handover.qty1" type="number" @wheel.native.prevent="stopScroll($event)" size="mini"
                      placeholder="请输入数量(副)" />
          </el-form-item>
          <el-form-item label="件数:" prop="jsQty" style="margin-bottom:8px;">
            <el-input v-model="handover.jsQty" size="mini" placeholder="请输入件数" />
          </el-form-item>
          <el-form-item label="加工内容:" prop="remJg" style="margin-bottom:8px;">
            <el-input v-model="handover.remJg" size="mini" placeholder="请输入加工内容" />
          </el-form-item>
          <el-form-item label="单重:" prop="cpdz" style="margin-bottom:8px;">
            <el-input v-model="handover.cpdz" type="number" @wheel.native.prevent="stopScroll($event)" size="mini"
                      placeholder="请输入单重" />
          </el-form-item>
          <el-form-item label="备注:" prop="rem" style="margin-bottom:8px;">
            <el-input v-model="handover.rem" size="mini" placeholder="请输入备注" />
          </el-form-item>
        </el-form>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="sureyijioa">确 定</el-button>
        <el-button size="mini" @click="visibleyijiao=!visibleyijiao">取 消</el-button>
      </span>
    </el-dialog>

    <el-dialog class="JustMake-dialog" title="提示" :visible.sync="visiblesure" width="20%"  style="height:100%">
      <div style="center='center'">
        确认清除所选内容吗?
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="clearsure">确 定</el-button>
        <el-button size="mini" @click="visiblesure=!visiblesure">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog class="JustMake-dialog" title="" :visible.sync="huanvisible" width="26%"  style="height:100%"
               :before-close='huandel' :close-on-click-modal='false'>
      <div style="center='center'">
        <div style="text-align:center">是否调用暂存派工单?</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <div style="text-align: center;">
          <el-button size="mini" type="primary" style="margin-right:50px;" @click="huansure">确 定</el-button>
          <el-button size="mini" @click="huandel">取 消</el-button>
        </div>
      </span>
    </el-dialog>


    <el-dialog class="JustMake-dialog" title="拆分" :visible.sync="quantitymodyvisible" width="80%"  style="height:100%"
               :close-on-click-modal='false'>
      <div style="center='center'">
        <div>拆分笔数: <el-input v-model="totalnumber" @input="paigong" style="width:100px;" type="number"
                             @wheel.native.prevent="stopScroll($event)" size="mini" placeholder="请输入笔数" />
          <a-button style="margin-left: 8px;margin-right: 8px" type="primary" @click="chaifen"
                    size="small">拆分</a-button>
          <span style="color:#f00">可拆分量:{{cansplit}}</span>
        </div>
        <!-- <el-form status-icon  inline-message  label-width="85px"  :model="handover"  ref="addEntityForm" style="margin: 0px;padding: 0px;"> -->
        <!-- <el-form-item  label="派工数量:" prop="mvNo" style="margin-bottom:8px;"> -->
        <!-- {{handover.mvNo}} -->
        <!-- <el-input v-model="handover.mvNo size="mini" placeholder="请输入数量(副)" /> -->
        <!-- </el-form-item> -->
        <!-- <el-form-item  label="交接单号:" prop="qrNo" style="margin-bottom:8px;"> -->
        <!-- {{handover.qrNo}} -->
        <!-- <el-input v-model="handover.qrNo size="mini" placeholder="请输入数量(副)" /> -->
        <!-- </el-form-item> -->
        <div style="font-size:10px;color: #f00;">拆分派工数量之和需等于总数量</div>
        <!-- <div style="display: flex;justify-content: space-between;">
            <div style="flex:1">
              <div v-for="(itematm, i) in handovernumber" style="margin-bottom:5px;">
                派工数量{{i+1}}
                <el-input v-model="itematm.label" type="number" @input="numberpai" @wheel.native.prevent="stopScroll($event)" style="width:200px;" size="mini" placeholder="请输入请输入数量" />
              </div>
            </div>
            <div style="flex:1">
              <div v-for="(itematm, i) in handovernumber" style="margin-bottom:5px;">
                派工副数量{{i+1}}
                <el-input v-model="itematm.value" type="number" @input="numberpaifu" @wheel.native.prevent="stopScroll($event)" style="width:200px;" size="mini" placeholder="请输入请输入派工副数量" />
              </div>
            </div>
          </div> -->
        <!-- <el-input prefix-icon="el-icon-edit" type="number" @wheel.native.prevent="stopScroll($event)" :disabled="isEdit" v-model="entity.boxCount" size="mini" style="width: 200px;"
            placeholder="请输入箱数"></el-input>  -->
        <!-- </el-form-item> -->
        <!-- <el-form-item  label="数量(副):" prop="pk3Qty" style="margin-bottom:8px;"> -->
        <!-- <el-input prefix-icon="el-icon-edit" type="number" @wheel.native.prevent="stopScroll($event)" :disabled="isEdit"  v-model="entity.pk3Qty" size="mini" style="width: 200px;"
            placeholder="请输入数量"></el-input> -->
        <!-- </el-form-item> -->
        <!-- <el-form-item  label="数量(副):"  prop="qty1" style="margin-bottom:8px;">
            <el-input v-model="handover.qty1" type="number" @wheel.native.prevent="stopScroll($event)" size="mini" placeholder="请输入数量(副)" />
          </el-form-item>
          <el-form-item  label="件数:"  prop="jsQty" style="margin-bottom:8px;">
            <el-input v-model="handover.jsQty" size="mini" placeholder="请输入件数" />
          </el-form-item>
          <el-form-item  label="加工内容:" prop="remJg" style="margin-bottom:8px;">
            <el-input v-model="handover.remJg" size="mini" placeholder="请输入加工内容" />
          </el-form-item>
          <el-form-item  label="单重:" prop="cpdz" style="margin-bottom:8px;">
            <el-input v-model="handover.cpdz"  type="number" @wheel.native.prevent="stopScroll($event)" size="mini" placeholder="请输入单重" />
          </el-form-item>
          <el-form-item  label="备注:" prop="rem" style="margin-bottom:8px;">
            <el-input v-model="handover.rem" size="mini" placeholder="请输入备注" />
          </el-form-item> -->
        <!-- </el-form> -->
        <el-table :data="tablechaifen" stripe row-key="mvNo" highlight-current-row
                  :cell-style="{ verticalAlign: 'top' }"
                  :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                  style="width: 100%;" height="480" ref="tablechai" @selection-change="handleSelectionChangetwo"
                  :row-class-name="tableRowClassName">
          <!-- v-loading="loading" -->
          <!-- :header-cell-style="{fontSize:'14px',color: '#606266', verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '700' }" -->
          <!-- <el-table-column type="selection" :reserve-selection="true" align="center" width="50"></el-table-column> -->

          <el-table-column prop="zcNo" width="140" align="left" label="工序"></el-table-column>
          <el-table-column prop="zcName" width="140" align="left" label="工序名称"></el-table-column>
          <el-table-column prop="prdNo" width="120" align="left" label="品号"></el-table-column>
          <el-table-column prop="prdName" width="100" align="left" label="品名"></el-table-column>
          <el-table-column prop="prdMark" width="100" align="left" label="特征"></el-table-column>
          <el-table-column prop="qty" width="100" align="left" label="派工数量">
            <template slot-scope="scope">
              <el-input v-model="scope.row.qty" oninput="value=value.replace(/[^0-9.]/g,'')"
                        @input="handleqtychai(scope)" @wheel.native.prevent="stopScroll($event)" size="mini"
                        placeholder="请输入派工数量" />
            </template>
          </el-table-column>

          <el-table-column prop="qty1" width="150" align="left" label="派工副数量">
            <template slot-scope="scope">
              <el-input v-model="scope.row.qty1" oninput="value=value.replace(/[^0-9.]/g,'')"
                        @input="handleqtychaifu(scope)" @wheel.native.prevent="stopScroll($event)" size="mini"
                        placeholder="请输入派工副数量" />
            </template>
          </el-table-column>
          <el-table-column prop="preStaDd" width="130" align="left" label="预开工日">
          </el-table-column>
          <el-table-column prop="preEndDd" width="130" align="left" label="预完工日">
          </el-table-column>
          <el-table-column prop="sebNo" width="130" align="left" label="设备">
          </el-table-column>
          <el-table-column prop="ygNo" width="130" align="left" label="作业人员">
          </el-table-column>
          <el-table-column prop="dep" width="150" align="left" label="制造部门">
          </el-table-column>
          <el-table-column prop="qtyFin" width="130" align="left" label="已完工量"></el-table-column>

          <el-table-column prop="bomRem" width="150" align="left" label="材质"></el-table-column>
          <el-table-column prop="moNo" width="130" align="left" label="工单号"></el-table-column>
          <el-table-column prop="moDd" width="130" align="left" label="工单日期">
            <template slot-scope="scope">
              {{scope.row.moDd | formatDate}}
            </template>
          </el-table-column>
          <el-table-column prop="batNo" width="150" align="left" label="批号"></el-table-column>
          <el-table-column prop="pgNo" width="130" align="left" label="派工单号">
            <template slot-scope="scope">
              {{scope.row.pgNo}}
            </template>
          </el-table-column>
          <el-table-column prop="baNo" width="130" align="left" label="批次单号"></el-table-column>
          <el-table-column prop="tzNo" width="150" align="left" label="工序单号"></el-table-column>
          <el-table-column prop="tzDd" width="130" align="left" label="通知日期">
            <template slot-scope="scope">
              {{scope.row.tzDd | formatDate}}
            </template>
          </el-table-column>
          <el-table-column prop="rem" width="150" align="left" label="备注" show-overflow-tooltip>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="totalnumbersuretwo">确 定</el-button>
        <el-button size="mini" @click="quantitymodyvisible=!quantitymodyvisible">取 消</el-button>
      </span>
    </el-dialog>

    <el-dialog class="JustMake-dialog" title="批次设置" :visible.sync="visiblepici" width="80%"  style="height:100%"
               :close-on-click-modal="false">
      <div style="center='center'">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="预开工时间">
                <a-date-picker style="width:100%" format="YYYY-MM-DD HH:mm:ss" valueFormat="YYYY-MM-DD HH:mm:ss"
                               v-model="queryParamtwo.preStaDd" placeholder="请输入开始日期" />
                <!-- <a-range-picker v-model="valueone" @change="onChange" format="YYYY-MM-DD"/> -->
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="预完工时间">
                <a-date-picker style="width:100%" format="YYYY-MM-DD HH:mm:ss" valueFormat="YYYY-MM-DD HH:mm:ss"
                               v-model="queryParamtwo.preEndDd" placeholder="请输入结束日期" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item :label="$t('制造部门')" prop="dep">
                <my-selectListwo url="mes/basicData/depPage" :read-only="true" :tableColumn="$Column.salmDep"
                                 :form="$Form.salmDep" :data="datatwo" name="dep" @choose="choosepop($event)" allowClear
                                 ref="selectList"
                                 v-decorator="['dep', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                                 :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
                <!-- <span>{{ entity.dep }}</span> -->
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item :label="$t('设备')">
                <a-input v-model="queryParamtwo.sebNo" :placeholder="$t('设备')" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <!-- :tableColumn="$Column.salmDep"
              :form="$Form.salmDep" -->
              <a-form-item :label="$t('作业人员')" prop="salNo">
                <my-selectListwo url="mes/salm/query" :read-only="true" :tableColumn="$Column.salm2" :form="$Form.salm"
                                 :data="datazuoye" name="salNo" @choose="choosezuoye($event)" allowClear ref="selectList"
                                 v-decorator="['salNo', { rules: [{ required: true, message:'请选择作业人员' } ] }]"
                                 placeholder="请选择作业人员"></my-selectListwo>
                <!-- <span>{{ entity.dep }}</span> -->
              </a-form-item>
            </a-col>

            <!-- <a-col
                :md="8"
                :sm="24"
              >
                <a-form-item :label="$t('作业人员')">
                  <a-input
                    v-model="queryParamtwo.ygNo"
                    :placeholder="$t('作业人员')"
                  />
                </a-form-item>
              </a-col> -->
          </a-row>
        </a-form>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="picisure">确 定</el-button>
        <el-button size="mini" @click="visiblepici=!visiblepici">取 消</el-button>
      </span>
    </el-dialog>
    <el-tabs type="border-card" v-model="activeNameMain" @tab-click="handleClick">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="开始日期">
                <a-date-picker style="width:100%" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                               v-model="queryParam.staDd" placeholder="请输入开始日期" />
                <!-- <a-range-picker v-model="valueone" @change="onChange" format="YYYY-MM-DD"/> -->
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="结束日期">
                <a-date-picker style="width:100%" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                               v-model="queryParam.endDd" placeholder="请输入结束日期" />
              </a-form-item>
            </a-col>
            <!-- <a-col
                  :md="6"
                  :sm="24"
                >
                  <a-form-item :label="$t('品号')">
                    <a-input
                      v-model="queryParam.prdNo"
                      :placeholder="$t('品号')"
                    />
                  </a-form-item>
                </a-col> -->
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('品号')" style="position: relative;">
                <el-popover placement="bottom-start" width="600" trigger="click" v-model="visible" ref="dcPopover"
                            title="" :popper-options="{ boundariesElement: 'body'}">
                  <!-- :popper-options="{ boundariesElement: 'viewport', removeOnDestroy: true }" -->
                  <!-- :popper-options="{ boundariesElement: 'viewport', removeOnDestroy: true }" -->
                  <div id="popover">
                    <el-input slot="reference" v-model="queryParam.prdNo" clearable placeholder="请选择"
                              style="max-width: 200px;width:100%;" @focus="zsMc" @input="zsMcinput" size="mini"></el-input>
                    <el-table :data="prdNoList" @row-click="handdle" style="width: 100%;overflow:scroll;height:450px;"
                              border size="mini">
                      <el-table-column prop="prdNo" label="代号"></el-table-column>
                      <el-table-column prop="name" label="名称"></el-table-column>
                    </el-table>
                    <div style="display: flex;justify-content: space-between;margin: 2px">
                      <el-pagination background :page-sizes="[5, 10,20, 30, 50]" :page-size="20" :pager-count="5"
                                     @size-change="pageSizeChangefour" :current-page="tablePagefour.currentPage"
                                     @current-change="currentChangefour" layout="total,sizes,prev, pager, next"
                                     :total="totalCountfour"></el-pagination>
                    </div>
                    <!-- <div>
                        <el-pagination
                          background
                          :page-size="10"
                          :current-page="padtPage"
                          @current-change="currentChangePrdt"
                          layout="prev, pager, next"
                          :total="prdtCount"
                        ></el-pagination>
                      </div> -->
                  </div>
                  <el-input slot="reference" v-model="queryParam.prdNo" clearable placeholder="请选择"
                            style="max-width: 200px;width:100%;" @focus="zsMc" @input="zsMcinput" size="mini"
                            @clear="handleEmpty"></el-input>
                  <!-- :readonly="isReadonlyPrdNameInput" -->
                </el-popover>
                <!-- <a-input
                     style="width:150px"
                      v-model="condition.prdNo"
                      clearable
                      :placeholder="$t('品号')"
                    /> -->
              </a-form-item>
            </a-col>

            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('品名')">
                <a-input v-model="queryParam.prdName" :placeholder="$t('品名')" />
              </a-form-item>
            </a-col>
            <!-- <a-col
                  :md="6"
                  :sm="24"
                >
                  <a-form-item :label="$t('制程')">
                    <a-input
                      v-model="queryParam.zcNo"
                      :placeholder="$t('制程')"
                    />
                  </a-form-item>
                </a-col> -->
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('工序')">
                <el-popover placement="bottom" width="600" trigger="click" v-model="visibletwo" title=""
                            :popper-options="{ boundariesElement: 'body'}">
                  <!-- ref="dcPopovertwo" -->
                  <!-- :popper-options="{ boundariesElement: 'viewport', removeOnDestroy: true }" -->
                  <div id="popover">
                    <el-input slot="reference" v-model="queryParam.zcNo" clearable placeholder="请选择"
                              style="max-width: 200px;width:100%;" @focus="zsMctwo" @input="zsMctwoinput"
                              size="mini"></el-input>
                    <el-table :data="zcNoList" @row-click="handdletwo" style="width: 100%;overflow:scroll;height:450px;"
                              border size="mini">
                      <el-table-column prop="zcNo" label="工序"></el-table-column>
                      <el-table-column prop="name" label="工序名称"></el-table-column>
                    </el-table>
                    <div style="display: flex;justify-content: space-between;margin: 2px">
                      <el-pagination background :page-sizes="[20, 30, 50,100]" :page-size="20" :pager-count="5"
                                     @size-change="pageSizeChangezhi" :current-page="tablePagezhi.currentPage"
                                     @current-change="currentChangezhi" layout="total,sizes,prev, pager, next"
                                     :total="totalCounzhi"></el-pagination>
                    </div>
                  </div>
                  <el-input slot="reference" @input="zsMctwoinput" v-model="queryParam.zcNo" clearable placeholder="请选择"
                            style="width:100%;" @focus="zsMctwo" size="mini"></el-input>
                </el-popover>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('制造部门')" prop="dep">
                <my-selectListwo url="mes/basicData/depPage" :read-only="true" :tableColumn="$Column.salmDep"
                                 :form="$Form.salmDep" :data="data" name="dep" @choose="choose($event)" allowClear ref="selectList"
                                 v-decorator="['dep', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                                 :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
                <!-- <span>{{ entity.dep }}</span> -->
              </a-form-item>
            </a-col>
            <!-- <a-col
                  :md="6"
                  :sm="24"
                >
                  <a-form-item :label="$t('工单号')">
                    <a-input
                      v-model="queryParam.moNo"
                      :placeholder="$t('工单号')"
                    />
                  </a-form-item>
                </a-col> -->
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('工单号')">
                <el-popover placement="bottom" width="600" trigger="click" v-model="visibledan" title=""
                            :popper-options="{ boundariesElement: 'body'}">
                  <!-- ref="dcPopovertwo" -->
                  <!-- :popper-options="{ boundariesElement: 'viewport', removeOnDestroy: true }" -->
                  <div id="popover">
                    <el-input slot="reference" v-model="queryParam.moNo" clearable placeholder="请选择"
                              style="max-width: 200px;width:100%;" @focus="zsMcdan" @input="zsMcdaninput"
                              size="mini"></el-input>
                    <el-table :data="zcNoListdan" @row-click="handdletwodan"
                              style="width: 100%;overflow:scroll;height:450px;" border size="mini">
                      <el-table-column prop="moNo" label="工单号"></el-table-column>
                      <el-table-column prop="mrpNo" label="品号"></el-table-column>
                      <el-table-column prop="prdName" label="品名"></el-table-column>
                    </el-table>
                    <div style="display: flex;justify-content: space-between;margin: 2px">
                      <el-pagination background :page-sizes="[20, 30, 50,100]" :page-size="20" :pager-count="5"
                                     @size-change="pageSizeChangedan" :current-page="tablePagedan.currentPage"
                                     @current-change="currentChangedan" layout="total,sizes,prev, pager, next"
                                     :total="totalCoundan"></el-pagination>
                    </div>
                  </div>
                  <el-input slot="reference" @input="zsMcdaninput" v-model="queryParam.moNo" clearable placeholder="请选择"
                            style="width:100%;" @focus="zsMcdan" size="mini"></el-input>
                </el-popover>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('特征')">
                <a-input v-model="queryParam.prdMark" :placeholder="$t('特征')" />
              </a-form-item>
            </a-col>

            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('设备')">
                <a-input v-model="queryParam.sebNo" :placeholder="$t('设备')" />
              </a-form-item>
            </a-col>
            <!-- <a-col
                :md="6"
                :sm="24"
              >
                <a-form-item :label="$t('客户')">
                  <a-input
                    v-model="queryParam.cusNo"
                    :placeholder="$t('客户')"
                  />
                </a-form-item>
              </a-col> -->
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('客户')">
                <el-popover placement="bottom" width="600" trigger="click" v-model="visiblekehu" title=""
                            :popper-options="{ boundariesElement: 'body'}">
                  <!-- ref="dcPopovertwo" -->
                  <!-- :popper-options="{ boundariesElement: 'viewport', removeOnDestroy: true }" -->
                  <div id="popover">
                    <el-input slot="reference" v-model="queryParamkehu.name" clearable placeholder="请选择"
                              style="max-width: 200px;width:100%;" @focus="zsMckehu" @input="zsMckehuinput"
                              size="mini"></el-input>
                    <el-table :data="zcNoListkehu" @row-click="handdletwokehu"
                              style="width: 100%;overflow:scroll;height:450px;" border size="mini">
                      <el-table-column prop="cusNo" label="客户"></el-table-column>
                      <el-table-column prop="name" label="客户名称"></el-table-column>
                    </el-table>
                    <div style="display: flex;justify-content: space-between;margin: 2px">
                      <el-pagination background :page-sizes="[20, 30, 50,100]" :page-size="20" :pager-count="5"
                                     @size-change="pageSizeChangekehu" :current-page="tablePagekehu.currentPage"
                                     @current-change="currentChangekehu" layout="total,sizes,prev, pager, next"
                                     :total="totalCounkehu"></el-pagination>
                    </div>
                  </div>
                  <el-input slot="reference" @input="zsMckehuinput" v-model="queryParamkehu.name" clearable
                            placeholder="请选择" style="width:100%;" @focus="zsMckehu" size="mini"></el-input>
                </el-popover>
              </a-form-item>
            </a-col>

            <!-- <a-col
                  :md="6"
                  :sm="24"

                >
                  <a-form-item :label="$t('通知单号')">
                    <a-input
                      v-model="queryParam.tzNo"
                      :placeholder="$t('通知单号')"
                    />
                  </a-form-item>
                </a-col> -->
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('工序单号')">
                <el-popover placement="bottom" width="600" trigger="click" v-model="visibletong" title=""
                            :popper-options="{ boundariesElement: 'body'}">
                  <!-- ref="dcPopovertwo" -->
                  <!-- :popper-options="{ boundariesElement: 'viewport', removeOnDestroy: true }" -->
                  <div id="popover">
                    <el-input slot="reference" v-model="queryParam.tzNo" clearable placeholder="请选择"
                              style="max-width: 200px;width:100%;" @focus="zsMctong" @input="zsMctonginput"
                              size="mini"></el-input>
                    <el-table :data="zcNoListtong" @row-click="handdletwotong"
                              style="width: 100%;overflow:scroll;height:450px;" border size="mini">
                      <el-table-column prop="tzNo" label="工序单号"></el-table-column>
                      <el-table-column prop="zcNo" label="工序">
                        <template slot-scope="scope">
                          {{scope.row.zcNo}}/{{scope.row.zcName}}
                        </template>
                      </el-table-column>
                      <el-table-column prop="mrpNo" label="品号"></el-table-column>
                      <el-table-column prop="prdName" label="品名"></el-table-column>
                      <!-- <el-table-column prop="bilId"  label="来源识别码"></el-table-column>
                      <el-table-column prop="closeId"  label="结案否"></el-table-column> -->
                    </el-table>
                    <div style="display: flex;justify-content: space-between;margin: 2px">
                      <el-pagination background :page-sizes="[20, 30, 50,100]" :page-size="20" :pager-count="5"
                                     @size-change="pageSizeChangetong" :current-page="tablePagetong.currentPage"
                                     @current-change="currentChangetong" layout="total,sizes,prev, pager, next"
                                     :total="totalCountong"></el-pagination>
                    </div>
                  </div>
                  <el-input slot="reference" @input="zsMctonginput" v-model="queryParam.tzNo" clearable
                            placeholder="请选择" style="width:100%;" @focus="zsMctong" size="mini"></el-input>
                </el-popover>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('受订计划')">
                <a-input v-model="queryParam.soNo" :placeholder="$t('受订计划')" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('材质')">
                <a-input v-model="queryParam.bomRem" :placeholder="$t('材质')" />
              </a-form-item>
            </a-col>

            <a-col :md="6" :sm="24" v-if="activeNameMain == 'three'">
              <a-form-item :label="$t('派工单号')">
                <a-input v-model="queryParam.pgNo" ref="submission" :placeholder="$t('派工单号')" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24" v-if="activeNameMain == 'three'">
              <a-form-item :label="$t('批次单号')">
                <a-input v-model="queryParam.baNo" :placeholder="$t('批次单号')" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24" v-if="activeNameMain == 'three'">
              <a-form-item :label="$t('结案')">
                <a-select v-model="queryParam.closeId" allowClear :placeholder="$t('请选择结案')">
                  <a-select-option v-for="item in stateList" :key="item.id" :value="item.id">
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24" v-if="activeNameMain == 'three'">
              <a-form-item :label="$t('打印状态')">
                <a-select v-model="queryParam.printId" allowClear :placeholder="$t('请选择打印状态')">
                  <a-select-option v-for="item in printList" :key="item.id" :value="item.id">
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

          </a-row>
          <!-- <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <span class="table-page-search-submitButtons">

              </span>
            </a-col>
          </a-row> -->
        </a-form>
      </div>
      <el-tab-pane label="待派工" name="first">
        <a-row :gutter="8">
          <a-col :span="24">
            <a-row>
              <vxe-toolbar custom>
                <template v-slot:buttons>
                </template>
              </vxe-toolbar>
              <vxe-table size='small' border="inner" resizable stripe highlight-current-row show-overflow
                         highlight-hover-row export-config ref="xTable" :loading="loading" :data="tableData"
                         :keyboard-config="{ isArrow: true }" :edit-config="{ trigger: 'click', mode: 'row' }"
                         :header-cell-style="{ backgroundColor: '#F4F5F9', color: '#7A8085', fontWeight: '400', fontSize: '12px' }"
                         :cell-style="{ fontSize: '12px' }">
                <vxe-table-column type="checkbox" align="center" :width="50"></vxe-table-column>
                <vxe-table-column field="zcNo" title="工序" align="center" width="140">
                  <template slot-scope="scope">
                    {{scope.row.zcNo}} / {{scope.row.zcName}}
                  </template>
                </vxe-table-column>

                <vxe-table-column field="prdNo" title="货品" align="center" width="120">
                  <template slot-scope="scope">
                    {{scope.row.prdNo}} / {{scope.row.prdName}}
                  </template>
                </vxe-table-column>
                <vxe-table-column field="prdMark" title="特征" align="center" width="100"></vxe-table-column>
                <vxe-table-column field="qty" title="待派工量" align="center" width="100"></vxe-table-column>
                <vxe-table-column field="qty1" title="待派工副数量" align="center" width="100"></vxe-table-column>
                <vxe-table-column field="moNo" title="工单号" width="130" align="center"></vxe-table-column>
                <vxe-table-column field="tzNo" title="工序单号" width="150" align="center"></vxe-table-column>
                <vxe-table-column field="batNo" title="批号" width="150" align="center"></vxe-table-column>
                <vxe-table-column field="sebNo" title="设备" width="130" align="center"></vxe-table-column>
                <vxe-table-column field="mdNo" title="模具" width="150" align="center"></vxe-table-column>
                <vxe-table-column field="moDd" title="工单日期" align="center" width="130">
                  <template slot-scope="scope">
                    {{scope.row.moDd | formatDate}}
                  </template>
                </vxe-table-column>
                <vxe-table-column field="bomRem" title="材质" width="150" align="center"></vxe-table-column>
                <vxe-table-column field="soNo" title="受订计划" show-overflow="tooltip" width="130"
                                  align="center"></vxe-table-column>
                <vxe-table-column field="spc" title="规格" align="center" width="130"></vxe-table-column>
                <vxe-table-column field="ypgQty" title="已派工量" width="100" align="center"></vxe-table-column>
                <vxe-table-column field="endDd" width="120" title="预交日期" align="center">
                  <template slot-scope="scope">
                    {{scope.row.endDd | formatDate}}
                  </template>
                </vxe-table-column>
                <vxe-table-column field="dep" width="150" title="制造部门" align="center"></vxe-table-column>
                <vxe-table-column field="depName" title="部门名称" width="150" align="center"></vxe-table-column>
                <vxe-table-column field="tzDd" title="通知日期" width="130" align="center">
                  <template slot-scope="scope">
                    {{scope.row.tzDd | formatDate}}
                  </template>
                </vxe-table-column>
                <vxe-table-column field="rem" title="备注" width="150" align="center"
                                  show-overflow="tooltip"></vxe-table-column>
              </vxe-table>
              <vxe-pager :loading="loading" :current-page="tablePage.currentPage" :page-size="tablePage.pageSize"
                         :total="totalCount" :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
                         @page-change="handlePageChange">
              </vxe-pager>
              <!-- <el-table
                    stripe
                    row-key="mvNo"
                    :data="tableData"
                    highlight-current-row
                    :cell-style="{ verticalAlign: 'top' }"
                    :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                    style="width: 100%;"
                    @selection-change="handleSelectionChange"
                    height="580px"
                    ref="tableone"
                  >
                    <el-table-column type="selection"  align="center" width="50"></el-table-column>
                    <el-table-column prop="zcNo" width="140" align="left" label="制程"></el-table-column>
                    <el-table-column prop="zcName" width="140" align="left" label="制程名称"></el-table-column>
                    <el-table-column prop="prdNo" width="120" align="left" label="品号"></el-table-column>
                    <el-table-column prop="prdName" width="100" align="left" label="品名"></el-table-column>
                    <el-table-column prop="prdMark" width="100" align="left" label="特征"></el-table-column>
                    <el-table-column prop="qty" width="100" align="left" label="待派工量">
                      <template slot-scope="scope">
                      {{scope.row.qty}}
                      </template>
                    </el-table-column>
                    <el-table-column prop="qty1" width="150" align="left" label="待派工副数量"></el-table-column>
                    <el-table-column prop="moNo" width="130" align="left" label="工单号"></el-table-column>
                    <el-table-column prop="tzNo" width="150" align="left" label="通知单号"></el-table-column>
                    <el-table-column prop="batNo" width="150" align="left" label="批号"></el-table-column>
                    <el-table-column prop="sebNo" width="130" align="left" label="设备"></el-table-column>
                    <el-table-column prop="mdNo" width="150" align="left" label="模具"></el-table-column>
                    <el-table-column prop="moDd" width="130" align="left" label="工单日期">
                      <template slot-scope="scope">
                        {{scope.row.moDd | formatDate}}
                      </template>
                    </el-table-column>
                    <el-table-column prop="bomRem" width="150" align="left" label="材质"></el-table-column>
                    <el-table-column prop="soNo" width="130" align="left" label="受订计划" :show-overflow-tooltip="true"></el-table-column>
                    <el-table-column prop="spc" width="130" align="left" label="规格"></el-table-column>
                    <el-table-column prop="ypgQty" width="130" align="left" label="已派工量"></el-table-column>
                    <el-table-column prop="endDd" width="100" align="left" label="预交日期">
                      <template slot-scope="scope">
                        {{scope.row.endDd | formatDate}}
                    </template>
                    </el-table-column>
                    <el-table-column prop="dep" width="150" align="left" label="制造部门"></el-table-column>
                    <el-table-column prop="depName" width="150" align="left" label="部门名称"></el-table-column>
                    <el-table-column prop="tzDd" width="130" align="left" label="通知日期">
                      <template slot-scope="scope">
                        {{scope.row.tzDd | formatDate}}
                      </template>
                    </el-table-column>
                    <el-table-column prop="rem" width="150" align="left" label="备注" show-overflow-tooltip></el-table-column>
                  </el-table>
                  <div style="display: flex;justify-content: space-between;margin: 2px">
                    <el-pagination
                      background
                      :page-sizes="[5, 10,20, 30, 50]"
                      :page-size="20"
                      :pager-count="5"
                      @size-change="pageSizeChange"
                      :current-page="tablePage.currentPage"
                      @current-change="currentChange"
                      layout="total,sizes,prev, pager, next"
                      :total="totalCount"
                    ></el-pagination>
                  </div> -->
            </a-row>
            <a-row>
            </a-row>
          </a-col>
        </a-row>
      </el-tab-pane>
      <el-tab-pane label="已派工" name="three">
        <vxe-table size='small' border="inner" resizable stripe highlight-current-row show-overflow highlight-hover-row
                   export-config :loading="loading" :data="tableDatathree" :keyboard-config="{ isArrow: true }"
                   :edit-config="{ trigger: 'click', mode: 'row' }" @cell-dblclick="mfBxHanddleone" ref="multipleTable"
                   :header-cell-style="{ backgroundColor: '#F4F5F9', color: '#7A8085', fontWeight: '400', fontSize: '12px' }"
                   :cell-style="{ fontSize: '12px' }">

          <vxe-table-column type="checkbox" align="center" :width="50"></vxe-table-column>
          <vxe-table-column field="pgDd" title="派工日期" align="center" width="100">
            <template slot-scope="scope">
              {{scope.row.pgDd | formatDate}}
            </template>
          </vxe-table-column>
          <vxe-table-column field="pgNo" title="派工单号" align="center" width="140">
          </vxe-table-column>
          <vxe-table-column field="zcNo" title="工序" align="center" width="140">
            <template slot-scope="scope">
              {{scope.row.zcNo}} / {{scope.row.zcName}}
            </template>
          </vxe-table-column>
          <vxe-table-column field="prdNo" title="货品" align="center" width="140">
            <template slot-scope="scope">
              {{scope.row.prdNo}} / {{scope.row.prdName}}
            </template>
          </vxe-table-column>
          <vxe-table-column field="prdMark" title="特征" align="center" width="140"></vxe-table-column>
          <vxe-table-column field="qty" title="派工数量" align="center" width="100">
            <template slot-scope="scope">
              {{scope.row.qty}} / {{scope.row.qty1}}
            </template>
          </vxe-table-column>
          <vxe-table-column field="preStaDd" title="开工日期" align="center" width="100">
            <template slot-scope="scope">
              {{scope.row.preStaDd | formatDate}}
            </template>
          </vxe-table-column>
          <vxe-table-column field="preEndDd" title="完工日期" align="center" width="100">
            <template slot-scope="scope">
              {{scope.row.preEndDd | formatDate}}
            </template>
          </vxe-table-column>

          <vxe-table-column field="ygNo" title="作业人员" align="center" width="140"></vxe-table-column>

          <vxe-table-column field="moNo" title="工单号" align="center" width="130"></vxe-table-column>
          <vxe-table-column field="tzNo" title="工序单号" align="center" width="130"></vxe-table-column>
          <vxe-table-column field="batNo" title="批号" align="center" width="130"></vxe-table-column>

          <vxe-table-column field="qtyFin" title="已完工量" align="center" width="100"></vxe-table-column>
          <vxe-table-column field="sebNo" title="不合格量" align="center" width="100"></vxe-table-column>

          <vxe-table-column field="baNo" title="批次单号" align="center" width="140"></vxe-table-column>
          <vxe-table-column field="rem" title="备注" width="150" align="center"
                            show-overflow="tooltip"></vxe-table-column>
        </vxe-table>
        <vxe-pager :loading="loading" :current-page="tablePagethree.currentPage" :page-size="tablePagethree.pageSize"
                   :total="totalCountthree" :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
                   @page-change="handlePageChangethree">
        </vxe-pager>
        <!-- <el-table
        :data="tableDatathree"
         stripe
        highlight-current-row
        :cell-style="{ verticalAlign: 'top' }"
        :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
        style="width: 100%;"
        @row-dblclick="mfBxHanddleone"
        @selection-change="handleSelectionChangethree"
        height="580px"
        ref="multipleTable"
        row-key="mvNo"
      >
                      <el-table-column type="selection" align="center"  width="50"></el-table-column>
                      <el-table-column prop="pgDd" width="110" align="left" label="派工日期">
                       <template slot-scope="scope">
                           {{scope.row.pgDd | formatDate}}
                       </template>
                      </el-table-column>
                     <el-table-column prop="pgNo" width="140" align="left" label="派工单号"></el-table-column>
                     <el-table-column prop="zcNo" width="140" align="left" label="制程"></el-table-column>
                     <el-table-column prop="zcName" width="140" align="left" label="制程名称"></el-table-column>
                      <el-table-column prop="prdNo" width="110" align="left" label="品号"></el-table-column>
                     <el-table-column prop="prdName" width="110" align="left" label="品名"></el-table-column>
                     <el-table-column prop="prdMark" width="110" align="left" label="特征"></el-table-column>
                     <el-table-column prop="qty" width="100" align="left" label="派工数量"></el-table-column>
                     <el-table-column prop="qty1" width="100" align="left" label="派工副数量"></el-table-column>
                     <el-table-column prop="preStaDd" width="110" align="left" label="开工日期">
                      <template slot-scope="scope">
                          {{scope.row.preStaDd | formatDate}}
                      </template>
                     </el-table-column>
                     <el-table-column prop="preEndDd" width="110" align="left" label="完工日期">
                      <template slot-scope="scope">
                          {{scope.row.preEndDd | formatDate}}
                      </template>
                     </el-table-column>
                     <el-table-column prop="sebNo" width="100" align="left" label="设备"></el-table-column>
                      <el-table-column prop="ygNo" width="100" align="left" label="作业人员"></el-table-column>
                      <el-table-column prop="dep" width="150" align="left" label="制造部门"></el-table-column>
                      <el-table-column prop="depName" width="150" align="left" label="部门名称"></el-table-column>
                      <el-table-column prop="moNo" width="130" align="left" label="工单号"></el-table-column>
                <el-table-column prop="tzNo" width="150" align="left" label="通知单号"></el-table-column>
                <el-table-column prop="batNo" width="150" align="left" label="批号"></el-table-column>
                <el-table-column prop="bomRem" width="150" align="left" label="材质"></el-table-column>
                <el-table-column prop="grpNo" width="150" align="left" label="模具"></el-table-column>
                <el-table-column prop="moDd" width="130" align="left" label="工单日期">
                  <template slot-scope="scope">
                    {{scope.row.moDd | formatDate}}
                  </template>
                </el-table-column>
                <el-table-column prop="tzDd" width="130" align="left" label="通知日期">
                  <template slot-scope="scope">
                    {{scope.row.tzDd | formatDate}}
                  </template>
                </el-table-column>
                <el-table-column prop="qtyFin" width="130" align="left" label="已完工量"></el-table-column>
                <el-table-column prop="sebNo" width="130" align="left" label="不合格量"></el-table-column>
                <el-table-column prop="spc" width="130" align="left" label="规格"></el-table-column>
                <el-table-column prop="soNo" width="130" align="left" label="受订计划" :show-overflow-tooltip="true"></el-table-column>
                <el-table-column prop="cusNo" width="130" align="left" label="客户"></el-table-column>
                <el-table-column prop="baNo" width="150" align="left" label="批次单号"></el-table-column>
                <el-table-column prop="rem" width="200" align="left" label="备注" show-overflow-tooltip></el-table-column>
      </el-table>

      <div style="display: flex;justify-content: space-between;margin: 2px">
        <el-pagination
          background
          :page-sizes="[5, 10,20, 30, 50]"
          :page-size="50"
          @size-change="pageSizeChangethree"
          :current-page="tablePagethree.currentPage"
          @current-change="currentChangethree"
          layout="total,sizes,prev, pager, next"
          :total="totalCountthree"
        ></el-pagination>
      </div> -->
      </el-tab-pane>
    </el-tabs>
    <Export ref="Export" />
  </div>
</template>
<script>
import {
  pgprintState, pgdpgPage, pgypgPage, pggetCache, prdZFQty, salmquery, pgdelPg, getPoi, mfTzPage, pgdelCache, mfMoPage, basicDatazc, basicDatacustPage, pgupdCache, pgpg, basicDataprd, fetchList, getItem, start, qcxjyjPage, qrcache, qrgetCache, qrdelCache, qrgetCache2, qrupdQr, qrprintCk, qrdelQr
} from '@/api/mes/quality'
import MySelectListwo from '@/components/MySelectListwo'
import { mapGetters, mapState } from 'vuex'
import moment from 'moment'
import { first } from 'xe-utils/methods'
import Export from './export'
import QRCode from 'qrcodejs2'

export default {
  components: {
    MySelectListwo,
    Export,
  },
  data() {
    return {
      mes_quality_query: 'mes_quality_query',
      mes_quality_reset: 'mes_quality_reset',
      mes_quality_newOpen: 'mes_quality_newOpen',
      mes_quality_inspection: 'mes_quality_inspection',
      mes_quality_pause: 'mes_quality_inspection',
      mes_quality_continues: 'mes_quality_continues',
      mes_quality_task: 'mes_quality_task',
      queryParam: {},
      tableData: [],
      tableDatatwo: [],

      datatwo: '',
      datazuoye: '',
      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      tablePagetwo: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      tablePagethree: {
        currentPage: 1,
        pageSize: 50,
        total: 0
      },
      row: {},
      rowIndex: {},
      // 按钮控制
      btn: {
        newOpen: true, // 首检开工
        open: true, // 开始
        pause: true, // 暂停
        continues: true, // 继续
        report: true, // 报工
        exceptional: true, // 异常
        inspection: true, // 送检
        introspecting: true // 自检
      },
      queryParamtwo: {
        preStaDd: '',
        preEndDd: '',
        sebNo: '',
        ygNo: '',
      },
      queryParam: {
        printId: 'F',
        staDd: '',
        endDd: '',
        cusNo: '',
        pgNo: '',
        baNo: '',
        moNo: '',
        prdName: '',
        prdNo: '',
        zcNo: '',
        boxNo: '',
        dep: '',
        depName: '',
        bomRem: '',
        tzNo: '',
        soNo: '',
        cusNo: '',
        sebNo: '',
        closeId: 'F',

      },
      activeName: '1',
      multipleSelection: [],
      multipleSelectiontwo: [],
      activeNameMain: "first",
      activeNameMain2: "diyi",
      radioSelection: null,
      radio: '',
      totalCount: -1,
      data: null,
      tableDatathree: [],
      tablePagethree: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      curidList: [],
      isPrint: false,
      dataprint: '',
      printObj2: {
        id: "",
      },
      isPrintButton: true,
      qtynumzong: 0,
      qtynum1zong: 0,
      totalCounttwo: -1,
      totalCountsai: -1,
      totalCountthree: -1,
      totalCountfour: -1,
      multipleSelectionthree: [],
      tableDatasaixuan: [],
      tablePagesai: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      visiblejiaojie: false,
      visibleyijiao: false,
      handover: {},
      visiblesure: false,
      datazhi: '',
      stateList: [
        {
          id: 'F', name: '未结案'
        },
        {
          id: 'T', name: '已结案'
        },
        {
          id: null, name: '全部'
        },
      ],
      printList: [
        {
          id: '', name: '全部'
        },
        {
          id: 'T', name: '已打印'
        },
        {
          id: 'F', name: '未打印'
        },
      ],

      curperson: '',
      quantitymodyvisible: false,
      totalnumber: 1,
      handoverqty: '',
      handoverqty0: '',
      handoverqty1: '',
      handoverqty2: '',
      handoverqty3: '',
      handoverqty4: '',
      handoverqty5: '',
      handoverqty6: '',
      handovernumber: [],
      visiblepici: false,
      datatwo: '',
      prdNoList: [],
      visible: false,
      tablePagefour: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      huanvisible: false,
      tableDatatwohuan: [],
      tablePagezhi: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      totalCounzhi: -1,
      visibletwo: false,
      zcNoList: [],
      visiblechuhuan: false,
      tablechaifen: [],
      poiQty: '',
      poiQty1: '',
      visiblekehu: false,
      visibledan: false,
      tablePagekehu: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      zcNoListkehu: [],
      queryParamkehu: {
        name: ''
      },
      totalCounkehu: -1,
      zcNoListdan: [],
      totalCoundan: -1,
      tablePagedan: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      tablePagetong: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      tablePagezuoye: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      zcNoListtong: [],
      zcNoListzuoye: [],
      totalCountong: -1,
      totalCounzuoye: -1,
      visibletong: false,
      visiblezuoye: false,
      cansplit: '',
      todaydata: ''
    }
  },

  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    }),
    ...mapGetters(['permissions'])
  },
  created() {
    if (this.activeNameMain == 'three') {
      this.$nextTick(() => {
        this.$refs.submission.focus()
      })
    }
    if (this.$route.query.tab) {
      this.queryParam = JSON.parse(localStorage.getItem('pgcx'))
      this.data = this.queryParam.dep + '-' + this.queryParam.depName
      this.activeNameMain = this.$route.query.tab
    } else {
      this.queryParam.staDd = moment().subtract(7, "days").format('YYYY-MM-DD')
      this.queryParam.endDd = moment(new Date()).format('YYYY-MM-DD')
      this.queryParam.printId = 'F'

    }
    this.queryonetwo()
  },
  mounted() {
    this.zcNokehu()
    this.zcNodan()
    this.curperson = this.$store.state.user.info.name
    getPoi().then((res) => {
      if (res.code === 0) {
        this.poiQty = res.data.poiQty
        this.poiQty1 = res.data.poiQty1
        console.log(res, 'gghjkl')
      }
    }).catch(err => this.requestFailed(err))
    if (this.activeNameMain == 'three') { } else {
      pggetCache({ usr: this.curperson }).then((res) => {
        if (res.code === 0) {
          if (res.data.length > 0) {
            this.huanvisible = true
            // this.tableDatatwo = res.data
            // for(let j = 0; j <this.tableDatatwo.length; j++){
            //   this.tableDatatwo[j].itm = j + 1
            //   this.tableDatatwo[j].qtytwo =this.tableDatatwo[j].qty
            //   this.tableDatatwo[j].qtytwo1 =this.tableDatatwo[j].qty1
            //   this.tableDatatwo[j].initial = 1
            //   this.tableDatatwo[j].flag =false
            // }
          }
        }
      }).catch(err => this.requestFailed(err))
    }
    window.addEventListener('keydown', this.handleKeyDown);
  },
  methods: {
    pageSizeChangezuoye(pageSize) {
      this.tablePagezuoye.pageSize = pageSize;
      this.zcNozuoye()
    },
    currentChangezuoye(currentPage) {
      this.tablePagezuoye.currentPage = currentPage;
      this.zcNozuoye()
    },
    zsMczuoye() {
      this.tablePagezuoye.currentPage = 1
      this.zcNozuoye()
    },
    zsMczuoyeinput() {
      this.tablePagezuoye.currentPage = 1
      this.zcNozuoye()
    },
    handdletwozuoye(row, event, column) {
      this.queryParamtwo.ygNo = row.name

      // this.queryParam.moNo = row.moNo
      // this.queryParam.tzNo = row.tzNo
      // this.queryParam.bilNo = row.bilNo
      // this.queryParam.bilId = row.bilId
      // this.queryParam.closeId = row.closeId

      // this.queryParam.name = row.name
      // this.condition.prdMark = row.prdMark
      this.visiblezuoye = false;
    },
    zcNozuoye() {
      salmquery(
        {
          current: this.tablePagezuoye.currentPage,
          size: this.tablePagezuoye.pageSize,
          // moNo: this.queryParam.moNo,
          //           salNo 员工代号
          // // name名称
          // dep部门
        }).then(response => {
        // this.prdloading = false
        this.zcNoListzuoye = response.data.records
        // this.$nextTick(()=> {
        //   this.$refs.dcPopovertwo.updatePopper() // 注意主要是这里
        // })

        this.totalCounzuoye = response.data.total
        // this.tablePagetwo.currentPage = response.data.current

      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },

    selectcheck(val, id, type) {
      if (val === true) {
        this[type] = this[type].filter(function (item) {
          return item !== id
        })
      }
    },
    pageSizeChangetong(pageSize) {
      this.tablePagetong.pageSize = pageSize;
      this.zcNotong()
    },
    currentChangetong(currentPage) {
      ;
      this.tablePagetong.currentPage = currentPage;
      this.zcNotong()
    },
    zsMctong() {
      this.tablePagetong.currentPage = 1
      this.zcNotong()
    },
    zsMctonginput() {
      this.tablePagetong.currentPage = 1
      this.zcNotong()
    },
    handdletwotong(row, event, column) {
      this.queryParam.tzNo = row.tzNo
      // this.queryParam.bilNo = row.bilNo
      // this.queryParam.bilId = row.bilId
      this.queryParam.closeId = row.closeId
      // this.queryParam.name = row.name
      // this.condition.prdMark = row.prdMark
      this.visibletong = false;
    },
    zcNotong() {
      mfTzPage(
        {
          current: this.tablePagetong.currentPage,
          size: this.tablePagetong.pageSize,
          tzNo: this.queryParam.tzNo,
          staDd: this.queryParam.staDd,
          endDd: this.queryParam.endDd
        }).then(response => {
        // this.prdloading = false
        this.zcNoListtong = response.data.records
        // this.$nextTick(()=> {
        //   this.$refs.dcPopovertwo.updatePopper() // 注意主要是这里
        // })

        this.totalCountong = response.data.total
        // this.tablePagetwo.currentPage = response.data.current

      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    pageSizeChangedan(pageSize) {
      this.tablePagedan.pageSize = pageSize;
      this.zcNodan()
    },
    currentChangedan(currentPage) {
      this.tablePagedan.currentPage = currentPage;
      this.zcNodan()
    },
    zsMcdan() {
      this.tablePagedan.currentPage = 1
      this.zcNodan()
    },
    zsMcdaninput() {
      this.tablePagedan.currentPage = 1
      this.zcNodan()
    },
    handdletwodan(row, event, column) {
      this.queryParam.moNo = row.moNo
      this.queryParam.tzNo = row.tzNo
      // this.queryParam.bilNo = row.bilNo
      // this.queryParam.bilId = row.bilId
      this.queryParam.closeId = row.closeId

      // this.queryParam.name = row.name
      // this.condition.prdMark = row.prdMark
      this.visibledan = false;
    },
    zcNodan() {
      mfMoPage(
        {
          current: this.tablePagedan.currentPage,
          size: this.tablePagedan.pageSize,
          moNo: this.queryParam.moNo,
          staDd: this.queryParam.staDd,
          endDd: this.queryParam.endDd
        }).then(response => {
        // this.prdloading = false
        this.zcNoListdan = response.data.records
        // this.$nextTick(()=> {
        //   this.$refs.dcPopovertwo.updatePopper() // 注意主要是这里
        // })

        this.totalCoundan = response.data.total
        // this.tablePagetwo.currentPage = response.data.current

      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    pageSizeChangekehu(pageSize) {
      this.tablePagekehu.pageSize = pageSize;
      this.zcNokehu()
    },
    currentChangekehu(currentPage) {
      ;
      this.tablePagekehu.currentPage = currentPage;
      this.zcNokehu()
    },
    zsMckehu() {
      this.tablePagekehu.currentPage = 1
      this.zcNokehu()
    },
    zsMckehuinput() {
      this.tablePagekehu.currentPage = 1
      this.zcNokehu()
    },
    handdletwokehu(row, event, column) {
      this.queryParam.cusNo = row.cusNo
      this.queryParamkehu.name = row.name
      // this.condition.prdMark = row.prdMark
      this.visiblekehu = false;
    },
    zcNokehu() {
      basicDatacustPage(
        {
          current: this.tablePagekehu.currentPage,
          size: this.tablePagekehu.pageSize,
          // zcNo: this.queryParamkehu.cusNo,
          name: this.queryParamkehu.name,
        }).then(response => {
        // this.prdloading = false
        this.zcNoListkehu = response.data.records
        // this.$nextTick(()=> {
        //   this.$refs.dcPopovertwo.updatePopper() // 注意主要是这里
        // })

        this.totalCounkehu = response.data.total
        // this.tablePagetwo.currentPage = response.data.current

      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    pageSizeChangezhi(pageSize) {
      this.tablePagezhi.pageSize = pageSize;
      this.zcNomethod()
    },
    currentChangezhi(currentPage) {
      this.tablePagezhi.currentPage = currentPage;
      this.zcNomethod()
    },
    zsMctwo() {
      this.tablePagezhi.currentPage = 1
      this.zcNomethod()
    },
    zsMctwoinput() {
      this.tablePagezhi.currentPage = 1
      this.zcNomethod()
    },
    handdletwo(row, event, column) {
      this.queryParam.zcNo = row.zcNo
      this.queryParam.zcName = row.name
      // this.condition.prdMark = row.prdMark
      this.visibletwo = false;
    },
    zcNomethod() {
      basicDatazc(
        {
          current: this.tablePagezhi.currentPage,
          size: this.tablePagezhi.pageSize,
          zcNo: this.queryParam.zcNo,
        }).then(response => {
        // this.prdloading = false
        this.zcNoList = response.data.records
        // this.$nextTick(()=> {
        //   this.$refs.dcPopovertwo.updatePopper() // 注意主要是这里
        // })

        this.totalCounzhi = response.data.total
        // this.tablePagetwo.currentPage = response.data.current

      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    huandel() {
      pggetCache({ usr: this.curperson }).then((res) => {
        if (res.code === 0) {
          if (res.data.length > 0) {
            let arryList = []
            res.data.forEach(item => {
              let currlist = { tzNo: item.tzNo, itm: item.itm }
              arryList.push(currlist)
            })
            pgdelCache(arryList).then((res) => {
              if (res) {
                if (res.msg == "success") {
                  this.$message.warning('缓存清空')
                  this.huanvisible = false
                }
              }
            }).catch(err => this.requestFailed(err))
              .finally(() => {
                this.loading = false
                // that.visiblesure = false
              })
          }
        }
      })
    },
    huansure() {
      this.visiblechuhuan = true
      pggetCache({ usr: this.curperson }).then((res) => {
        if (res.code === 0) {
          if (res.data.length > 0) {
            this.tableDatatwohuan = res.data
            for (let j = 0; j < this.tableDatatwohuan.length; j++) {
              // this.tableDatatwohuan[j].itm = j + 1
              this.tableDatatwohuan[j].qtytwo = this.tableDatatwohuan[j].qty
              this.tableDatatwohuan[j].qtytwo1 = this.tableDatatwohuan[j].qty1
              this.tableDatatwohuan[j].qtythree = this.tableDatatwohuan[j].qty
              this.tableDatatwohuan[j].qtythree1 = this.tableDatatwohuan[j].qty1
              this.tableDatatwohuan[j].initial = 1
              this.tableDatatwohuan[j].flag = false
            }
            this.huanvisible = false
            this.tableDatatwo = this.tableDatatwohuan
          }
        }
      }).catch(err => this.requestFailed(err))
    },
    handleEmpty(val) {
      this.queryParam.prdNo = ''
      this.queryParam.prdName = ''
    },
    prdNoListmethod() {
      this.loading = false
      basicDataprd(
        {
          current: this.tablePagefour.currentPage,
          size: this.tablePagefour.pageSize,
          prdNo: this.queryParam.prdNo,
        }).then(response => {
        // this.prdloading = false
        this.prdNoList = response.data.records
        this.$nextTick(() => {
          this.$refs.dcPopover.updatePopper() // 注意主要是这里
        })

        this.totalCountfour = response.data.total
        // this.tablePagetwo.currentPage = response.data.current

      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    pageSizeChangefour(pageSize) {
      this.tablePagefour.pageSize = pageSize;
      // this.getListdialog();
      this.prdNoListmethod()
    },
    currentChangefour(currentPage) {
      // this.visible = true;
      this.tablePagefour.currentPage = currentPage;
      // this.getListdialog();
      this.prdNoListmethod()
    },
    handdle(row, event, column) {
      this.queryParam.prdNo = row.prdNo
      this.queryParam.prdName = row.name
      this.queryParam.prdMark = row.prdMark
      this.visible = false;
    },
    zsMc() {
      this.tablePagefour.currentPage = 1
      this.prdNoListmethod()
    },
    zsMcinput() {
      this.tablePagefour.currentPage = 1
      this.prdNoListmethod()
    },
    tableRowClassName({ row, rowIndex }) {
      row.row_index = rowIndex;
    },
    numberpai(val) {
    },
    numberpaifu(val) {
    },
    paigong(val) {
      this.handovernumber = []
      this.totalnumber = +val
      console.log(val, 'bbbbbbbbbbb')
      for (let j = 0; j < this.totalnumber; j++) {
        let curidarry = { label: 'handover' + j, value: 'handoverfu' + j }
        this.handovernumber.push(curidarry)
      }
      console.log(this.handovernumber, 'rrrrrrrrrrrr')
      this.$forceUpdate()
      for (let j = 0; j < this.totalnumber; j++) {

      }
    },
    handleqtychai(scope) {
      if (+scope.row.qty > +this.cansplit) {
        this.$message.warning('每条数据不能大于可拆分量')
      } else {
        if (+scope.row.qty1 > 0) {
          prdZFQty({
            prdNo: scope.row.prdNo,
            type: 'z',
            qty: scope.row.qty
          }).then((res) => {
            if (res) {
              // for(let j = 0; j <this.tableDatatwo.length; j++){
              //   this.tableDatatwo[j]
              scope.row.qty1 = res.data.qty1
              // }
              //
            }
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        }
      }
    },
    handleqtychaifu(scope) {
    },
    chaifen() {
      let tablechaihoutwo = []
      for (let j = 0; j < this.totalnumber; j++) {
        tablechaihoutwo.push(this.tablechaifen[0])
      }
      let pgNoval
      pgNoval = JSON.parse(JSON.stringify(this.tablechaifen[0].pgNo))
      let qtyFinval
      qtyFinval = JSON.parse(JSON.stringify(this.tablechaifen[0].qtyFin))
      for (var j = 0; j < tablechaihoutwo.length; j++) {
        tablechaihoutwo[j].pgNo = ''
      }
      // let fieldToClear = 'pgNo';
      // tablechaihoutwo = tablechaihoutwo.map((obj,index) => {
      //   if(index==0){
      //     return obj
      //   }
      //   // obj[fieldToClear] = '';
      //   return { ...obj};
      //     // obj[fieldToClear] = '';
      //     // return obj;
      // });
      //     let updatedArray = complexArray.map(item => {
      // // 使用对象解构来复制原对象，并将name属性置为空字符串
      //       return { ...item, name: '' };
      //     });
      let zongliang
      if (this.tablechaifen[0].qtyFin) {
        zongliang = this.tablechaifen[0].qtytwo - +this.tablechaifen[0].qtyFin
      } else {
        zongliang = this.tablechaifen[0].qtytwo
      }
      let tablechaihou = JSON.parse(JSON.stringify(tablechaihoutwo))
      let zhengshu = parseFloat((zongliang / this.totalnumber).toFixed(this.poiQty))
      let zhengshufu = parseFloat((this.tablechaifen[0].qtytwo1 / this.totalnumber).toFixed(this.poiQty1))
      console.log(zhengshu, 'ggggggggg111')
      // console.log(zhengshu,'bggggggggggg')
      let yushu = zongliang % this.totalnumber;
      let yushufu = this.tablechaifen[0].qtytwo1 % this.totalnumber;
      console.log(yushu, '111dffffffffff')
      for (let j = 0; j < tablechaihou.length; j++) {
        tablechaihou[j].qty = zhengshu.toFixed(this.poiQty)
        tablechaihou[j].qty1 = zhengshufu.toFixed(this.poiQty1)
        if (yushu > 0) {
          if (j == tablechaihou.length - 1) {
            console.log(zongliang, zhengshu * j, '1222222')
            let beijian = (zhengshu * j * 100).toFixed(this.poiQty)

            tablechaihou[j].qty = ((zongliang * 100 - beijian) / 100).toFixed(this.poiQty)
            console.log(tablechaihou[j].qty, 'ggggggggg')
          }
        }
        if (yushufu > 0) {
          if (j == tablechaihou.length - 1) {
            let beijian1 = (zhengshufu * j * 100).toFixed(this.poiQty1)
            tablechaihou[j].qty1 = ((this.tablechaifen[0].qtytwo1 * 100 - beijian1) / 100).toFixed(this.poiQty1)
          }
        }
      }
      // 第一条派工数量加上已完工量

      if (tablechaihou[0].qtyFin) {
        tablechaihou[0].qty = +tablechaihou[0].qty + +tablechaihou[0].qtyFin
        console.log(tablechaihou[0].qty, 'jkoi')
      }
      for (var j = 0; j < tablechaihou.length; j++) {
        tablechaihou[j].qtyFin = ''
      }
      this.tablechaifen = tablechaihou

      this.tablechaifen[0].pgNo = pgNoval
      this.tablechaifen[0].qtyFin = qtyFinval

      // for(let j = 0; j <this.handovernumber.length; j++){

      //   this[this.handovernumber[j].label] = zhengshu
      //   this.$forceUpdate()
      //  console.log(this[this.handovernumber[j].label],'ccccccccccc')
      //   // if(j=this.handovernumber-1){

      //   // }
      // }
    },
    cancelpaigong() {
      this.visiblejiaojie = false
      this.tableDatatwo = []
      this.multipleSelectiontwo = []
      this.multipleSelection = []
      this.$refs.multipleTable.clearCheckboxRow();
      // this.$refs.tableone.clearSelection()
    },
    cancelpaigonghuan() {

      this.visiblechuhuan = false
      this.tableDatatwo = []
      this.multipleSelectiontwo = []
      this.multipleSelection = []
      this.$refs.xTable.clearCheckboxRow();
      this.$refs.multipleTable.clearCheckboxRow();

      // this.$refs.tableone.clearSelection()
    },
    stopScroll(evt) {
      evt = evt || window.event;
      if (evt.preventDefault) {
        // Firefox
        evt.preventDefault();
        evt.stopPropagation();
      } else {
        // IE
        evt.cancelBubble = true;
        evt.returnValue = false;
      }
      return false;
    },
    sureyijioa() {
      qrupdQr({
        ...this.handover
      }).then((res) => {
        if (res.code === 0) {
          if (res.msg === 'success') {
            //  this.queryParam.bilNo = res.data
            //   this.getListthree()
            //   this.activeNameMain = 'three'
            //   this.handleClick('three')
            this.$message.success('保存成功')
            this.visibleyijiao = false
            // this.activeNameMain2 = 'dier'
            // this.handleClick2('dier')
          } else {
            this.$message.error(res.data)
            return
          }

        }
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 导出
    // current: this.tablePagethree.currentPage,
    //       // current: this.tablePage.currentPage,
    //       size: this.tablePagethree.pageSize,
    //       ...this.queryParam,
    dropdownMenuEvent() {
      if (this.tableDatathree.length === 0) return this.$message.warning('请先查询后再导出！')
      let obj = {
        queryParam: this.queryParam,
        // bjStartDd: this.bjStartDd,
        // bjEndDd: this.bjEndDd,
        total: this.totalCountthree,
      }
      this.$refs.Export.create(obj)
    },
    printClose() {
      this.isPrint = false
    },

    printjiekou() {
      if (this.queryParam.dep1 && this.queryParam.dep2) {
        if (this.multipleSelectionthree.length > 0) {
          let arrythree = []
          this.multipleSelectionthree.forEach(item => {
            arrythree.push(item.qrNo)
          })
          qrprintCk(arrythree).then((res) => {
            if (res.code === 0) {
              this.dataprint = res.data
              for (var f = 0; f < this.dataprint.length; f++) {
                if (this.dataprint[f].qty) {
                  this.dataprint[f].qty = this.dataprint[f].qty / 1000
                  this.dataprint[f].qty = this.dataprint[f].qty.toFixed(2)
                }
              }

              this.print()
              // that.visiblesure = false
              // if (res.msg === 'success') {

              // } else {
              //   this.$message.error(res.data)
              //   return
              // }
            }
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
              // this.visiblesure = false
            })
        } else {
          this.$message.warning('请选择一条数据')
        }
      } else {
        this.$message.warning('本道部门和下道部门必填')
      }
    },
    pgprint() {
      this.dataprint = this.$refs.multipleTable.getCheckboxRecords()
      this.todaydata = moment(new Date()).format('YYYY-MM-DD')
      if (this.dataprint.length > 0) {
        this.printObj2.id = 'form1'
        this.curidList = []
        for (var d = 0; d < this.dataprint.length; d = d + 20) {
          let qtynum = 0
          let qty1num = 0
          // for (var f = d; f < this.dataprint.length; f++) {
          //   if (f < d + 20) {
          //     // qtynum += +this.dataprint[f].qty
          //     // qty1num += +this.dataprint[f].qty1
          //   }
          // }
          // qtynum = qtynum.toFixed(2)
          // qty1num = qty1num.toFixed(2)
          // this.dataprint[]
          // let amtnTotal2 = 0
          // let qtyTotal2 = 0
          // for (var f = d; f < this.multipleSelectiontwo.length; f++) {
          //   if (f < d + 10) {
          //     let amtn2 = +this.data[f].qtyBj * +this.data[f].up
          //     this.data[f].amtn = this.toDecimal2(amtn2)
          //     amtnTotal2 += +this.data[f].amtn
          //     qtyTotal2 += +this.data[f].qtyBj
          //   }
          // }
          // amtnTotal2 = amtnTotal2.toFixed(2)
          // qtyTotal2 = qtyTotal2.toFixed(2)

          let curidarry = {}
          // value: amtnTotal2, qty: qtyTotal2
          curidarry = { label: d / 20, dataprint: [], qtynum: qtynum, qty1num: qty1num, }
          for (var f = d; f < this.dataprint.length; f++) {
            if (f < d + 20) {
              curidarry.dataprint.push(this.dataprint[f])
              // qtynum += +this.dataprint[f].qty
              // qty1num += +this.dataprint[f].qty1
            }
          }
          this.curidList.push(curidarry)
          console.log(this.curidList, 'bnnnnnnnnnnn')
        }

        this.toperwei()
      } else {
        this.$message.warning('请选择数据')
      }

    },
    toperwei() {
      // this.bodyObj2.forEach((item, index) => {
      this.$nextTick(() => {
        for (let i = 0; i < this.curidList.length; i++) {
          for (let j = 0; j < this.curidList[i].dataprint.length; j++) {
            // for (let i = 0; i < this.dataprint[j][0].curidList.length; i++) {
            const dom8 = 'yuxin' + i + j
            // const dom10 = 'yuxinright' + this.dataprint[j].addid + i
            if (document.getElementById(dom8).innerHTML === null || document.getElementById(dom8).innerHTML === '') {
            } else {
              document.getElementById(dom8).innerHTML = ''
            }
            // if (document.getElementById(dom10).innerHTML === null || document.getElementById(dom10).innerHTML === '') {
            // } else {
            //   document.getElementById(dom10).innerHTML = ''
            // }
            // }
          }
        }
      })
      this.$nextTick(() => {
        console.log(this.dataprint, 'gyyyyyyyyyyyy')
        for (let i = 0; i < this.curidList.length; i++) {
          for (let j = 0; j < this.curidList[i].dataprint.length; j++) {

            // for (let i = 0; i < this.dataprint[j][0].curidList.length; i++) {
            new QRCode('yuxin' + i + j, {
              width: 85,
              height: 85,
              text: this.curidList[i].dataprint[j].pgNo // 二维码地址
            })
            // new QRCode('yuxinright' + this.dataprint[j][0].addid + i, {
            // width: 42,
            // height: 42,
            // text: this.dataprint[j][0].prdName // 二维码地址
            // })
            // }
          }
        }
        this.isPrint = true
        this.isPrintButton = false
      })
    },
    print() {
      // let multipleSelect
      // if(val=="second"){
      //   multipleSelect = this.multipleSelectiontwo
      // }
      // else{
      //   multipleSelect = this.multipleSelectionthree
      // }
      // if(this.multipleSelectionthree.length>0){
      // this.dataprint = this.multipleSelectionthree
      console.log(this.dataprint, 'mmmmmjjjjj')
      this.printObj2.id = 'form1'
      this.curidList = []
      // for (var j = 0; j < this.Len; j++) {
      this.qtynumzong = 0
      this.qtynum1zong = 0
      for (var d = 0; d < this.dataprint.length; d = d + 20) {
        let qtynum = 0
        let qty1num = 0
        for (var f = d; f < this.dataprint.length; f++) {
          if (f < d + 20) {
            qtynum += +this.dataprint[f].qty
            qty1num += +this.dataprint[f].qty1
          }
        }
        qtynum = qtynum.toFixed(2)
        qty1num = qty1num.toFixed(2)
        // this.dataprint[]
        // let amtnTotal2 = 0
        // let qtyTotal2 = 0
        // for (var f = d; f < this.multipleSelectiontwo.length; f++) {
        //   if (f < d + 10) {
        //     let amtn2 = +this.data[f].qtyBj * +this.data[f].up
        //     this.data[f].amtn = this.toDecimal2(amtn2)
        //     amtnTotal2 += +this.data[f].amtn
        //     qtyTotal2 += +this.data[f].qtyBj
        //   }
        // }
        // amtnTotal2 = amtnTotal2.toFixed(2)
        // qtyTotal2 = qtyTotal2.toFixed(2)

        let curidarry = {}
        // value: amtnTotal2, qty: qtyTotal2
        curidarry = { label: d / 20, qtynum: qtynum, qty1num: qty1num, }
        this.curidList.push(curidarry)
      }
      console.log(this.curidList, 'nnnnnnnnn')
      for (var i = 0; i < this.dataprint.length; i = i + 1) {
        this.qtynumzong += +this.dataprint[i].qty
        this.qtynum1zong += +this.dataprint[i].qty1
      }
      if (this.qtynumzong) {
        this.qtynumzong = this.qtynumzong.toFixed(2)
      }
      if (this.qtynum1zong) {
        this.qtynum1zong = this.qtynum1zong.toFixed(2)
      }
      this.isPrint = true
      this.isPrintButton = false

      // }
    },
    printSure() {
      this.isPrint = false
      let multidata = this.$refs.multipleTable.getCheckboxRecords()
      let multipgNo = []
      multidata.forEach(item => {
        multipgNo.push(item.pgNo)
      })
      pgprintState({
        pgNos: multipgNo
      }).then((res) => {
        this.getListthree('')
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },

    cleardelthree() {
      this.multipleSelectionthree = this.$refs.multipleTable.getCheckboxRecords()
      if (this.multipleSelectionthree.length > 0) {
        let arryList = []
        this.multipleSelectionthree.forEach(item => {
          let currlist = { pgNo: item.pgNo }
          arryList.push(currlist)
        })
        const that = this
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('public.del.content'),
          okText: this.$t('public.sure'),
          okType: 'danger',
          cancelText: this.$t('public.cancel'),
          onOk() {
            pgdelPg(arryList).then((res) => {
              if (res) {
                if (res.msg == "success") {
                  that.$message.warning('删除成功')
                  that.getListthree('')
                } else {
                  that.$message.warning('删除失败')
                }
              }
            }).catch(err => that.requestFailed(err))
              .finally(() => {
                that.loading = false
                // that.visiblesure = false
              })
          },
          onCancel() {
            that.loading = false
          }
        })
      } else {
        this.$message.warning('请选择数据')
      }
    },
    cleardeltwo() {
      if (this.multipleSelectiontwo.length > 0) {
        const that = this
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('public.del.content'),
          okText: this.$t('public.sure'),
          okType: 'danger',
          cancelText: this.$t('public.cancel'),
          onOk() {
            that.loading = true
            pgdelPg({ pgNo: that.multipleSelectiontwo[0].pgNo }).then((res) => {
              if (res) {
                if (res.msg == "success") {
                  that.$message.warning('删除成功')
                  that.visiblejiaojie = false
                  that.multipleSelectiontwo = []
                  that.getListthree('')
                } else {
                  that.$message.warning('删除失败')
                }
              }
            }).catch(err => that.requestFailed(err))
              .finally(() => {
                that.loading = false
                // that.visiblesure = false
              })
          },
          onCancel() {
            that.loading = false
          }
        })
      } else {
        this.$message.warning('请选择数据')
      }
    },
    cleardel() {

      if (this.multipleSelectiontwo.length > 0) {
        let arryList = []
        this.multipleSelectiontwo.forEach(item => {
          let currlist = { tzNo: item.tzNo, itm: item.itm }
          arryList.push(currlist)
        })
        pgdelCache(arryList).then((res) => {
          if (res) {
            if (res.msg == "success") {
              this.$message.warning('删除成功')
              pggetCache({ usr: this.curperson }).then((res) => {
                if (res.code === 0) {
                  if (res.data.length > 0) {
                    this.tableDatatwohuan = res.data
                    for (let j = 0; j < this.tableDatatwohuan.length; j++) {
                      this.tableDatatwohuan[j].itm = j + 1
                      this.tableDatatwohuan[j].qtytwo = this.tableDatatwohuan[j].qty
                      this.tableDatatwohuan[j].qtytwo1 = this.tableDatatwohuan[j].qty1
                      this.tableDatatwohuan[j].initial = 1
                      this.tableDatatwohuan[j].flag = false
                    }
                    this.tableDatatwo = this.tableDatatwohuan
                  } else {

                    this.tableDatatwo = []
                    this.visiblechuhuan = false
                  }
                }
              }).catch(err => this.requestFailed(err))
            }
          }
        }).catch(err => this.requestFailed(err))
          .finally(() => {
            this.loading = false
            // that.visiblesure = false
          })
        // this.visiblesure = true
      } else {
        this.$message.warning('请选择一条数据')
      }
    },
    delshan() {

      if (this.multipleSelectionthree.length == 1) {
        // let arry =[]
        // this.multipleSelectionthree.forEach(item=>{
        //   arry.push(item.qrNo)
        // })
        const that = this
        qrdelQr({ qrNo: that.multipleSelectionthree[0].qrNo }).then((res) => {
          if (res.code === 0) {
            // that.visiblesure = false
            if (res.msg === 'success') {
              // that.$refs.table2.clearSelection()
              that.getListthree('')
              that.$message.success('清除成功')
            } else {
              // that.$refs.table2.clearSelection()
              that.$message.error(res.data)
              return
            }
          }
        }).catch(err => that.requestFailed(err))
          .finally(() => {
            that.loading = false
            // that.visiblesure = false
          })
      } else {
        this.$message.warning('只能选择一条数据删除')
      }
    },
    clearsure() {
      // if(this.multipleSelectiontwo.length>0){
      let arry = []
      this.multipleSelectiontwo.forEach(item => {
        arry.push(item.mvNo)
      })
      const that = this
      qrdelCache(arry).then((res) => {
        if (res.code === 0) {
          that.visiblesure = false
          if (res.msg === 'success') {
            that.$refs.table2.clearSelection()
            that.getListtwo()
            that.$message.success('清除成功')
          } else {
            that.$refs.table2.clearSelection()
            that.$message.error(res.data)
            return
          }
        }
      }).catch(err => that.requestFailed(err))
        .finally(() => {
          that.loading = false
          that.visiblesure = false
        })





      //   const that = this
      //   this.$confirm({
      //     title: this.$t('public.del.title'),
      //     content: '确认清除所选内容吗?',
      //     okText: this.$t('public.sure'),
      //     okType: 'warn',
      //     cancelText: this.$t('public.cancel'),
      //   onOk() {
      //     qrdelCache(arry).then((res) => {
      //     if (res.code === 0) {
      //           if (res.msg === 'success') {
      //             that.getListtwo()
      //             that.$message.success('清除成功')
      //           } else {
      //             that.$message.error(res.data)
      //             return
      //           }
      //     }
      //   }).catch(err => that.requestFailed(err))
      //       .finally(() => {
      //         that.loading = false
      // })
      //   },
      //   onCancel() {}
      // })
      // }
      // else{
      //     this.$message.warning('请选择一条数据')
      // }
    },

    savezan() {
      if (this.multipleSelectiontwo.length > 0) {
        let flagling = true
        let flagxiao = true
        // for(let j = 0; j <this.multipleSelectiontwo.length; j++){
        //   this.multipleSelectiontwo[j].itm = j + 1
        // }
        let tablechaihou = JSON.parse(JSON.stringify(this.multipleSelectiontwo))
        for (let j = 0; j < tablechaihou.length; j++) {
          if (tablechaihou[j].qty == 0 || tablechaihou[j].qty == '') {
            flagling = false
            break
          }
          if (+tablechaihou[j].qty < +tablechaihou[j].qtyFin) {
            flagxiao = false
            break
          }
          if (tablechaihou[j].isBaNo === true) {
            tablechaihou[j].isBaNo = 1
          } else {
            tablechaihou[j].isBaNo = 0
          }
        }
        if (flagling) {
          if (flagxiao) {
            pgupdCache(tablechaihou).then((res) => {
              if (res.code === 0) {
                if (res.msg === 'success') {
                  this.$refs.table2.clearSelection()
                  this.$message.success(res.data)
                } else {
                  this.$refs.table2.clearSelection()
                  this.$message.error(res.data)
                  return
                }

              }
            }).catch(err => this.requestFailed(err))
              .finally(() => {
                this.loading = false
              })
          } else {
            this.$message.warning('派工数量不能小于已完工量')
          }
        } else {
          this.$message.warning('派工数量不能为0或者空')
        }
      } else {
        this.$message.warning('请选择一条数据')
      }
    },
    handlebao(scope) {
      if (+scope.row.pakExc > 0) {
        scope.row.qty = +scope.row.pakExc * +scope.row.pk3Unit
      }
      if (scope.row.pk3Unit == '') {
        scope.row.qty = scope.row.qtytwo
      }
    },
    handleqty(scope) {

      if (scope.row.qty <= scope.row.qtythree) {
        //  if(scope.row.qty >= scope.row.qtyFin){
        //  }else{
        //   this.$message.warning('派工数量不能小于已完工量')
        //   return
        //  }
        scope.row.qtytwo = scope.row.qty
        if (+scope.row.qty1 > 0) {
          prdZFQty({
            prdNo: scope.row.prdNo,
            type: 'z',
            qty: scope.row.qty
          }).then((res) => {
            if (res) {
              // for(let j = 0; j <this.tableDatatwo.length; j++){
              //   this.tableDatatwo[j]
              scope.row.qty1 = res.data.qty1
              // }
              //
            }
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        }
      } else {
        scope.row.qty = ''
        // scope.row.qty = scope.row.qtytwo
        this.$message.warning('派工数量不能大于派工数量总值')
      }
      console.log(scope.row.qty, '111ggggggggggg')
      // if()
      // if(scope.row.initial == 1){
      //   for(let j = 0; j <this.tableDatatwo.length; j++){
      //     if(this.tableDatatwo[j].tzNo == scope.row.tzNo){
      //       if(this.tableDatatwo[j].initial == 2){
      //         this.tableDatatwo[j].qty = scope.row.qtytwo - scope.row.qty
      //       }
      //     }
      //   }
      // }
      // if(scope.row.initial == 2){
      //   for(let i = 0; i <this.tableDatatwo.length; i++){
      //     if(this.tableDatatwo[i].tzNo == scope.row.tzNo){
      //       if(this.tableDatatwo[i].initial == 1){
      //         this.tableDatatwo[i].qty = scope.row.qtytwo - scope.row.qty
      //       }
      //     }
      //   }
      // }
      // console.log(scope,'ttttttttttttt')
      // for(let j = 0; j <this.tableDatatwo.length; j++){
      //   if(this.tableDatatwo[j].tzNo == scope.row.tzNo){
      //     if(this.tableDatatwo[j].flag == false){
      //       this.tableDatatwo[j+1].qty = scope.row.qtytwo - scope.row.qty
      //     }else{
      //       this.tableDatatwo[j-1].qty = scope.row.qtytwo - scope.row.qty
      //     }
      //   }
      //   // if(this.tableDatatwo[j].tzNo == scope.row.tzNo && this.tableDatatwo[j].flag == true){
      //   //   this.tableDatatwo[j-1].qty = scope.row.qtytwo - scope.row.qty
      //   // }
      // }
    },
    handleqtyfu(scope) {
      console.log(scope, 'ggggggggggg')
      if (scope.row.qty1 <= scope.row.qtythree1) { } else {
        scope.row.qty1 = ''
        // scope.row.qty1 = scope.row.qtytwo1
        this.$message.warning('派工副数量不能大于派工副数量总值')
      }
      // if(scope.row.initial == 1){
      //   for(let j = 0; j <this.tableDatatwo.length; j++){
      //     if(this.tableDatatwo[j].tzNo == scope.row.tzNo){
      //       if(this.tableDatatwo[j].initial == 2){
      //         this.tableDatatwo[j].qty1 = scope.row.qtytwo1 - scope.row.qty1
      //       }
      //     }
      //   }
      // }
      // if(scope.row.initial == 2){
      //   for(let i = 0; i <this.tableDatatwo.length; i++){
      //     if(this.tableDatatwo[i].tzNo == scope.row.tzNo){
      //       if(this.tableDatatwo[i].initial == 1){
      //         this.tableDatatwo[i].qty1 = scope.row.qtytwo1 - scope.row.qty1
      //       }
      //     }
      //   }
      // }

    },
    totalnumbersuretwo() {
      console.log(this.tablechaifen, 'gghjjjjjjjjjj')
      let sum = 0
      let sum1 = 0
      for (let j = 0; j < this.tablechaifen.length; j++) {
        sum = (sum * 10 + +this.tablechaifen[j].qty * 10) / 10
        sum1 = (sum1 * 10 + +this.tablechaifen[j].qty1 * 10) / 10
        // sum += +this.tablechaifen[j].qty;
      }
      console.log(sum1.toFixed(this.poiQty1), this.tablechaifen[0].qtytwo1, '99999999')
      console.log(sum, this.tablechaifen[0].qtytwo, 'hhhhhhhhhhh')
      if (sum.toFixed(this.poiQty) == +this.tablechaifen[0].qtytwo && sum1.toFixed(this.poiQty1) == +this.tablechaifen[0].qtytwo1) {
        for (let j = 0; j < this.tablechaifen.length; j++) {
          this.tablechaifen[j].qtytwo = this.tablechaifen[j].qty
        }
        let multipleSelectjson = JSON.parse(JSON.stringify(this.multipleSelectiontwo))
        this.tableDatatwo.splice(multipleSelectjson[0].row_index, 1, ...this.tablechaifen)
        for (let i = 0; i < this.tableDatatwo.length; i++) {
          this.tableDatatwo[i].itm = i + 1
        }
        console.log(this.tableDatatwo, 'bhwwwwwwwwww')

        // for(let i = 0; i <this.tablechaifen.length; i++){
        //   this.tableDatatwo.splice(multipleSelectjson[0].row_index+1+i, 1, this.tablechaifen[i])
        //   // this.tableDatatwo.splice(multipleSelectjson[0].itm+i, 0, multipleSelectjson[0])
        // }

        this.$refs.table2.clearSelection()
        this.multipleSelectiontwo = []
        this.quantitymodyvisible = false
        multipleSelectjson = []
        console.log(this.multipleSelectiontwo, 'this.multipleSelectiontwo')
        console.log(this.tableDatatwo, 'gtttttttttt')
        this.$refs.table2.clearSelection()
        this.multipleSelectiontwo = []
      } else {
        this.$message.warning('拆分数量合计不等于表头数量')
        return
      }
    },
    totalnumbersure() {
      // if(this.multipleSelectiontwo[0].qty<=0 || this.multipleSelectiontwo[0].qty1<=0){
      //   this.$message.warning('派工数量副数量小于等于0不能拆分')
      //   return
      // }
      let paigongflag = false
      if (this.totalnumber > 1) {
        let sum = 0;
        let sumfu = 0;
        for (let i = 0; i < this.handovernumber.length; i++) {
          sum += +this.handovernumber[i].label;
          sumfu += +this.handovernumber[i].value;
        }
        console.log(this.multipleSelectiontwo[0].qtytwo, 'hyyyyyyyyyyy')
        if (sum == this.multipleSelectiontwo[0].qtytwo) { } else {
          this.$message.warning('拆分派工数量之和应等于总数量')
          return
        }
        if (sumfu == this.multipleSelectiontwo[0].qtytwo1) { } else {
          this.$message.warning('拆分派工副数量之和应等于总数量')
          return
        }
      } else {
        this.$message.warning('拆分总笔数必须大于1')
      }

      console.log(this.handovernumber, 'rttttttttttttt')
      for (let j = 0; j < this.tableDatatwo.length; j++) {
        this.tableDatatwo[j].addid = j
      }
      // handovernumber
      console.log(this.tableDatatwo, 'qqqqqqqq')
      let multipleSelectjson = JSON.parse(JSON.stringify(this.multipleSelectiontwo))
      let weizhi
      // for(let j = 0; j <multipleSelectjson.length; j++){
      //   // for(let i = 0; i <this.tableDatatwo.length; i++){
      //   //   if(this.tableDatatwo[i].itm == multipleSelectjson[j].itm){
      //     // if(multipleSelectjson[j].addid == this.tableDatatwo[j].addid)
      //           weizhi = multipleSelectjson[j].itm
      //           for(let i = 0; i <this.handovernumber.length; i++){
      //             console.log(this.handovernumber[i].label,'uuuuuuuuuuu')
      //             // multipleSelectjson[j].qty = this.handovernumber[i].label
      //             this.tableDatatwo.splice(multipleSelectjson[j].itm+i, 0, multipleSelectjson[j])
      //             console.log(this.tableDatatwo,'bnnnnnnnnnn')
      //             // this.tableDatatwo.splice(multipleSelectjson[j].itm+1, 0, multipleSelectjson[j])
      //             // this.$set(this.tableDatatwo, i+1, multipleSelectjson[j]);
      //           }
      //     // }
      //   // }
      // }
      // for(let j = 0; j <multipleSelectjson.length; j++){
      // for(let i = 0; i <this.tableDatatwo.length; i++){
      //   if(this.tableDatatwo[i].itm == multipleSelectjson[j].itm){
      // if(multipleSelectjson[j].addid == this.tableDatatwo[j].addid)
      weizhi = multipleSelectjson[0].itm
      for (let i = 0; i < this.handovernumber.length; i++) {
        console.log(this.handovernumber[i].label, 'uuuuuuuuuuu')
        // multipleSelectjson[0].qty = this.handovernumber[i].label
        // this.tableDatatwo.splice(multipleSelectjson[0].itm+i, 0, multipleSelectjson[0])
        this.tableDatatwo.splice(multipleSelectjson[0].row_index + 1 + i, 0, {
          qty: this.handovernumber[i].label,
          qty1: this.handovernumber[i].value,
          addid: multipleSelectjson[0].addid,
          baNo: multipleSelectjson[0].baNo,
          batNo: multipleSelectjson[0].batNo,
          bomRem: multipleSelectjson[0].bomRem,
          cusNo: multipleSelectjson[0].cusNo,
          dep: multipleSelectjson[0].dep,
          endDd: multipleSelectjson[0].endDd,
          flag: multipleSelectjson[0].flag,
          initial: multipleSelectjson[0].initial,
          itm: multipleSelectjson[0].itm,
          mdNo: multipleSelectjson[0].mdNo,
          moDd: multipleSelectjson[0].moDd,
          moNo: multipleSelectjson[0].moNo,
          pgDd: multipleSelectjson[0].pgDd,
          pgNo: multipleSelectjson[0].pgNo,
          prdMark: multipleSelectjson[0].prdMark,
          prdName: multipleSelectjson[0].prdName,
          prdNo: multipleSelectjson[0].prdNo,
          preEndDd: multipleSelectjson[0].preEndDd,
          preStaDd: multipleSelectjson[0].preStaDd,

          qtyFin: multipleSelectjson[0].qtyFin,
          qtyLost: multipleSelectjson[0].qtyLost,
          qtytwo: multipleSelectjson[0].qtytwo,
          qtytwo1: multipleSelectjson[0].qtytwo1,
          rem: multipleSelectjson[0].rem,
          sebNo: multipleSelectjson[0].sebNo,
          soNo: multipleSelectjson[0].soNo,
          spc: multipleSelectjson[0].spc,
          tzDd: multipleSelectjson[0].tzDd,
          tzNo: multipleSelectjson[0].tzNo,
          ygNo: multipleSelectjson[0].ygNo,
          ypgQty: multipleSelectjson[0].ypgQty,
          ypgQty1: multipleSelectjson[0].ypgQty1,
          zcName: multipleSelectjson[0].zcName,
          zcNo: multipleSelectjson[0].zcNo
        })
        // this.tableDatatwo[multipleSelectjson[0].itm+i].qty = this.handovernumber[i].label
        console.log(this.tableDatatwo, 'bnnnnnnnnnn')
        // 更新表格显示的数据
        // this.$nextTick(() => {

        //   this.tableDatatwo = [...this.tableDatatwo];
        // });
        // this.tableDatatwo.splice(multipleSelectjson[j].itm+1, 0, multipleSelectjson[j])
        // this.$set(this.tableDatatwo, i+1, multipleSelectjson[j]);
      }
      // }
      // }
      // }
      // for(let i = 0; i <this.handovernumber.length; i++){
      //   for(let j = 0; j <this.tableDatatwo.length; j++){
      //     this.tableDatatwo[j].qty = 0
      //   }
      // }
      // for(let j = 0; j <this.tableDatatwo.length; j++){
      //   for(let i = 0; i <this.handovernumber.length; i++){
      //     if(j == weizhi){

      //       this.tableDatatwo[j].qty = this.handovernumber[i].label
      //       // this.tableDatatwo[j].qty = 0
      //       // this.tableDatatwo[j].qty1 = 0
      //       this.tableDatatwo[j].initial = 2
      //       this.tableDatatwo[j].flag = true
      //     }
      //   }
      // }
      console.log(this.tableDatatwo, '6666666777777777')
      // for(let j = 0; j <this.tableDatatwo.length; j++){
      //   if(j == weizhi){
      //     this.tableDatatwo[j].qty = 0
      //     this.tableDatatwo[j].qty1 = 0
      //     this.tableDatatwo[j].initial = 2
      //     this.tableDatatwo[j].flag = true
      //   }
      // }
      this.$refs.table2.clearSelection()
      this.quantitymodyvisible = false
    },
    split() {
      console.log(this.multipleSelectiontwo, 'ggggggg')
      if (this.multipleSelectiontwo.length == 1) {
        if (this.multipleSelectiontwo[0].qty <= 1) {
          this.$message.warning('派工数量大于1才能拆分')
          return
        }
        if (+this.multipleSelectiontwo[0].qty < +this.multipleSelectiontwo[0].qtyFin) {
          this.$message.warning('派工数量不能小于已完工量')
          return
        }
        this.handovernumber = []
        this.totalnumber = 1
        this.quantitymodyvisible = true
        this.tablechaifen = JSON.parse(JSON.stringify(this.multipleSelectiontwo))
        if (this.multipleSelectiontwo[0].qtyFin) {
          this.cansplit = +this.multipleSelectiontwo[0].qty - +this.multipleSelectiontwo[0].qtyFin
        } else {
          this.cansplit = +this.multipleSelectiontwo[0].qty
        }
        //   if(this.multipleSelectiontwo[0].qty<=0 || this.multipleSelectiontwo[0].qty1<=0){
        //     this.$message.warning('派工数量副数量小于等于0不能拆分')
        //     return
        //   }
        //   for(let j = 0; j <this.tableDatatwo.length; j++){
        //     this.tableDatatwo[j].addid = j
        //   }
        //   console.log(this.tableDatatwo,'qqqqqqqq')
        //   // JSON.parse(JSON.stringify(this.multipleSelection))
        //  let multipleSelectjson = JSON.parse(JSON.stringify(this.multipleSelectiontwo))
        //  let weizhi
        //   for(let j = 0; j <multipleSelectjson.length; j++){
        //     // for(let i = 0; i <this.tableDatatwo.length; i++){

        //     //   if(this.tableDatatwo[i].itm == multipleSelectjson[j].itm){
        //       // if(multipleSelectjson[j].addid == this.tableDatatwo[j].addid)
        //             weizhi = multipleSelectjson[j].itm
        //             this.tableDatatwo.splice(multipleSelectjson[j].itm, 0, multipleSelectjson[j])
        //             console.log(this.tableDatatwo,'bnnnnnnnnnn')
        //             this.tableDatatwo.splice(multipleSelectjson[j].itm+1, 0, multipleSelectjson[j])
        //         // this.$set(this.tableDatatwo, i+1, multipleSelectjson[j]);
        //       // }
        //     // }
        //   }
        //   console.log(this.tableDatatwo,'6666666777777777')
        //   for(let j = 0; j <this.tableDatatwo.length; j++){
        //     if(j == weizhi){
        //       this.tableDatatwo[j].qty = 0
        //       this.tableDatatwo[j].qty1 = 0
        //       this.tableDatatwo[j].initial = 2
        //       this.tableDatatwo[j].flag = true
        //     }
        //   }
        //   this.$refs.table2.clearSelection()






        // this.tableDatatwo.forEach((item,index)=>{

        //   if(item.itm == index){
        //     item.qty = 0
        //     item.qty1 = 0
        //   }
        // })
        // this.multipleSelectiontwo.forEach((item,index)=>{
        //   if(item.itm == index){
        //     item.qty = 0
        //     item.qty1 = 0
        //     this.tableDatatwo.splice(item.itm, 0, item)
        //   }
        // })
      } else {
        this.$message.warning('请选择一条数据拆分')
      }
    },
    picisure() {

      // tableDatatwo
      if (this.queryParamtwo.preStaDd && this.queryParamtwo.preEndDd && this.queryParamtwo.dep) {
        console.log(this.queryParamtwo.dep, this.multipleSelectiontwo, 'dddd555555555')
        if (this.multipleSelectiontwo.length > 0) {
          this.multipleSelectiontwo.forEach((item, index) => {

            item.preStaDd = this.queryParamtwo.preStaDd
            item.preEndDd = this.queryParamtwo.preEndDd
            item.sebNo = this.queryParamtwo.sebNo
            item.ygNo = this.queryParamtwo.ygNo
            item.dep = this.queryParamtwo.dep
            item.depName = this.queryParamtwo.depName
            item.name = this.queryParamtwo.name
            this.$set(this.multipleSelectiontwo, index, item);
          })
          this.visiblepici = false
        } else {
          this.$message.warning('请选择数据')
        }
      } else {
        this.$message.warning('预开工日、预完工日、制造部门必须选择')
      }
    },
    batchset() {
      if (this.multipleSelectiontwo.length > 0) {
        // this.queryParamtwo = {}
        this.visiblepici = true
        this.queryParamtwo = {
          preStaDd: '',
          preEndDd: '',
          sebNo: '',
          ygNo: '',
          dep: '',
          depName: ''
        }
        this.datatwo = ''
      } else {
        this.$message.warning('请选择数据')
      }

    },
    save() {
      this.multipleSelection = this.$refs.xTable.getCheckboxRecords()
      if (this.multipleSelection.length) { } else {
        this.$message.warning('请选择数据')
        return
      }
      // this.visiblechuhuan = true
      // pggetCache({usr:this.curperson}).then((res) => {
      //     if (res.code === 0) {
      //       if(res.data.length>0){
      //         this.tableDatatwohuan = res.data
      //         for(let j = 0; j <this.tableDatatwohuan.length; j++){
      //           this.tableDatatwohuan[j].itm = j + 1
      //           this.tableDatatwohuan[j].qtytwo =this.tableDatatwohuan[j].qty
      //           this.tableDatatwohuan[j].qtytwo1 =this.tableDatatwohuan[j].qty1
      //           this.tableDatatwohuan[j].initial = 1
      //           this.tableDatatwohuan[j].flag =false
      //         }
      //         this.huanvisible = false
      //         this.tableDatatwo = this.tableDatatwohuan
      //       }
      //     }
      // })
      this.queryParamtwo = {
        preStaDd: '',
        preEndDd: '',
        sebNo: '',
        ygNo: '',
        dep: '',
        depName: ''
      }
      this.multipleSelectioncopy = []
      this.tableDatatwo = []
      this.datatwo = ''
      this.visiblejiaojie = true
      this.multipleSelectioncopy = JSON.parse(JSON.stringify(this.multipleSelection))
      // if(this.multipleSelectiontwo.length>0){
      //   this.$refs.table2.clearSelection()
      // }
      this.tableDatatwo = this.multipleSelectioncopy
      for (let j = 0; j < this.tableDatatwo.length; j++) {
        this.tableDatatwo[j].itm = j + 1
        this.tableDatatwo[j].qtytwo = this.tableDatatwo[j].qty
        this.tableDatatwo[j].qtytwo1 = this.tableDatatwo[j].qty1
        this.tableDatatwo[j].qtythree = this.tableDatatwo[j].qty
        this.tableDatatwo[j].qtythree1 = this.tableDatatwo[j].qty1
        this.tableDatatwo[j].initial = 1
        this.tableDatatwo[j].flag = false
      }
      this.$nextTick(() => {
        this.$refs.table2.toggleAllSelection()
        this.$forceUpdate()
      })



    },
    savetwo() {
      let flagling = true
      let flagxiao = true
      if (this.multipleSelectiontwo.length > 0) {
        console.log(this.multipleSelectiontwo, 'mmmmmmmmmmmm')
        let tablechaihou = JSON.parse(JSON.stringify(this.multipleSelectiontwo))
        for (let j = 0; j < tablechaihou.length; j++) {
          if (tablechaihou[j].qty == 0 || tablechaihou[j].qty == '') {
            flagling = false
            break
          }
          if (+tablechaihou[j].qty < +tablechaihou[j].qtyFin) {
            flagxiao = false
            break
          }

          if (tablechaihou[j].isBaNo === true) {
            tablechaihou[j].isBaNo = 1
          } else {
            tablechaihou[j].isBaNo = 0
          }
        }
        if (flagling) {
          if (flagxiao) {
            console.log(tablechaihou, 'ddddddddddddd')
            // qraddQr
            pgpg(tablechaihou).then((res) => {
              if (res.code === 0) {
                if (res.msg === 'success') {
                  this.$refs.table2.clearSelection()
                  this.queryParam.qrNo = res.data

                  //  this.activeNameMain = 'three'
                  // this.handleClick('three')
                  // this.getListthree('3')
                  this.getList()

                  this.$message.success('派工成功')
                  this.visiblejiaojie = false
                  this.visiblechuhuan = false
                  // this.activeNameMain2 = 'dier'
                  // this.handleClick2('dier')
                } else {
                  this.$refs.table2.clearSelection()
                  this.$message.error(res.data)
                  return
                }

              }
            }).catch(err => this.requestFailed(err))
              .finally(() => {
                this.loading = false
              })
          } else {
            this.$message.warning('派工数量不能小于已完工量')
          }
        } else {
          this.$message.warning('派工数量不能为0或者空')
        }
      } else {
        this.$message.warning('请选择一条数据')
      }
    },
    choosezhi(obj) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.queryParam.usr = ''
          this.queryParam.salName = ''
          return
        }
      }
      var map = {}

      if (obj.obj.name === 'salNo') {
        if (obj.obj.data.salNo) {
          this.queryParam.salName = obj.obj.data.name
          this.queryParam.usr = obj.obj.data.salNo
          this.datazhi = this.queryParam.usr + '-' + this.queryParam.salName
        } else {
          this.datazhi = ''
          this.queryParam.usr = ''
          this.queryParam.salName = ''
        }

      }


    },
    choosepop(obj) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.queryParamtwo.dep = ''
          this.queryParamtwo.depName = ''
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'dep') {
        this.flag = true
        this.queryParamtwo.depName = obj.obj.data.name
        if (obj.obj.data.deptCode) {
          this.queryParamtwo.dep = obj.obj.data.deptCode
          this.datatwo = this.queryParamtwo.dep + '-' + this.queryParamtwo.depName
        } else {
          this.datatwo = ''
          this.queryParamtwo.dep = ''
          this.queryParamtwo.depName = ''
        }
      }
    },
    choosescope(obj, scope) {

      console.log(obj, scope, '9999999999')
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {

          // this.queryParam.dep=''
          // this.queryParam.depName=''
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'salNo') {

        this.flag = true
        this.queryParam.name = obj.obj.data.name
        if (obj.obj.data.salNo) {
          this.queryParam.ygNo = obj.obj.data.salNo
          // this.data = this.queryParam.ygNo+ '-' + this.queryParam.name
          this.tableDatatwo.forEach((item, index) => {
            if (scope.row.row_index == item.row_index) {
              item.ygNo = obj.obj.data.salNo
              item.name = obj.obj.data.name
            }
            this.$set(this.tableDatatwo, index, item);

          })
          console.log(this.tableDatatwo, 'gggggggggggg')
        } else {
          this.data = ''
          this.queryParam.salNo = ''
          this.queryParam.name = ''
        }
      }
    },
    choosezuoye(obj) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.queryParamtwo.salNo = ''
          this.queryParamtwo.name = ''
          return
        }
      }

      var map = {}
      if (obj.obj.name === 'salNo') {

        this.flag = true
        this.queryParamtwo.name = obj.obj.data.name
        if (obj.obj.data.salNo) {
          this.queryParamtwo.salNo = obj.obj.data.salNo
          this.queryParamtwo.ygNo = obj.obj.data.salNo
          this.datazuoye = this.queryParamtwo.salNo + '-' + this.queryParamtwo.name
        } else {
          this.datatwo = ''
          this.queryParamtwo.salNo = ''
          this.queryParamtwo.name = ''
          this.queryParamtwo.ygNo = ''
        }
      }
    },
    choosedan(obj, scope) {


      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {

          // this.queryParam.dep=''
          // this.queryParam.depName=''
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'dep') {
        this.flag = true
        // this.queryParam.depName = obj.obj.data.name
        if (obj.obj.data.deptCode) {
          // this.queryParam.dep = obj.obj.data.deptCode
          this.datatwo = obj.obj.data.deptCode + '-' + obj.obj.data.name
          // this.tableDatatwo
          // multipleSelectiontwo
          this.tableDatatwo.forEach((item, index) => {
            if (scope.row.row_index == item.row_index) {
              item.dep = obj.obj.data.deptCode
              item.depName = obj.obj.data.name
            }
            this.$set(this.tableDatatwo, index, item);
          })
        } else {
          this.datatwo = ''
          // this.queryParam.dep=''
          // this.queryParam.depName=''
        }
      }
    },
    choose(obj) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.queryParam.dep = ''
          this.queryParam.depName = ''
          this.data = ''
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'dep') {
        this.flag = true
        this.queryParam.depName = obj.obj.data.name
        if (obj.obj.data.deptCode) {
          this.queryParam.dep = obj.obj.data.deptCode
          this.data = this.queryParam.dep + '-' + this.queryParam.depName
        } else {
          this.data = ''
          this.queryParam.dep = ''
          this.queryParam.depName = ''
        }
      }
    },
    choosetwo(obj) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.queryParam.dep2 = ''
          this.queryParam.depName = ''
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'dep2') {
        this.flag = true
        this.queryParam.depName = obj.obj.data.name
        if (obj.obj.data.deptCode) {
          this.queryParam.dep2 = obj.obj.data.deptCode
          this.datatwo = this.queryParam.dep2 + '-' + this.queryParam.depName
        } else {
          this.datatwo = ''
          this.queryParam.dep2 = ''
          this.queryParam.depName = ''
        }
      }
    },
    queryonetwo() {
      if (this.activeNameMain == 'first') {
        // if(this.queryParam.dep1 && this.queryParam.dep2){
        this.tablePage.currentPage = 1
        this.getList()
        // }else{
        //   this.$message.warning('本道部门和下道部门必填')
        // }
      } else if (this.activeNameMain == 'second') {
        this.tablePagetwo.currentPage = 1
        this.getListtwo()
      } else {
        this.tablePagethree.currentPage = 1
        this.getListthree('')
      }
    },
    raidchange(row) {
      // 获取选中数据 row表示选中这一行的数据，可以从里面提取所需要的值
      this.multipleSelection = row

    },
    pageSizeChange(pageSize) {
      this.tablePage.pageSize = pageSize;
      this.getList();
    },
    pageSizeChangetwo(pageSize) {
      this.tablePagetwo.pageSize = pageSize;
      this.getListtwo();
    },
    pageSizeChangesai(pageSize) {
      this.tablePagesai.pageSize = pageSize;
      this.saixuan();
    },
    pageSizeChangethree(pageSize) {
      this.tablePagethree.pageSize = pageSize;
      this.getListthree('');
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    handlePageChangethree({ currentPage, pageSize }) {
      this.tablePagethree.currentPage = currentPage
      this.tablePagethree.pageSize = pageSize
      this.getListthree('');
    },
    currentChange(currentPage) {

      // this.visible = true;
      this.tablePage.currentPage = currentPage;
      this.getList();
    },
    currentChangetwo(currentPage) {
      // this.visible = true;
      this.tablePagetwo.currentPage = currentPage;
      this.getListtwo();
    },
    currentChangesai(currentPage) {
      // this.visible = true;
      this.tablePagesai.currentPage = currentPage;
      this.saixuan();
    },
    currentChangethree(currentPage) {
      // this.visible = true;
      this.tablePagethree.currentPage = currentPage;
      this.getListthree('');
    },
    // 表格双击事件
    mfBxHanddle(row, column, event) {
      // this.edit = row;
      // this.editId = row.id;
      // this.$router.push({
      //   path: '/mes/qualitysure/detail',
      //   query: {
      //     tyNo: row.tyNo
      //   }
      // });
      this.$router.push({
        path: '/mes/patrolinspection/detail',
        query: {
          xjNo: row.xjNo
        }
      });
      //  this.$router.push({name:'patrolinspectiondetail', params:{
      //     tyNo: row.tyNo
      //   }})
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSelectionChangetwo(val) {
      this.multipleSelectiontwo = val
    },
    handleSelectionChangethree(val) {
      this.multipleSelectionthree = val
    },
    mfBxHanddleone({ row }) {

      localStorage.setItem("paigong", row.pgNo);
      localStorage.setItem("pgcx", JSON.stringify(this.queryParam));

      this.$router.push({
        name: 'dispatchworkerdetail', params: {
          id: row.pgNo
        }
      })
    },
    handleClick2(val) {

      if (this.activeNameMain2 == 'diyi') {
        // this.tablePage.currentPage = 1
        this.tablePagetwo.currentPage = 1
        this.getListtwo()
      } else if (this.activeNameMain2 == 'dier') {
        // this.tablePagetwo.currentPage = 1
        // this.getListtwo()
        this.saixuan()
      }
    },
    saixuan() {
      qrgetCache2(
        Object.assign({
          current: this.tablePagesai.currentPage,

          size: this.tablePagesai.pageSize,
          ...this.queryParam,

        })
      ).then(response => {
        this.loading = false
        this.tableDatasaixuan = response.data.records
        this.totalCountsai = response.data.total
        // this.tablePage.currentPage = response.data.current
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    handleClick(val) {
      this.reset()
      if (this.activeNameMain == 'first') {

        this.tablePage.currentPage = 1

        this.getList()
        pggetCache({ usr: this.curperson }).then((res) => {
          if (res.code === 0) {
            if (res.data.length > 0) {
              this.huanvisible = true
            }
          }
        }).catch(err => this.requestFailed(err))
      } else if (this.activeNameMain == 'second') {
        this.tablePagetwo.currentPage = 1
        this.getListtwo()
      } else {
        this.queryParam = {
          printId: 'F',
          staDd: '',
          endDd: '',
          cusNo: '',
          pgNo: '',
          baNo: '',
          moNo: '',
          prdName: '',
          prdNo: '',
          zcNo: '',
          dep: '',
          depName: '',
          bomRem: '',
          tzNo: '',
          soNo: '',
          cusNo: '',
          sebNo: '',
          closeId: 'F'
        },
          this.datatwo = ''

        this.queryParam.staDd = moment().subtract(7, "days").format('YYYY-MM-DD')
        this.queryParam.endDd = moment(new Date()).format('YYYY-MM-DD')
        this.tablePagethree.currentPage = 1
        this.getListthree('')
      }

    },

    onChange(data, dateString) {
      this.queryParam.date = dateString
      this.queryParam.staDd = this.queryParam.date[0]
      this.queryParam.endDd = this.queryParam.date[1]

    },
    // 按钮初始化
    btnInit() {
      this.btn.newOpen = true // 首检开工 null||""
      this.btn.pause = true // 暂停 2
      this.btn.continues = true // 继续 3
      this.btn.inspection = true // 首检 1
    },
    getListthree(val) {
      this.loading = true
      pgypgPage(
        Object.assign({
          current: this.tablePagethree.currentPage,
          // current: this.tablePage.currentPage,
          size: this.tablePagethree.pageSize,
          ...this.queryParam,
          // staDd:this.queryParam.staDd,
          // endDd:this.queryParam.endDd,
          // tyNo:this.queryParam.tyNo,
          // zcNo:this.queryParam.zcNo,
          // moNo:this.queryParam.moNo,
          // prdNo:this.queryParam.prdNo,
          // prdName:this.queryParam.prdName,
          // tiNo:this.queryParam.tiNo,
          // sys: this.queryParam.sys ? 'T' : 'F',
          // chkKnd: this.queryParam.chkKnd ? 'T' : 'F',
          // tzNo:this.queryParam.tzNo,
          // usr:this.queryParam.usr
          // bilNo:this.queryParam.bilNo,
          // pgNo: this.queryParam.pgNo
        })
      ).then(response => {
        this.loading = false
        this.tableDatathree = response.data.records
        // if(val=='3'){
        //   this.$refs.multipleTable.clearSelection()
        //   this.$refs.multipleTable.toggleAllSelection()
        // }
        this.totalCountthree = response.data.total
        // this.tablePage.currentPage = response.data.current
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    getListtwo() {
      this.loading = true
      qrgetCache(
        Object.assign({
          current: this.tablePagetwo.currentPage,
          // current: this.tablePage.currentPage,
          size: this.tablePagetwo.pageSize,
          ...this.queryParam,
          // staDd:this.queryParam.staDd,
          // endDd:this.queryParam.endDd,
          // tyNo:this.queryParam.tyNo,
          // zcNo:this.queryParam.zcNo,
          // moNo:this.queryParam.moNo,
          // prdNo:this.queryParam.prdNo,
          // prdName:this.queryParam.prdName,
          // tiNo:this.queryParam.tiNo,
          // sys: this.queryParam.sys ? 'T' : 'F',
          // chkKnd: this.queryParam.chkKnd ? 'T' : 'F',
          // tzNo:this.queryParam.tzNo,
          // usr:this.queryParam.usr
          // bilNo:this.queryParam.bilNo,
          // pgNo: this.queryParam.pgNo
        })
      ).then(response => {
        this.loading = false
        this.tableDatatwo = response.data
        this.totalCounttwo = response.data.total
        // this.tablePage.currentPage = response.data.current
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 获取列表数据
    getList() {
      this.loading = true
      pgdpgPage(
        Object.assign({
          current: this.tablePage.currentPage,
          // current: this.tablePage.currentPage,
          size: this.tablePage.pageSize,
          ...this.queryParam,
          //   staDd:this.queryParam.staDd,
          // endDd:this.queryParam.endDd,
          // tiNo:this.queryParam.tiNo,
          // zcNo:this.queryParam.zcNo,
          // moNo:this.queryParam.moNo,
          // prdNo:this.queryParam.prdNo,
          // prdName:this.queryParam.prdName,
          // sys: this.queryParam.sys ? 'T' : 'F',
          // chkKnd: this.queryParam.chkKnd ? 'T' : 'F',
          // bilNo:this.queryParam.bilNo,
          // pgNo: this.queryParam.pgNo
        })
      ).then(response => {
        this.loading = false
        this.tableData = response.data.records
        this.tableData.forEach((i, index) => {
          i.addid = index + 1
          i.isBaNo = false
        })
        this.totalCount = response.data.total
        // this.tablePage.currentPage = response.data.current
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    ClickEvent({ row }) {
      this.btnInit()
      this.rowIndex = row
      getItem(
        Object.assign({
          pgNo: row.pgNo,
          itm: row.itm,
          tiNo: row.tiNo,
          prdNo: row.prdNo
        })
      ).then(response => {
        this.row = response.data
        this.btnState(this.row)
      }).catch(err => this.requestFailed(err))
    },
    // 按钮状态
    btnState(row) {
      // 首检 ：3 暂停：2  继续：1
      if (row.state === '-1') {
        this.btn.newOpen = false
      }
      if (row.state === '0' || row.state === '3' || row.state === '1') {
        this.btn.pause = false // 暂停
        this.btn.inspection = false // 首检
      }
      if (row.state === '2') {
        this.btn.continues = false // 继续 3
      }
    },
    getBtnValue(state) {
      // 获取对应按钮提示
      let value = ''
      switch (state) {
        case '0':
          value = this.$t('quality.newOpen')
          break
        case '3':
          value = this.$t('quality.inspection')
          break
        case '2':
          value = this.$t('quality.pause')
          break
        case '1':
          value = this.$t('quality.continues')
          break
        case '4':
          value = this.$t('quality.task')
          break
      }
      return value
    },
    // 按钮
    onSubmit(state) {
      switch (state) {
        case '0':
        case '1':
        case '2':
          this.loading = true
          let btnMes = this.getBtnValue(state)
          start({
            state: state,
            pgNo: this.row.pgNo,
            itm: this.row.itm,
            id: this.row.id
          }).then((res) => {

            if (res.code === 0) {
              if (res.msg === 'fail') {
                btnMes = res.data
              } else {
                btnMes = btnMes + this.$t('public.success')
                const row = this.row
                this.ClickEvent({ row })
              }
            }
            this.$notification.success({
              message: this.$t('public.tip'),
              description: btnMes
            })
            this.loading = false
          }).catch(err => this.requestFailed(err))

          break
        case '3':
          const row = this.rowIndex
          this.$refs.a.create(row)
          break
      }
    },
    reset() {
      this.queryParam = {
        staDd: '',
        endDd: '',
        cusNo: '',
        pgNo: '',
        baNo: '',
        moNo: '',
        prdName: '',
        prdNo: '',
        zcNo: '',
        dep: '',
        depName: '',
        bomRem: '',
        tzNo: '',
        soNo: '',
        cusNo: '',
        sebNo: '',
      },
        this.datatwo = ''
      this.data = ''
      this.queryParam.staDd = moment().subtract(7, "days").format('YYYY-MM-DD')
      this.queryParam.endDd = moment(new Date()).format('YYYY-MM-DD')
    }
  }
}
</script>

<style>
/* @media print {
#gfvrgh {
  page-break-before: always;
}
} */
@media print {
  #gfvrgh {
    page-break-before: always;
    page-break-after: always;
  }
}

#gfvrgh {
  page-break-before: always;
  page-break-after: always;
}

@page {
  margin: 0;
}

.el-table .el-table__cell {
  padding: 6px 0;
}

.ant-input {
  height: 28px;
}

.consrm .saixuan .el-tabs--border-card>.el-tabs__header .el-tabs__item {
  font-size: 12px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  -o-appearance: none !important;
  -ms-appearance: none !important;
  appearance: none !important;
  margin: 0;
}

input[type="number"] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  -o-appearance: textfield;
  -ms-appearance: textfield;
  appearance: textfield;
}

.handoverjiao .el-dialog__wrapper {
  z-index: 999 !important;
}

/*.v-modal {
  z-index: 998 !important
}*/

/* .ant-tabs-nav-container{font-size:16px}
::v-deep .el-radio__label{
display:none
} */
/* 将日期选择器的图标颜色更改为灰色 */
/* .el-date-editor .el-input__icon {
color: grey;
} */
.el-date-editor .anticon svg {
  display: none;
}
</style>
<style>
/* .v-modal{background: none!important;} */
</style>