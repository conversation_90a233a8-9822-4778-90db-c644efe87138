<template>
  <div class="bill-container">
    <div class="bill-header">
      <!-- 工具栏 操作按钮 -->
      <dToolbar :data="toolbarItems" @toolbarClick="handleToolbarClick"
                :dropSetShow="dropSetShow"
                @handOpenDocDia = "handOpenDocDia"
                @handOpenDefDia = "handOpenDefDia"
      ></dToolbar>
    </div>
    <div class="bill-body">
      <!-- 单据页面设计-视窗 -->
      <docDialog
        :docDialogVisible.sync="docDialogVisible"
        :form-config="formConfig"
        :grid-config="gridConfig"
      ></docDialog>
      <!-- 自定义栏位设计-视窗 -->
      <defDialog
        :defDialogVisible.sync="defDialogVisible"
        :form-config="formConfig"
        :grid-config="gridConfig"
      ></defDialog>

      <!-- 数据 表头vxe-form及表身vxe-grid -->
      <div>
        <el-collapse v-model="activeNames">
          <el-collapse-item style="margin-bottom: 10px" name="1">
            <template #title>
              基础信息
            </template>
            <dForm
              ref = "dfo"
              :formConfig="formConfig"
              :billData.sync="billData"
            >
            </dForm>
          </el-collapse-item>

          <el-collapse-item style="margin-bottom: 10px" name="2">
            <template #title>
              附件
            </template>
            <annex ref="anRef" @getAnnexData="getAnnexData" ></annex>
          </el-collapse-item>

          <el-collapse-item style="margin-bottom: 10px" name="3">
            <template #title>

              <div class="title-container">
                <template class="title-text">不合格原因</template>
                <div class="title-actions" v-show="activeNames.includes('3')">
                  <el-button size="mini" type="primary" icon="el-icon-circle-plus" @click.stop="addTableRow" circle></el-button>
                  <el-button size="mini" type="danger" icon="el-icon-delete" @click.stop="locaDelTableRow" circle></el-button>
                </div>
              </div>
            </template>
            <el-col :span="24">
              <div class="sup_info_basics_container" style="padding-bottom:5px;">
<!--                <div style="padding-top:5px;padding-bottom: 5px; display: flex; justify-content: flex-end;">-->
<!--                  <el-button size="mini" icon="el-icon-plus" id="add_table" type="primary" @click="addTableRow()"></el-button>-->
<!--                  <el-button size="mini" icon="el-icon-minus" type="primary"-->
<!--                             :style="'background-color: #f56c6c;border-color: #f56c6c;color:#fff;'" id="add_table"-->
<!--                             @click="locaDelTableRow()"></el-button>-->
<!--                </div>-->
                <el-table ref="multipleTable" border :data="testData" class="tableheight bordervisib" style="width: 100%;"
                          height="300" @row-click="handRowClick" @selection-change="handleSelectionChangereason">
                  <!-- max-height="400" -->
                  <el-table-column type="selection" ></el-table-column>
                  <el-table-column align="center" type="index" width="38"></el-table-column>
                  <el-table-column label="原因代号"  height="30" v-if="colData[0].istrue">
                    <template slot-scope="scope">
                      <el-input @focus="focus(scope.$index,scope.row, $event)" @blur="blurNameInput"
                                @input="inputNameInput" ref="prdt3Input" v-model="scope.row.spcNo"
                                style="border: none;font-size:12px;height:10px;padding:0"></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column label="不合格原因" v-if="colData[1].istrue">
                    <template slot-scope="scope">
                      {{scope.row.spcName}}
                      <!-- <el-input
                        class="cell-input"
                        size="mini"
                        v-model="scope.row.spcName"
                      disabled
                      ></el-input> -->
                    </template>
                  </el-table-column>
                  <el-table-column label="不合格量" v-if="colData[2].istrue">
                    <template slot-scope="scope">
                      <el-input type="number" class="cell-input" size="mini" @input="changeqtyLost(scope)"
                                v-model="scope.row.qtyLost" :disabled="!scope.row.spcNo"
                                @wheel.native.prevent="stopScroll($event)"></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column label="不合格量(副)" prop="qty1Lost">
                    <template slot-scope="scope">
                      <el-input type="number" class="cell-input" size="mini" @input="changeqty1Lost(scope)"
                                @change="changeqty1Lost(scope)" v-model="scope.row.qty1Lost" :disabled="!scope.row.spcNo"
                                @wheel.native.prevent="stopScroll($event)"></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column prop="ygNo" align="left" label="工序">
                    <template slot-scope="scope">
                      <my-selectListwo url="mes/zcNo/curt" :read-only="true" :tableColumn="$Column.zcNotwo"
                                       :form="$Form.zcNotwo" :data="scope.row.zcNo + ' ' + scope.row.zcName" name="zcNotwo"
                                       @choose="choosezcNo($event,scope)" allowClear ref="selectList"
                                       v-decorator="['zcNo', { rules: [{ required: true, message:'请选择工序' } ] }]"
                                       placeholder="请选择工序"></my-selectListwo>
                    </template>
                  </el-table-column>
                  <el-table-column prop="ygNo"  align="left" label="作业人员">
                    <template slot-scope="scope">
                      <a-input-search v-model="scope.row.ygNo" @search="selectzyry(scope)" readOnly>
                        <a-button ref="btn" slot="enterButton">
                          <a-icon type="database" />
                        </a-button>
                      </a-input-search>
                      <!-- <my-selectListwo url="mes/salm/query" :read-only="true" :tableColumn="$Column.salmmult"
                        :form="$Form.salm" :data="scope.row.ygNo" multiple name="salNo"
                        @choose="choosescope($event,scope)" allowClear ref="selectList"
                        v-decorator="['salNo', { rules: [{ required: true, message:'请选择作业人员' } ] }]"
                        placeholder="请选择作业人员"></my-selectListwo> -->
                    </template>
                  </el-table-column>
                  <el-table-column prop="dep"  align="left" label="制造部门">
                    <template slot-scope="scope">
                      <my-selectListwo url="mes/basicData/depPage" :read-only="true" :tableColumn="$Column.salmDep"
                                       :form="$Form.salmDep" :data="scope.row.dep + ' ' + scope.row.depName" name="dep"
                                       @choose="choosedan($event,scope)" allowClear ref="selectList"
                                       v-decorator="['dep', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                                       :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
                    </template>
                  </el-table-column>
                  <el-table-column label="质量单价">
                    <template slot-scope="scope">
                      <el-input type="number" class="cell-input" size="mini" v-model="scope.row.qup"
                                :disabled="!scope.row.spcNo" @wheel.native.prevent="stopScroll($event)"></el-input>
                    </template>
                  </el-table-column>
                </el-table>

                <!--focus激活选择货品  -->
                <div @mouseleave="prdt3leave">
                  <el-popover placement="bottom" :style="objStyle" width="400" style="position: fixed;z-index: 99999;"
                              v-model="visible">
                    <div>
                      <el-table class="el-table" :data="pickerList" stripe max-height="360px" size="mini" height="360px"
                                highlight-current-row :cell-style="{ verticalAlign: 'top' }"
                                :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                                style="width: 100%;">
                        <el-table-column fixed="left" align="center" label="选择" width="80">
                          <template slot-scope="scope">
                            <el-button round type="primary" style="padding: 5px 5px 5px 5px;" size="mini"
                                       icon="el-icon-d-arrow-right" @click="getGoodsPopover(scope.row)">选择</el-button>
                          </template>
                        </el-table-column>
                        <el-table-column fixed="left" prop="spcNo" align="left" label="原因代号" width="70"></el-table-column>
                        <!-- :show-overflow-tooltip="true" -->
                        <el-table-column prop="name" align="left" label="不合格原因" width="130"></el-table-column>
                      </el-table>
                      <div>
                        <el-pagination background :pager-count="5" :page-sizes="[5,10, 30, 50]" :page-size="10"
                                       @size-change="pageSizeChangespcNo" :current-page="tablePagespcNo.currentPage"
                                       @current-change="currentChangespcNo" layout="prev, pager, next" :total="spcNototal"
                                       style="margin-top: 5px;"></el-pagination>
                      </div>
                    </div>
                    <!-- <div>
            <el-pagination
              background
              :pager-count="5"
              :page-sizes="[5,10, 30, 50]"
              :page-size="10"
              @size-change="goods_pageSizeChange"
              :current-page="goods_currentPage"
              @current-change="goods_currentChange"
              layout="prev, pager, next"
              :total="goods_pickerCount"
              style="margin-top: 5px;"
            ></el-pagination>
          </div> -->
                  </el-popover>
                </div>
              </div>
            </el-col>
          </el-collapse-item>
        </el-collapse>

      </div>
    </div>
  </div>
</template>

<script>
import docDialog from "@/components/def/DocDialog.vue";
import defDialog from "@/components/def/DefDialog.vue";
import dGrid from "@/components/def/DGrid.vue"
import dForm from "@/components/def/DForm.vue"
import vToolbar from "@/components/amtxts/vTable/vToolbar.vue"
import dToolbar from "@/components/def/DToolbar.vue"
import useFieldDefine from "@/utils/def/useFieldDefine";

import {
  prdZFQty, fetchList, lsqcupd, lsqcadd, getItem, datadepPage, workqctyDel, workqcyjlnfo, workqcaddsave, workdjInfodetail, workqcUpdedit, spcLstpage, sysfiledel
} from '@/api/mes/quality'
import { mapGetters, mapState } from 'vuex'

import moment from 'moment'
import MySelectListwo from '@/components/MySelectListwo'
import selectall from '@/components/selectall'
import annex from '@/components/Upload/annex.vue'

export default {
  components: {
    annex,
    docDialog, defDialog,
    dGrid, dForm,
    vToolbar, dToolbar,
    MySelectListwo,
    selectall
  },
  data() {
    return {
      defDialogVisible: false,
      docDialogVisible: false,
      activeNames: ['1', '2', '3'],

      formConfig: {
        //托工检
        PGM: "TGTY",
        MENU_NAME: "TGTY",
        TABLE_NAME: "MES_QC_TY",

        initItems: null,
        items: [
          {
            span: 24,
            children: []
          }
        ],
        rules: {
          dep: [{ required: true, message: "必填:部门", trigger: ["change", "blur"] }],
        },
      },

      formItems: [
        {
          span: 24,
          children: [
            {
              field: 'tyDd',
              title: '检验日期',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'tyNo',
              title: '检验单号',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'qcTypeName',
              title: '检验方式',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'cusNo',
              title: '客户厂商',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'dep',
              title: '部门',
              span: 6,
              itemRender: {
                name: 'MySelectList',
                props: {
                  placeholder: this.$t('salm.placeholder.depName'),
                  url:'/mes/basicData/depPage',
                  tableColumn:this.$Column.salmDep,
                  form:this.$Form.salmDep,
                },
              }
            },
            {
              field: 'tiNo',
              title: '送检单号',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'salNo',
              title: '检验人员',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'bilType',
              title: '单据类别',
              span: 6,
              itemRender: { name: 'MyElInput', props: { type: '', placeholder: '', disabled: true } }
            },
            {
              field: 'sys',
              title: '实验室检验',
              span: 6,
              itemRender: {
                name: 'MyElCheckBox',
                props: {
                  placeholder: '',
                  disabled: true,
                  trueLable: 'T',
                  falseLable: 'F'
                },
                events: {
                  change: this.onChangeLaboratory
                }
              }
            },
            {
              field: 'qcIds',
              title: '检验结果',
              span: 6,
              itemRender: {
                name: 'MyElInput',
                props: {
                  placeholder: '',
                  disabled: true,
                },
              }
            },
            {
              field: 'chkKnd',
              title: '复检',
              span: 6,
              itemRender: {
                name: 'MyElCheckBox',
                props: {
                  placeholder: '',
                  disabled: true,
                  trueLable: 'T',
                  falseLable: 'F'
                },
                events: {
                  change: this.onChangeclosecase
                }
              }
            },
            {
              field: 'prdNo',
              title: '品号',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'prdName',
              title: '品名',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'prdMark',
              title: '特征',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'unitName',
              title: '单位',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'whName',
              title: '仓库',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'batNo',
              title: '批号',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'moNo',
              title: '工单号',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'pgNo',
              title: '派工单号',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'zcNo1',
              title: '工序',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'qtyRtn',
              title: '已转合格量',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'lsNo',
              title: '不合格评审单',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'boxNo',
              title: '周转箱号',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '', disabled: true } }
            },
            {
              field: 'rem',
              title: '备注',
              span: 24,
              itemRender: { name: 'MyElInput', props: { placeholder: '' } }
            },
            {
              field: 'qty',
              title: '检验数量',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '' } }
            },
            {
              field: 'qtyOk',
              title: '合格量',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '' } }
            },
            {
              field: 'qtyLost',
              title: '不合格量',
              span: 6,
              itemRender: { name: 'MyElInput', props: { placeholder: '' } }
            },
          ]
        }
      ],

      gridConfig: {
        TABLE_NAME: "",
        height: '180',
        columns: [],
        rules: {
        }
      },
      billData: {
        pgm: 'MES_QC_TY',
        formData: {},
      },
      toolbarItems: [
        { label: '删除', value: 'del', icon: 'icon-delete' },
        { label: '保存', value: 'save', icon: 'icon-save' },
        { label: '关闭', value: 'close', icon: 'icon-close' },
        { value: 'edit', visible: false },
      ],
      dropSetShow: true,


      isEdit: true,
      mes_quality_query: 'mes_quality_query',
      mes_quality_reset: 'mes_quality_reset',
      mes_quality_newOpen: 'mes_quality_newOpen',
      mes_quality_inspection: 'mes_quality_inspection',
      mes_quality_pause: 'mes_quality_inspection',
      mes_quality_continues: 'mes_quality_continues',
      mes_quality_task: 'mes_quality_task',

      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 5,
        total: 0
      },
      row: {},
      rowIndex: {},
      // 按钮控制
      btn: {
        newOpen: true, // 首检开工
        open: true, // 开始
        pause: true, // 暂停
        continues: true, // 继续
        report: true, // 报工
        exceptional: true, // 异常
        inspection: true, // 送检
        introspecting: true // 自检
      },
      queryParam: {
        date: [],
      },
      activeName: '1',
      valueone: [],
      visible: false,
      multipleSelection: [],
      reasondata: [],
      multiplereason: [],
      entity: {
        tyDd: '',
        chkKnd: '',
        qcId: '',
        sys: '',
        chktyId: '',
        tiNo: '',
        tyNo: '',
        prdt3Name: "",
        way: 2,
        auxId: "",
        otRemark: "",
        bxId: "",
        qaId: "",
        otId: "",
        bxNo: "",
        bxDd: "",
        status: "",
        statusMsg: "",
        kndId: 3,
        applyDepId: null,
        applyDepNo: "",
        applyDepName: "",
        applyUserId: "",
        applyUserNo: "",
        applyUserName: "",
        prdId: "",
        prdNo: "",
        prdName: "",
        prdt3Id: "",
        prdSpc: "",
        faultRem: "",
        cntId: "",
        cntName: "",
        cntTel: "",
        cntAdr: "",
        dcId: "",
        dcName: "",
        dcLmt: "",
        urgent: 0,
        finId: "",
        finName: "",
        finLmt: "",
        bxPic1: "",
        bxPic2: "",
        bxPic3: "",
        serverDeptId: "",
        otUserId: "",
        qty: null,
        serviceCode: "",
        coDd: null,
        prdUt: "",
        bxType: '1',
        initialTime: '',
        completeTime: ''
      },
      inspectiontype: [
        { value: '1', label: '完工检验' },
        { value: '2', label: '首检检验' },
        { value: '3', label: '托工检验' },
      ],
      sysFiles: [],
      colData: [
        { title: "原因代号", istrue: true },
        { title: "不合格原因", istrue: true },
        { title: "不合格量", istrue: true },
        { title: "货品名称", istrue: true },
        { title: "货品代号", istrue: true },
        { title: "规格", istrue: true },
        { title: "现有库存", istrue: true },
        { title: "借出量", istrue: true },
        { title: "单位", istrue: true },
        { title: "单价", istrue: true },
        { title: "数量", istrue: true },
        { title: "已还数量", istrue: true },
        { title: "税率%", istrue: true },
        { title: "未税金额", istrue: true },
        { title: "税额", istrue: true },
        { title: "金额", istrue: true },
      ],
      objStyle: {
        top: "433px",
        left: "",
      },
      pickerList: [],
      testData: [],
      detailIds: [],
      pickerIndex: 0,
      isShowPopVel: false,
      rightHeight: 0,
      tablerightHeight: 0,
      tyNotwo: '',
      tyDdtwo: '',
      flag: false,
      ccc: true,
      isClaim: false,
      data: '',
      tableLoading: false,
      bottomWidth: '',
      spcNototal: 0,
      tablePagespcNo: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      mesLsQc1s: [],
      bhgvisible: false,
      dataczSal: '',
      disposalresults: [
        { value: '5', label: '放行' },
        { value: '2', label: '报废' },
        { value: '3', label: '返工' },
        { value: '7', label: '复检（全检）' },
        { value: '8', label: '复检（抽检）' },
        { value: '9', label: '重新首检' },
        { value: '1', label: '强制缴库' },
      ],
      multipleSelectionresult: [],
      no: []
    }
  },

  mounted() {
    this.formConfig.items = this.formItems
    //单据设计页面用 initItems, initColumns
    this.formConfig.initItems = JSON.parse(JSON.stringify(this.formItems[0].children))
    this.gridConfig.initColumns = JSON.parse(JSON.stringify(this.gridConfig.columns))
    //初始化, 页面栏位信息加载 及动态渲染
    useFieldDefine.init(this.formConfig, this.gridConfig, this.billData)
  },

  activated() {
    if (localStorage.getItem('tuogongtwotiNo')) {
      this.billData.formData.tiNo = localStorage.getItem('tuogongtwotiNo')
      this.getListtreat()
    } else {
      this.billData.formData.tyNo = localStorage.getItem('tuogongtwotyNo')
      this.getList()
    }
  },

  watch: {
    //自定义栏位设计 关闭, 触发更新栏位数据
    defDialogVisible(newVal) {
      if (newVal === false) {
        useFieldDefine.init(this.formConfig, this.gridConfig, this.billData)
      }
    },
    //单据页面设计 关闭, 触发更新栏位数据
    docDialogVisible(newVal) {
      if (newVal === false) {
        useFieldDefine.init(this.formConfig, this.gridConfig, this.billData)
      }
    },
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    }),
    ...mapGetters(['permissions'])
  },
  created() {
    this.sysFiles = []
    var data1 = moment().subtract(30, "days").format('YYYY-MM-DD')
    var data2 = moment(new Date()).format('YYYY-MM-DD')
    this.valueone = [data1, data2]
  },
  methods: {
    getAnnexData(param){
      this.tableData=param
    },
    handleToolbarClick(params) {
      switch (params) {
        case 'del':
          this.deldata()
          break;
        case 'close':
          this.$bus.$emit('closeTab',this.$route.path)
          break;
        case 'save':
          this.save()
          break;
        default:
      }

    },

    //打开单据页面设计窗口
    handOpenDocDia() {
      this.docDialogVisible = true;
    },

    //打开自定义栏位设计页面窗口
    handOpenDefDia() {
      this.defDialogVisible = true;
    },

    zyList(list) {
      this.testData.forEach((item, index) => {
        if (this.selectrow == index) {
          item.ygNo = list
          this.$set(this.testData, index, item);
        }
      })
    },
    selectzyry(scope) {
      this.selectrow = scope.$index
      this.$refs.refzyry.open(this.typeGroupNo)
    },
    choosezcNo(obj, scope, clear) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.testData.forEach((item, index) => {
            if (scope.$index == index) {
              item.zcNo = ''
              item.zcName = ''
            }
            this.$set(this.testData, index, item);
          })
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'zcNotwo') {
        this.testData.forEach((item, index) => {
          if (scope.$index == index) {
            item.zcNo = obj.obj.data.zcNo
            item.zcName = obj.obj.data.zcName
          }
          this.$set(this.testData, index, item);
        })
      }
    },
    chooseczSal(obj) {
      if (obj.obj.name === 'salNo') {
        // this.billData.formData.salName = obj.obj.data.name
        this.billData.formData.czSal = obj.obj.data.salNo
      }
    },
    choosescope(obj, scope, clear) {
      this.no = []
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.testData.forEach((item, index) => {
            if (scope.$index == index) {
              item.ygNo = ''
              this.$set(this.testData, index, item);
            }
          })
          return
        }
      }
      if (obj.obj.name === 'salNo') {
        let arr = []
        if (obj.obj.data != null) {
          arr = obj.obj.data
          arr.forEach((i) => {
            this.no.push(i.salNo)
          })
        }
        const no = new Set(this.no)
        this.testData.forEach((item, index) => {
          if (scope.$index == index) {
            // item.ygNo = obj.obj.data.salNo
            item.ygNo = clear === undefined ? [...no].join(',') : ''
            this.$set(this.testData, index, item);
          }
        })
        console.log(this.testData, 'sshhhhhhhhhh')
      }
    },
    changeqty1Lost(val) {
      if (val.row.qty1Lost < 0) {
        this.$message.warning('不合格量(副)不能为负')
        val.row.qty1Lost = 0
      }
      this.billData.formData.qty1Lost = 0
      this.testData.forEach(i => {
        if (i.spcNo) {
          this.billData.formData.qty1Lost += +i.qty1Lost;
        }
      })
      this.billData.formData.qty1Lost = parseFloat(this.billData.formData.qty1Lost.toFixed(2))
      this.$forceUpdate()
    },
    // choosescope(obj,scope) {
    //   console.log(obj,scope,'9999999999')
    //   if(obj.obj.clear){
    //     if(obj.obj.clear == 1){

    //       // this.queryParam.dep=''
    //       // this.queryParam.depName=''
    //       return
    //     }
    //   }
    //   var map = {}
    //     if (obj.obj.name === 'salNo') {
    //       this.flag = true
    //       this.queryParam.name = obj.obj.data.name
    //       if(obj.obj.data.salNo){
    //         // this.queryParam.usr = obj.obj.data.salNo
    //         // this.data = this.queryParam.ygNo+ '-' + this.queryParam.name
    //         this.testData.forEach((item,index)=>{
    //           if(scope.$index == index){
    //             item.usr = obj.obj.data.salNo
    //             // item.name = obj.obj.data.name
    //           }
    //           this.$set(this.testData, index, item);

    //         })
    //         console.log(this.testData,'gggggggggggg')
    //       }else{
    //         // this.data = ''
    //         // this.queryParam.salNo=''
    //         // this.queryParam.name=''
    //       }
    //     }
    // },
    choosedan(obj, scope) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {

          this.testData.forEach((item, index) => {
            if (scope.$index == index) {
              item.dep = ''
              item.depName = ''
            }
            this.$set(this.testData, index, item);
          })
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'dep') {
        this.flag = true
        // this.queryParam.depName = obj.obj.data.name
        if (obj.obj.data.deptCode) {
          // this.queryParam.dep = obj.obj.data.deptCode
          this.datatwo = obj.obj.data.deptCode + '-' + obj.obj.data.name
          // multipleSelectiontwo
          this.testData.forEach((item, index) => {
            if (scope.$index == index) {
              item.dep = obj.obj.data.deptCode
              item.depName = obj.obj.data.name
            }
            this.$set(this.testData, index, item);
          })
        } else {
          // this.datatwo = ''
          // this.queryParam.dep=''
          // this.queryParam.depName=''
        }
      }
    },
    changeprocess(scope) {
      if (scope.row.qty < 0) {
        this.$message.warning('数量不能为负')
        scope.row.qty = 0
      }
      let bhgsl = 0
      this.mesLsQc1s.forEach(i => {
        bhgsl += +i.qty;
      })
      if (bhgsl > this.billData.formData.qtyLost) {
        this.$message.info('不合格量超出')
        scope.row.qty = ''
      }
      this.$forceUpdate()
    },
    clfsinput(index, row, e) {
      let clfsdata = JSON.parse(JSON.stringify(this.mesLsQc1s))
      const result = clfsdata.filter((item, index2) => {
        if (index2 !== index) {
          if (item.czNo == row.czNo) {
            return true
          }
        }
      })
      console.log(result, 'w1sgggggggggg')
      if (result.length) {
        row.czNo = ''
        this.$message.warning('不允许重复选择')
      }
    },
    handleSelectionresult(rows) {
      this.multipleSelectionresult = rows;
    },
    changeSelectable(row, index) {
      return !row.bilNo
    },
    savebhg() {
      let errordata = false
      for (let j = 0; j < this.mesLsQc1s.length; j++) {
        if (this.mesLsQc1s[j].czNo == '2' || this.mesLsQc1s[j].czNo == '3') {
          if (this.mesLsQc1s[j].qty == null || this.mesLsQc1s[j].qty == '') {
            errordata = true
            break
          }
        }
      }
      if (errordata) {
        this.$message.warning('处置结果为报废返修栏位，数量必填')
        return
      }
      //处置结果
      // this.mesLsQc1s
      this.mesLsQc1s = this.mesLsQc1s.filter((item) => {
        return item.czNo !== '' && item.czNo !== null
      })
      debugger
      let mesLsQc1svalue = false
      if (this.mesLsQc1s.length) {
        for (let j = 0; j < this.mesLsQc1s.length; j++) {
          if (this.mesLsQc1s[j].qty) {
            mesLsQc1svalue = true
            break
          }
        }
      } else {
        this.$message.warning('请填写处置结果表格')
        return
      }
      if (mesLsQc1svalue) {
        if (this.billData.formData.czSal) { } else {
          this.$message.warning('请选择处置结果确认人')
        }
        if (this.billData.formData.czDate) { } else {
          this.$message.warning('请选择处置结果日期')
        }
        if (this.billData.formData.czSal && this.billData.formData.czDate) {
        } else {
          return
        }
      } else {
        this.$message.warning('请填写处置结果表格')
        return
      }

      let mesQcTy1s = []
      if (this.testData) {
        mesQcTy1s = this.testData.filter((item) => {
          return item.spcNo !== "" && item.spcNo !== null;
        })
      }
      if (this.billData.formData.lsNo) {
        lsqcupd({
          ...this.billData.formData,
          mesLsQc1s: this.mesLsQc1s,
          sys: this.billData.formData.sys ? 'T' : 'F',
          chkKnd: this.billData.formData.chkKnd ? 'T' : 'F',
          mesQcTy1s: mesQcTy1s
          // mesTiQcFileList:this.tableData,
        }).then((res) => {
          if (res.code === 0) {
            if (res.msg === 'success') {
              this.$message.success(res.data)
              localStorage.setItem("tuogongtwotyNo", res.data.tyNo)
              this.getList()
              this.bhgvisible = false
            } else {
              this.$message.error(res.data)
              return
            }
          }
        }).catch(err => this.requestFailed(err))
          .finally(() => {
            this.loading = false
          })

      } else {

        // delete this.billData.formData.tiDd
        lsqcadd({
          ...this.billData.formData,
          mesLsQc1s: this.mesLsQc1s,
          sys: this.billData.formData.sys ? 'T' : 'F',
          chkKnd: this.billData.formData.chkKnd ? 'T' : 'F',
          mesQcTy1s: mesQcTy1s
          // mesTiQcFileList:this.tableData,
        }).then((res) => {
          if (res.code == 0) {
            if (res.msg === 'success') {
              this.billData.formData.lsNo = res.data.lsNo
              localStorage.setItem("tuogongtwotyNo", res.data.tyNo)
              localStorage.removeItem('tuogongtwotiNo')
              this.getList()
              this.$message.success('保存成功')
              // if(localStorage.getItem('completiontwotiNo')){
              //   this.billData.formData.tiNo = localStorage.getItem('completiontwotiNo')
              //   this.getListtreat()
              // }else{
              //   this.billData.formData.tyNo = localStorage.getItem('completiontwotyNo')
              //   this.getList()
              // }
              this.bhgvisible = false
            } else {
              this.$message.error(res.data)
              return
            }
          }
        }).catch(err => this.requestFailed(err))
          .finally(() => {
            this.loading = false
          })
      }
    },
    deldatabhg() {
      const that = this
      if (that.billData.formData.lsNo) {
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('identifier.delcont'),
          okText: this.$t('public.sure'),
          okType: 'warn',
          cancelText: this.$t('public.cancel'),
          onOk() {
            that.loading = true
            lsqcdel({
              lsNo: that.billData.formData.lsNo,
              usr: that.billData.formData.usr
            }).then((res) => {
              if (res.code === 0) {
                if (res.msg === 'success') {
                  that.bhgvisible = false
                  if (localStorage.getItem('completiontwotiNo')) {
                    that.billData.formData.tiNo = localStorage.getItem('completiontwotiNo')
                    that.getListtreat()
                  } else {
                    that.billData.formData.tyNo = localStorage.getItem('completiontwotyNo')
                    that.getList()
                  }
                  that.$message.success(res.data)
                } else {
                  that.$message.error(res.data)
                }
              }
            }).catch(err => that.requestFailed(err))
              .finally(() => {
                that.loading = false
              })
          },
          onCancel() { }
        })
      }
    },
    addTableRowtwo() {
      this.tableAddtwo();
    },
    tableAddtwo() {
      for (let i = 0; i < 3; i++) {
        let obj = {
          czNo: "",
          qty: "",
          rem: "",
          id: null,
          bilNo: null
        };
        this.mesLsQc1s.push(obj);
      }
    },
    bhghandleCancel() {
      this.mesLsQc1s = []
      this.bhgvisible = false
    },
    bhghandle() {
      if (this.testData.length > 0 && this.billData.formData.tyNo && +this.billData.formData.qtyLost > 0) {
        if (JSON.parse(JSON.stringify(this.billData.formData.mesLsQc1s))) {
          this.mesLsQc1s = []
          this.mesLsQc1s = JSON.parse(JSON.stringify(this.billData.formData.mesLsQc1s))
        } else {
          this.mesLsQc1s = []
        }
        this.bhgvisible = true
        this.addTableRowtwo()
        console.log(this.$store.state.user.info.username, 'vvvghhhhhhhhhhhh')
        this.billData.formData.czSal = this.$store.state.user.info.username
        this.dataczSal = this.$store.state.user.info.username
        this.billData.formData.czDate = moment(new Date()).format('YYYY-MM-DD')
      } else {
        this.$message.warning('不符合条件')
      }
    },
    currentChangespcNo(currentPage) {
      this.tablePagespcNo.currentPage = currentPage;
      this.pickerSearch();
    },
    pageSizeChangespcNo(pageSize) {
      this.tablePagespcNo.pageSize = pageSize;
      this.pickerSearch();
    },
    jumpnonconform() {
      this.$router.push({
        path: '/mes/nonconform/detail',
        query: {
          lsNo: this.billData.formData.lsNo
        }
      })
    },
    jumpentrust() {
      localStorage.setItem("tuogongtyNo", this.billData.formData.tiNo);
      this.$router.push({
        path: '/mes/entrustworker/check',
        query: {
          tiNo: this.billData.formData.tiNo
        }
      })
    },
    stopScroll(evt) {
      evt = evt || window.event;
      if (evt.preventDefault) {
        // Firefox
        evt.preventDefault();
        evt.stopPropagation();
      } else {
        // IE
        evt.cancelBubble = true;
        evt.returnValue = false;
      }
      return false;
    },
    cosetiao() {
      if (this.billData.formData.tyNo) {
        this.$router.push({
          name: 'entrustwo',
          params: {
            tab: 'second'
          }
        })
      } else {
        this.$router.push({
          name: 'entrustwo',
          params: {
            tab: 'first'
          }
        })
      }
    },
    handleDelVersion(row) {

      var list = []
      //  { id, fileName,bucketName }
      if (row.distinguish) {
        this.tableData = this.tableData.filter(i => i.id !== row.id)

      } else {
        sysfiledel(
          {
            id: row.id,
            // fileName:row.fileName,
            // bucketName:row.bucketName,
            // idList:list
          }
        )
          .then(res => {
            if (res.msg == 'success') {
              this.tableData = this.tableData.filter(i => i.id !== row.id)
              this.$message.success('删除成功')
            } else {
              this.$message.success('删除失败')
            }
            this.loading = false
            // this.getList()
          })
          .catch(err => {
            this.loading = false
            this.requestFailed(err)
          })

      }
      //  this.tableData.forEach((item, index) => {
      //     if(item.id === id){
      //       item.contraFileUrlList.forEach((item2, index2) => {
      //       list.push(item2.id)
      //       })
      //     }
      //  })
    },
    handleAddVersion() {
      this.$refs.uploadFile.create({ title: '上传' }, 'oooooooooo')
      // this.$refs.AddVersion.create(true, true)
    },
    deldata() {
      const that = this
      this.$confirm(this.$t('public.del.content'), this.$t('public.del.title'), {
        confirmButtonText: this.$t('public.sure'),
        cancelButtonText: this.$t('public.cancel'),
        type: 'warning'
      }).then(async () => {
        that.loading = true
        workqctyDel({
          tyNo: that.billData.formData.tyNo,
          usr: that.billData.formData.usr
        }).then((res) => {
          if (res.code === 0) {
            if (res.msg === 'success') {
              // that.cosetiao()
              this.$bus.$emit('closeTab',this.$route.path)
              that.$message.success(res.data)
            } else {
              that.$message.error(res.data)
            }
          }
        }).catch(err => that.requestFailed(err))
          .finally(() => {
            that.loading = false
          })
      }).catch(() => {
        this.loading = false;
      });
    },
    // 渲染组件
    choose(obj) {

      var map = {}
      // if (obj.obj.name === 'upSalNo') {
      //   this.salName = obj.obj.data.name
      //   map[obj.obj.name] = obj.obj.data.salNo
      // }
      // if (obj.obj.name === 'salNo') {
      //   this.salName = obj.obj.data.name
      //   map[obj.obj.name] = obj.obj.data.salNo
      // }
      if (obj.obj.name === 'dep') {
        this.flag = true
        this.billData.formData.depName = obj.obj.data.name

        this.billData.formData.dep = obj.obj.data.deptCode
      }
      // this.form.setFieldsValue(map)
    },
    changeqtyLost(val) {
      if (val.row.qtyLost < 0) {
        this.$message.warning('不合格量不能为负')
        val.row.qtyLost = ''
        return
      }
      if (val.row.qtyLost == 0) {
        this.$message.warning('不合格量不能为零')
        val.row.qtyLost = ''
        return
      }
      this.billData.formData.qtyLost = 0

      let getQtys = 0;
      if (!this.billData.formData.qtyLost) {
        this.billData.formData.qtyLost = 0
      }
      this.testData.forEach(i => {
        if (i.spcNo) {
          this.billData.formData.qtyLost += +i.qtyLost;
        }
      })
      if (this.billData.formData.qtyLost > 0) {
        this.billData.formData.qcId = 'F'
        if (+this.billData.formData.qty - +this.billData.formData.qtyLost < 0) {

          this.$message.info('不合格量超出')
          val.row.qtyLost = ''
          this.billData.formData.qtyLost = 0
          this.testData.forEach(i => {
            if (i.spcNo) {
              this.billData.formData.qtyLost += +i.qtyLost;
            }
          })
        } else {
          this.billData.formData.qtyOk = +this.billData.formData.qty - +this.billData.formData.qtyLost
        }
      } else {
        this.billData.formData.qcId = 'T'
      }
      this.$forceUpdate()
      prdZFQty({
        prdNo: this.billData.formData.prdNo,
        type: 'z',
        qty: val.row.qtyLost
      }).then((res) => {
        if (res) {
          val.row.qty1Lost = res.data.qty1
          this.$forceUpdate()
          this.changeqty1Lost(val)
        }
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 货品关键字搜索回显数据
    getGoodsPopover({
                      spcNo,
                      name,
                      prd3No,
                      spc,
                      qtyNow,
                      qtyLrn,
                      ut,
                      price,
                      qty,
                      rtoTax,
                      amtnNet,
                      tax,
                      amt,
                      itm,
                      id,
                      qty1Lost,
                      ygNo,
                      dep,
                      qup,
                      qtyLost,
                    }) {
      let newRow = {
        spcNo,
        spcName: name,
        prdNo: prd3No,
        spc,
        qtyNow,
        qtyLrn,
        ut,
        price,
        qty: "1",
        lastQty: 0,
        rtoTax: rtoTax,
        amtnNet: "",
        tax: "",
        amt: "",
        itm: -1,
        prdtId: id,
        id: null,
        qtyRtn: null,
        qty1Lost,
        ygNo,
        dep,
        qup,
        qtyLost,
      };
      newRow.show = false;
      // this.$set(this.testData, this.pickerIndex, newRow);
      this.testData[this.pickerIndex].spcNo = spcNo
      this.testData[this.pickerIndex].spcName = name
      this.isShowPopVel = false;
      // let getQtys = 0;
      // this.testData.forEach((item) => {
      //   if (item.qty) {
      //     getQtys += +item.qty;
      //     this.qtys = getQtys;
      //   }
      // });
      this.visible = false;
      for (let i = 0; i < this.testData.length; i++) {//for循环遍历每个元素，每遍历一个元素就是arr[i]
        let num = 0//定义一个num统计arr[i]出现的次数，
        for (let j = 0; j < this.testData.length; j++) { //根据这个arr[i]做一次循环遍历这个数组，
          if (this.testData[i].spcNo) {
            if (this.testData[i].spcNo == this.testData[j].spcNo) {//arr[i]出现一次就会+1
              num++
            }
          }
        }
        if (num > 1) {
          this.testData[this.pickerIndex].spcNo = ''
          this.testData[this.pickerIndex].spcName = ''
          this.$message.warning('原因代号不能重复')
          this.$forceUpdate()
        }
      }
    },
    prdt3leave() {
      setTimeout(() => {
        this.visible = false;
      }, 100);
    },
    pickerSearch() {
      // {
      //     ...this.billData.formData,
      //     sysFiles:this.sysFiles
      //   }
      spcLstpage({
        spcNo: this.queryKeyword,
        name: this.queryKeyword,
        current: this.tablePagespcNo.currentPage,
        size: this.tablePagespcNo.pageSize,

      }).then((res) => {
        if (res.code === 0) {

          this.pickerList = res.data.records
          this.spcNototal = res.data.total
        }
      }).catch(err => this.requestFailed(err))
      //    .finally(() => {
      //         this.loading = false
      // })
    },
    // input聚焦
    focus(index, row, e) {
      this.pickerSearch();
      this.visible = true;
      // this.isSaveColor = "danger";
      this.pickerIndex = index;
      let getTop = e.target.getBoundingClientRect().top + 35;

      if (e.target.getBoundingClientRect().top - 380 < 0) {
        this.objStyle.top = getTop.toString() + "px";
      } else {
        this.objStyle.top = (e.target.getBoundingClientRect().top - 390).toString() + "px";
      }
      this.objStyle.left = e.target.getBoundingClientRect().left + "px";



      // let getTop = e.target.getBoundingClientRect().top + 35;
      // this.objStyle.left = e.target.getBoundingClientRect().left + "px";
      // this.objStyle.top = getTop.toString() + "px";


    },
    blurNameInput() {
      // this.visible = false;
      setTimeout(() => {
        this.isShowPopVel = false;
      }, 100);
    },
    inputNameInput(val) {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        this.queryKeyword = val;
        this.goods_currentPage = 1;
        this.pickerSearch();
      }, 500);
    },
    handleSelectionChangereason(rows) {
      this.multipleSelection = rows;
    },
    // handleSelectionChange(rows) {
    //   this.multipleSelection = rows;
    // },
    // 添加行
    addTableRow() {
      this.tableAdd();
    },
    // 本地删除
    locaDelTableRow() {
      let that = this
      if (this.multipleSelection.length === 0 || this.multipleSelection === []) {
        this.$message.error("请勾选要操作的数据！");
        return false;
      }
      this.$confirm(this.$t('public.del.content'), this.$t('public.del.title'), {
        confirmButtonText: this.$t('public.sure'),
        cancelButtonText: this.$t('public.cancel'),
        type: 'warning'
      }).then(async () => {
        let selectRows = that.multipleSelection
        selectRows.forEach((item) => {
          if (item.id === null || item.id === "" || item.id === 0) {
          } else {
            that.detailIds.push(
              Object.assign(
                {},
                {
                  id: item.id,
                  prdNo: item.prdNo,
                  itm: item.itm,
                  lastQty: item.lastQty,
                  prdtId: item.prdtId,
                }
              )
            );
          }
        });
        that.billData.formData.qtyLost = 0
        selectRows.forEach((item) => {
          that.testData.splice(that.testData.indexOf(item), 1);
        });

        that.testData.forEach(i => {
          that.billData.formData.qtyLost += +i.qtyLost;
        })
        if (that.billData.formData.qtyLost > 0) {
          that.billData.formData.qcId = 'F'
        } else {
          that.billData.formData.qcId = 'T'
        }
        that.billData.formData.qtyOk = +that.billData.formData.qty - +that.billData.formData.qtyLost
      }).catch(() => {
        that.$refs.multipleTable.clearSelection();
        that.$message.info("已取消删除");
      });
      
      // this.$confirm("此操作将删除, 是否继续?", "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning",
      // })
      // .then(() => {
      // })
      // .catch(() => {
      //   this.$refs.multipleTable.clearSelection();
      //   this.$message.info("已取消删除");
      // });
    },
    // 表格初始化，往数组里面添加50个对象
    tableAdd() {

      for (let i = 0; i < 1; i++) {
        let obj = {
          spcNo: "",
          spcName: "",
          qtyLost: null,
          name: "", // 品名
          id: null,
          qty1Lost: "",
          ygNo: "",
          dep: this.billData.formData.dep,
          qup: "",
          ygName: "",
          depName: this.billData.formData.depName,
          zcName: this.billData.formData.zcName,
          zcNo: this.billData.formData.zcNo
        };
        this.testData.push(obj);
      }
    },
    //行点击
    handRowClick(row, column, event) {
      var index;
      this.testData.map((c, i) => {
        if (c == row) {
          index = i;
        }
      });
      this.$set(this.testData, index, row);
      event.stopPropagation();
    },
    // this.billData.formData.chktyId: this.billData.formData.chktyId ? 'T' : 'F'
    onChangeinspect(e) {

      // this.checked=e
      //  this.billData.formData.qcId = e.target.checked
    },
    onChange(checked) {
      this.checked = checked;

    },
    onChangeclosecase(e) {


      this.billData.formData.chkKnd = e.target.checked
      this.$forceUpdate()
    },
    onChangeLaboratory(e) {
      this.billData.formData.sys = e.target.checked
    },
    // seeFile(obj, formD) {
    //   this.sysFiles = obj
    //   this.sysFiles.forEach((item, index) => {
    //     this.tableData.push(item)
    //   })
    //   // this.objpic = obj
    //   // this.formD = formD
    //   // this.tableData.forEach((item, index) => {
    //   //   if (this.formD.wyID === item.wyID) {
    //   //     if (item.imageUrlList == null) {
    //   //       item.imageUrlList = []
    //   //       this.objpic.forEach((item2, index2) => {
    //   //         item.imageUrlList.push(item2)
    //   //       })
    //   //     } else {
    //   //       this.objpic.forEach((item2, index2) => {
    //   //         item.imageUrlList.push(item2)
    //   //       })
    //   //     }
    //   //   }
    //   // })
    // },
    async save() {
      this.$refs.anRef.sendData()
      let validflag
      const vxeFo = this.$refs.dfo.$refs.vxeForm
      await vxeFo.validate(valid => {
        validflag = ((valid === undefined) ? true : false);
        if (valid) {
          this.$message.warning('请选择部门')
          return
        }
      })
      if (validflag) {
        //  this.billData.formData.chkKnd = this.billData.formData.chkKnd ? 'T' : 'F'
        //  this.billData.formData.sys = this. entity.sys ? 'T' : 'F'
        let mesQcTy1s = []
        if (this.testData) {
          mesQcTy1s = this.testData.filter((item) => {
            return item.spcNo !== "" && item.spcNo !== null;
          })
        }
        let flagqtyLost = false
        if (mesQcTy1s.length) {
          for (let i = 0; i < mesQcTy1s.length; i++) {
            if (+mesQcTy1s[i].qtyLost > 0) { } else {
              flagqtyLost = true
              break
            }
          }
        }
        if (flagqtyLost) {
          this.$message.warning('请输入不合格量')
          return
        }
        this.tableData.forEach(item => {
          if (item.distinguish) {
            delete item.distinguish
          }
        })
        // if(this.billData.formData.chktyId==true){
        //   this.billData.formData.chktyId = 'T'
        // }else{
        //   this.billData.formData.chktyId = 'F'
        // }
        delete this.billData.formData.unitName
        delete this.billData.formData.qcTypeName
        delete this.billData.formData.whName
        if (this.billData.formData.tyNo) {
          workqcUpdedit({
            ...this.billData.formData,
            sys: this.billData.formData.sys ? 'T' : 'F',
            chkKnd: this.billData.formData.chkKnd ? 'T' : 'F',
            mesTiQcFileList: this.tableData,
            mesQcTy1s: mesQcTy1s
          }).then((res) => {
            if (res.code === 0) {
              if (res.msg === 'success') {
                this.$message.success(res.data)
                this.getList()
              } else {

                this.$message.error(res.data)
                return
              }

            }
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })

        } else {
          delete this.billData.formData.tiDd
          this.billData.formData.closeId = 'F'
          workqcaddsave({
            ...this.billData.formData,
            sys: this.billData.formData.sys ? 'T' : 'F',
            chkKnd: this.billData.formData.chkKnd ? 'T' : 'F',
            mesTiQcFileList: this.tableData,
            mesQcTy1s: mesQcTy1s,
          }).then((res) => {
            if (res.code == 0) {
              if (res.msg === 'success') {
                this.billData.formData.tyDd = res.data.tyDd
                this.billData.formData.tyNo = res.data.tyNo
                localStorage.setItem("tuogongtwotyNo", res.data.tyNo)
                localStorage.removeItem('tuogongtwotiNo')
                this.getList()
                this.$message.success('保存成功')
              } else {
                this.$message.error(res.data)
                return
              }

            }
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        }
      }
    },
    selectChangeEvent({ checked, records, reserves, row, rowIndex }) {

      this.multipleSelectiontwo = records
    },
    selectChangeAll({ records, checked }) {

      this.multipleSelectiontwo = records
    },

    editclick() {
      this.isEdit = true
    },
    closeclick() {
      this.isEdit = false
    },
    reset() {
      this.billData.formData = {}
    },
    // seepic(index) {
    //   let arry = []
    //   arry.push(this.tableData[index])
    //
    //   this.$refs.ModalPic.create({ title: '查看' }, arry)
    // },
    // uploadpic(row) {
    //   this.$refs.uploadFile.create({ title: '上传' }, row)
    //   // this.$refs.Upload.create(
    //   //   {
    //   //     title: '上传'
    //   //   },
    //   // )
    //   // this.$refs.Upload.create({ title: '上传' }, row)
    // },
    // check(){
    //   this.$router.push('')
    // },
    handleChangereson(val) {

      this.multiplereason = val
    },
    cancelGetData() {
      this.visible = false
    },
    otngetData() {

      if (this.multiplereason.length) {
        this.reasondata = this.multiplereason
      }
      this.visible = false
    },
    reasonclick(val) {
      this.visible = true
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSelectionChangetwo(val) {
      this.multipleSelectiontwo = val
    },
    handleTabClick(key) {
      if (key == '1') {
        this.getList()
      } else if (key == '2') {
        this.getListtwo()
      }
    },
    // onChange(data, dateString) {
    //
    //   this.queryParam.date = dateString
    //
    // },
    depme(row) {
      datadepPage(
        Object.assign({
          current: 1,
          size: 10,
          deptCode: row
        })
      ).then(response => {
        this.billData.formData.depName = response.data.records[0].name
        this.data = this.billData.formData.dep + '-' + this.billData.formData.depName
        this.testData.forEach(item => {
          if (item.spcNo) { } else {
            item.dep = this.data
          }
        })
        this.tableAdd()
      }).catch(err => this.requestFailed(err))
    },
    getListtreat() {
      this.loading = true
      workdjInfodetail(
        Object.assign({
          // total: this.tablePage.currentPage,
          // current: this.tablePage.currentPage,
          // size: this.tablePage.pageSize,
          // staDd:this.queryParam.staDd,
          // endDd:this.queryParam.endDd,
          tiNo: this.billData.formData.tiNo,
          // zcNo:this.queryParam.zcNo,
          // moNo:this.queryParam.moNo,
          // prdNo:this.queryParam.prdNo,
          // prdName:this.queryParam.prdName,
          // bilNo:this.queryParam.bilNo
        })
      ).then(response => {
        this.loading = false
        this.billData.formData = response.data
        this.billData.formData = response.data
        if (this.billData.formData.lsNo) {
          this.isClaim = true
        } else {
          this.isClaim = false
        }
        if (this.billData.formData.dep) {
          this.depme(this.billData.formData.dep)
        }

        this.billData.formData.qcId = ''
        if (this.billData.formData.qtyLost) {
          if (this.billData.formData.qtyLost > 0) {
            this.billData.formData.qcId = 'F'
          } else {
            this.billData.formData.qcId = 'T'
          }
        } else {
          this.billData.formData.qtyLost = 0
          this.billData.formData.qcId = 'T'
        }
        this.billData.formData.qtyOk = this.billData.formData.qty - +this.billData.formData.qtyLost
        // this.data = this.billData.formData.dep + '-' + this.billData.formData.depName
        // this.tableData = response.data.mesTiQcFileList
        this.$refs.anRef.getList(response.data.mesTiQcFileList,1);

        if (this.billData.formData.chkKnd == 'T') {
          this.billData.formData.chkKnd = true
        } else {
          this.billData.formData.chkKnd = false
        }
        if (this.billData.formData.sys == 'T') {
          this.billData.formData.sys = true
        } else {
          this.billData.formData.sys = false
        }
        // if(this.billData.formData.qtyRtn >= this.billData.formData.qty){
        //   this.closeId = 'T'
        // }
        if (this.billData.formData.qcId == 'T') {
          this.billData.formData.qcIds = '合格'
        }else if (this.billData.formData.qcId == 'F') {
          this.billData.formData.qcIds = '不合格'
        }
        this.billData.formData.tyDd = moment(this.billData.formData.tyDd).format('YYYY-MM-DD')
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    getList() {
      this.loading = true
      workqcyjlnfo(
        Object.assign({
          // total: this.tablePage.currentPage,
          // current: this.tablePage.currentPage,
          // size: this.tablePage.pageSize,
          // staDd:this.queryParam.staDd,
          // endDd:this.queryParam.endDd,
          tyNo: this.billData.formData.tyNo,
          // zcNo:this.queryParam.zcNo,
          // moNo:this.queryParam.moNo,
          // prdNo:this.queryParam.prdNo,
          // prdName:this.queryParam.prdName,
          // bilNo:this.queryParam.bilNo
        })
      ).then(response => {
        this.loading = false

        this.billData.formData = response.data
        this.billData.formData = response.data
        if (this.billData.formData.lsNo) {
          this.isClaim = true
        } else {
          this.isClaim = false
        }
        if (this.billData.formData.qtyLost) {
        } else {
          this.billData.formData.qtyLost = 0
        }
        if (this.billData.formData.dep) {
          this.depme(this.billData.formData.dep)
        }

        this.testData = response.data.mesQcTy1s
        this.testData.forEach(item => {
          if (item.dep == null) {
            item.dep = ''
          }
          if (item.depName == null) {
            item.depName = ''
          }
          if (item.zcName == null) {
            item.zcName = ''
          }
          if (item.zcNo == null) {
            item.zcNo = ''
          }
          if (item.spcNo) { } else {
            item.zcNo = this.billData.formData.zcNo
            item.zcName = this.billData.formData.zcName
          }
        })
        // this.data = this.billData.formData.dep + '-' + this.billData.formData.depName
        this.$refs.anRef.getList(response.data.mesTiQcFileList,1);

        // this.tableData = response.data.mesTiQcFileList
        if (this.billData.formData.chkKnd == 'T') {
          this.billData.formData.chkKnd = true
        } else {
          this.billData.formData.chkKnd = false
        }

        if (this.billData.formData.sys == 'T') {
          this.billData.formData.sys = true
        } else {
          this.billData.formData.sys = false
        }
        if (this.billData.formData.qtyRtn >= this.billData.formData.qty) {
          this.closeId = 'T'
        }
        if (this.billData.formData.qcId == 'T') {
          this.billData.formData.qcIds = '合格'
        }else if (this.billData.formData.qcId == 'F') {
          this.billData.formData.qcIds = '不合格'
        }
        this.billData.formData.tyDd = moment(this.billData.formData.tyDd).format('YYYY-MM-DD')
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
  }
}

</script>

<style lang="less" scoped>
.title-container {
  display: flex;
  align-items: center;
  justify-content: left;
  width: 100%;
}

.title-text {
  color: #303133;
  font-weight: 600;
}

.title-actions {
  margin-left: 10px;
  display: flex;
  gap: 5px;
}
.el-collapse {
  ::v-deep .el-collapse-item__header.is-active {
    border-bottom: 1px solid darkgrey;
  }

  ::v-deep .el-collapse-item__header {
    border-bottom: 1px solid darkgrey;
    font-size: 17px;
    font-weight: 520;
    padding-left: 20px;
  }

  ::v-deep .el-collapse-item__content {
    padding: 5px 5px;
    //border-bottom: red 5px solid;
  }
}

::v-deep .grid-wrapper {
  height: 180px;
  overflow: auto;
}

.sup_info_item {
  padding: 15px 0;
}



.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 0px
}

.compoct .el-form-item--small .el-form-item__content,
.el-form-item--small .el-form-item__label {
  line-height: 24px;
}

.sup_info_basics_container {
  border: 1px solid #E5E5E5;
  border-radius: 10px;
  padding: 0px 20px 0px 20px;
}

.dialogborder .ant-modal-body {
  padding: 10px !important;
}

.dialogborder .ant-modal {
  top: 5% !important;
}
</style>
