<template>
  <div class="consrm" style="position:relative">
    <div style="position: absolute;top: 6px;left:195px;z-index:1">
      <!-- <a-button
      style="margin-left: 8px"
      type="primary"
      @click="newlyadd"
      size="small"
    >新增</a-button> -->
      <a-button style="margin-left: 8px" icon="search" type="primary" size="small" @click="search">{{ $t('public.query')
        }}</a-button>
      <a-button icon="reload" size="small"
                style="margin-left: 8px;    color: #909399;background: #f4f4f5;border-color: #d3d4d6;" @click="reset">{{
          $t('public.reset') }}</a-button>
      <a-button style="margin-left: 8px" type="primary" @click="handle" size="small"
                v-if="activeNameMain=='first'">处理</a-button>
      <!--
    <a-button
      style="margin-left: 8px"
      type="primary"
      @click="check"
      size="small"
    >检验</a-button> -->
    </div>
    <el-tabs type="border-card" v-model="activeNameMain" @tab-click="handleClick">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="开始日期">
                <a-date-picker style="width:100%" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                               v-model="queryParam.staDd" placeholder="请输入开始日期" />
                <!-- <a-range-picker v-model="valueone" @change="onChange" format="YYYY-MM-DD"/> -->
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="结束日期">
                <a-date-picker style="width:100%" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                               v-model="queryParam.endDd" placeholder="请输入结束日期" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('异常单号')">
                <a-input v-model="queryParam.trNo" :placeholder="$t('异常单号')" ref="submissiontwo" />
                <!-- @keyup.enter.native="queryonetwo" -->
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('部门')" prop="dep">
                <my-selectListwo url="mes/basicData/depPage" :read-only="true" :tableColumn="$Column.salmDep"
                                 :form="$Form.salmDep" :data="data" name="dep" @choose="choose($event)" allowClear ref="selectList"
                                 v-decorator="['dep', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                                 :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
                <!-- <span>{{ entity.dep }}</span> -->
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('品号')" style="position: relative;">
                <el-popover placement="bottom-start" width="600" trigger="click" v-model="visible" ref="dcPopover"
                            title="" :popper-options="{ boundariesElement: 'body'}">
                  <!-- :popper-options="{ boundariesElement: 'viewport', removeOnDestroy: true }" -->
                  <!-- :popper-options="{ boundariesElement: 'viewport', removeOnDestroy: true }" -->
                  <div id="popover">
                    <el-input slot="reference" v-model="queryParam.prdNo" clearable placeholder="请选择"
                              style="width:100%;" @focus="zsMc" @input="zsMcinput" size="mini"></el-input>
                    <el-table :data="prdNoList" @row-click="handdle" style="width: 100%;overflow:scroll;height:450px;"
                              border size="mini">
                      <el-table-column prop="prdNo" label="代号"></el-table-column>
                      <el-table-column prop="name" label="名称"></el-table-column>
                    </el-table>
                    <div style="display: flex;justify-content: space-between;margin: 2px">
                      <el-pagination background :page-sizes="[5, 10,20, 30, 50]" :page-size="20" :pager-count="5"
                                     @size-change="pageSizeChangefour" :current-page="tablePagefour.currentPage"
                                     @current-change="currentChangefour" layout="total,sizes,prev, pager, next"
                                     :total="totalCountfour"></el-pagination>
                    </div>
                    <!-- <div>
                        <el-pagination
                          background
                          :page-size="10"
                          :current-page="padtPage"
                          @current-change="currentChangePrdt"
                          layout="prev, pager, next"
                          :total="prdtCount"
                        ></el-pagination>
                      </div> -->
                  </div>
                  <el-input slot="reference" v-model="queryParam.prdNo" clearable placeholder="请选择" style="width:100%;"
                            @focus="zsMc" @input="zsMcinput" size="mini" @clear="handleEmpty"></el-input>
                  <!-- :readonly="isReadonlyPrdNameInput" -->
                </el-popover>
                <!-- <a-input
                     style="width:150px"
                      v-model="condition.prdNo"
                      clearable
                      :placeholder="$t('品号')"
                    /> -->
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('工序')">
                <el-popover placement="bottom" width="600" trigger="click" v-model="visibletwo" title=""
                            :popper-options="{ boundariesElement: 'body'}">
                  <!-- ref="dcPopovertwo" -->
                  <!-- :popper-options="{ boundariesElement: 'viewport', removeOnDestroy: true }" -->
                  <div id="popover">
                    <el-input slot="reference" v-model="queryParam.zcNo" clearable placeholder="请选择"
                              style="max-width: 200px;width:100%;" @focus="zsMctwo" @input="zsMctwoinput"
                              size="mini"></el-input>
                    <el-table :data="zcNoList" @row-click="handdletwo" style="width: 100%;overflow:scroll;height:450px;"
                              border size="mini">
                      <el-table-column prop="zcNo" label="工序"></el-table-column>
                      <el-table-column prop="name" label="工序名称"></el-table-column>
                    </el-table>
                    <div style="display: flex;justify-content: space-between;margin: 2px">
                      <el-pagination background :page-sizes="[20, 30, 50,100]" :page-size="20" :pager-count="5"
                                     @size-change="pageSizeChangezhi" :current-page="tablePagezhi.currentPage"
                                     @current-change="currentChangezhi" layout="total,sizes,prev, pager, next"
                                     :total="totalCounzhi"></el-pagination>
                    </div>
                  </div>
                  <el-input slot="reference" @input="zsMctwoinput" v-model="queryParam.zcNo" clearable placeholder="请选择"
                            style="width:100%;" @focus="zsMctwo" size="mini"></el-input>
                </el-popover>
              </a-form-item>
            </a-col>

            <!-- <a-col
                :md="6"
                :sm="24"
              >
                <a-form-item :label="$t('处理方式')">
                  <el-select
                  v-model="queryParam.prcId"
                  placeholder="请选择处理方式"
                  size="mini"
                  clearable
                  style="width:100%;"
                >
                  <el-option
                    v-for="item in categoryList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                </a-form-item>
              </a-col> -->
            <!-- <a-col
                :md="6"
                :sm="24"
              >
                <a-form-item :label="$t('处理否')">
                  <el-select
                  v-model="queryParam.prcId"
                  placeholder="请选择处理否"
                  size="mini"
                  clearable
                  style="width:100%;"
                >

                  <el-option
                    v-for="item in processingNo"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                </a-form-item>
              </a-col> -->
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('工单号')">
                <el-popover placement="bottom" width="600" trigger="click" v-model="visibledan" title=""
                            :popper-options="{ boundariesElement: 'body'}">
                  <!-- ref="dcPopovertwo" -->
                  <!-- :popper-options="{ boundariesElement: 'viewport', removeOnDestroy: true }" -->
                  <div id="popover">
                    <el-input slot="reference" v-model="queryParam.moNo" clearable placeholder="请选择" style="width:100%;"
                              @focus="zsMcdan" @input="zsMcdaninput" size="mini"></el-input>
                    <el-table :data="zcNoListdan" @row-click="handdletwodan"
                              style="width: 100%;overflow:scroll;height:450px;" border size="mini">
                      <el-table-column prop="moNo" label="工单号"></el-table-column>
                      <el-table-column prop="mrpNo" label="品号"></el-table-column>
                      <el-table-column prop="prdName" label="品名"></el-table-column>

                      <!--
                        <el-table-column prop="bilId"  label="来源识别码"></el-table-column> -->
                      <!-- <el-table-column prop="closeId"  label="结案否"></el-table-column> -->
                    </el-table>
                    <div style="display: flex;justify-content: space-between;margin: 2px">
                      <el-pagination background :page-sizes="[20, 30, 50,100]" :page-size="20" :pager-count="5"
                                     @size-change="pageSizeChangedan" :current-page="tablePagedan.currentPage"
                                     @current-change="currentChangedan" layout="total,sizes,prev, pager, next"
                                     :total="totalCoundan"></el-pagination>
                    </div>
                  </div>
                  <el-input slot="reference" @input="zsMcdaninput" v-model="queryParam.moNo" clearable placeholder="请选择"
                            style="width:100%;" @focus="zsMcdan" size="mini"></el-input>
                </el-popover>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('派工单号')">
                <a-input v-model="queryParam.pgNo" :placeholder="$t('派工单号')" />
              </a-form-item>
            </a-col>

            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('货品特征')">
                <a-input v-model="queryParam.prdMark" :placeholder="$t('货品特征')" ref="submissiontwo" />
                <!-- @keyup.enter.native="queryonetwo" -->
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24" v-if="activeNameMain =='first'">
              <a-form-item :label="$t('处理方式:')">
                <el-select v-model="queryParam.prcId" placeholder="请选择处理方式" size="mini" clearable style="width:100%;">

                  <el-option v-for="item in categoryList" :key="item.value" :label="item.label"
                             :value="item.value"></el-option>
                </el-select>
              </a-form-item>
            </a-col>

            <a-col :md="6" :sm="24" v-if="activeNameMain =='second'">
              <a-form-item :label="$t('转单否')">
                <el-select v-model="queryParam.zdId" placeholder="请选择转单否" size="mini" clearable style="width:100%;">

                  <el-option v-for="item in zhuandan" :key="item.value" :label="item.label"
                             :value="item.value"></el-option>
                </el-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24" v-if="activeNameMain =='second'">
              <a-form-item :label="$t('处理单号')">
                <a-input v-model="queryParam.ycclNo" :placeholder="$t('处理单号')" ref="submissiontwo" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <span class="table-page-search-submitButtons">

              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <el-tab-pane label="待处理" name="first">
        <a-row :gutter="8">
          <a-col :span="24">
            <a-row>
              <vxe-toolbar custom>
                <template v-slot:buttons>
                </template>
              </vxe-toolbar>
              <vxe-table size='small' border="inner" resizable stripe highlight-current-row show-overflow
                         highlight-hover-row export-config :loading="loading" :data="tableData"
                         :keyboard-config="{ isArrow: true }" :edit-config="{ trigger: 'click', mode: 'row' }"
                         :header-cell-style="{ backgroundColor: '#F4F5F9', color: '#7A8085', fontWeight: '400', fontSize: '12px' }"
                         :cell-style="{ fontSize: '12px' }" height="580px" ref="tableone" @cell-dblclick="mfBxHanddleone">
                <vxe-table-column type="checkbox" align="center" :width="50"></vxe-table-column>
                <vxe-table-column field="trNo" title="异常单号" align="center" width="140"></vxe-table-column>
                <vxe-table-column field="moNo" title="工单号" align="center" width="130"></vxe-table-column>
                <vxe-table-column field="zcNo" title="工序" align="center" width="150"></vxe-table-column>
                <vxe-table-column field="zcName" title="工序名称" align="center" width="160"></vxe-table-column>
                <vxe-table-column field="trDd" title="异常日期" align="center" width="110"></vxe-table-column>
                <vxe-table-column field="prdNo" title="品号" align="center" width="120"></vxe-table-column>
                <vxe-table-column field="prdName" title="品名" align="center" width="120"></vxe-table-column>
                <vxe-table-column field="qty" title="异常数量" align="center" width="100"></vxe-table-column>
                <vxe-table-column field="spcNo" title="异常原因" align="center" width="130"></vxe-table-column>
                <vxe-table-column field="prcId" title="处理方式" align="center" width="110">
                  <template slot-scope="scope">
                    <div v-if="scope.row.prcId == 1">强制缴库</div>
                    <div v-if="scope.row.prcId == 2">报废处理</div>
                    <div v-if="scope.row.prcId == 3">返工</div>
                    <div v-if="scope.row.prcId == 5">放行</div>
                  </template>
                </vxe-table-column>
                <vxe-table-column field="pgNo" title="派工单号" align="center" width="120"></vxe-table-column>
                <vxe-table-column field="tzNo" title="工序单号" align="center" width="130"></vxe-table-column>
                <vxe-table-column field="usr" title="制单人" align="center" width="110"></vxe-table-column>
                <vxe-table-column field="prdMark" title="特征" align="center" width="110"></vxe-table-column>
                <vxe-table-column field="prdMark" title="特征" align="center" width="120"></vxe-table-column>
                <vxe-table-column field="rem" title="备注" align="center" width="160"
                                  show-overflow="tooltip"></vxe-table-column>
              </vxe-table>
              <vxe-pager :loading="loading" :current-page="tablePage.currentPage" :page-size="tablePage.pageSize"
                         :total="totalCount" :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
                         @page-change="handlePageChange">
              </vxe-pager>
              <!-- <el-table
                stripe
                :data="tableData"
                highlight-current-row
                :cell-style="{ verticalAlign: 'top' }"
                :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                style="width: 100%;"
                @selection-change="handleSelectionChange"
               height="580px"
              >
                <el-table-column type="selection" align="center" width="50"></el-table-column>
                <el-table-column prop="trNo" width="140" align="left" label="异常单号"></el-table-column>
                <el-table-column prop="moNo" width="130" align="left" label="工单号"></el-table-column>
                <el-table-column prop="zcNo" width="170" align="left" label="制程"></el-table-column>
                <el-table-column prop="zcName" width="200" align="left" label="制程名称"></el-table-column>
                <el-table-column prop="trDd" width="110" align="left" label="异常日期"></el-table-column>
                <el-table-column prop="prdNo" width="120" align="left" label="品号"></el-table-column>
                <el-table-column prop="prdName" width="120" align="left" label="品名"></el-table-column>
                <el-table-column prop="qty" width="110" align="left" label="异常数量">
                </el-table-column>
                <el-table-column prop="spcNo" width="130" align="left" label="异常原因"></el-table-column>
                <el-table-column prop="prcId"  width="110" align="left" label="处理方式"></el-table-column>
                <el-table-column prop="pgNo" width="110" align="left" label="派工单号"></el-table-column>
                <el-table-column prop="tzNo" width="400" align="left" label="通知单号"></el-table-column>
                <el-table-column prop="usr" width="110" align="left" label="制单人"></el-table-column>
                <el-table-column prop="prdMark" width="100" align="left" label="特征"></el-table-column>
                <el-table-column prop="rem" width="100" align="left" label="备注" show-overflow-tooltip></el-table-column>
              </el-table>

              <div style="display: flex;justify-content: space-between;margin: 2px">
                <el-pagination
                  background
                  :page-sizes="[5, 10,20, 30, 50]"
                  :page-size="20"
                  :pager-count="5"
                  @size-change="pageSizeChange"
                  :current-page="tablePage.currentPage"
                  @current-change="currentChange"
                  layout="total,sizes,prev, pager, next"
                  :total="totalCount"
                ></el-pagination>
              </div> -->

            </a-row>
          </a-col>
        </a-row>
      </el-tab-pane>
      <el-tab-pane label="已处理" name="second">
        <vxe-toolbar custom>
          <template v-slot:buttons>
          </template>
        </vxe-toolbar>
        <vxe-table size='small' border="inner" resizable stripe highlight-current-row show-overflow highlight-hover-row
                   export-config :loading="loading" :data="tableDatatwo" :keyboard-config="{ isArrow: true }"
                   :edit-config="{ trigger: 'click', mode: 'row' }"
                   :header-cell-style="{ backgroundColor: '#F4F5F9', color: '#7A8085', fontWeight: '400', fontSize: '12px' }"
                   :cell-style="{ fontSize: '12px' }" height="580px" @cell-dblclick="mfBxHanddle">
          <vxe-table-column type="checkbox" align="center" :width="50"></vxe-table-column>
          <vxe-table-column field="trNo" title="异常单号" align="center" width="140"></vxe-table-column>
          <vxe-table-column field="ycclDd" title="处理日期" align="center" width="140">
            <template slot-scope="scope">
              {{scope.row.ycclDd | formatDate}}
            </template>
          </vxe-table-column>
          <vxe-table-column field="ycclNo" title="处理单号" align="center" width="140"></vxe-table-column>
          <vxe-table-column field="moNo" title="工单号" align="center" width="130"></vxe-table-column>
          <vxe-table-column field="trDd" title="异常日期" align="center" width="110"></vxe-table-column>
          <vxe-table-column field="prdNo" title="品号" align="center" width="120"></vxe-table-column>
          <vxe-table-column field="prdName" title="品名" align="center" width="120"></vxe-table-column>
          <vxe-table-column field="qty" title="异常数量" align="center" width="100"></vxe-table-column>
          <vxe-table-column field="spcNo" title="异常原因" align="center" width="130"></vxe-table-column>
          <vxe-table-column field="bilBuild" title="生成单据否" align="center" width="120"></vxe-table-column>
          <vxe-table-column field="usrClName" title="处理人名" align="center" width="130"></vxe-table-column>
          <vxe-table-column field="prdMark" title="特征" align="center" width="130"></vxe-table-column>
          <vxe-table-column field="rem" title="备注" align="center" width="160"
                            show-overflow="tooltip"></vxe-table-column>
        </vxe-table>
        <vxe-pager :loading="loading" :current-page="tablePagetwo.currentPage" :page-size="tablePagetwo.pageSize"
                   :total="totalCount" :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
                   @page-change="handlePageChangetwo">
        </vxe-pager>
        <!-- <el-table
                :data="tableDatatwo"
                 stripe
                highlight-current-row
                :cell-style="{ verticalAlign: 'top' }"
                :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                style="width: 100%;"
                @row-dblclick="mfBxHanddle"
                height="580px"
              >
              <el-table-column type="selection" align="center" width="50"></el-table-column>
              <el-table-column prop="trNo" width="100" align="left" label="异常单号"></el-table-column>
                 <el-table-column prop="tyDd" width="110" align="left" label="处理日期">
                  <template slot-scope="scope">
                      {{scope.row.rcclDd | formatDate}}
                  </template>
                 </el-table-column>
                <el-table-column prop="ycclNo" width="130" align="left" label="处理单号"></el-table-column>
                 <el-table-column prop="moNo" width="110" align="left" label="工单号"></el-table-column>
                <el-table-column prop="trDd" width="110" align="left" label="异常日期"></el-table-column>

                <el-table-column prop="prdNo" width="100" align="left" label="品号"></el-table-column>
                <el-table-column prop="prdName" width="100" align="left" label="品名"></el-table-column>
                <el-table-column prop="qty" width="120" align="left" label="异常数量">

                </el-table-column>
                <el-table-column prop="spcNo" width="140" align="left" label="异常原因"></el-table-column>
                <el-table-column prop="bilBuild" width="120" align="left" label="生成单据否"></el-table-column>
                <el-table-column prop="usrClName" width="130" align="left" label="处理人名"></el-table-column>
                <el-table-column prop="prdMark" width="130" align="left" label="特征"></el-table-column>
               <el-table-column prop="rem" width="120" align="left" label="备注" show-overflow-tooltip></el-table-column>
              </el-table>

              <div style="display: flex;justify-content: space-between;margin: 2px">
                <el-pagination
                  background
                  :page-sizes="[5, 10,20, 30, 50]"
                  :page-size="20"
                  @size-change="pageSizeChangetwo"
                  :current-page="tablePagetwo.currentPage"
                  @current-change="currentChangetwo"
                  layout="total,sizes,prev, pager, next"
                  :total="totalCount"
                ></el-pagination>
              </div> -->
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { errpage, errerrClPage, errerrClStaPage, errerrClStart, } from '@/api/mes/work'
import {
  basicDatazc, mfMoPage, basicDataprd, fetchList, getItem, start, completverified
} from '@/api/mes/quality'
import MySelectListwo from '@/components/MySelectListwo'
import { sbtzqueryPage } from '@/api/device/category'
import { mapGetters, mapState } from 'vuex'
import moment from 'moment'
import { first } from 'xe-utils/methods'
// import formList from './formList'
// import inspection from './inspection'

export default {
  components: {
    MySelectListwo
  },
  data() {
    return {
      mes_quality_query: 'mes_quality_query',
      mes_quality_reset: 'mes_quality_reset',
      mes_quality_newOpen: 'mes_quality_newOpen',
      mes_quality_inspection: 'mes_quality_inspection',
      mes_quality_pause: 'mes_quality_inspection',
      mes_quality_continues: 'mes_quality_continues',
      mes_quality_task: 'mes_quality_task',
      queryParam: {
        staDd: '',
        endDd: '',
        prcId: '3',
        zdId: 'F',
        prdMark: '',
        moNo: '',
        pgNo: '',
        zcNo: '',
        prdNo: '',
        dep: '',
        trNo: '',
        ycclNo: '',
      },
      tableData: [],
      tableDatatwo: [],
      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      tablePagetwo: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      row: {},
      rowIndex: {},
      // 按钮控制
      btn: {
        newOpen: true, // 首检开工
        open: true, // 开始
        pause: true, // 暂停
        continues: true, // 继续
        report: true, // 报工
        exceptional: true, // 异常
        inspection: true, // 送检
        introspecting: true // 自检
      },
      // queryParam: {
      //   tyNo:'',
      //   tiNo:'',
      //   zcNo:'',
      //   moNo:'',
      //   prdName:'',
      //   prdNo:'',
      //   staDd:'',
      //   endDd:'',
      //    chkKnd:false,
      //   sys:false,
      // },
      activeName: '1',
      valueone: [],
      multipleSelection: [],
      activeNameMain: "first",
      radioSelection: null,
      radio: '',
      totalCount: -1,
      data: '',
      visibletwo: false,
      tablePagezhi: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      zcNoList: [],
      totalCounzhi: -1,
      categoryList: [
        { value: '1', label: '强制缴库', disabled: false },
        { value: '2', label: '报废处理', disabled: false },
        { value: '3', label: '返工', disabled: false },
        { value: '5', label: '放行', disabled: false },
      ],
      processingNo: [
        { value: '1', label: '否', disabled: false },
        { value: '2', label: '是', disabled: false },
        { value: '3', label: '全部', disabled: false },
      ],
      visibledan: false,
      zcNoListdan: [],
      totalCoundan: -1,
      tablePagedan: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      prdNoList: [],
      visible: false,
      tablePagefour: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      totalCountfour: -1,
      zhuandan: [
        { value: 'F', label: '未转单' },
        { value: 'T', label: '已转单' },
        { value: '', label: '全部', },
      ],
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    }),
    ...mapGetters(['permissions'])
  },
  created() {
    if (this.$route.params.tab) {
      this.activeNameMain = this.$route.params.tab
      if (this.$route.params.tab == 'first') {
        this.queryParam = JSON.parse(localStorage.getItem('ycclzydai'))
        this.data = this.queryParam.dep + '-' + this.queryParam.depNm
        this.getList()
      } else {
        this.queryParam = JSON.parse(localStorage.getItem('ycclzyyi'))
        this.data = this.queryParam.dep + '-' + this.queryParam.depNm
        this.getListtwo()
      }
    } else {
      this.queryParam.staDd = moment().subtract(30, "days").format('YYYY-MM-DD')
      this.queryParam.endDd = moment(new Date()).format('YYYY-MM-DD')
      this.queryParam.zdId = 'F'
    }
    this.getList()
  },
  methods: {
    handleEmpty(val) {
      this.queryParam.prdNo = ''
      this.queryParam.prdName = ''
    },
    handle() {
      if (this.activeNameMain == 'first') {
        this.multipleSelection = this.$refs.tableone.getCheckboxRecords()
        if (this.multipleSelection.length) {
          let arryzong = []
          this.multipleSelection.forEach(element => {
            arryzong.push(element.trNo)
          })

          if (this.activeNameMain == 'first') {
            localStorage.setItem("yichangzuoyedai", JSON.stringify(arryzong));
            localStorage.removeItem('yichangzuoyedaisj')
            localStorage.removeItem('yichangzuoyeyi')

            localStorage.removeItem('ycclzyyi')
            this.$router.push({ name: 'abnormalworkdetail' })
          }
        } else {
          this.$message.warning('请选择数据')
        }
      } else {
        this.$message.warning('请双击进入详情页')
      }
    },
    handleClick(val) {
      this.reset()
      if (val.name == 'first') {
        this.getList()
        this.$nextTick(() => {
          this.$refs.submission.focus()
        })
      } else {
        this.getListtwo()
        this.$nextTick(() => {
          this.$refs.submissiontwo.focus()
        })
      }
    },
    prdNoListmethod() {
      this.loading = false
      basicDataprd(
        {
          current: this.tablePagefour.currentPage,
          size: this.tablePagefour.pageSize,
          prdNo: this.queryParam.prdNo,
        }).then(response => {
        // this.prdloading = false
        this.prdNoList = response.data.records
        this.$nextTick(() => {
          this.$refs.dcPopover.updatePopper() // 注意主要是这里
        })

        this.totalCountfour = response.data.total
        // this.tablePagetwo.currentPage = response.data.current

      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    pageSizeChangefour(pageSize) {
      this.tablePagefour.pageSize = pageSize;
      // this.getListdialog();
      this.prdNoListmethod()
    },
    currentChangefour(currentPage) {
      // this.visible = true;
      this.tablePagefour.currentPage = currentPage;
      // this.getListdialog();
      this.prdNoListmethod()
    },
    handdle(row, event, column) {
      this.queryParam.prdNo = row.prdNo
      this.queryParam.prdName = row.name
      this.queryParam.prdMark = row.prdMark
      this.visible = false;
    },
    zsMc() {
      this.tablePagefour.currentPage = 1
      this.prdNoListmethod()
    },
    zsMcinput() {
      this.tablePagefour.currentPage = 1
      this.prdNoListmethod()
    },
    pageSizeChangedan(pageSize) {
      this.tablePagedan.pageSize = pageSize;
      this.zcNodan()
    },
    currentChangedan(currentPage) {
      ;
      this.tablePagedan.currentPage = currentPage;
      this.zcNodan()
    },
    zsMcdan() {
      this.tablePagedan.currentPage = 1
      this.zcNodan()
    },
    zsMcdaninput() {
      this.tablePagedan.currentPage = 1
      this.zcNodan()
    },
    handdletwodan(row, event, column) {
      this.queryParam.moNo = row.moNo
      this.queryParam.tzNo = row.tzNo
      // this.queryParam.bilNo = row.bilNo
      // this.queryParam.bilId = row.bilId
      this.queryParam.closeId = row.closeId

      // this.queryParam.name = row.name
      // this.condition.prdMark = row.prdMark
      this.visibledan = false;
    },
    zcNodan() {
      mfMoPage(
        {
          current: this.tablePagedan.currentPage,
          size: this.tablePagedan.pageSize,
          moNo: this.queryParam.moNo,
        }).then(response => {
        // this.prdloading = false
        this.zcNoListdan = response.data.records
        // this.$nextTick(()=> {
        //   this.$refs.dcPopovertwo.updatePopper() // 注意主要是这里
        // })

        this.totalCoundan = response.data.total
        // this.tablePagetwo.currentPage = response.data.current

      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    pageSizeChangezhi(pageSize) {
      this.tablePagezhi.pageSize = pageSize;
      this.zcNomethod()
    },
    currentChangezhi(currentPage) {
      ;
      this.tablePagezhi.currentPage = currentPage;
      this.zcNomethod()
    },
    zsMctwo() {
      this.tablePagezhi.currentPage = 1
      this.zcNomethod()
    },
    zsMctwoinput() {
      this.tablePagezhi.currentPage = 1
      this.zcNomethod()
    },
    handdletwo(row, event, column) {
      this.queryParam.zcNo = row.zcNo
      this.queryParam.zcName = row.name
      // this.condition.prdMark = row.prdMark
      this.visibletwo = false;
    },
    zcNomethod() {
      basicDatazc(
        {
          current: this.tablePagezhi.currentPage,
          size: this.tablePagezhi.pageSize,
          zcNo: this.queryParam.zcNo,
        }).then(response => {
        // this.prdloading = false
        this.zcNoList = response.data.records
        // this.$nextTick(()=> {
        //   this.$refs.dcPopovertwo.updatePopper() // 注意主要是这里
        // })

        this.totalCounzhi = response.data.total
        // this.tablePagetwo.currentPage = response.data.current

      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    choose(obj) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.queryParam.dep = ''
          this.queryParam.depNm = ''
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'dep') {
        this.flag = true
        this.queryParam.depNm = obj.obj.data.name
        if (obj.obj.data.deptCode) {
          this.queryParam.dep = obj.obj.data.deptCode
          this.data = this.queryParam.dep + '-' + this.queryParam.depNm
        } else {
          this.data = ''
          this.queryParam.dep = ''
          this.queryParam.depNm = ''
        }
      }
    },
    search() {

      // this.getList()
      if (this.activeNameMain == 'first') {
        this.tablePage.currentPage = 1
        this.getList()
        //    this.$nextTick(() => {
        //   //  this.$refs.submission.focus()
        //  })
      } else {
        this.tablePagetwo.currentPage = 1
        this.getListtwo()
        //     this.$nextTick(() => {
        //   //  this.$refs.submissiontwo.focus()
        //  })
      }
    },
    newlyadd() {
      localStorage.removeItem('yichangzuoye')
      this.$router.push({
        name: 'abnormalworkdetail', params: {
          id: ''
        }
      })
    },
    // queryonetwo(){

    //   // if(this.activeNameMain == 'first'){
    //      this.tablePage.currentPage = 1
    //     this.getList()
    //   // }else{
    //   //   this.tablePagetwo.currentPage = 1
    //   //   this.getListtwo()
    //   // }
    // },
    raidchange(row) {
      // 获取选中数据 row表示选中这一行的数据，可以从里面提取所需要的值
      this.multipleSelection = row


    },
    pageSizeChange(pageSize) {
      this.tablePage.pageSize = pageSize;
      this.getList();
    },
    pageSizeChangetwo(pageSize) {
      this.tablePagetwo.pageSize = pageSize;
      this.getListtwo();
    },
    currentChange(currentPage) {

      // this.visible = true;
      this.tablePage.currentPage = currentPage;
      this.getList();
    },
    currentChangetwo(currentPage) {
      // this.visible = true;
      this.tablePagetwo.currentPage = currentPage;
      this.getListtwo();
    },
    mfBxHanddleone({ row }) {
      localStorage.setItem("yichangzuoyedaisj", row.trNo);
      localStorage.setItem("ycclzydai", JSON.stringify(this.queryParam))
      localStorage.removeItem("yichangzuoyedai");
      localStorage.removeItem('yichangzuoyeyi')

      localStorage.removeItem('ycclzyyi')
      this.$router.push({
        name: 'abnormalworkdetail', params: {
          id: row.trNo
        }
      })
    },
    // 表格双击事件
    mfBxHanddle({ row }) {
      this.edit = row;
      this.editId = row.id;
      localStorage.setItem("yichangzuoyeyi", row.ycclNo);
      localStorage.setItem("ycclzyyi", JSON.stringify(this.queryParam));
      localStorage.removeItem('yichangzuoyedai')
      localStorage.removeItem("yichangzuoyedaisj");

      localStorage.removeItem("ycclzydai");
      this.$router.push({
        name: 'abnormalworkdetail', params: {
          id: row.ycclNo
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // handleClick(val) {


    //   if (val.name == 'first') {
    //    this.getList()
    //    this.$nextTick(() => {
    //     this.$refs.submission.focus()
    //   })
    //   }else{
    //     this.getListtwo()
    //     this.$nextTick(() => {
    //     this.$refs.submissiontwo.focus()
    //   })
    //   }

    // },
    // onChange (data, dateString) {
    //
    //   this.queryParam.date = dateString

    // },
    onChange(data, dateString) {
      this.queryParam.date = dateString
      this.queryParam.staDd = this.queryParam.date[0]
      this.queryParam.endDd = this.queryParam.date[1]

    },
    // 按钮初始化
    btnInit() {
      this.btn.newOpen = true // 首检开工 null||""
      this.btn.pause = true // 暂停 2
      this.btn.continues = true // 继续 3
      this.btn.inspection = true // 首检 1
    },
    getListtwo() {
      this.loading = true
      errerrClPage(
        Object.assign({
          current: this.tablePagetwo.currentPage,
          // current: this.tablePage.currentPage,
          size: this.tablePagetwo.pageSize,
          staDd: this.queryParam.staDd,
          endDd: this.queryParam.endDd,

          prdMark: this.queryParam.prdMark,
          moNo: this.queryParam.moNo,
          pgNo: this.queryParam.pgNo,
          zcNo: this.queryParam.zcNo,
          prdNo: this.queryParam.prdNo,
          dep: this.queryParam.dep,
          trNo: this.queryParam.trNo,
          zdId: this.queryParam.zdId,
          ycclNo: this.queryParam.ycclNo,
          // bilNo:this.queryParam.bilNo,
          // pgNo: this.queryParam.pgNo
        })
      ).then(response => {
        this.loading = false
        this.tableDatatwo = response.data.records
        this.totalCount = response.data.total
        // this.tablePage.currentPage = response.data.current
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    getList() {

      // this.loading = true
      errerrClStaPage(
        Object.assign(
          {
            staDd: this.queryParam.staDd,
            endDd: this.queryParam.endDd,
            prcId: this.queryParam.prcId,
            prdMark: this.queryParam.prdMark,
            moNo: this.queryParam.moNo,
            pgNo: this.queryParam.pgNo,
            zcNo: this.queryParam.zcNo,
            prdNo: this.queryParam.prdNo,
            dep: this.queryParam.dep,
            trNo: this.queryParam.trNo,
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
          }
        )
      )
        .then(res => {

          this.tableData = res.data.records
          this.totalCount = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 获取列表数据
    // getList () {
    //   this.loading = true

    //   sbtzqueryPage(
    //     // Object.assign(
    //       {
    //         current: this.tablePage.currentPage,
    //       // current: this.tablePage.currentPage,
    //       size: this.tablePage.pageSize,
    //       // keyword:this.queryParam.keyword
    //       //   staDd:this.queryParam.staDd,
    //       // endDd:this.queryParam.endDd,
    //       // tiNo:this.queryParam.tiNo,
    //       // zcNo:this.queryParam.zcNo,
    //       // moNo:this.queryParam.moNo,
    //       // prdNo:this.queryParam.prdNo,
    //       // prdName:this.queryParam.prdName,
    //       // sys: this.queryParam.sys ? 'T' : 'F',
    //       // chkKnd: this.queryParam.chkKnd ? 'T' : 'F',
    //       // bilNo:this.queryParam.bilNo,
    //       // pgNo: this.queryParam.pgNo
    //     }
    //   ).then(response => {
    //     this.loading = false
    //     this.tableData = response.data.records
    //     this.tableData.forEach((i,index) => {
    //       i.addid = index + 1
    //     })
    //     this.totalCount = response.data.total
    //     // this.tablePage.currentPage = response.data.current
    //   }).catch(err => this.requestFailed(err))
    //   .finally(() => {
    //     this.loading = false
    //   })
    // },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    handlePageChangetwo({ currentPage, pageSize }) {
      this.tablePagetwo.currentPage = currentPage
      this.tablePagetwo.pageSize = pageSize
      this.getListtwo()
    },
    ClickEvent({ row }) {
      this.btnInit()
      this.rowIndex = row
      getItem(
        Object.assign({
          pgNo: row.pgNo,
          itm: row.itm,
          tiNo: row.tiNo,
          prdNo: row.prdNo
        })
      ).then(response => {
        this.row = response.data
        this.btnState(this.row)
      }).catch(err => this.requestFailed(err))
    },
    // 按钮状态
    btnState(row) {
      // 首检 ：3 暂停：2  继续：1
      if (row.state === '-1') {
        this.btn.newOpen = false
      }
      if (row.state === '0' || row.state === '3' || row.state === '1') {
        this.btn.pause = false // 暂停
        this.btn.inspection = false // 首检
      }
      if (row.state === '2') {
        this.btn.continues = false // 继续 3
      }
    },
    getBtnValue(state) {
      // 获取对应按钮提示
      let value = ''
      switch (state) {
        case '0':
          value = this.$t('quality.newOpen')
          break
        case '3':
          value = this.$t('quality.inspection')
          break
        case '2':
          value = this.$t('quality.pause')
          break
        case '1':
          value = this.$t('quality.continues')
          break
        case '4':
          value = this.$t('quality.task')
          break
      }
      return value
    },
    // 按钮
    onSubmit(state) {
      switch (state) {
        case '0':
        case '1':
        case '2':
          this.loading = true
          let btnMes = this.getBtnValue(state)
          start({
            state: state,
            pgNo: this.row.pgNo,
            itm: this.row.itm,
            id: this.row.id
          }).then((res) => {

            if (res.code === 0) {
              if (res.msg === 'fail') {
                btnMes = res.data
              } else {
                btnMes = btnMes + this.$t('public.success')
                const row = this.row
                this.ClickEvent({ row })
              }
            }
            this.$notification.success({
              message: this.$t('public.tip'),
              description: btnMes
            })
            this.loading = false
          }).catch(err => this.requestFailed(err))

          break
        case '3':
          const row = this.rowIndex
          this.$refs.a.create(row)
          break
      }
    },
    reset() {
      this.queryParam = {
        staDd: '',
        endDd: '',
        dep: '',
        depNm: '',
        trNo: '',
        zcNo: '',
        pgNo: '',
        moNo: '',
        prdNo: '',
        prdMark: '',
        prdName: '',
        zdId: '',
      },
        this.data = ''
      this.queryParam.staDd = moment().subtract(30, "days").format('YYYY-MM-DD')
      this.queryParam.endDd = moment(new Date()).format('YYYY-MM-DD')

    }
  }
}
</script>

<style>
.el-table .el-table__cell {
  padding: 6px 0;
}

.ant-input {
  height: 28px;
}

/* .ant-tabs-nav-container{font-size:16px}
::v-deep .el-radio__label{
display:none
} */
</style>