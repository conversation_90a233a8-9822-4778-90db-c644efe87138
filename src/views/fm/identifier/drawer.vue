<template>
  <div>
    <a-drawer
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :destroyOnClose="true"
      width="70%"
    >
      <template slot="title">
        <span class="title-name">{{ title }}</span>
        <span class="title-age">
          <a-dropdown v-if="this.modeType==='2'">
            <a-button class="ant-dropdown-link">
              {{ $t('public.action') }}
              <a-icon type="down" />
            </a-button>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="delet()">{{ $t('public.delete') }}</a>
              </a-menu-item>
            </a-menu>

          </a-dropdown>
        </span>
      </template>
      <a-form-model
        layout="horizontal"
        ref="ruleForm"
        :rules="rules"
        :model="form"
      >
        <a-row>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('identifier.fileNo')"
              prop="fileNo"
            >
              <a-input
                v-model="form.fileNo"
                :disabled="formStatus"
                :placeholder="$t('identifier.placeholder.fileNo')"
                @blur="fileNoInputBlur"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('identifier.fileName')"
              prop="fileName"
            >
              <a-input
                v-model="form.fileName"
                :disabled="formStatus"
                :placeholder="$t('identifier.placeholder.fileName')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('identifier.pageCount')"
              prop="pageCount"
            >
              <a-input-number
                style="width:100%"
                v-model="form.pageCount"
                :disabled="formStatus"
                :placeholder="$t('identifier.placeholder.pageCount')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('identifier.prdNo')"
              prop="prdNo"
            >
              <my-selectList
                :url.sync='url'
                :tableColumn="$Column.fmVerPrdNo"
                :form="$Form.fmVerPrdNo"
                :read-only="true"
                :data="dataPrdNo"
                :flieNo="this.flieNo"
                multiple
                name="prdNo"
                :disabled="isDisabalePrdNo"
                @choose="choose($event)"
                ref="selectList"
                v-model="form.prdNo"
                :placeholder="$t('identifier.placeholder.prdNo')"
              ></my-selectList>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('identifier.verNo')"
              prop="verNo"
            >
              <my-selectList
                url="/fm/fileveryl/page?stopId=F"
                :read-only="true"
                :tableColumn="$Column.fmVer"
                :form="$Form.fmVer"
                :data="data"
                name="verNo"
                :disabled="formStatus"
                @choose="choose($event)"
                ref="selectList"
                v-model="form.verNoName"
                :placeholder="$t('identifier.placeholder.verNo')"
              ></my-selectList>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('identifier.idxNo')"
              prop="idxNo"
            >
              <!-- /fm/fileidnxyl/page?stopId=F&approveFlag=T -->
              <my-selectList
                url="/fm/fileidnxyl/page?stopId=F"
                :read-only="true"
                :tableColumn="$Column.fmIdx"
                :form="$Form.fmIdx"
                :data="data1"
                name="idxNo"
                :disabled="formStatus"
                @choose="choose($event)"
                ref="selectList"
                v-model="form.idxNo"
                :placeholder="$t('identifier.placeholder.idxNo')"
              ></my-selectList>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('identifier.ygNo')"
            >
              <my-selectList
                url="/fm/filedepyl/page"
                :read-only="true"
                :tableColumn="$Column.fmsalm"
                :form="$Form.fmsalm"
                :data="data2"
                name="ygNo"
                :disabled="formStatus"
                @choose="choose($event)"
                ref="selectList"
                v-model="form.ygNo"
                :placeholder="$t('salm.placeholder.depName')"
              ></my-selectList>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('identifier.rem')"
            >
              <a-input
                type="textarea"
                v-model="form.rem"
                :disabled="formStatus"
                :placeholder="$t('identifier.placeholder.rem')"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-item
              :label="$t('file.enclosure')"
              v-bind="formItemLayout"
            >
              <a-upload-dragger
                accept=".pdf,.jpg,.png,.xlsx,.doc,.docx,.dwg"
                name="file"
                multiple
                :fileList="fileList"
                :remove="handleRemove"
                :beforeUpload="beforeUpload"
              >
                <p class="ant-upload-drag-icon">
                  <a-icon type="inbox" />
                </p>
                <p class="ant-upload-hint">
                  {{ $t('file.tip') }}
                </p>
              </a-upload-dragger>
            </a-form-item>
          </a-col>
          <!-- <a-col v-else>
            <a-form-item
              :label="$t('file.enclosure')"
              v-bind="formItemLayout"
            >
              <a-input
                v-model="form.fiName"
                :disabled="true"
              />
            </a-form-item>
          </a-col> -->

        </a-row>
      </a-form-model>
      <a-row :gutter="16">
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:right"
        >
          <a-button
            type="primary"
            :loading="loading"
            v-if="modeType==='0'"
            @click="handleOK()"
          >{{ $t('public.save') }}</a-button>
          <a-button
            type="primary"
            :loading="loading"
            v-if="modeType==='2'"
            @click="handleEdit()"
          >{{ $t('public.save') }}</a-button>
        </a-col>
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:left"
        >
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script>
import {
  edit,
  del
} from '@/api/fm/identifier'
import Vue from 'vue'
import {
  DEFAULT_TENANT_ID
} from '@/store/mutation-types'
import {
  addObjs, checkNo
} from '@/api/fm/identifier'
import store from '@/store'
import MySelectList from '@/components/MySelectList'
import moment from 'moment'

export default {
  components: {
    MySelectList
  },
  data () {
    return {
      title: '',
      disabled: false,
      visible: false,
      formStatus: false,
      loading: false,
      isDisabalePrdNo: false,
      url: '',
      modeType: '',
      data: '',
      data1: '',
      data2: '',
      dataPrdNo: '',
      flieNo: '',
      formD: {},
      row: {},
      fileId: '',
      fileList: [],
      headers: {},
      form: {},
      fileStat: -1,
      rules: {
        fileNo: [{
          required: true,
          // validator: this.handlePass,
          // message: this.$t('identifier.placeholder.fileNo'),
          trigger: 'change'
        }],
        fileName: [{
          required: true,
          message: this.$t('identifier.placeholder.fileName'),
          trigger: 'blur'
        }]
      },
      formItemLayout: {
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 15
          }
        }
      }
    }
  },
  props: {
    // save: {
    //   required: true,
    //   type: Boolean,
    // },
    id: {
      required: false,
      type: String,
    }
  },
  created () {
    const TENANT_ID = Vue.ls.get(DEFAULT_TENANT_ID)
    const token = store.getters.access_token
    this.headers = {
      Authorization: 'Bearer ' + token,
      TENANT_ID: TENANT_ID
    }
    if (this.form.prdNo === undefined) {
      this.isDisabalePrdNo = true
    }
  },
  methods: {
    // 校验
    handlePass (rule, value, callback) {
      // if (value) {
      //   checkNo({ fileNo: value }).then(response => {
      //     const result = response.data
      //     if (result === 0) {
      //       callback(new Error('文件编号已存在，请重新输入！'))
      //     } else {
      //       callback()
      //     }
      //   })
      // } else {
      //   callback(new Error(this.$t('identifier.placeholder.fileNo')))
      // }

    },
    handleRemove (file) {
      const index = this.fileList.indexOf(file)
      const newFileList = this.fileList.slice()
      newFileList.splice(index, 1)
      this.fileList = newFileList
    },
    beforeUpload (file) {
      this.fileStat = 0
      // this.fileList = [...this.fileList, file]   //多个批量文件
      this.fileList = [file]  //单个文件
      return false
    },
    choose (obj) {
      this.form[obj.obj.name] = obj.obj.data.name
      if (obj.obj.name === 'idxNo') {
        this.form[obj.obj.name] = obj.obj.data.idxNo
        this.form.idxName = obj.obj.value
      }
      if (obj.obj.name === "ygNo") {
        this.form[obj.obj.name] = obj.obj.data.rec
        this.form.ygName = obj.obj.data.recName
      }
      if (obj.obj.name === 'verNo') {
        this.form.verNo = obj.obj.data.verNo
        this.form.verNoName = obj.obj.value
      }
      if (obj.obj.name === 'prdNo') {
        let arr = []
        obj.obj.data.forEach(item => {
          arr.push(item.prdNo)
        })
        this.form.prdNo = arr.join()
        this.dataPrdNo = arr.join()
      }
    },
    // 取消
    onClose () {
      this.fileStat = -1
      this.loading = false
      this.visible = false
      this.form = {}
      this.fileList = []
      this.data = ''
      this.data1 = ''
      this.data2 = ''
      this.dataPrdNo = ''
    },
    create (model, formD) {
      this.formD = formD
      this.title = model.title
      this.modeType = '0'
      this.visible = true
      this.formStatus = false
      this.disabled = false
    },
    // 点击编辑按钮
    handleMenuClick () {
      this.disabled = false
      this.modeType = '2'
      this.title = this.$t('public.edit')
      this.formStatus = false
    },
    fileNoInputBlur (val) {
      // this.url = `/fm/mftffileyl/pageprd?fileNo=${val.target.value}`
      // 发请求获取品号
   
    },
    edit (model, row, formD) {
      this.formD = formD
      this.title = model.title
      this.modeType = '2'
      this.row = row
      this.visible = true
      this.data = row.verNo
      this.data1 = row.idxNo
      this.data2 = row.ygName
      this.dataPrdNo = row.prdNo
      if (row.fileId) {
        this.fileList = [
          {
            uid: '-1',
            name: row.fiName,
            status: 'done'
          }
        ]
      }
      this.form = {
        fileId: row.fileId,
        fileNo: row.fileNo,
        fileName: row.fileName,
        pageCount: row.pageCount,
        rem: row.rem,
        verNo: row.verNo,
        idxNo: row.idxNo,
        ygNo: row.ygNo,
        fiName: row.fiName,
        fiUrl: row.fiUrl,
        prdNo: row.prdNo
      }
    },
    // 添加确认
    handleOK () {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          if (this.save) { } else {
            var id = ''
            if (this.fileList.length > 0 && this.fileStat === 0) {
              const formData = new FormData()
              this.fileList.forEach(file => {
                formData.append('files', file)
              })
              formData.append('filelog', true)
              formData.append('bucketName', 'file-fm') // minio 文件存储桶名称
              this.uploading = true
              addObjs(formData)
                .then((res) => {
                  if (res) {
                    if (res.code === 0) {
                      id = res.data[0].id
                      this.fileId = id
                      this.form.fiName = res.data[0].original
                      this.form.fiUrl = res.data[0].url
                      this.loading = false
                      this.$message.success(this.$t('public.success'))
                      let prama = {
                        ...this.form,
                        fileId: id,
                        invalidId: 'F',
                        rcvDd: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
                        ...this.formD,
                      }
                      this.$emit('getList', prama)
                      this.onClose()
                    }
                  }
                })
                .catch(err => this.requestFailed(err))
                .finally(() => {
                  this.loading = false
                })
            } else {
              setTimeout(() => {
                let prama = {
                  ...this.form,
                  fileId: id,
                  invalidId: 'F',
                  rcvDd: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
                  ...this.formD,
                }

                this.$emit('getList', prama)
                this.onClose()
              }, 500)
            }

          }

        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
          return false
        }
      })
    },
    delet () {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('public.del.content'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk () {
          if (that.save) {
            that.form.id = that.row.id
            del(that.form).then((res) => {
              that.loading = false
              that.onClose()
              that.$emit('getSaveList', that.id)
              that.$message.success(that.$t('public.success'))
            }).catch(err => that.requestFailed(err))
              .finally(() => {
                that.loading = false
              })
          } else {
            let id = that.row._XID
            let del = 1
            that.$emit('getList', null, null, id, del)
            that.onClose()
          }
        },
        onCancel () { }
      })
    },
    // 确认编辑
    handleEdit () {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          if (this.save) {
            var fileId = ''
            if (this.fileList.length > 0 && this.fileStat === 0) {
              const formData = new FormData()
              this.fileList.forEach(file => {
                formData.append('files', file)
              })
              formData.append('filelog', true)
              formData.append('bucketName', 'file-fm') // minio 文件存储桶名称
              this.uploading = true
              addObjs(formData)
                .then((res) => {
                  if (res) {
                    if (res.code === 0) {
                      fileId = res.data[0].id
                      this.form.fiName = res.data[0].original
                      this.form.fiUrl = res.data[0].url
                      this.form.fileId = res.data[0].id
                      this.loading = false
                      this.$message.success(this.$t('public.success'))
                      this.form.id = this.row.id
                      this.form.rcvDd = moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
                        edit(this.form).then((res) => {
                          this.loading = false
                          this.onClose()
                          this.$emit('getSaveList', this.id)
                        }).catch(err => this.requestFailed(err))
                          .finally(() => {
                            this.loading = false
                          })
                    }
                  }
                }).catch(err => this.requestFailed(err))
            } else {

              this.form.id = this.row.id
              this.form.rcvDd = moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
                edit(this.form).then((res) => {
                  this.loading = false
                  this.onClose()
                  this.$message.success(this.$t('public.success'))
                  this.$emit('getSaveList', this.id)
                }).catch(err => this.requestFailed(err))
                  .finally(() => {
                    this.loading = false
                  })
            }

          } else {
            var fileId = ''
            if (this.fileList.length > 0 && this.fileStat === 0) {
              const formData = new FormData()
              this.fileList.forEach(file => {
                formData.append('files', file)
              })
              formData.append('filelog', true)
              formData.append('bucketName', 'file-fm') // minio 文件存储桶名称
              this.uploading = true
              addObjs(formData)
                .then((res) => {
                  if (res) {
                    if (res.code === 0) {
                      fileId = res.data[0].id
                      this.fileId = fileId
                      this.form.fiName = res.data[0].original
                      this.form.fiUrl = res.data[0].url
                      this.loading = false
                      this.$message.success(this.$t('public.success'))
                      this.form._XID = this.row._XID
                      let edit = -1
                      let prama = {
                        rcvDd: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
                        ...this.form,
                        ...this.formD,
                        // fileId必须放在最后 替换form中值
                        fileId: fileId || this.row.fileId,
                      }
                      this.$emit('getList', prama, edit)
                      this.onClose()
                    }
                  }
                }).catch(err => this.requestFailed(err))
            } else {
              setTimeout(() => {
                this.form._XID = this.row._XID
                let edit = -1
                let prama = {
                  rcvDd: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
                  ...this.form,
                  ...this.formD,
                  // fileId必须放在最后 替换form中值
                  fileId: fileId || this.row.fileId,
                }
               
                this.$emit('getList', prama, edit)
                this.onClose()
              }, 500)
            }

          }

        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
          return false
        }
      })
    }
  },
  watch: {
    'form.fileNo': {
      handler (value) {
        if (value === undefined || value === '' || value === null) {
          this.flieNo = ''
          this.url = `/fm/mftffileyl/pageprd?fileNo=${this.flieNo}`
          this.isDisabalePrdNo = true
    
        } else if (value !== '' || value !== undefined) {
          this.url = `/fm/mftffileyl/pageprd?fileNo=${value}`
          this.isDisabalePrdNo = false
          this.flieNo = value
        }
      },
      deep: true
    }
  }
}
</script>
