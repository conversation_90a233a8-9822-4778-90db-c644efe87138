
<template>
  <div class="consrm">
    <div  style="text-align:right;margin-right:16px;position: absolute;top: 102px;right:0px;z-index:999">
      <a-button
        size='small'
        type="primary"
        style="margin-left:10px"
        icon="plus"
        @click="add()"
      >{{ $t('public.add') }}</a-button>
      <a-button
        type="primary"
        size='small'
        style="margin-left: 8px"
        @click="dropdownMenuEvent('remove')"
      >{{ $t('public.delete') }}</a-button>
      <a-button
        type="primary"
        size='small'
        style="margin-left: 8px"
        @click="reset"
      >{{ $t('public.reset') }}</a-button>
    </div>
    <el-tabs type="border-card">
      <el-tab-pane label="币别资料">
        <a-card :bordered="false">
          <a-row :gutter="8">
            <a-col>
              <div class="table-page-search-wrcurer">
                <a-form layout="inline">
                  <a-row :gutter="48">
                    <a-col
                      :md="8"
                      :sm="24"
                    >
                      <a-form-item :label="$t('cur.curId')">
                        <a-input
                          v-model="queryParam.curId"
                          :placeholder="$t('cur.curId')"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col
                      :md="8"
                      :sm="24"
                    >
                      <a-form-item :label="$t('cur.name')">
                        <a-input
                          v-model="queryParam.name"
                          :placeholder="$t('cur.name')"
                        />
                      </a-form-item>
                    </a-col>
                    <a-col
                      :md="3"
                      :sm="24"
                      style="margin-top:5px;"
                    >
                <span class="table-page-search-submitButtons">
                  <a-button
                    size='small'
                    type="primary"
                    @click="search"
                  >{{ $t('public.query') }}</a-button>
                  
                </span>
                    </a-col>
                  </a-row>
                </a-form>
              </div>
              <!-- <vxe-toolbar custom>
                <template v-slot:buttons>
                  <a-dropdown :trigger="['click']">
                    <a-button size='small'>{{ $t('public.action') }}
                      <a-icon type="down" />
                    </a-button>
                    <a-menu slot="overlay">
                      <a-menu-item key="0">
                        <a @click="dropdownMenuEvent('remove')">{{ $t('public.delete') }}</a>
                      </a-menu-item>

                    </a-menu>
                  </a-dropdown>

                </template>
              </vxe-toolbar> -->
              <vxe-table
                border
                resizable
                stripe
                size='small'
                highlight-current-row
                show-overflow
                highlight-hover-row
                export-config
                ref="xTable"
                :loading="loading"
                :data="tableData"
                :keyboard-config="{ isArrow: true }"
                @cell-dblclick="cellDBLClickEvent"
                :edit-config="{ trigger: 'click', mode: 'row' }"
              >
                <vxe-table-column
                  type="checkbox"
                  fixed="left"
                  align="center"
                  :width="50"
                ></vxe-table-column>
                <vxe-table-column
                  field="curId"
                  fixed="left"
                  title="cur.curId"
                  align="center"
                ></vxe-table-column>
                <vxe-table-column
                  field="name"
                  title="cur.name"
                  align="center"
                ></vxe-table-column>
                <vxe-table-column
                  field="ijDd"
                  title="cur.ijDd"
                  align="center"
                ></vxe-table-column>
                <vxe-table-column
                  field="excRto"
                  title="cur.excRto"
                  align="center"
                ></vxe-table-column>
                <vxe-table-column
                  field="sysDept.name"
                  title="cur.dep"
                  align="center"
                ></vxe-table-column>
                <vxe-table-column
                  field="deproNo"
                  title="cur.deproNo"
                  align="center"
                ></vxe-table-column>
              </vxe-table>
              <vxe-pager
                :loading="loading"
                :current-page="tablePage.currentPage"
                :page-size="tablePage.pageSize"
                :total="tablePage.total"
                :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
                @page-change="handlePageChange"
              >
              </vxe-pager>
            </a-col>
            <!-- 添加弹出框 -->
            <cur-Drawer
              ref="Drawer"
              @onOk="onOk"
            />
          </a-row>
        </a-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { fetchCurList, delAll } from '@/api/basic/cur'
import curDrawer from './curDrawer'
import { mapGetters } from 'vuex'
export default {
  name: 'CurList',
  components: {
    curDrawer
  },
  data () {
    return {
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,
      tableData: [],
      // 查询参数
      queryParam: {}
    }
  },

  created () {
    this.getList()
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  methods: {
    search () {
      this.tablePage.currentPage = 1
      this.getList()
    },
    getList () {
      this.loading = true
      fetchCurList(
        Object.assign(
          {
            curId: this.queryParam.curId,
            name: this.queryParam.name,
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize
          }
        )
      )
        .then(res => {
          this.tableData = res.data.records
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    // 重置搜索内容
    reset () {
      this.queryParam = {}
      this.getList()
    },
    // 新增
    add () {
      this.$refs.Drawer.create({ title: this.$t('public.add') })
    },
    // 双击弹出编辑框
    cellDBLClickEvent ({ row }) {
      this.$refs.Drawer.edit({ title: this.$t('public.Detailed') }, row)
    },
    onOk () {
      this.getList()
    },
    // 删除按钮
    dropdownMenuEvent (name) {
      switch (name) {
        case 'remove': {
          const selectRecords = this.$refs.xTable.getCheckboxRecords()
          if (selectRecords.length) {
            const arr = []
            selectRecords.forEach(i => {
              return arr.push(i.id)
            })
            const that = this
            this.$confirm({
              title: this.$t('public.del.title'),
              content: this.$t('public.del.content'),
              okText: this.$t('public.sure'),
              okType: 'danger',
              cancelText: this.$t('public.cancel'),
              onOk () {
                that.loading = true
                delAll(arr)
                  .then(() => {
                    that.loading = false
                    that.getList()
                    that.$message.success(that.$t('public.success'))
                  })
                  .catch(err => that.requestFailed(err))
                  .finally(() => {
                    that.loading = false
                  })
              },
              onCancel () {
                that.loading = false
              }
            })
          } else {
            this.$message.warning(this.$t('public.list'))
          }
          break
        }
      }
    }

  }
}
</script>
<style lang="less">
</style>
