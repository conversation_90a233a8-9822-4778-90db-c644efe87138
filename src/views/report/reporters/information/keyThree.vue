<template>
  <a-card :bordered="false">
    <a-row :gutter="8">
      <div>
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="48">
              <a-col
                :xxl="6"
                :xl="6"
                :lg="8"
                :md="12"
                :sm="12"
              >
                <a-form-item :label="$t('information.date')">
                  <a-row :gutter="15">
                    <a-col
                      :xxl="12"
                      :xl="24"
                      :lg="24"
                      :md="24"
                      :sm="24"
                    >
                      <a-date-picker
                        v-model="staDd"
                        :disabled-date="disabledStartDate"
                        format="YYYY-MM-DD"
                        :placeholder="$t('information.staDd')"
                        @openChange="handleStartOpenChange"
                      />
                    </a-col>
                    <a-col
                      :xxl="12"
                      :xl="24"
                      :lg="24"
                      :md="24"
                      :sm="24"
                    >
                      <a-date-picker
                        v-model="endDd"
                        :disabled-date="disabledEndDate"
                        format="YYYY-MM-DD"
                        :placeholder="$t('information.endDd')"
                        :open="endOpen"
                        @openChange="handleEndOpenChange"
                      />
                    </a-col>
                  </a-row>
                </a-form-item>
              </a-col>
              <a-col
                :xxl="6"
                :xl="6"
                :lg="8"
                :md="12"
                :sm="12"
              >
                <!-- 炉号改为熔炼炉号information.HeatNumber -->
                <a-form-item :label="$t('Furnace_No.rlRllNo')">
                  <a-input
                    style="width: 100%"
                    v-model="queryParam.rem"
                  > </a-input>
                </a-form-item>
              </a-col>
              <a-col
                :xxl="6"
                :xl="6"
                :lg="8"
                :md="12"
                :sm="12"
              >
                <a-form-item :label="$t('work_details.moNo')">
                  <a-select
                    showSearch
                    :allowClear="true"
                    :placeholder="$t('work_details.moNo')"
                    style="width: 100%"
                    :filterOption="false"
                    @focus="handle_moNo"
                    @select="select_moNo"
                    v-model="queryParam.moNo"
                  >
                    <a-select-option
                      v-for="(i, index) in MoList"
                      :key="index"
                      :value="i"
                    >{{ i }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col
                :xxl="6"
                :xl="6"
                :lg="8"
                :md="12"
                :sm="12"
              >
                <a-form-item :label="$t('information.code')">
                  <a-input
                    v-model="queryParam.casNo"
                    :placeholder="$t('information.placeholder.code')"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="48">
              <template v-if="advanced">
                <a-col
                  :xxl="6"
                  :xl="6"
                  :lg="8"
                  :md="12"
                  :sm="12"
                >
                  <a-form-item :label="$t('information.zcNo')">
                    <a-select
                      showSearch
                      :allowClear="true"
                      :placeholder="$t('information.placeholder.zcNo')"
                      style="width: 100%"
                      :filterOption="false"
                      v-model="queryParam.zcNo"
                      @focus="handle_zcNo"
                    >
                      <a-select-option
                        v-for="(i, index) in ZcList"
                        :key="index"
                        :value="i.zcNo"
                      >{{ i.zcNo }}{{ i.name }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :xxl="6"
                  :xl="6"
                  :lg="8"
                  :md="12"
                  :sm="12"
                >
                  <a-form-item :label="$t('information.tzNo')">
                    <a-select
                      showSearch
                      :allowClear="true"
                      :placeholder="$t('information.placeholder.tzNorb')"
                      style="width: 100%"
                      :filterOption="false"
                      v-model="queryParam.tzNo"
                      @focus="handle_TzNorb"
                    >
                      <a-select-option
                        v-for="(i, index) in WrList"
                        :key="index"
                        :value="i"
                      >{{ i }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :xxl="6"
                  :xl="6"
                  :lg="8"
                  :md="12"
                  :sm="12"
                >
                  <a-form-item :label="$t('information.tzNorb')">
                    <a-select
                      showSearch
                      :allowClear="true"
                      :placeholder="$t('information.placeholder.tzNo')"
                      style="width: 100%"
                      :filterOption="true"
                      v-model="queryParam.wrNo"
                      @focus="handle_TzNo"
                    >
                      <a-select-option
                        v-for="(i, index) in TzList"
                        :key="index"
                        :value="i"
                      >{{ i }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :xxl="6"
                  :xl="6"
                  :lg="8"
                  :md="12"
                  :sm="12"
                >
                  <a-form-item :label="$t('information.cs1')">
                    <a-select
                      showSearch
                      :allowClear="true"
                      :placeholder="$t('information.placeholder.cs1')"
                      style="width: 100%"
                      :filterOption="false"
                      @focus="handle_CsNo1"
                      v-model="queryParam.csNo1"
                    >
                      <a-select-option
                        v-for="(i, index) in Zc1List"
                        :key="index"
                        :value="i.cs_no"
                      >{{
                        i.cs_no
                      }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :xxl="6"
                  :xl="6"
                  :lg="8"
                  :md="12"
                  :sm="12"
                >
                  <a-form-item :label="$t('information.zcNo2')">
                    <a-select
                      showSearch
                      :allowClear="true"
                      :placeholder="$t('information.placeholder.zcNo2')"
                      style="width: 100%"
                      :filterOption="false"
                      @focus="handle_ZcNo2"
                      v-model="queryParam.zcNo2"
                    >
                      <a-select-option
                        v-for="(i, index) in ZcNo2List"
                        :key="index"
                        :value="i.zcNo"
                      >{{
                        i.zcName
                      }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :xxl="6"
                  :xl="6"
                  :lg="8"
                  :md="12"
                  :sm="12"
                >
                  <a-form-item :label="$t('information.pgNo')">
                    <a-select
                      showSearch
                      :allowClear="true"
                      :placeholder="$t('information.placeholder.pgNo')"
                      style="width: 100%"
                      @focus="handle_PgNo"
                      :filterOption="false"
                      v-model="queryParam.pgNo"
                    >
                      <a-select-option
                        v-for="(i, index) in PgList"
                        :key="index"
                        :value="i"
                      >{{ i }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :xxl="6"
                  :xl="6"
                  :lg="8"
                  :md="12"
                  :sm="12"
                >
                  <a-form-item :label="$t('information.wgNo')">
                    <a-select
                      showSearch
                      :allowClear="true"
                      :placeholder="$t('information.placeholder.wgNo')"
                      style="width: 100%"
                      :filterOption="false"
                      @focus="handle_WgNo"
                      v-model="queryParam.wgNo"
                    >
                      <a-select-option
                        v-for="(i, index) in WgList"
                        :key="index"
                        :value="i"
                      >{{ i }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col
                  :xxl="6"
                  :xl="6"
                  :lg="8"
                  :md="12"
                  :sm="12"
                >
                  <a-form-item :label="$t('information.cs2')">
                    <a-select
                      showSearch
                      :allowClear="true"
                      :placeholder="$t('information.placeholder.cs2')"
                      style="width: 100%"
                      :filterOption="false"
                      @focus="handle_CsNo2"
                      v-model="queryParam.csNo2"
                    >
                      <a-select-option
                        v-for="(i, index) in CsNo2List"
                        :key="index"
                        :value="i.cs_no"
                      >{{
                        i.cs_no
                      }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </template>
            </a-row>
            <a-row :gutter="48">
              <a-col style="float: right">
                <span
                  class="table-page-search-submitButtons"
                  :style="(advanced && { float: 'right', overflow: 'hidden' }) || {}"
                >
                  <a-button
                    :loading="loading1"
                    size="small"
                    type="primary"
                    v-permission="fill_list"
                    @click="pic_add"
                  >{{ $t('information.picParameter') }}</a-button>
                  <!-- 点击当前按钮进行转圈效果 加loadingtwo loadingthree -->
                  <a-button
                    v-if="submitshow"
                    :loading="loading"
                    size="small"
                    type="primary"
                    style="margin-left: 8px"
                    @click="handleSave($event)"
                    v-permission="rep_information_savethree"
                  >{{ $t('public.save') }}</a-button>
                  <a-button
                    :loading="loadingtwo"
                    size="small"
                    type="primary"
                    style="margin-left: 8px"
                    v-permission="rep_information_querythree"
                    @click="search"
                  >{{
                    $t('public.query')
                  }}</a-button>
                  <a-button
                    :loading="listLoading"
                    size="small"
                    style="margin-left: 8px"
                    type="primary"
                    @click="exportDataEvent"
                    v-permission="rep_information_exportthree"
                  >{{ exportButton }}</a-button>
                  <a-button
                    size="small"
                    style="margin-left: 8px"
                    @click="reset"
                    v-permission="rep_information_resetthree"
                  >{{ $t('public.reset') }}</a-button>

                  <a-button
                    size="small"
                    style="margin-left: 8px"
                    @click="deleteTable($event)"
                    v-permission="rep_information_deletethree"
                  >{{ $t('public.delete') }}</a-button>
                  <a
                    @click="toggleAdvanced"
                    style="margin-left: 8px"
                  >
                    {{ advanced ? $t('job.a') : $t('job.b') }}
                    <a-icon :type="advanced ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </div>
    </a-row>
    <a-spin :spinning="spinning">
      <vxe-table
        border
        resizable
        stripe
        size="small"
        max-height="450px"
        highlight-current-row
        show-overflow
        highlight-hover-row
        ref="xTable"
        :loading="loading"
        :data="tableData"
        :keyboard-config="{ isArrow: true }"
        @checkbox-change="selectChangeEvent"
        @checkbox-all="selectChangeAll"
        :checkbox-config="{checkMethod: checCheckboxkMethod}"
        :edit-config="{ trigger: 'click', mode: 'cell'}"
        @edit-actived="editActivedEvent"
      >
        <!-- , showStatus: true  -->
        <!-- , activeMethod: activeRowMethod -->
        <vxe-table-column
          fixed="left"
          field="csNo"
          title="information.csNo"
          align="center"
          :width="150"
        ></vxe-table-column>
        <vxe-table-column
          field="zcNo"
          title="information.workingProcedure"
          align="center"
          :width="150"
        >
          <template slot-scope="scope">
            <div>{{ scope.row.zcNo1 }}{{ scope.row.zcName1 }}/ {{ scope.row.zcNo2 }}{{ scope.row.zcName2 }}</div>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="casNo"
          title="information.code"
          align="center"
          :width="150"
        ></vxe-table-column>
        <vxe-table-column
          field="paraNo"
          title="information.paraNo"
          align="center"
          :width="150"
        ></vxe-table-column>
        <vxe-table-column
          field="name"
          title="information.name"
          align="center"
          :width="150"
        ></vxe-table-column>
        <vxe-table-column
          field="value"
          title="information.value"
          align="center"
          :width="150"
        >
          <template slot-scope="scope">
            <div>
              <span> {{ scope.row.value }}</span>
            </div>
          </template>
        </vxe-table-column>
        <vxe-table-column
          v-if="submitshow"
          type="checkbox"
          align="center"
          :width="50"
        ></vxe-table-column>
        <vxe-table-column
          v-if="submitshow"
          field="paraValue3"
          title="报告参数"
          align="center"
          :edit-render="{ name: 'input', attrs: {disabled: nameDisabled }}"
          :width="150"
        >
          <!-- , events: { change: sexChangeEvent }  -->
          <template slot-scope="scope">
            <div>
              <a-icon
                :style="{ fontSize: '18px' }"
                v-if="scope.row.type === '7' || scope.row.type === 'A'"
                @click="getPic(scope.row)"
                height="100%"
                type="eye"
              />
              <div v-else>
                <a
                  @click="touchList(scope.row)"
                  v-if="
                    scope.row.paraNo === '0301_rll_no' ||
                    scope.row.paraNo === '0801_lh' ||
                    scope.row.paraNo === '1701_lh'
                  "
                >{{ scope.row.paraValue3 }}
                </a>
                <div v-else> {{ scope.row.paraValue3 }}</div>
              </div>
            </div>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="vmin"
          title="information.vMin"
          align="center"
          :width="150"
        ></vxe-table-column>
        <vxe-table-column
          field="vmax"
          title="information.vMax"
          align="center"
          :width="150"
        ></vxe-table-column>
        <vxe-table-column
          field="result"
          title="information.result"
          align="center"
          :width="150"
        ></vxe-table-column>
        <vxe-table-column
          field="man"
          title="information.man"
          align="center"
          :width="150"
        ></vxe-table-column>
        <vxe-table-column
          field="recordTime"
          title="information.recordTime"
          align="center"
          :width="150"
        ></vxe-table-column>
        <vxe-table-column
          field="wgNo"
          title="information.wgNo"
          align="center"
          :width="150"
        ></vxe-table-column>
        <vxe-table-column
          field="pgNo"
          title="information.pgNo"
          align="center"
          :width="150"
        ></vxe-table-column>
        <vxe-table-column
          field="wrNo"
          title="information.wrNo"
          align="center"
          :width="150"
        ></vxe-table-column>
        <vxe-table-column
          field="tzNo"
          title="information.tzNo"
          align="center"
          :width="150"
        ></vxe-table-column>
        <vxe-table-column
          field="moNo"
          title="information.moNo"
          align="center"
          :width="150"
        ></vxe-table-column>
      </vxe-table>
      <vxe-pager
        :loading="loading"
        :current-page="tablePage.currentPage"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange"
      >
      </vxe-pager>
      <ModalPic ref="ModalPic" />
      <ModalList ref="ModalList" />
      <Export ref="Export"></Export>
    </a-spin>
  </a-card>
</template>

<script>
import {
  getMo1,
  getZc,
  getTZ,
  getWr,
  getcs1,
  getZcNo2,
  getPgNo,
  getCs2,
  fetchList,
  upparfindall,
  getwgNo,
  getMo2,
  Filist,
  paradd,
  qcfin,
  upparfin,
  deletedata
} from '@/api/report/information'
import ModalPic from './ModalPic'
import ModalList from './ModalList'
import Export from '@/components/barcodeExport/barcodeExport'
export default {
  components: {
    ModalPic,
    ModalList,
    Export
  },
  data () {
    return {
      rep_information_savethree: 'rep_information_savethree',
      rep_information_querythree: 'repinformation_querythree',
      rep_information_exportthree: 'rep_information_exportthree',
      rep_information_resetthree: 'rep_information_resetthree',
      rep_information_deletethree: 'rep_information_deletethree',
      // 高级搜索 展开/关闭
      advanced: false,
      spinning: false,
      exportButton: '导出',
      tableData: [],
      endOpen: false,
      loading: false,
      loading1: false,
      loadingtwo: false,
      listLoading: false,
      fill_list: 'fill_list',
      staDd: null,
      endDd: null,
      staDd2: null,
      endDd2: null,
      MoList: [],
      ZcList: [],
      TzList: [],
      WrList: [],
      Zc1List: [],
      ZcNo2List: [],
      PgList: [],
      WgList: [],
      CsNo2List: [],
      qcNoList: [],
      key: '1',
      tablePage: {
        currentPage: 1,
        pageSize: 100,
        total: 0,
      },
      queryParam: {
        rem: '',
        staDd: '',
        endDd: '',
        moNo: '',
        casNo: '',
        zcNo: '',
        tzNo: '',
        wrNo: '',
        csNo1: '',
        zcNo2: '',
        pgNo: '',
        wgNo: '',
        csNo2: '',
        qcNo: '',
      },
      submitshow: true,
      deleteparam: {
        csNo: '',
        paraNo: '',
        itm: null,
        type: 1
      },
      csnoarry: [],
      paraNoarry: [],
      nameDisabled: false,
      recordsdata: []
    }
  },
  created () {
    // this.getList()
  },
  watch: {
    'queryParam.moNo' (val) {
      this.queryParam.zcNo = ''
      this.queryParam.tzNo = ''
      this.queryParam.wrNo = ''
      this.queryParam.csNo1 = ''
      this.queryParam.zcNo2 = ''
      this.queryParam.pgNo = ''
      this.queryParam.wgNo = ''
      this.queryParam.csNo2 = ''
      if (!val) this.queryParam.moNo = ''
    },
    staDd (e) {
      this.staDd = e
      this.queryParam.tzNo = ''
      this.queryParam.wrNo = ''
      this.queryParam.csNo1 = ''
      this.queryParam.zcNo2 = ''
      this.queryParam.zcNo = ''
      this.queryParam.casNo = ''
      this.queryParam.moNo = ''
      this.queryParam.pgNo = ''
      this.queryParam.wgNo = ''
      this.queryParam.csNo2 = ''
      if (e) {
        this.queryParam.staDd = moment(e).format('YYYY-MM-DD 12:00:00')
      } else {
        this.queryParam.staDd = ''
      }
    },
    endDd (e) {
      this.endDd = e
      this.queryParam.tzNo = ''
      this.queryParam.wrNo = ''
      this.queryParam.csNo1 = ''
      this.queryParam.zcNo2 = ''
      this.queryParam.zcNo = ''
      this.queryParam.casNo = ''
      this.queryParam.moNo = ''
      this.queryParam.pgNo = ''
      this.queryParam.wgNo = ''
      this.queryParam.csNo2 = ''
      if (e) {
        this.queryParam.endDd = moment(e).format('YYYY-MM-DD 12:00:00')
      } else {
        this.queryParam.endDd = ''
      }
    },
    'queryParam.rem' () {
      this.queryParam.tzNo = ''
      this.queryParam.wrNo = ''
      this.queryParam.csNo1 = ''
      this.queryParam.zcNo2 = ''
      this.queryParam.zcNo = ''
      this.queryParam.casNo = ''
      this.queryParam.moNo = ''
      this.queryParam.pgNo = ''
      this.queryParam.wgNo = ''
      this.queryParam.csNo2 = ''
    },
    'queryParam.zcNo' () {
      this.queryParam.tzNo = ''
      this.queryParam.wrNo = ''
      this.queryParam.csNo1 = ''
      this.queryParam.zcNo2 = ''
      this.queryParam.pgNo = ''
      this.queryParam.wgNo = ''
      this.queryParam.csNo2 = ''
    },
    'queryParam.tzNo' () {
      this.queryParam.wrNo = ''
      this.queryParam.csNo1 = ''
      this.queryParam.zcNo2 = ''
      this.queryParam.pgNo = ''
      this.queryParam.wgNo = ''
      this.queryParam.csNo2 = ''
    },
    'queryParam.csNo1' () {
      this.queryParam.zcNo2 = ''
      this.queryParam.pgNo = ''
      this.queryParam.wgNo = ''
      this.queryParam.csNo2 = ''
    },
    'queryParam.zcNo2' () {
      this.queryParam.wrNo = ''
      this.queryParam.csNo1 = ''
      this.queryParam.pgNo = ''
      this.queryParam.wgNo = ''
      this.queryParam.csNo2 = ''
    },
    'queryParam.pgNo' () {
      this.queryParam.wrNo = ''
      this.queryParam.csNo1 = ''
      this.queryParam.wgNo = ''
      this.queryParam.csNo2 = ''
    },
    'queryParam.wgNo' () {
      this.queryParam.wrNo = ''
      this.queryParam.csNo1 = ''
      this.queryParam.csNo2 = ''
    },
    'queryParam.csNo2' () {
      this.queryParam.wrNo = ''
      this.queryParam.csNo1 = ''
    },
  },
  methods: {
    transferData (data, columnHeader) {
      let content = [], header = [];
      for (let i in columnHeader) {
        header.push(columnHeader[i])
      }
      content.push(header);
      data.forEach((item, index) => {
        let arr = [];
        for (let i in columnHeader) {
          arr.push(item[i])
        }
        content.push(arr);
      });
      return content;
    },
    export2 (datapages2, type) {
      if (type == '1') {
        return [
          {
            createTime2: datapages2.createTime2,
            hxcfAl2: datapages2.hxcfAl2,
            hxcfC2: datapages2.hxcfC2,
            hxcfCr2: datapages2.hxcfCr2,
            hxcfCu2: datapages2.hxcfCu2,
            hxcfMn2: datapages2.hxcfMn2,
            hxcfMo2: datapages2.hxcfMo2,
            hxcfNi2: datapages2.hxcfNi2,
            hxcfP2: datapages2.hxcfP2,
            hxcfQt2: datapages2.hxcfQt2,
            hxcfS2: datapages2.hxcfS2,
            hxcfSi2: datapages2.hxcfSi2,
            hxcfV2: datapages2.hxcfV2,
            man2: datapages2.man2,
            moNoRem2: datapages2.moNoRem2,
            rlAl2: datapages2.rlAl2,
            rlCksj2: datapages2.rlCksj2,
            rlCkwd2: datapages2.rlCkwd2,
            rlCysj2: datapages2.rlCysj2,
            rlDjmn2: datapages2.rlDjmn2,
            rlGbngswd2: datapages2.rlGbngswd2,
            rlGbwd2: datapages2.rlGbwd2,
            rlGbxh2: datapages2.rlGbxh2,
            rlGcr2: datapages2.rlGcr2,
            rlGmn2: datapages2.rlGmn2,
            rlGxm2: datapages2.rlGxm2,
            rlHlglzl2: datapages2.rlHlglzl2,
            rlKlsj2: datapages2.rlKlsj2,
            rlKszj2: datapages2.rlKszj2,
            rlMu2: datapages2.rlMu2,
            rlNi2: datapages2.rlNi2,
            rlQtys2: datapages2.rlQtys2,
            rlRllLk2: datapages2.rlRllLk2,
            rlRllNo: datapages2.rlRllNo,
            rlSi2: datapages2.rlSi2,
            rlSlys2: datapages2.rlSlys2,
            rlTp2: datapages2.rlTp2,
            rlTyclzl2: datapages2.rlTyclzl2,
            rlTyclzlpc2: datapages2.rlTyclzlpc2,
            rlTyhcy2: datapages2.rlTyhcy2,
            rlTyqcy2: datapages2.rlTyqcy2,
            rlV2: datapages2.rlV2,
            rlWcr2: datapages2.rlWcr2,
            rlXglzl2: datapages2.rlXglzl2,
            rlXglzlpc2: datapages2.rlXglzlpc2,
            rlZdsj2: datapages2.rlZdsj2,
            tlRem2: datapages2.tlRem2,
            valid2: datapages2.valid2,
            ycTp2: datapages2.ycTp2
          }
        ];
      } else if (type == '2') {
        return [
          {
            rclNo: datapages2.rclNo,
            createTime2: datapages2.createTime2,
            man2: datapages2.man2,
            moNoRem2: datapages2.moNoRem2,
            rclBwHour2: datapages2.rclBwHour2,
            rclFs2: datapages2.rclFs2,
            rclLxh2: datapages2.rclLxh2,
            rclWd2: datapages2.rclWd2,
            tp12: datapages2.tp12,
            tp22: datapages2.tp22,
            valid2: datapages2.valid2
          }
        ];
      }

    },
    editActivedEvent ({ row, rowIndex }) {
      let datarecord
      datarecord = []
      datarecord = JSON.parse(localStorage.getItem("keyrecords"))
      let itemdata = []
      datarecord.forEach((item, index) => {
        itemdata.push(item.plain)
      })
      let isyou = itemdata.indexOf(rowIndex)
      if (isyou != -1) {
        this.nameDisabled = false
      } else {
        this.nameDisabled = true
      }
      // let panduan
      // if (itemdata) {
      //   for (let i = 0; i < itemdata.length; i++) {
      //     panduan = panduan + rowIndex + '==' + itemdata[i] + '||'
      //   }
      // }
      // if (itemdata) {
      //   for (let i = 0; i < itemdata.length; i++) {
      //     if (rowIndex == itemdata[i] || rowIndex == itemdata[i + 1]) {
      //       this.nameDisabled = false
      //     } else {
      //       this.nameDisabled = true
      //     }
      //   }
      // }
      // let panduan = ''
      // if (itemdata) {
      //   for (let i = 0; i < itemdata.length; i++) {
      //     // panduan = 'rowIndex == itemdata[0]'
      //     // if (itemdata.length == 1) {
      //     //   panduan = panduan + 'rowIndex == itemdata' + '[' + i + ']'
      //     // }
      //     // if (itemdata.length > 1) {
      //     panduan = panduan + 'rowIndex == itemdata[' + i + '] ||'
      //     // }

      //   }
      // }
      // panduan = panduan.substring(0, panduan.length - 2);
      // if (itemdata) {
      //   if (panduan) {
      //     this.nameDisabled = false
      //   } else {
      //     this.nameDisabled = true
      //   }
      // }
      // itemdata.forEach((item,index) => {
      //   itemdata.length
      //   //  = rowIndex == item.plain || 
      //   if (rowIndex == item || ) {
      //     this.nameDisabled = true
      //   }
      // })
      // if (itemdata != []) {
      //   this.nameDisabled = false
      // } else {
      //   this.nameDisabled = true
      // }
      // this.tableData.forEach((itemdata, indexdata) => {
      //   datarecord.forEach((item, index) => {
      //     if (itemdata.plain == item.plain) {
      //       this.nameDisabled = false
      //     }
      //     // if (rowIndex == item.plain || rowIndex == item.plain + 1) {
      //     //   this.nameDisabled = false
      //     // } else {
      //     //   this.nameDisabled = true
      //     // }
      //     // rowIndex != item.plain ||
      //     //   this.nameDisabled =
      //   })
      // })
      // this.sexDisabled = row.sex === '1'
      // this.dateDisabled = !!row.date
    },
    // editDisabledEvent ({ row, column }) {
    //   this.$XModal.message({ content: '禁止编辑', status: 'error' })
    // },
    // 勾选事件
    selectChangeEvent ({ checked, records, rowIndex }) {
      this.recordsdata = records
      localStorage.setItem('keyrecords', JSON.stringify(this.recordsdata));
      if (checked) {
        let that = this
        records.forEach((item, index) => {
          that.$refs.xTable.setActiveRow(item)
        })
        // this.$refs.xTable.setActiveRow(this.tableData[rowIndex])
        this.tableData[rowIndex].paraValue3 = this.tableData[rowIndex].paraValue3
      } else {
        // this.tableData[rowIndex].paraValue3 = ''
      }
    },
    selectChangeAll ({ records, checked }) {
      this.recordsdata = records
      localStorage.setItem('keyrecords', JSON.stringify(this.recordsdata));
      if (checked) {
        records.forEach((i) => {
          i.paraValue3 = i.paraValue3
        })
      } else {
        // this.tableData.forEach((i) => {
        //   i.paraValue3 = ''
        // })
      }
    },
    // activeRowMethod ({ row, rowIndex }) {
    //   if (row.paraNo == '0301_rll_no' || row.paraNo == '0801_lh' || row.paraNo == '1701_lh') {
    //     return false
    //   } else {
    //   }
    // },
    handleSave (e) {
      let target = e.target
      if (target.nodeName == 'SPAN' || target.nodeName == 'I') {
        target = e.target.parentNode
      }
      target.blur()
      if (this.tableData == [] || this.tableData.length == 0) return this.$message.error('请查询之后再提交！')
      const selectRecords = this.$refs.xTable.getCheckboxRecords()
      if (selectRecords == [] || selectRecords.length == 0) return this.$message.error('请至少选择一条数据！')
      // const arr = JSON.parse(JSON.stringify(this.tableData))
      const arr = JSON.parse(JSON.stringify(selectRecords))
      arr.forEach((i) => {
        i.itm = null
        i.type = this.key
      })
      this.loading = true
      paradd(arr)
        .then((res) => {
          this.loading = false
          this.$message.success(res.data)
          this.$refs.xTable.clearCheckboxRow()
        })
        .catch((err) => {
          this.loading = false
          this.requestFailed(err)
        })
    },
    sexChangeEvent () {
    },
    // 格式化导出数据
    Format (res) {
      var list = [...res]
      for (let index = 0; index < list.length; index++) {
        for (let item in list[index]) {
          if (list[index][item] === null) {
            list[index][item] = ''
          }
        }
      }
      return list
    },
    // 导出
    exportDataEvent () {
      if (!this.queryParam.casNo) {
        this.listLoading = false
        return this.$message.error('请填写产品唯一码')
      }
      const hide = this.$message.loading('导出中..', 0)
      this.spinning = true
      this.listLoading = true
      upparfindall({ ...this.queryParam })
        .then((res) => {
          if (res) {
            setTimeout(hide, 10)
            this.$message.success('导出成功')
            this.spinning = false
            this.listLoading = false
            let datapages2
            datapages2 = res.data.wmsHeatNo2
            let sheetcontent
            sheetcontent = {}
            if (res.data.type == '2') {
              datapages2 = res.data.wmsHotHeatNo2
              sheetcontent = {
                'rclNo': '熔炼炉号',
                'createTime2': '创建时间',
                'man2': '作业人员',
                'moNoRem2': '制令单号',
                'rclBwHour2': '保温时间(小时)',
                'rclFs2': '热处理方式',
                'rclLxh2': '热处理炉型号',
                'rclWd2': '热处理温度',
                'tp12': '正常图片',
                'tp22': '异常图片',
                'valid2': '是否有效',
              }
            } else if (res.data.type == '1') {
              datapages2 = res.data.wmsHeatNo2
              sheetcontent = {
                'createTime2': '创建时间',
                'hxcfAl2': '化学成分-AL',
                'hxcfC2': '化学成分-C',
                'hxcfCr2': '化学成分-Cr',
                'hxcfCu2': '化学成分-Cu',
                'hxcfMn2': '化学成分-Mn',
                'hxcfMo2': '化学成分-Mo',
                'hxcfNi2': '化学成分-Ni',
                'hxcfP2': '化学成分-P',
                'hxcfQt2': '化学成分-其他',
                'hxcfS2': '化学成分-S',
                'hxcfSi2': '化学成分-Si',
                'hxcfV2': '化学成分-V',
                'man2': '作业人员',
                'moNoRem2': '制令单号',
                'rlAl2': '铝',
                'rlCksj2': '出钢时间',
                'rlCkwd2': '出钢温度',
                'rlCysj2': '吹氩时间/秒',
                'rlDjmn2': '电解Mn',
                'rlGbngswd2': '钢包内钢水温度',
                'rlGbwd2': '钢包温度',
                'rlGbxh2': '钢包型号',
                'rlGcr2': '高Cr',
                'rlGmn2': '高Mn',
                'rlGxm2': '钙矽锰',
                'rlHlglzl2': '回炉钢料重量/公斤',
                'rlKlsj2': '开炉时间',
                'rlKszj2': '口水直径',
                'rlMu2': '钼',
                'rlNi2': '镍',
                'rlQtys2': '其他',
                'rlRllLk2': '炉况',
                'rlRllNo': '熔炼炉号',
                'rlSi2': 'Si',
                'rlSlys2': '上炉余水',
                'rlTp2': '正常图片',
                'rlTyclzl2': '脱氧材料重量/公斤',
                'rlTyclzlpc2': '脱氧材料批次',
                'rlTyhcy2': '脱氧后测氧',
                'rlTyqcy2': '脱氧前测氧',
                'rlV2': '钒',
                'rlWcr2': '微Cr',
                'rlXglzl2': '新钢材重量/公斤',
                'rlXglzlpc2': '新钢料批次',
                'rlZdsj2': '镇定时间/秒s',
                'tlRem2': '异常说明',
                'valid2': '是否有效',
                'ycTp2': '异常图片'
              }
            }
            const data = {
              'sheet1': res.data.pages,
              'sheet2': this.export2(datapages2, res.data.type),
            };
            let columnHeaders = {
              'sheet1': {
                'csNo': '参数代号',
                'zcNo1': '一级工序',
                'casNo': '产品唯一码',
                'paraNo': '参数代码',
                'name': '参数名称',
                'value': '参数值',
                'vmin': '参数下限',
                'vmax': '参数上限',
                'result': '判定结果',
                'man': '报参数人员',
                'recordTime': '报参时间',
                'wgNo': '完工单号',
                'pgNo': '派工单号',
                'wrNo': '日报单号',
                'tzNo': '通知单号',
                'moNo': '制令单号',
              },
              'sheet2': sheetcontent
            }
            if (res.data.type == '0') {
              delete columnHeaders.sheet2;
              delete data.sheet2;
            }
            let wscols = [{ wch: 18 }, { wch: 10 }, { wch: 16 }, { wch: 16 }, { wch: 20 }, { wch: 66 }, { wch: 16 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 20 }, { wch: 20 }, { wch: 18 }, { wch: 18 }, { wch: 18 }, { wch: 18 }, { wch: 10 }, { wch: 10 }
              , { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 }];//设置字段宽度


            let sheetNames = [];
            let sheetsList = {};
            const wb = XLSX.utils.book_new();
            for (let key in data) {
              sheetNames.push(key);
              let columnHeader = columnHeaders[key] // 此处是每个sheet的表头
              let temp = this.transferData(data[key], columnHeader);
              sheetsList[key] = XLSX.utils.aoa_to_sheet(temp);
              // sheetsList[key]["!cols"] = wscols;
              const colWidth = temp.map((row) =>
                row.map((val) => {
                  if (val == null) {
                    return { wch: 10 }
                  } else if (val.toString().charCodeAt(0) > 255) {
                    return { wch: val.toString().length + 5 }
                  } else {
                    return { wch: val.toString().length + 5 }
                  }
                })
              )
              let result = colWidth[0]
              for (let i = 1; i < colWidth.length; i++) {
                for (let j = 0; j < colWidth[i].length; j++) {
                  if (result[j]['wch'] < colWidth[i][j]['wch']) {
                    result[j]['wch'] = colWidth[i][j]['wch']
                  }
                }
              }
              sheetsList[key]["!cols"] = result
            }
            wb["SheetNames"] = sheetNames;
            wb["Sheets"] = sheetsList;
            XLSX.writeFile(wb, "参数资料.xlsx");
            // const obj = [{
            //   data: res.data.pages,
            //   tableColumnZh: [
            //     '参数代号',
            //     '一级工序',
            //     '产品唯一码',
            //     '参数代码',
            //     '参数名称',
            //     '参数值',
            //     '参数下限',
            //     '参数上限',
            //     '判定结果',
            //     '报参数人员',
            //     '报参时间',
            //     '完工单号',
            //     '派工单号',
            //     '日报单号',
            //     '通知单号',
            //     '制令单号'
            //   ],
            //   tableColumnEn: [
            //     'csNo',
            //     'zcNo1',
            //     'casNo',
            //     'paraNo',
            //     'name',
            //     'value',
            //     'vmin',
            //     'vmax',
            //     'result',
            //     'man',
            //     'recordTime',
            //     'wgNo',
            //     'pgNo',
            //     'wrNo',
            //     'tzNo',
            //     'tzNo'
            //   ],
            //   name: '参数资料',
            // },
            // {
            //   data: res.data.pages,
            //   tableColumnZh: [
            //     '参数代号',
            //     '一级工序',
            //     '产品唯一码',
            //     '参数代码',
            //     '参数名称',
            //     '参数值',
            //     '参数下限',
            //     '参数上限',
            //     '判定结果',
            //     '报参数人员',
            //     '报参时间',
            //     '完工单号',
            //     '派工单号',
            //     '日报单号',
            //     '通知单号',
            //     '制令单号'
            //   ],
            //   tableColumnEn: [
            //     'csNo',
            //     'zcNo1',
            //     'casNo',
            //     'paraNo',
            //     'name',
            //     'value',
            //     'vmin',
            //     'vmax',
            //     'result',
            //     'man',
            //     'recordTime',
            //     'wgNo',
            //     'pgNo',
            //     'wrNo',
            //     'tzNo',
            //     'tzNo'
            //   ],
            //   name: '2222参数资料2222',
            // }

            // ]
            // this.$refs.Export.create(obj)
          }
        })
        .catch(() => {
          setTimeout(hide, 10)
          this.spinning = false
          this.listLoading = false
          this.$message.error('导出失败')
        })
      // const hide = this.$message.loading('导出中..', 0)
      // this.spinning = true
      // this.listLoading = true
      // upparfindall({ ...this.queryParam })
      //   .then((res) => {
      //     if (res) {
      //       setTimeout(hide, 10)
      //       this.$message.success('导出成功')
      //       this.spinning = false
      //       this.listLoading = false
      //       const obj = {
      //         data: res.data,
      //         tableColumnZh: [
      //           '参数代号',
      //           '一级工序',
      //           '产品唯一码',
      //           '参数代码',
      //           '参数名称',
      //           '参数值',
      //           '参数下限',
      //           '参数上限',
      //           '判定结果',
      //           '报参数人员',
      //           '报参时间',
      //           '完工单号',
      //           '派工单号',
      //           '日报单号',
      //           '通知单号',
      //           '制令单号'
      //         ],
      //         tableColumnEn: [
      //           'csNo',
      //           'zcNo1',
      //           'casNo',
      //           'paraNo',
      //           'name',
      //           'value',
      //           'vmin',
      //           'vmax',
      //           'result',
      //           'man',
      //           'recordTime',
      //           'wgNo',
      //           'pgNo',
      //           'wrNo',
      //           'tzNo',
      //           'tzNo'
      //         ],
      //         name: '参数资料',
      //       }
      //       this.$refs.Export.create(obj)
      //     }
      //   })
      //   .catch(() => {
      //     setTimeout(hide, 10)
      //     this.spinning = false
      //     this.listLoading = false
      //     this.$message.error('导出失败')
      //   })
    },
    disabledStartDate (startValue) {
      const endValue = this.endDd
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate (endValue) {
      const startValue = this.staDd
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() >= endValue.valueOf()
    },
    // 制令单号
    handle_moNo () {
      let staDd
      let endDd
      if (this.staDd !== null) {
        staDd = moment(this.staDd).format('YYYY-MM-DD 12:00:00')
      } else {
        staDd = null
      }
      if (this.endDd !== null) {
        endDd = moment(this.endDd).format('YYYY-MM-DD 12:00:00')
      } else {
        endDd = null
      }
      getMo1(
        Object.assign({
          rem: this.queryParam.rem ? this.queryParam.rem : '',
          staDd: staDd,
          endDd: endDd,
        })
      )
        .then((res) => {
          if (res.msg == 'fail') {
            this.MoList = []
            return this.$message.error(res.data)
          }
          this.MoList = res.data
        })
        .catch((err) => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 选中制令单号回调
    select_moNo () {
      getMo2(
        Object.assign({
          moNo: this.queryParam.moNo ? this.queryParam.moNo : '',
        })
      )
        .then((res) => {
          this.queryParam.casNo = res.data
        })
        .catch((err) => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 一级制程
    handle_zcNo () {
      getZc(
        Object.assign({
          moNo: this.queryParam.moNo ? this.queryParam.moNo : '',
          casNo: this.queryParam.casNo ? this.queryParam.casNo : '',
        })
      )
        .then((res) => {
          this.ZcList = res.data
        })
        .catch((err) => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 通知单号
    handle_TzNo () {
      let staDd
      let endDd
      if (this.staDd !== null) {
        staDd = moment(this.staDd).format('YYYY-MM-DD 12:00:00')
      } else {
        staDd = null
      }
      if (this.endDd !== null) {
        endDd = moment(this.endDd).format('YYYY-MM-DD 12:00:00')
      } else {
        endDd = null
      }
      getWr(
        Object.assign({
          staDd: staDd,
          endDd: endDd,
          moNo: this.queryParam.moNo ? this.queryParam.moNo : '',
          casNo: this.queryParam.casNo ? this.queryParam.casNo : '',
          zcNo: this.queryParam.zcNo ? this.queryParam.zcNo : '',
          tzNo: this.queryParam.tzNo ? this.queryParam.tzNo : '',
        })
      )
        .then((res) => {
          this.TzList = res.data
        })
        .catch((err) => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 通知单日报单
    handle_TzNorb () {
      let staDd
      let endDd
      if (this.staDd !== null) {
        staDd = moment(this.staDd).format('YYYY-MM-DD 12:00:00')
      } else {
        staDd = null
      }
      if (this.endDd !== null) {
        endDd = moment(this.endDd).format('YYYY-MM-DD 12:00:00')
      } else {
        endDd = null
      }
      getTZ(
        Object.assign({
          staDd: staDd,
          endDd: endDd,
          moNo: this.queryParam.moNo ? this.queryParam.moNo : '',
          casNo: this.queryParam.casNo ? this.queryParam.casNo : '',
          zcNo: this.queryParam.zcNo ? this.queryParam.zcNo : '',
        })
      )
        .then((res) => {
          this.WrList = res.data
        })
        .catch((err) => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 一级参数单号
    handle_CsNo1 () {
      let staDd
      let endDd
      if (this.staDd !== null) {
        staDd = moment(this.staDd).format('YYYY-MM-DD 12:00:00')
      } else {
        staDd = null
      }
      if (this.endDd !== null) {
        endDd = moment(this.endDd).format('YYYY-MM-DD 12:00:00')
      } else {
        endDd = null
      }
      getcs1(
        Object.assign({
          staDd: staDd,
          endDd: endDd,
          moNo: this.queryParam.moNo ? this.queryParam.moNo : '',
          casNo: this.queryParam.casNo ? this.queryParam.casNo : '',
          zcNo: this.queryParam.zcNo ? this.queryParam.zcNo : '',
          tzNo: this.queryParam.tzNo ? this.queryParam.tzNo : '',
          wrNo: this.queryParam.wrNo ? this.queryParam.wrNo : '',
        })
      )
        .then((res) => {
          this.Zc1List = res.data
        })
        .catch((err) => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 二级制程
    handle_ZcNo2 () {
      getZcNo2(
        Object.assign({
          zcNo: this.queryParam.zcNo ? this.queryParam.zcNo : '',
        })
      )
        .then((res) => {
          this.ZcNo2List = res.data
        })
        .catch((err) => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 派工单号
    handle_PgNo () {
      let staDd
      let endDd
      if (this.staDd !== null) {
        staDd = moment(this.staDd).format('YYYY-MM-DD 12:00:00')
      } else {
        staDd = null
      }
      if (this.endDd !== null) {
        endDd = moment(this.endDd).format('YYYY-MM-DD 12:00:00')
      } else {
        endDd = null
      }
      getPgNo(
        Object.assign({
          staDd: staDd,
          endDd: endDd,
          moNo: this.queryParam.moNo ? this.queryParam.moNo : '',
          casNo: this.queryParam.casNo ? this.queryParam.casNo : '',
          zcNo: this.queryParam.zcNo ? this.queryParam.zcNo : '',
          tzNo: this.queryParam.tzNo ? this.queryParam.tzNo : '',
          wrNo: this.queryParam.wrNo ? this.queryParam.wrNo : '',
          zcNo2: this.queryParam.zcNo2 ? this.queryParam.zcNo2 : '',
        })
      )
        .then((res) => {
          this.PgList = res.data
        })
        .catch((err) => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 完工单号
    handle_WgNo () {
      let staDd
      let endDd
      if (this.staDd !== null) {
        staDd = moment(this.staDd).format('YYYY-MM-DD 12:00:00')
      } else {
        staDd = null
      }
      if (this.endDd !== null) {
        endDd = moment(this.endDd).format('YYYY-MM-DD 12:00:00')
      } else {
        endDd = null
      }
      getwgNo(
        Object.assign({
          staDd: staDd,
          endDd: endDd,
          moNo: this.queryParam.moNo ? this.queryParam.moNo : '',
          casNo: this.queryParam.casNo ? this.queryParam.casNo : '',
          zcNo: this.queryParam.zcNo ? this.queryParam.zcNo : '',
          tzNo: this.queryParam.tzNo ? this.queryParam.tzNo : '',
          wrNo: this.queryParam.wrNo ? this.queryParam.wrNo : '',
          zcNo2: this.queryParam.zcNo2 ? this.queryParam.zcNo2 : '',
          pgNo: this.queryParam.pgNo ? this.queryParam.pgNo : '',
        })
      )
        .then((res) => {
          this.WgList = res.data
        })
        .catch((err) => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 参数单号2
    handle_CsNo2 () {
      let staDd
      let endDd
      if (this.staDd !== null) {
        staDd = moment(this.staDd).format('YYYY-MM-DD 12:00:00')
      } else {
        staDd = null
      }
      if (this.endDd !== null) {
        endDd = moment(this.endDd).format('YYYY-MM-DD 12:00:00')
      } else {
        endDd = null
      }
      getCs2(
        Object.assign({
          staDd: staDd,
          endDd: endDd,
          moNo: this.queryParam.moNo ? this.queryParam.moNo : '',
          casNo: this.queryParam.casNo ? this.queryParam.casNo : '',
          zcNo: this.queryParam.zcNo ? this.queryParam.zcNo : '',
          tzNo: this.queryParam.tzNo ? this.queryParam.tzNo : '',
          wgNo: this.queryParam.wgNo ? this.queryParam.wgNo : '',
          zcNo2: this.queryParam.zcNo2 ? this.queryParam.zcNo2 : '',
          pgNo: this.queryParam.pgNo ? this.queryParam.pgNo : '',
        })
      ).then((res) => {
        this.CsNo2List = res.data
      }).catch(err => this.requestFailed(err))
    },
    // 是否展开查询条件
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    search () {
      this.tablePage.currentPage = 1
      this.getList()
    },
    // 查询列表
    getList () {
      this.loadingtwo = true
      this.getDataOne()
    },
    getDataOne () {
      if (!this.queryParam.casNo) {
        this.loadingtwo = false
        return this.$message.error('请填写产品唯一码')
      }
      upparfin(
        Object.assign(
          {
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
          },
          this.queryParam
        )
      )
        .then((response) => {
          this.tableData = response.data.records
          this.tablePage.total = response.data.total
          this.tablePage.currentPage = response.data.current
          this.loadingtwo = false

          this.tableData.forEach((item, index) => {
            item.plain = index
          })
        })
        .catch((err) => {
          this.loadingtwo = false
          this.tableData = []
          this.requestFailed(err)
        })
    },
    handleStartOpenChange (open) {
      if (!open) {
        this.endOpen = true
      }
    },
    handleEndOpenChange (open) {
      this.endOpen = open
    },
    handleStartOpenChange1 (open) {
      if (!open) {
        this.endOpen1 = true
      }
    },
    handleEndOpenChange1 (open) {
      this.endOpen1 = open
    },
    // 获取部门列表
    choose (obj) {
      // 客户多选保留上次
      if (obj.obj.name === 'cusNos') {
        let arr = obj.obj.data
        this.noname.push(obj.obj.value)
        const set = new Set(this.noname.join(',').split(','))
        arr.forEach((i) => {
          this.no.push(i.cusNo)
        })
        this.data = String([...set])
        const no = new Set(this.no)
        this.queryParam[obj.obj.name] = [...no].join(',')
      }
      if (obj.obj.name === 'wh') {
        this.datawh = obj.obj.data.whName
        this.queryParam.wh = obj.obj.data.whNo
      }
    },
    reset () {
      this.queryParam = {
        staDd: '',
        endDd: '',
        moNo: '',
        casNo: '',
        zcNo: '',
        tzNo: '',
        wrNo: '',
        csNo1: '',
        zcNo2: '',
        pgNo: '',
        wgNo: '',
        csNo2: '',
        rem: '',
        qcNo: '',
      }
      this.staDd = null
      this.endDd = null
    },
    deleteTable (e) {
      var writerInfoArr = new Array();
      var writerInfo = new Object();
      let target = e.target
      if (target.nodeName == 'SPAN' || target.nodeName == 'I') {
        target = e.target.parentNode
      }
      target.blur()
      if (this.tableData == [] || this.tableData.length == 0) return this.$message.error('请查询之后再提交！')
      const selectRecords = this.$refs.xTable.getCheckboxRecords()
      if (selectRecords == [] || selectRecords.length == 0) return this.$message.error('请至少选择一条数据！')
      // const arr = JSON.parse(JSON.stringify(this.tableData))
      const arr = JSON.parse(JSON.stringify(selectRecords))
      arr.forEach((i, index) => {
        i.itm = null
        i.type = this.key
        writerInfo.csNo = i.csNo
        writerInfo.paraNo = i.paraNo
        writerInfo.itm = null
        writerInfo.type = 1
        writerInfoArr[index] = writerInfo;
        writerInfo = {}
      })
      deletedata(
        Object.assign(
          writerInfoArr
        )
      )
        .then((res) => {
          this.$message.success(res.data)
          this.getDataOne()
          this.$refs.xTable.clearCheckboxRow()
        })
        .catch((err) => {
          this.loading = false
          this.requestFailed(err)
        })
    },
    // 查看图片
    getPic (row) {
      this.$refs.ModalPic.create({ title: '查看图片' }, row)
    },
    touchList (row) {
      this.$refs.ModalList.create({ title: '详情', val: '3' }, row)
    },
    pic_add () {
      this.loading1 = true
      Filist(Object.assign({}))
        .then((res) => {
          if (res) {
            this.$message.success(this.$t('public.success'))
            this.loading1 = false
          } else {
            this.$message.error(this.$t('public.error'))
          }
        })
        .catch((err) => this.requestFailed(err))
        .finally(() => {
          this.loading1 = false
        })
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    clearData () {
      this.reset()
      this.tableData = []
      this.tablePage = {
        currentPage: 1,
        pageSize: 100,
        total: 0,
      }
    },
    checCheckboxkMethod ({ row }) {
      if (row.paraNo == '0301_rll_no' || row.paraNo == '0801_lh' || row.paraNo == '1701_lh') {
        return false
      } else {
        return true
      }
    }
  },
}
</script>

<style lang="less" scoped>
::v-deep .ant-tabs-nav-scroll {
  margin-top: -25px;
}
</style>
