<template>
  <a-card :bordered="false">
    <a-spin :spinning="spinning">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col
              :xxl='6'
              :xl='6'
              :lg='8'
              :md="12"
              :sm="12"
            >
              <a-form-item :label="$t('information.date')">
                <a-row :gutter="15">
                  <a-col
                    :xxl="12"
                    :xl="24"
                    :lg="24"
                    :md="24"
                    :sm="24"
                  >
                    <a-date-picker
                      v-model="staDd"
                      :disabled-date="disabledStartDate"
                      format="YYYY-MM-DD "
                      :placeholder="$t('information.staDd')"
                      @openChange="handleStartOpenChange"
                    />
                  </a-col>
                  <a-col
                    :xxl="12"
                    :xl="24"
                    :lg="24"
                    :md="24"
                    :sm="24"
                  >
                    <a-date-picker
                      v-model="endDd"
                      :disabled-date="disabledEndDate"
                      format="YYYY-MM-DD"
                      :placeholder="$t('information.endDd')"
                      :open="endOpen"
                      @openChange="handleEndOpenChange"
                    />
                  </a-col>
                </a-row>
              </a-form-item>
            </a-col>
            <!-- <a-col
              :xxl='6'
              :xl='6'
              :lg='8'
              :md="12"
              :sm="12"
            >
              <a-form-item :label="$t('work_details.moNo')">
                <a-select
                  showSearch
                  :allowClear="true"
                  :placeholder="$t('work_details.moNo')"
                  style="width: 100%"
                  :filterOption="false"
                  @focus="handleSearch"
                  v-model="queryParam.moNo"
                >
                  <a-select-option
                    v-for="(i,index) in custList"
                    :key="index"
                    :value="i"
                  >{{ i }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col> -->
            <a-col
              :xxl='6'
              :xl='6'
              :lg='8'
              :md="12"
              :sm="12"
            >
              <a-form-item :label="$t('work_details.casNo')">
                <div class="salesNo">
                  <a-select
                    mode="tags"
                    style="width: 100%"
                    placeholder="请选择唯一码"
                    v-model="queryParam.casNos"
                    @focus="handleSearch_casNo"
                  >
                    <a-select-option
                      v-for="(i) in csaList"
                      :key="i"
                    >
                      {{i}}
                    </a-select-option>
                  </a-select>
                </div>

                <!-- <a-select
                  mode="tags"
                  :size="size"
                  placeholder="Please select"
                  style="width: 200px"
                  @change="handleChange"
                >
                  <a-select-option
                    v-for="i in 25"
                    :key="(i + 9).toString(36) + i"
                  >
                    {{ (i + 9).toString(36) + i }}
                  </a-select-option>
                </a-select> -->

                <!-- <a-input
                  v-model="queryParam.casNo"
                  :placeholder="$t('work_details.casNo')"
                /> -->
              </a-form-item>
            </a-col>
            <a-col
              :xxl='6'
              :xl='6'
              :lg='8'
              :md="12"
              :sm="12"
            >
              <a-form-item label="一级工序">
                <a-select
                  mode="multiple"
                  showSearch
                  :allowClear="true"
                  :placeholder="$t('information.placeholder.zcNo')"
                  style="width: 100%"
                  :filterOption="false"
                  @focus="handle_zcNo"
                  v-model="queryParam.zcNos"
                >
                  <a-select-option
                    v-for="(i,index) in ZcList"
                    :key="index"
                    :value="i.zcNo"
                  >{{i.zcNo}}{{ i.zcName }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col style="float:right">
              <span class="table-page-search-submitButtons">
                <a-button
                  type="primary"
                  size='small'
                  @click="getList"
                  v-permission="rep_assigned_query"
                >{{ $t('public.query') }}</a-button>
                <a-button
                  size='small'
                  style="margin-left: 8px"
                  @click="clearPG"
                  v-permission="rep_assigned_clear"
                >清除派工</a-button>
                <a-button
                  size='small'
                  style="margin-left: 8px"
                  @click="reset"
                  v-permission="rep_assigned_reset"
                >{{ $t('public.reset') }}</a-button>
              </span>
            </a-col>

          </a-row>
        </a-form>
      </div>
      <div style="width:100%">
        <el-table
          id="tab"
          :data="tableData"
          row-key="id"
          show-overflow-tooltip
          style="width:100%"
          max-height='500px'
          border
          ref="multipleTable"
          highlight-current-row
          :row-class-name="tableRowClassName"
          :expand-row-keys='expandKeys'
          @selection-change="handleSelectionChange"
          lazy
          :load="load"
          :tree-props="{children: 'tzList', hasChildren: 'hasChildren'}"
        >
          <!-- fixed="left" -->
          <el-table-column
            prop="moNo"
            fixed="left"
            label="制令单号"
            align="center"
            sortable
          >
          </el-table-column>
          <el-table-column
            prop="casNo"
            fixed="left"
            label="唯一码"
            align="center"
            :width="wd"
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="caiz"
            :width="wd"
            label="材质"
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="sbsl"
            :width="wd"
            label="试棒数量"
          >
          </el-table-column>
          <el-table-column
            prop="jhq"
            align="center"
            :width="wd"
            label="交货期"
            sortable
          >
          </el-table-column>
          <el-table-column
            prop="zcNo"
            align='left'
            :width="wd"
            label="一级工序"
          >
            <template
              slot-scope="scope"
              v-if="scope.row.zcName&&scope.row.zcNo"
            >
              {{ scope.row.zcNo }}{{scope.row.zcName}}
            </template>
          </el-table-column>
          <el-table-column
            prop="tzNo"
            :width="wd"
            align="center"
            label="通知单号"
          >
          </el-table-column>
          <el-table-column
            prop="wr"
            :width="wd"
            align="center"
            label="一级工序报工"
          >
          </el-table-column>
          <el-table-column
            prop="zcNo2"
            align='left'
            :width="wd"
            label="二级工序"
          >
            <template
              slot-scope="scope"
              v-if="scope.row.zcName2&&scope.row.zcNo2"
            >
              {{ scope.row.zcNo2 }}{{scope.row.zcName2}}
            </template>
          </el-table-column>
          <el-table-column
            prop="ygs"
            :width="wd"
            align="center"
            label="作业人员"
          >
            <template
              slot-scope="scope"
              v-if="scope.row.ygs&&scope.row.ygs.length>0"
            >
              <el-popover
                trigger="hover"
                placement="top"
              >
                <a-tag
                  color='blue'
                  v-for="(i,index) in scope.row.ygs"
                  :key="index"
                > {{ i.salNo }}-{{ i.salName }}</a-tag>
                <div slot="reference">
                  {{ scope.row.ygs[0].salName }}
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            prop="sebName"
            align="center"
            :width="wd"
            label="设备"
          >
            <template
              slot-scope="scope"
              v-if="scope.row.sebName&&scope.row.sebNo"
            >
              {{ scope.row.sebNo }}{{scope.row.sebName}}
            </template>
          </el-table-column>
          <el-table-column
            prop="staDd"
            :width="wd"
            align="center"
            label="开始时间"
          >
          </el-table-column>
          <el-table-column
            prop="endDd"
            align="center"
            :width="wd"
            label="结束时间"
          >
          </el-table-column>
          <el-table-column
            prop="pgNo"
            :width="wd"
            align="center"
            label="派工单号"
          >
          </el-table-column>
          <el-table-column
            prop="wgCount"
            :width="wd"
            align="center"
            label="完工单数"
          >
            <template slot-scope="scope">
              <div v-if="scope.row.wgCount===0">
                {{ scope.row.wgCount }}
              </div>
              <a
                v-else
                @click="click_wgCount(scope.row)"
              > {{ scope.row.wgCount }}</a>
            </template>
          </el-table-column>

          <!-- fixed="right" -->
          <el-table-column
            type="selection"
            width="50"
            align="center"
          >
          </el-table-column>
        </el-table>
      </div>

      <Modal ref="modal" />
    </a-spin>
  </a-card>
</template>

<script>
import {
  findList, getZC1, getMo, del, getZc, getCas
} from '@/api/report/assigned'
import Modal from './modal'
import moment from 'moment'
export default {
  components: {
    Modal
  },
  data () {
    return {
      rep_assigned_query: 'rep_assigned_query',
      rep_assigned_clear: 'rep_assigned_clear',
      rep_assigned_reset: 'rep_assigned_reset',
      spinning: false,
      expandKeys: [],
      tableData: [
      ],
      status: true,
      endOpen: false,
      loading: false,
      staDd: moment(new Date()),
      endDd: moment(new Date()),
      custList: [],
      ZcList: [],
      zcNos: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      type: {
        zcNo2: false,
      },
      queryParam: {
        moNo: '',
        casNos: [],
        zcNos: []
      },
      select: [],
      obj: {},
      wd: '0',
      numbers: [],
      csaList: []
    }
  },
  created () {
    // this.getList()
  },
  mounted () {
    let a = document.getElementById('tab')

    this.$nextTick(() => {
      this.wd = (a.clientWidth - 5) / 17
    })
  },

  watch: {
    staDd (e) {
      this.staDd = e
      this.queryParam.moNo = ''
      // this.queryParam.casNo = ''
      this.queryParam.zcNo = ''
      // this.handleSearch()
    },
    endDd (e) {
      this.endDd = e
      this.queryParam.moNo = ''
      // this.queryParam.casNo = ''
      this.queryParam.zcNo = ''
      // this.handleSearch()
    }
  },
  methods: {
    tableRowClassName ({ row, rowIndex }) {
      if (row.moNo) {
        return 'warning-row '
      }
      return ''
    },
    handleSelectionChange (val) {
      this.select = val
    },
    click_wgCount (row) {
      this.$refs.modal.create({ title: '完工详情' }, row)
    },
    load (tree, treeNode, resolve) {
      getZc({
        tzNo: tree.tzNo
        // tzNo: 'TZ2009100001'

      }).then(function (response) {
        resolve(response.data)
      })
        .catch(err => this.requestFailed(err))
        .finally(() => {
        })
    },
    handleSearch_casNo () {
      let staDd
      let endDd
      if (this.endDd !== null) {
        staDd = moment(this.staDd).format('YYYY-MM-DD 12:00:00')
      } else {
        staDd = null
      }
      if (this.endDd !== null) {
        endDd = moment(this.endDd).format('YYYY-MM-DD 12:00:00')
      } else {
        endDd = null
      }
      getCas(Object.assign({
        staDd: staDd,
        endDd: endDd
      })).then((res) => {

        this.csaList = res.data
      }).catch(err => this.requestFailed(err))
    },
    handleSearch () {
      let staDd
      let endDd
      if (this.endDd !== null) {
        staDd = moment(this.staDd).format('YYYY-MM-DD 12:00:00')
      } else {
        staDd = null
      }
      if (this.endDd !== null) {
        endDd = moment(this.endDd).format('YYYY-MM-DD 12:00:00')
      } else {
        endDd = null
      }
      getMo(Object.assign({
        staDd: staDd,
        endDd: endDd
      })).then((res) => {
        this.custList = res.data
      }).catch(err => this.requestFailed(err))
    },
    disabledStartDate (startValue) {
      const endValue = this.endDd
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate (endValue) {
      const startValue = this.staDd
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() >= endValue.valueOf()
    },
    // 一级制程
    handle_zcNo () {
      getZC1(Object.assign({
        moNo: this.queryParam.moNo,
        casNo: this.queryParam.casNo
      })).then((res) => {
        this.ZcList = res.data
      }).catch(err => this.requestFailed(err))
    },
    // 查询列表
    getList (row) {
      let staDd
      let endDd
      if (this.endDd !== null) {
        staDd = moment(this.staDd).format('YYYY-MM-DD 12:00:00')
      } else {
        staDd = null
      }
      if (this.endDd !== null) {
        endDd = moment(this.endDd).format('YYYY-MM-DD 12:00:00')
      } else {
        endDd = null
      }
      this.spinning = true
      this.obj = {
        pgBz: 2,
        moNo: '',
        casNos: this.queryParam.casNos ? this.queryParam.casNos : null,
        staDd: staDd,
        endDd: endDd,
        zcNos: this.queryParam.zcNos ? this.queryParam.zcNos : ''
      }
      findList(Object.assign({
        ...this.obj
      })).then(res => {
        let a = res.data
        a.forEach(e => {
          this.expandKeys.push(e.id)
        })
        setTimeout(() => {
          this.tableData = res.data
        }, 100)
        this.spinning = false

      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.spinning = false
        })
    },
    handleStartOpenChange (open) {
      if (!open) {
        this.endOpen = true
      }
    },
    handleEndOpenChange (open) {
      this.endOpen = open
    },
    reset () {
      this.queryParam = {
        moNo: '',
        casNos: [],
        zcNos: []
      }
      this.staDd = null
      this.endDd = null
    },
    clearPG () {
      let trg
      if (this.select.length > 0) {
        this.select.find((e) => {
          if (!e.pgNo) {
            return trg = false
          }
          if (trg === false) {
            this.$notification['error']({
              message: this.$t('public.message'),
              description: '请选择含有派工单号的数据清除！'
            })
          } else {
            const that = this
            this.$confirm({
              title: this.$t('public.del.title'),
              content: '确定清除该派工？',
              okText: this.$t('public.sure'),
              okType: 'danger',
              cancelText: this.$t('public.cancel'),
              onOk () {
                let pgNos = []
                that.select.forEach(e => {
                  pgNos.push(e.pgNo)
                })
                that.spinning = true
                del(pgNos).then(res => {
                  if (res) {
                    that.spinning = false
                    if (res.data.length > 0) {
                      res.data.forEach(e => {
                        that.$message.warn(e, 5)
                      })
                    } else {
                      that.tableData = []
                      that.$message.success(that.$t('public.success'))
                    }
                    that.getList(that.obj)
                  }
                }).catch(err => that.requestFailed(err))
                  .finally(() => {
                    that.spinning = false
                  })
              },
              onCancel () { }
            })
          }
        })
      } else {
        this.$notification['error']({
          message: this.$t('public.message'),
          description: '请选择含有派工单号的数据清除！'
        })
      }


    },

  }

}
</script>

 <style lang="css" >
.salesNo /deep/ .ant-select-selection {
  position: relative !important;
  z-index: 999 !important;
}
.el-table .warning-row {
  background: #cccccc;
}
.el-table >>> .warning-row1 {
  background-color: red;
}
/* ::-webkit-scrollbar {
  display: none;
} */
.el-table {
  overflow: auto;
  /* // 必须设置 */
  position: relative;
}
</style>
