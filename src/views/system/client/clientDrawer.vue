/* eslint-disable no-self-compare */
<template>
  <div>
    <a-drawer placement="right" :closable="false" @close="onClose" :visible="visible" :destroyOnClose="true"
      width="70%">
      <template slot="title">
        <span class="title-name">{{ title }}</span>
        <span v-if="modeType !== '0'" class="title-age">
          <a-dropdown>
            <a-button class="ant-dropdown-link">
              {{ $t('public.action') }}
              <a-icon type="down" />
            </a-button>
            <a-menu slot="overlay">
              <a-menu-item v-if="permissions.system_client_del">
                <a @click="del()">{{ $t('public.delete') }}</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </template>
      <a-form :form="form" ref="form">
        <a-row :gutter="16">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('client.clientId')" v-bind="formItemLayout">
              <a-input :disabled="formEdit"
                v-decorator="['clientId', { rules: [{ required: true, message: $t('client.placeholder.clientId') }] }]"
                :placeholder="$t('client.placeholder.clientId')" />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('client.clientSecret')" v-bind="formItemLayout">
              <a-input :disabled="formEdit" v-decorator="[
                'clientSecret',
                { rules: [{ required: true, message: $t('client.placeholder.clientSecret') }] }
              ]" :placeholder="$t('client.placeholder.clientSecret')" />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('client.scope')" v-bind="formItemLayout">
              <!-- <a-input
                :disabled="formEdit"
                v-decorator="['scope', { rules: [{ required: true, message: $t('client.placeholder.scope') }] }]"
                :placeholder="$t('client.placeholder.scope')"
              /> -->
              <a-select :disabled="formEdit"
                v-decorator="['scope', { rules: [{ required: true, message: $t('client.placeholder.scope') }] }]"
                :placeholder="$t('client.placeholder.scope')">
                <a-select-option value="server">server</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('client.autoapprove')" v-bind="formItemLayout">
              <a-radio-group v-model="autoapprove" :disabled="formEdit">
                <a-radio-button value="true" style="margin-right:20px">{{ $t('client.auto.a') }}</a-radio-button>
                <a-radio-button value="false">{{ $t('client.auto.b') }}</a-radio-button>
              </a-radio-group>
            </a-form-item>
          </a-col>

          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('client.accessTokenValidity')" v-bind="formItemLayout">
              <a-input :disabled="formEdit" type="number" v-decorator="[
                'accessTokenValidity',
                { rules: [{ required: true, message: $t('client.placeholder.accessTokenValidity') }] }
              ]" :placeholder="$t('client.placeholder.accessTokenValidity')" />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('client.refreshTokenValidity')" v-bind="formItemLayout">
              <a-input :disabled="formEdit" type="number" v-decorator="[
                'refreshTokenValidity',
                { rules: [{ required: true, message: $t('client.placeholder.refreshTokenValidity') }] }
              ]" :placeholder="$t('client.placeholder.refreshTokenValidity')" />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('client.webServerRedirectUri')" v-bind="formItemLayout">
              <a-input :disabled="formEdit" v-decorator="[
                'webServerRedirectUri',
                { rules: [{ message: $t('client.placeholder.webServerRedirectUri') }] }
              ]" :placeholder="$t('client.placeholder.webServerRedirectUri')" />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item :label="$t('client.authorities')" v-bind="formItemLayout">
              <a-input :disabled="formEdit"
                v-decorator="['authorities', { rules: [{ message: $t('client.placeholder.authorities') }] }]"
                :placeholder="$t('client.placeholder.authorities')" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <a-form-item :label="$t('client.authorizedGrantTypes')" :labelCol="{ span: 3 }" :wrapperCol="{ span: 18 }">
              <a-checkbox-group :disabled="formEdit" :options="plainOptions" v-model="authorizedGrantTypes"
                @change="onChange" />
              <!-- <a-checkbox :checked="isChecked" @change="onChange">Checkbox</a-checkbox> -->
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <a-form-item :label="$t('client.additionalInformation')" :labelCol="{ span: 3 }" :wrapperCol="{ span: 18 }">
              <a-input :disabled="formEdit" type="textarea" v-decorator="[
                'additionalInformation',
                { rules: [{ message: $t('client.placeholder.additionalInformation') }] }
              ]" :placeholder="$t('client.placeholder.additionalInformation')" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <a-row :gutter="16">
        <a-col class="gutter-row" :span="12" style="text-align:right">
          <a-button type="primary" v-if="modeType === '0'" @click="handleOK()" :loading="loading">{{
            $t('public.save')
          }}</a-button>
          <a-button type="primary" v-if="modeType === '1' && permissions.system_client_edit"
            @click="handleMenuClick()">{{
              $t('public.edit') }}</a-button>
          <a-button type="primary" v-if="modeType === '2' && permissions.system_client_edit" :loading="loading"
            @click="handleEdit()">{{ $t('public.save') }}</a-button>
        </a-col>
        <a-col class="gutter-row" :span="12" style="text-align:left">
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { addObj, putObj, delObj } from '@/api/system/client'
export default {
  name: 'DictModel',
  data() {
    return {
      checkAll: false,
      plainOptions: [],
      authorizedGrantTypes: [],
      authorizedGrantType: '',
      autoapprove: '',
      title: '',
      loading: false,
      visible: false,
      modeType: '', // 添加为0，编辑为1
      system: '', // 类型
      row: {},
      formEdit: false,
      confirmLoading: false,
      isChecked: true,
      form: this.$form.createForm(this),
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 7 },
          md: { span: 8 },
          lg: { span: 7 }
        },
        wrapperCol: {
          xs: { span: 5 },
          sm: { span: 16 },
          md: { span: 17 },
          lg: { span: 16 }
        }
      }
    }
  },

  created() {
    this.plainOptions = ['password', 'refresh_token', 'authorization_code', 'client_credentials']
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  methods: {
    onChange(authorizedGrantTypes) {
      this.authorizedGrantTypes = authorizedGrantTypes
      this.indeterminate = !!authorizedGrantTypes.length && authorizedGrantTypes.length < this.plainOptions.length
      this.checkAll = authorizedGrantTypes.length === this.plainOptions.length
    },
    onClose() {
      this.authorizedGrantTypes = []
      this.visible = false
      this.form.resetFields()
      this.startTime = null
      this.endTime = null
      this.status = '0'
    },
    // 删除按钮
    del(record) {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('public.del.content'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk() {
          const id = that.row.clientId
          that.loading = true
          delObj(id)
            .then(() => {
              that.onClose()
              that.$emit('getList')
              that.loading = false
              that.$message.success(that.$t('public.success'))
            })
            .catch(() => { })
        },
        onCancel() { }
      })
    },
    // 添加弹框
    create(model) {
      this.title = model.title
      this.visible = true
      this.formEdit = false
      this.modeType = '0'
    },
    // 点击编辑按钮
    handleMenuClick() {
      this.modeType = '2'
      this.title = this.$t('public.edit')
      this.formEdit = false
    },
    // 编辑弹框
    edit(model, record) {
      this.title = model.title
      this.modeType = '1'
      this.visible = true
      this.formEdit = true
      this.row = record
      this.autoapprove = record.autoapprove
      this.authorizedGrantTypes = record.authorizedGrantTypes

      this.$nextTick(() => {
        this.form.setFieldsValue({
          clientId: record.clientId,
          clientSecret: record.clientSecret,
          scope: record.scope,
          accessTokenValidity: record.accessTokenValidity,
          webServerRedirectUri: record.webServerRedirectUri,
          authorities: record.authorities,
          additionalInformation: record.additionalInformation,
          refreshTokenValidity: record.refreshTokenValidity
        })
      })
    },
    // 点击确定保存按钮
    handleOK() {
      this.form.validateFields((err, values) => {
        const Params = {
          ...values,
          authorizedGrantTypes: this.authorizedGrantTypes,
          autoapprove: this.autoapprove
        }
        if (!err) {
          this.loading = true
          addObj(Params)
            .then(() => {
              this.$emit('getList')
              this.visible = false
              this.loading = false
              this.$message.success(this.$t('public.success'))
            })
            .catch(() => {
              this.loading = false
              this.$message.success(this.$t('public.error'))
            })
        }
      })
    },
    // 点击修改按钮
    handleEdit() {
      this.form.validateFields((err, values) => {
        const Params = {
          ...values,
          autoapprove: this.autoapprove,
          authorizedGrantTypes: this.authorizedGrantTypes
        }
        if (!err) {
          this.loading = true
          putObj(Params)
            .then(() => {
              this.$emit('getList')
              this.visible = false
              this.loading = false
              this.$message.success(this.$t('public.success'))
            })
            .catch(() => {
              this.loading = false
              this.$message.success(this.$t('public.error'))
            })
        }
      })
    }
  }
}
</script>
