<template>
  <div class="consrm" style="position:relative">
    <div style="position: absolute;top: 6px;left:190px;z-index:1">
      <a-button icon="search" size='small' style="margin-left:10px" type="primary" @click="search">{{ $t('public.query')
        }}</a-button>
      <a-button icon="download" style="margin-left: 8px" type="primary" @click="dropdownMenuEvent"
                size="small">导出</a-button>
      <!-- <a-button
    type="primary"
    id="add_table"
    @click="addTableRow()"
    style="margin-right:8px;"
    >新增</a-button>
     <a-button
      type="primary"
      @click="save"
    style="margin-right:8px;"
    >保存</a-button>
    <a-button
     type="primary"
     style="background-color: red;background-color: #f56c6c;border-color: #f56c6c;color:#fff;"
      @click="locaDelTableRow()"
    >删除</a-button> -->
    </div>

    <el-tabs type="border-card" v-model="activeNameMain" @tab-click="handleClick">

      <el-tab-pane label="设备日常点检表" name="first">
        <deviceselect ref="refdevice" @deviceList="deviceList" />
        <a-card :bordered="false">
          <a-form layout="inline">
            <a-row :gutter="48">
              <a-col :span="8">
                <a-form-item :label="$t('设备位置')" prop="dep">
                  <my-selectListwo url="ems/place/queryPage" :read-only="true" :tableColumn="$Column.deviceplaceNo"
                                   :form="$Form.devicesalmDep" :data="datatplaceNo" name="placeNo" allowClear
                                   @choose="chooseplaceNo($event)" ref="selectListplaceNo"
                                   v-decorator="['placeNo', { rules: [{ required: true, message:'请选择设备位置' } ] }]"
                                   placeholder="请选择设备位置"></my-selectListwo>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-input-search v-model="datatsbNo" placeholder="请选择设备" @search="selectclick()" readOnly>
                  <a-tooltip slot="suffix" title="清空">
                    <a-icon @click="clearclick" type="close-circle" style="color: rgba(0,0,0,.45)" />
                  </a-tooltip>
                  <a-button ref="btn" slot="enterButton">
                    <a-icon type="database" />
                  </a-button>
                </a-input-search>
                <!-- <a-form-item label="设备">
                  <my-selectListwo url="ems/sbtz/queryPage" :read-only="true" :tableColumn="$Column.devicesbNo"
                    :form="$Form.devicesalmDep" :data="datatsbNo" name="sbNo" @choose="choosesbNo($event)" allowClear
                    ref="selectList"
                    v-decorator="['sbNo', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                    :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
                </a-form-item> -->
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item :label="$t('年月')" prop="dep">
                  <a-date-picker mode="month" placeholder="请选择年月" format="YYYY-MM" v-model="yaerMode" style="width:100%"
                                 allowClear :open="yearShowOne" @openChange="openChangeOne" @panelChange="panelChangeOne" />
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
              </a-col>
            </a-row>
          </a-form>

          <a-row :gutter="8">
            <a-col :span="24">
              <a-row>
                <!-- <a-tabs v-model="activeName" type="card" @change="handleTabClick" style="font-size:16px"> -->
                <!--
              size="mini"
                max-height="620"
              height="64vh"
       :cell-style="{ verticalAlign: 'top' }"
      -->
                <!-- font-size: 14px;
    color: #606266; -->
                <!-- <a-tab-pane key="1" tab="已检验"> -->
                <!-- :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }" -->
                <!-- <table border="1">
                <tr>
                  <th rowspan="2">合并两行的表头</th>
                  <th>第一列</th>
                  <th>第二列</th>
                </tr>
                <tr>
                  <td>数据1</td>
                  <td>数据2</td>
                </tr>
                <tr>
                  <td>数据3</td>
                  <td>数据4</td>
                  <td>数据5</td>
                </tr>
              </table>
              <table
              id="table"
              border="1"
              align="center"
              style="width: 100%; border-collapse: collapse"
              class="confluenceTable"
            >
              <thead id="table2">
                <tr>
                  <th
                    align="center"
                    style="text-align: center; font-size: 10px"
                  >序号</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 10px;width:100px;"
                  >点检项目</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 10px"
                  >项目内容</th>
                  <th
                    align="center"
                    style="text-align: center; font-size: 10px"
                  >点检方法</th>
                  <th
                    align="center"
                    colspan="31"
                    style="text-align: center; font-size: 10px"
                  >点检日期</th>
                </tr>
              </thead>
              <tbody class="form1body">
                <tr
                    v-for="(item, index) in tableDatatwo2"
                    :key="item"
                  >
                    <td
                      style="text-align: center; font-size: 10px;width:20px;"

                    ><div>{{index+1}}</div>
                    </td>
                    <td
                      style="text-align: left;width:100px;font-size: 10px;"

                    >{{item.xmNm}}</td>
                    <td >{{item.xmNr}}</td>
                    <td >{{item.ffNm}}</td>

                    <td>{{item.xmBaiBanJgVo[0]}}</td>
                    <td>{{item.xmBaiBanJgVo[1]}}</td>
                    <td>{{item.xmBaiBanJgVo[2]}}</td>
                    <td>{{item.xmBaiBanJgVo[3]}}</td>
                    <td>{{item.xmBaiBanJgVo[4]}}</td>
                    <td>{{item.xmBaiBanJgVo[5]}}</td>
                    <td>{{item.xmBaiBanJgVo[6]}}</td>
                    <td>{{item.xmBaiBanJgVo[7]}}</td>
                    <td>{{item.xmBaiBanJgVo[8]}}</td>
                    <td>{{item.xmBaiBanJgVo[9]}}</td>

                    <td>{{item.xmBaiBanJgVo[10]}}</td>
                    <td>{{item.xmBaiBanJgVo[11]}}</td>
                    <td>{{item.xmBaiBanJgVo[22]}}</td>
                    <td>{{item.tiDd}}</td>
                    <td>{{item.tiDd}}</td>
                    <td>{{item.tiDd}}</td>
                    <td>{{item.tiDd}}</td>
                    <td>{{item.tiDd}}</td>
                    <td>{{item.tiDd}}</td>
                    <td>{{item.tiDd}}</td>

                    <td>{{item.tiDd}}</td>
                    <td>{{item.tiDd}}</td>
                    <td>{{item.tiDd}}</td>
                    <td>{{item.tiDd}}</td>
                    <td>{{item.tiDd}}</td>
                    <td>{{item.tiDd}}</td>
                    <td>{{item.tiDd}}</td>
                    <td>{{item.tiDd}}</td>
                    <td>{{item.tiDd}}</td>
                    <td>{{item.tiDd}}</td>

                    <td>{{item.tiDd}}</td>
                </tr>
                <tr>
                  <th colspan="3">合计：</th>
                  <th>白班</th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                </tr>
                <tr>
                  <th colspan="3"></th>
                  <th>晚班</th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
               </tr>
              </tbody>
            </table> -->
                <el-table stripe :data="tableDatatwo2" highlight-current-row ref="multipleTable"
                          style="width: 100%;margin-top:10px;" @selection-change="handleSelectionChangereason" height="500px"
                          :span-method="objectSpanMethod" class="dianjian">
                  <!-- :cell-style="{ verticalAlign: 'top' }" -->
                  <!-- @row-dblclick="mfBxHanddleone" -->
                  <!-- :summary-method="data=>summaries(data,index)"
              show-summary -->
                  <!-- :span-method="objectSpanMethod" -->
                  <!--  -->
                  <!-- :header-cell-style="headerStyle" -->
                  <!-- :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }" -->
                  <!-- <el-table
                stripe
                :data="tableData"
                highlight-current-row
                :cell-style="{ verticalAlign: 'top' }"
                style="width: 100%;margin-top:10px;"
                @selection-change="handleSelectionChange"
                height="400px"
                :span-method="objectSpanMethod"
                :header-cell-style="{fontSize:'14px',color: '#606266', verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '700' }"
              > -->
                  <!-- :header-cell-style="headerStyle" -->
                  <!-- :span-method="objectSpanMethod" -->
                  <!-- :header-cell-style="{fontSize:'14px',color: '#606266', verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '700' }" -->
                  <!-- @sort-change="sortChange" -->
                  <!-- <el-table-column type="selection" align="center" width="50"></el-table-column> -->
                  <!-- <el-table-column
                  width="60px"
                  label="选择"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-radio
                      v-model="radio"
                      :label="scope.$index"
                      class="radio"
                      @change.native="raidchange(scope.row)"
                    >&nbsp;</el-radio>
                  </template>
                </el-table-column> -->

                  <!-- <el-table-column prop="tyDd" width="100" align="left" label="检验日期">
                  <template slot-scope="scope">
                    {{scope.row.tyDd | formatDate}}
                  </template>
                </el-table-column> -->
                  <!-- <el-table-column type="selection" width="48"></el-table-column> -->
                  <el-table-column prop="tiDd" width="110" align="center" label="序号">
                    <template slot-scope="scope">
                      {{scope.row.addid}}
                    </template>
                  </el-table-column>
                  <el-table-column prop="xmNm" width="110" align="center" label="点检项目"></el-table-column>
                  <el-table-column prop="xmNr" width="110" align="center" label="项目内容"></el-table-column>
                  <el-table-column prop="ffNm" width="110" align="center" label="点检方法">
                    <template slot-scope="scope">
                      <div v-if="tableDatatwo2.length-1 == scope.$index">
                        <div style="border-bottom: 1px solid #EBEEF5;margin-bottom:3px">白班</div>
                        <div style="margin-top:3px">晚班</div>
                      </div>
                      <div v-else>{{scope.row.ffNm}}</div>
                    </template>

                  </el-table-column>
                  <el-table-column prop="tiDd" width="110" align="center" label="点检日期">
                    <el-table-column prop="name" align="center" label="1" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[0]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[0]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="2" align="center" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[1]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[1]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="3" align="center" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[2]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[2]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="4" align="center" class="twocell" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[3]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[3]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="5" align="center" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[4]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[4]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="6" align="center" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[5]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[5]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="7" align="center" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[6]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[6]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="8" align="center" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[7]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[7]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" align="center" label="9" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[8]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[8]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" align="center" label="10" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[9]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[9]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="11" align="center" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[10]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[10]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="12" align="center" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[11]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[11]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="13" align="center" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[12]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[12]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" align="center" label="14" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5">{{scope.row.xmBaiBanJgVo[13]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[13]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" align="center" label="15" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5">{{scope.row.xmBaiBanJgVo[14]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[14]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="16" align="center" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[15]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[15]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="17" align="center" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5">{{scope.row.xmBaiBanJgVo[16]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[16]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="18" align="center" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[17]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[17]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" align="center" label="19" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[18]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[18]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" align="center" label="20" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[19]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[19]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" align="center" label="21" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[20]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[20]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" align="center" label="22" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[21]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[21]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="23" align="center" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[22]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[22]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" align="center" label="24" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[23]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[23]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="25" align="center" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[24]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[24]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="26" align="center" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5">{{scope.row.xmBaiBanJgVo[25]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[25]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" align="center" label="27" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[26]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[26]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="28" align="center" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[27]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[27]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" align="center" label="29" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5">{{scope.row.xmBaiBanJgVo[28]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[28]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" align="center" label="30" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[29]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[29]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" align="center" label="31" width="120">
                      <template slot-scope="scope">
                        <div class="multi-row-cell">
                          <div style="border-bottom: 1px solid #EBEEF5;">{{scope.row.xmBaiBanJgVo[30]}}</div>
                          <div>{{scope.row.xmWanBanJgVo[30]}}</div>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table-column>
                </el-table>
                <!-- <div style="display: flex;justify-content: space-between;margin: 2px">
                <el-pagination
                  background
                  :page-sizes="[5, 10,20, 30, 50]"
                  :page-size="20"
                  :pager-count="5"
                  @size-change="pageSizeChange"
                  :current-page="tablePage.currentPage"
                  @current-change="currentChange"
                  layout="total,sizes,prev, pager, next"
                  :total="totalCount"
                ></el-pagination>
              </div> -->


                <!-- <el-table
    :data="tableData2"
    border
    style="width: 600"
    :header-cell-style="headerCellStyle"
  >
    <el-table-column
      prop="name"
      label="姓名、年龄、家乡"
      width="150"
      align="center"
    ></el-table-column>
    <el-table-column
      prop="age"
      label="年龄"
      width="150"
      align="center"
    ></el-table-column>
    <el-table-column
      prop="home"
      label="家乡"
      width="150"
      align="center"
    ></el-table-column>
    <el-table-column
      prop="hobby"
      label="爱好"
      width="150"
      align="center"
    ></el-table-column>
  </el-table>

  <el-table
    :data="tableData2"
    border
    style="width: 600"
    :header-cell-style="headerCellStyle2"
  >
    <el-table-column prop="name" label="姓名" width="150" align="center">
      <el-table-column
        prop="name"
        label="三列基础信息"
        width="150"
        align="center"
      ></el-table-column>
    </el-table-column>
    <el-table-column prop="name" label="年龄" width="150" align="center">
      <el-table-column
        prop="age"
        label="年龄"
        width="150"
        align="center"
      ></el-table-column>
    </el-table-column>
    <el-table-column prop="name" label="家乡" width="150" align="center">
      <el-table-column
        prop="home"
        label="家乡"
        width="150"
        align="center"
      ></el-table-column>
    </el-table-column>
    <el-table-column
      prop="hobby"
      label="爱好"
      width="150"
      align="center"
    ></el-table-column>
  </el-table> -->


                <!-- <el-table
    :data="tableData3"
    border
    style="width: 600"
    :header-cell-style="headerCellStyle3"
  >
    <el-table-column
      prop="name"
      label="基本信息（姓名、年龄、家乡）"
      align="center"
    >
    </el-table-column>
    <el-table-column
      prop="age"
      label="年龄"
      align="center"
    >
    </el-table-column>
    <el-table-column
      prop="home"
      label="家乡"
      align="center"
    >
    </el-table-column>
    <el-table-column prop="kind" label="所属种族" align="center">
    </el-table-column>
    <el-table-column label="重要信息" align="center">
      <el-table-column label="公开重要信息" align="center">
        <el-table-column prop="nickname" label="法号" align="center">
        </el-table-column>
        <el-table-column prop="hobby" label="爱好&性格" align="center">
        </el-table-column>
        <el-table-column prop="personality" label="性格" align="center">
        </el-table-column>
      </el-table-column>
      <el-table-column label="保密重要信息" align="center">
        <el-table-column prop="bornBackground" label="出身背景" align="center">
        </el-table-column>
        <el-table-column prop="skill" label="技能" align="center">
        </el-table-column>
      </el-table-column>
    </el-table-column>
  </el-table> -->

                <!-- </a-tab-pane> -->
                <!-- <a-tab-pane key="2" tab="已检验">

            </a-tab-pane> -->
                <!-- </a-tabs> -->
                <!-- <vxe-table
            border
            resizable
            stripe
            highlight-current-row
            show-overflow
            highlight-hover-row
            export-config
            ref="xTable"
            :loading="loading"
            :data="tableData"
            @cell-click="ClickEvent"
            :keyboard-config="{ isArrow: true }"
            :edit-config="{ trigger: 'click', mode: 'row' }"
          >
            <vxe-table-column
              field="tiNo"
              fixed="left"
              title="quality.tiNo"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="prdNo"
              title="quality.prdNo"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="prdtName"
              title="quality.prdtName"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="pgNo"
              title="quality.pgNo"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="zcName"
              title="quality.zcName"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="prdMark"
              title="quality.prdMark"
              align="center"
            ></vxe-table-column>
          </vxe-table>
          <vxe-pager
            :loading="loading"
            :current-page="tablePage.currentPage"
            :page-size="tablePage.pageSize"
            :total="tablePage.total"
            :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
            @page-change="handlePageChange"
          >
          </vxe-pager> -->
              </a-row>
              <a-row>
                <!-- <a-col :span="24">
            <a-button
              :disabled="btn.newOpen"
              style="margin-left: 8px"
              type="primary"
              @click="onSubmit('0')"
              v-permission="mes_quality_newOpen"
            >{{ $t('quality.newOpen') }}</a-button>
            <a-button
              :disabled="btn.inspection"
              style="margin-left: 8px"
              type="primary"
              v-permission="mes_quality_inspection"
              @click="onSubmit('3')"
            >{{ $t('quality.inspection') }}</a-button>
            <a-button
              :disabled="btn.pause"
              style="margin-left: 8px"
              type="primary"
              v-permission="mes_quality_pause"
              @click="onSubmit('2')"
            >{{ $t('quality.pause') }}</a-button>
            <a-button
              :disabled="btn.continues"
              style="margin-left: 8px"
              type="primary"
              v-permission="mes_quality_continues"
              @click="onSubmit('1')"
            >{{ $t('quality.continues') }}</a-button>
            <a-button
              style="margin-left: 8px"
              type="primary"
              v-permission="mes_quality_task"
            >{{ $t('work.task') }}</a-button>

          </a-col> -->
                <!-- <a-col>
            <formList :row="row"></formList>
          </a-col> -->

              </a-row>
            </a-col>
          </a-row>
        </a-card>
      </el-tab-pane>
    </el-tabs>
    <Export ref="Export" />
  </div>
</template>
<script>
import {
  fetchList, getItem, start, lsqcdps, lsqcyps, datasalData,
  lsqcfinCtrl, lsqcupdCtrl, delCtrl
} from '@/api/mes/quality'
import { queryEverydayEs, djqueryEveryday } from '@/api/device/category'
import MySelectListwo from '@/components/MySelectListwo'
import { mapGetters, mapState } from 'vuex'
import moment from 'moment'
import Export from './export'
import deviceselect from './deviceselect'
export default {
  components: {
    MySelectListwo,
    Export,
    deviceselect
  },
  data() {
    return {
      mes_quality_query: 'mes_quality_query',
      mes_quality_reset: 'mes_quality_reset',
      mes_quality_newOpen: 'mes_quality_newOpen',
      mes_quality_inspection: 'mes_quality_inspection',
      mes_quality_pause: 'mes_quality_inspection',
      mes_quality_continues: 'mes_quality_continues',
      mes_quality_task: 'mes_quality_task',
      queryParam: {},
      tableData: [
        {
          tyNo: '1',
          tiDd: '1',
          name: '看看',
          prdNo: '1',
          prdName: '1',
          qty: '1',
          qtyLost: '1',
          zcName: '1',
          chkTypeId: '1',
          salNo: '1',
        },
        {
          tyNo: '2',
          prdNo: '2',
          tiDd: '2',
          name: 'vv',
          prdName: '2',
          qty: '2',
          qtyLost: '2',
          zcName: '2',
          chkTypeId: '12',
          salNo: '1',
        }
      ],
      tableDatatwo: [],
      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      tablePagetwo: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      row: {},
      rowIndex: {},
      // 按钮控制
      btn: {
        newOpen: true, // 首检开工
        open: true, // 开始
        pause: true, // 暂停
        continues: true, // 继续
        report: true, // 报工
        exceptional: true, // 异常
        inspection: true, // 送检
        introspecting: true // 自检
      },
      queryParam: {
        dep: '',
        depName: '',
        yearStr: '',
        monthStr: '',
        sbNo: ''
      },
      activeName: '1',
      multipleSelection: [],
      activeNameMain: "first",
      radioSelection: null,
      radio: '',
      totalCount: -1,
      querypinzhi: '',
      tableData2: [
        {
          name: "孙悟空",
          age: 500,
          home: "花果山水帘洞",
          hobby: "大闹天宫",
        },
        {
          name: "猪八戒",
          age: 88,
          home: "高老庄",
          hobby: "吃包子",
        },
        {
          name: "沙和尚",
          age: 1000,
          home: "通天河",
          hobby: "游泳",
        },
        {
          name: "唐僧",
          age: 99999,
          home: "东土大唐",
          hobby: "取西经",
        },
      ],
      tableData3: [
        {
          name: "孙悟空",
          age: 500,
          home: "花果山水帘洞",
          kind: "monkey",
          nickname: "斗战胜佛",
          hobby: "大闹天宫",
          personality: "勇敢坚韧、疾恶如仇",
          bornBackground: "仙石孕育而生",
          skill: "72变、筋斗云",
        },
        {
          name: "猪八戒",
          age: 88,
          home: "高老庄",
          kind: "pig",
          nickname: "净坛使者",
          hobby: "吃包子",
          personality: "好吃懒做、贪图女色",
          bornBackground: "天蓬元帅错投猪胎",
          skill: "36变",
        },
        {
          name: "沙和尚",
          age: 1000,
          home: "通天河",
          kind: "human",
          nickname: "金身罗汉",
          hobby: "游泳",
          personality: "憨厚老实、任劳任怨",
          bornBackground: "卷帘大将被贬下界",
          skill: "18变",
        },
        {
          name: "唐僧",
          age: 99999,
          home: "东土大唐",
          kind: "human",
          nickname: "檀功德佛",
          hobby: "取西经",
          personality: "谦恭儒雅、愚善固执",
          bornBackground: "金蝉子转世",
          skill: "念紧箍咒",
        },
      ],
      yaerMode: '',
      yearShowOne: false,
      data: '',
      tableDatatwo2: [],
      datatsbNo: '',
      sbNm: '',
      datatplaceNo: ''


    }
  },
  computed: {
    // doubledData() {
    //   return this.tableDatatwo2.concat(this.tableDatatwo2);
    // },
    ...mapState({
      userInfo: state => state.user.userInfo
    }),
    ...mapGetters(['permissions'])
  },
  created() {

    this.saldatamed()

    this.$nextTick(() => {
      this.$refs.jianyan.focus();
    })
    // let d = this.$refs.jianyan;
    if (this.$route.query.tab) {
      this.activeNameMain = this.$route.query.tab
    }
    let newdata = moment(new Date()).format('YYYY-MM')
    this.yaerMode = newdata
    // let newdata = moment(this.yaerMode).format('YYYY-MM-DD')
    console.log(newdata, '9999999999')
    this.queryParam.yearStr = newdata.split('-')[0]
    this.queryParam.monthStr = newdata.split('-')[1]
    // this.queryParam.staDd = moment().subtract(30, "days").format('YYYY-MM-DD')
    // this.queryParam.endDd = moment(new Date()).format('YYYY-MM-DD')

    // this.valueone = [this.queryParam.staDd,this.queryParam.endDd]

    //     this.startTime = this.$moment().subtract(30, "days").format('YYYY-MM-DD');
    // this.endTime = this.$moment(new Date()).format('YYYY-MM-DD');
    // this.getList()
  },
  methods: {
    clearclick() {
      this.queryParam.sbNo = ''
      this.sbNm = ''
      this.datatsbNo = ''
    },
    deviceList(list) {
      this.queryParam.sbNo = list[0].sbNo
      this.sbNm = list[0].name
      this.datatsbNo = this.queryParam.sbNo + ' ' + this.sbNm
      console.log(this.queryParam.sbNo, this.sbNm)
    },
    selectclick() {
      // this.selectrow = scope.$index
      this.$refs.refdevice.open(this.placeId)
    },
    chooseplaceNo(obj) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.placeName = ''
          this.queryParam.placeNo = ''
          this.placeId = ''
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'placeNo') {
        this.flag = true
        this.placeName = obj.obj.data.name
        this.queryParam.placeNo = obj.obj.data.placeNo
        this.placeId = obj.obj.data.id
        // this.entity.typeNo = obj.obj.data.typeNo
        map[obj.obj.name] = obj.obj.data.placeNo
      }
    },
    dropdownMenuEvent() {
      if (this.tableDatatwo2.length === 0) return this.$message.warning('无导出数据！')
      let obj = {
        queryParam: this.queryParam,
        sbNm: this.sbNm,
        yaerMode: this.yaerMode
        // bjStartDd: this.bjStartDd,
        // bjEndDd: this.bjEndDd,
        // total: this.totalCountthree,
      }
      console.log(this.queryParam, 'ghhhhhhhhhh')
      this.$refs.Export.create(obj)
    },
    choosesbNo(obj) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.queryParam.sbNo = ''
          // this.queryParam.sbNm=''
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'sbNo') {
        this.flag = true
        this.queryParam.sbNo = obj.obj.data.sbNo
        this.sbNm = obj.obj.data.name

        if (obj.obj.data.dep) {
          this.queryParam.dep = obj.obj.data.dep
          this.queryParam.depName = obj.obj.data.depName
          this.data = this.queryParam.dep + '-' + this.queryParam.depName
        }
        // this.queryParam.sbNm= obj.obj.data.name
        map[obj.obj.name] = obj.obj.data.sbNo
      }
    },

    objectSpanMethodjiu({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex % 5 === 0) {
          return {
            rowspan: 5,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      if (columnIndex === 1) {
        if (rowIndex % 5 === 0) {
          return {
            rowspan: 5,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    addRow(index, newData) {
      console.log(index, newData, '6666666')

      this.tableDatatwo2.splice(index + 1, 0, newData);
    },
    getList() {
      this.loading = true
      queryEverydayEs(
        Object.assign({
          ...this.queryParam,
        })
      ).then(res => {
        this.loading = false
        if (res.code == 0) {
          console.log(res.data.xmVoList, 'vvbbbhhhhhhhhhhhhhh')
          this.tableDatatwo2 = res.data.xmVoList
          if (this.tableDatatwo2.length) {
            for (let i = 0; i < this.tableDatatwo2.length; i++) {
              this.tableDatatwo2[i].addid = i + 1
            }

            // for (let i = 0; i < this.tableDatatwo2.length; i++) {
            //   this.addRow(i, this.tableDatatwo2[i])
            // }
            // this.tableDatatwo2.forEach((item,index)=>{
            //   console.log(index,'gggggggggggg')
            //   this.addRow(index, item)
            // })
            let arry = {
              xmNm: '',
              xmNr: '',
              ffNm: '',
              xmBaiBanJgVo: [],
              xmWanBanJgVo: [],
              addid: '点检人员'
            }
            arry.xmBaiBanJgVo = res.data.baiBanYgVoList
            arry.xmWanBanJgVo = res.data.wanBanYgVoList
            this.tableDatatwo2.push(arry)
            console.log(this.tableDatatwo2, 'g111111222')
            let arrytwo = {
              xmNm: '',
              xmNr: '',
              ffNm: '',
              xmBaiBanJgVo: [],
              xmWanBanJgVo: [],
              addid: '点检人员'
            }
          }

          // this.tableDatatwo2.push(arrytwo)
          // res.data.baiBanYgVoList
          // const newTableData = this.tableDatatwo2.map(row => ({ ...row }));
          // this.tableDatatwo2 = [...this.tableDatatwo2, ...newTableData];
          // this.tableDatatwo2.forEach(item=>{
          //   this.zcNodetailDatatwo.splice(this.multipleSelectiontwo[num].index+1, 0, ...arrytwo)
          // })

          // const tds = document.querySelectorAll('.el-table__footer-wrapper tr>td');
          // tds[0].colSpan=3;
          // tds[0].style.textAlign='center'
          // tds[1].style.display='none'
          // tds[2].style.display='none'
          // tds[3].style.display='none'
        }

        // this.totalCount = response.data.total
        // this.tablePage.currentPage = response.data.current
      }).catch(err => {

        this.requestFailed(err)
      })
        .finally(() => {

          this.loading = false
        })
    },
    choose(obj) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.queryParam.dep = ''
          this.queryParam.depName = ''
          this.data = ''
          this.$forceUpdate()
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'dep') {
        this.queryParam.depName = obj.obj.data.depName
        this.queryParam.dep = obj.obj.data.dep
        map[obj.obj.name] = obj.obj.data.dep
      }
      // this.form.setFieldsValue(map)
    },
    search() {
      // this.tablePage.currentPage = 1
      if (this.queryParam.yearStr && this.queryParam.sbNo) {
        this.getList()
      } else {
        this.$message.warning('请填写查询条件')
      }
    },
    // 弹出日历和关闭日历的回调
    openChangeOne(status) {
      //status是打开或关闭的状态
      this.yearShowOne = !this.yearShowOne
    },
    // 得到年份选择器的值
    panelChangeOne(value) {
      this.yaerMode = value
      let riqi = moment(this.yaerMode).format('YYYY-MM')
      this.queryParam.yearStr = riqi.split('-')[0]
      this.queryParam.monthStr = riqi.split('-')[1]
      // console.log(moment(this.yaerMode).format('YYYY-MM'),'dw2222222')
      // this.yaerMode
      this.yearShowOne = !this.yearShowOne
    },
    // https://zhuanlan.zhihu.com/p/622566512?utm_id=0
    headerCellStyle3({ row, column, rowIndex, columnIndex }) {
      let colArr = [1, 2];
      if (colArr.includes(columnIndex)) {
        if (rowIndex == 0) {
          // 把第1列第0行和第2列第0行的单元格隐去
          return { display: "none" };
        }
      }
      if ((columnIndex == 0) & (rowIndex == 0)) {
        // 把第0列第0行的单元格横向延伸，补上刚刚隐去的单元格位置，并上个色
        this.$nextTick(() => {
          document.querySelector(`.${column.id}`).setAttribute("colspan", "3");
        });
        return { background: "pink" };
      }
      if (column.label == "性格") {
        return { display: "none" };
      }
      if (column.label == "爱好&性格") {
        this.$nextTick(() => {
          document.querySelector(`.${column.id}`).setAttribute("colspan", "2");
        });
        return { background: "orange" }; // 不加这个也行，加了只是为了更好区分
      }
      if (column.label == "技能") {
        return { background: "#baf" };
      }
      for (const item of row) {
        if (item.label == "重要信息") {
          this.$nextTick(() => {
            document.querySelector(`.${item.id}`).style.background = "#ea66a6";
          });
          break;
        }
      }
    },
    headerCellStyle2({ row, column, rowIndex, columnIndex }) {
      // 把第1列第1行和第2列第1行的单元格隐去
      if ((columnIndex == 1) | (columnIndex == 2)) {
        if (rowIndex == 1) { // 加上rowIndex精准定位
          return { display: "none" };
        }
      }
      // 然后让第0列第1行的单元格横向占据3个单元格位置填充刚刚隐去导致的空白
      if ((columnIndex == 0) & (rowIndex == 1)) { // 加上rowIndex精准定位
        this.$nextTick(() => {
          document.querySelector(`.${column.id}`).setAttribute("colspan", "3");
        });
      }
    },
    headerCellStyle({ row, column, rowIndex, columnIndex }) {
      // 第一步：设置表头的第0列暂不操作，将地1列和第2列隐去使其消失
      if ((columnIndex == 1) | (columnIndex == 2)) {
        return { display: "none" };
      }
      // 第二步， 由于1、2列没有了，后续列就会贴上来（后续列往左错位问题）
      if ((rowIndex == 0) & (columnIndex == 0)) {
        // 解决后续列错位问题，就是将隐去的第1、2列的位置再补上，通过第0列来补
        this.$nextTick(() => {
          // 原来第0列只占据一个位置，现在要去占据三个位置。即占据三列，即设置为横向三个单元格
          document.querySelector(`.${column.id}`).setAttribute("colspan", "3");
          // 这里的column.id实际是dom元素的class，故用点.不用井#，可审查dom验证
          // 通过设置原生的colspan属性，让原来的第一列只占据一个单元格的表头占据3个单元格即可
        });
      }
    },


    summaries(param, val) {
      const {
        columns,
        data
      } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = (() => {
            let el = <p class="count_row">点检人员</p>
            return el;
          })();
          return;
        }
        if (index === 4) {
          sums[index] = (() => {
            let num = <p class="count_row">
              <div style="border-bottom:1px solid #000;">白班</div>
              <div>晚班</div>
            </p>
            // this.orderList[val].deliveryPrice.toFixed(2)
            return num;
          })();
          return;
        }
      });
      return sums;
    },
    // customSummaryMethod({ columns, data }) {
    //   return [
    //     // 第一行数据
    //     columns.map((column, index) => {
    //       console.log(column,'kkkkkkkkk')
    //       if (index === 0) {
    //         return '合计（第一行1）';
    //       }
    //       if (typeof column.property === 'string') {
    //         return this.summariesMethod(column.property, data);
    //       }
    //       return null;
    //     }),
    //     // 第二行数据
    //     columns.map((column, index) => {
    //       if (index === 0) {
    //         return '合计（第二行2）';
    //       }
    //       if (typeof column.property === 'string') {
    //         return this.summariesMethod(column.property, data);
    //       }
    //       return null;
    //     })
    //   ];
    // },
    summariesMethod(attr, data) {
      const sum = data.reduce((total, item) => {
        const value = Number(item[attr]);
        if (!isNaN(value)) {
          return total + value;
        }
        return total;
      }, 0);
      return sum;
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (rowIndex % 2 === 0) {
        if (columnIndex === 0) {
          return [1, 2];
        } else if (columnIndex === 1) {
          return [0, 0];
        }
      }
    },
    getSummaries(param) {
      let { columns, data } = param;
      let sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "点检人员";
          return;
        } else if (index === 2) {
          sums[index] = ''
          return;
        } else if (index === 3) {
          sums[index] = this.totalCount.invitedNumCount;
          return;
        } else if (index === 4) {
          sums[index] = this.totalCount.evaluatedNumCount;
          return;
        } else if (index === 5) {
          sums[index] = this.totalCount.qualifiedNumCount;
          return;
        } else if (index === 6) {
          sums[index] = this.totalCount.unqualifiedNumCount;
          return;
        } else if (index === 7) {
          sums[index] = this.totalCount.qualifiedRateCount;
          return;
        } else if (index === 8) {
          sums[index] = this.totalCount.invitedRateCount;
          return;
        }
        // let values = data.map(item => Number(item[column.property]));
        // if (!values.every(value => isNaN(value))) {
        //   sums[index] = values.reduce((prev, curr) => {
        //     let value = Number(curr);
        //     if (!isNaN(value)) {
        //       return prev + curr;
        //     } else {
        //       return prev;
        //     }
        //   }, 0);
        //   sums[index] += ' 元';
        //   if(column.property == "numbertwo"){
        //     sums[index] = 9999
        //   }
        // } else {
        //   sums[index] = 'N/A';
        // }
      });
      return sums;
    },
    handleSelectionChangereason(rows) {
      this.multipleSelection = rows;
    },
    // 本地删除
    locaDelTableRow() {
      let that = this

      if (this.multipleSelection.length === 0 || this.multipleSelection === []) {
        this.$message.error("请勾选要操作的数据！");
        return false;
      }
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('identifier.delcont'),
        okText: this.$t('public.sure'),
        okType: 'warn',
        cancelText: this.$t('public.cancel'),
        onOk() {
          let ids = []

          that.multipleSelection.forEach((item) => {
            if (item.id) {
              ids.push(item.id)
            }
          })
          if (ids.length) {
            delCtrl(
              ids
            ).then((res) => {
              if (res.code === 0) {
                that.loading = false
                that.$refs.multipleTable.clearSelection();
                if (res.msg === 'success') {
                  that.$message.success(res.data)
                } else {
                  that.$message.error(res.data)
                }
              }
            }).catch(err => {
              that.requestFailed(err)
              that.loading = false
            })
              .finally(() => {
                that.loading = false
              })
          }
          let selectRows = that.multipleSelection
          selectRows.forEach((item) => {
            if (item.id === null || item.id === "" || item.id === 0) {
            } else {
              // that.deldata(item.id)
            }
          })
          selectRows.forEach((item) => {
            that.tableData.splice(that.tableData.indexOf(item), 1);
          });
        },
        onCancel() { }
      })



    },
    // deldata(id){

    // },
    // 表格初始化，往数组里面添加50个对象
    tableAdd() {
      for (let i = 0; i < 1; i++) {
        let obj = {
          salName: '',
          salNo: '',
          ctrl1: false,
          ctrl2: false,
          ctrl3: false,
          ctrl4: false,
          ctrl5: false,
          ctrl6: false,
          id: ''
        };
        this.tableData.push(obj);
      }
    },
    // 添加行
    addTableRow() {
      this.tableAdd();
    },
    save() {
      this.tableData.forEach((item, index) => {

        if (item.ctrl1 == true) {
          item.ctrl1 = 'T'
        } else {
          item.ctrl1 = 'F'
        }
        if (item.ctrl2 == true) {
          item.ctrl2 = 'T'
        } else {
          item.ctrl2 = 'F'
        }
        if (item.ctrl3 == true) {
          item.ctrl3 = 'T'
        } else {
          item.ctrl3 = 'F'
        }
        if (item.ctrl4 == true) {
          item.ctrl4 = 'T'
        } else {
          item.ctrl4 = 'F'
        }
        if (item.ctrl5 == true) {
          item.ctrl5 = 'T'
        } else {
          item.ctrl5 = 'F'
        }
        if (item.ctrl6 == true) {
          item.ctrl6 = 'T'
        } else {
          item.ctrl6 = 'F'
        }
      })

      lsqcupdCtrl(
        this.tableData
      ).then((res) => {
        this.tableData.forEach((i, index) => {
          if (i.ctrl1 == 'T') {
            i.ctrl1 = true
          } else {
            i.ctrl1 = false
          }
          if (i.ctrl2 == 'T') {
            i.ctrl2 = true
          } else {
            i.ctrl2 = false
          }
          if (i.ctrl3 == 'T') {
            i.ctrl3 = true
          } else {
            i.ctrl3 = false
          }
          if (i.ctrl4 == 'T') {
            i.ctrl4 = true
          } else {
            i.ctrl4 = false
          }
          if (i.ctrl5 == 'T') {
            i.ctrl5 = true
          } else {
            i.ctrl5 = false
          }
          if (i.ctrl6 == 'T') {
            i.ctrl6 = true
          } else {
            i.ctrl6 = false
          }
        })
        if (res.msg === 'success') {
          this.$message.success(res.data)
        } else {
          this.$message.error(res.data)
          return
        }
        // if(this.$route.query.id){
        //   this.getListtreat()
        // }else{
        //   this.getList()
        // }
      }).catch(err => {
        this.requestFailed(err)
      }).finally(() => {
        this.loading = false
      })
    },
    inputNameInput(val) {
      this.querypinzhi = val
    },
    onChangepinzhi(e, item) {


      this.querypinzhi = e.target.checked
    },
    // onChangeyijian  onChangejieguo onChangegaijin onChangeyanzheng  onChangejichu
    choosecuoshi(obj, index) {

      this.tableData[index].salNo = obj.obj.data.salNo
      this.tableData[index].salName = obj.obj.data.name
      //  this.entity.salName = obj.obj.data.name

      // this.tableData.forEach((item, index) => {
      //   if(item.ctrl2 == true){
      //     item.ctrl2 == 'T'
      //   }else{
      //     item.ctrl2 == 'F'
      //   }
      // })


      var map = {}
      //   this.data[obj.obj.name] = obj.obj.value
      // this.entity[obj.obj.name] = obj.obj.id
      // if (obj.obj.name === 'upSalNo') {
      //   this.salName = obj.obj.data.name
      //   map[obj.obj.name] = obj.obj.data.salNo
      // }
      // if (obj.obj.name == 'salNo') {
      //     this.data = obj.obj.value
      // this.entity.salNo = obj.obj.data.salNo
      //   // this.salName = obj.obj.data.name
      //   // map[obj.obj.name] = obj.obj.data.salNo
      // }
      if (obj.obj.name === 'salNo') {
        this.entity.salName = obj.obj.data.name

        this.entity.salNoZr1 = obj.obj.data.salNo

      }

      // if (obj.obj.name === 'dep') {
      //   this.flag = true
      //   this.entity.depName = obj.obj.data.name

      //   this.entity.dep = obj.obj.data.deptCode
      // }
      // this.form.setFieldsValue(map)
    },
    saldatamed(row) {
      datasalData(
        {
          current: 1,
          size: '10000',
          salText: '',
        }
      )
        .then(res => {
          this.salNoall = res.data.records
          // this.data=res.data.records[0].salNo + '-' + res.data.records[0].name
          // if(res.msg == 'success'){
          //   this.$message.success('删除成功')
          // }else{
          //   this.$message.success('删除失败')
          // }
          // this.loading = false
          // this.getList()
        })
        .catch(err => {
          this.loading = false
          this.requestFailed(err)
        })
    },
    // headerStyle({row,column,rowIndex,columnIndex}){
    //   row[5].colSpan = 3
    //   row[6].colSpan = 0
    //   row[7].colSpan = 0
    //   if(columnIndex == 6 || columnIndex == 7 ){
    //     return 'display:none'
    //   }
    // },

    headerStyle({ row, column, rowIndex, columnIndex }) {
      row[1].colSpan = 5
      row[2].colSpan = 0
      row[3].colSpan = 0
      row[4].colSpan = 0
      row[5].colSpan = 0
      // row[4].colSpan = 3
      // row[5].colSpan = 0
      // row[6].colSpan = 0
      // if(columnIndex == 2 || columnIndex == 3 ){
      //   return 'display:none'
      // }
    },
    // mergeRows
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      console.log(this.tableData.length, rowIndex, 'bnnnnnnnnnnngggg')

      if (rowIndex === this.tableDatatwo2.length - 1) {
        if (columnIndex == 0) {
          // if(row.addid == ''){
          return [1, 3]; //
          // }
        } else if (columnIndex === 1) {
          return [0, 0]; // 不合并
        } else if (columnIndex === 2) {
          return [0, 0]; // 不合并
        }
        // else{
        //   return {
        //     rowspan: 0,
        //     colspan: 0
        //   }
        // }
      }

      // if (rowIndex === this.tableDatatwo2.length - 1 ) {
      //   // || rowIndex === this.tableDatatwo2.length - 2
      //   if(columnIndex === 0 ){
      //     return [1, 3];
      //   }
      //   // else if(columnIndex === 1){
      //   //   return [0, 0];
      //   // }

      //   // return [0, 2]; // 合并第一个和第二个单元格
      // }
      // if (rowIndex === this.tableDatatwo2.length - 2) {
      //   if(columnIndex === 0){
      //     return [1, 3];
      //   }
      // else if(columnIndex === 1){
      //   return [0, 0];
      // }

      // return [0, 2]; // 合并第一个和第二个单元格
      // }
      //   let _row = 0;
      // let _col = 0;
      // 前三列
      // if (columnIndex == 0 || columnIndex == 1 || columnIndex == 2) {
      //   if (rowIndex % 2 == 0) {
      //     _row = 2; // 偶数行 两行
      //     _col = 1;
      //   } else {
      //     _row = 0; // 奇数行 0行
      //     _col = 1;
      //   }
      //   return {
      //     rowspan: _row,
      //     colspan: _col,
      //   };
      // }
      // if (columnIndex == 0) {
      // 第四列
      // if (columnIndex == 7) {
      //   // if (rowIndex % 2 == 0) {
      //     // 偶数行时
      //     _row = 1;
      //     _col = 6; // 跨三列
      //     return {
      //       rowspan: _row,
      //       colspan: _col,
      //     };
      //   // }
      // }
      // 第五、六列
      // if (columnIndex == 4 || columnIndex == 5) {
      //   if (rowIndex % 2 == 0) {
      //     // 偶数行时
      //     _row = 1;
      //     _col = 0; // 跨0列
      //     return {
      //       rowspan: _row,
      //       colspan: _col,
      //     };
      //   }
      // }



      // if (columnIndex === 0) {
      //     if (rowIndex % 2 === 0) {
      //       return {
      //         rowspan: 2,
      //         colspan: 1
      //       };
      //     } else {
      //       return {
      //         rowspan: 0,
      //         colspan: 0
      //       };
      //     }
      //   }

      //row:对象形式，保存了当前行的数据
      //column：对象形式，保存了当前列的参数
      //rowIndex:当前行的行号
      //column：当前列的列号

      // if (rowIndex === 1 || rowIndex === 6) {
      //     return {
      //       rowspan: 2,  //rowspan:单元格可横跨的行数
      //       colspan: 2,  //colspan:单元格可横跨的列数
      //     }

      // }
      //     if(columnIndex === 1||columnIndex === 2||columnIndex === 3||columnIndex === 4||columnIndex === 5||columnIndex === 6){
      //       return {
      //         rowspan: 1,  //rowspan:单元格可横跨的行数
      //         colspan: 6,  //colspan:单元格可横跨的列数
      //       }
      // }else{
      //         return [0, 0];
      //     }

      //  if (rowIndex === 2 || rowIndex === 3) {
      //       let len = Object.keys(row).length
      //       if(columnIndex === 0){
      //         return [1, 1];
      //       }else if(columnIndex === 1){
      //         return [1, 6];
      //       }else{
      //           return [0, 0];
      //       }
      //   }
      // if(columnIndex === 2){
      //       return [2, 10];
      //     }else{
      //         return [0, 0];
      //     }

      // const _row = this.spanArr[rowIndex]
      // const _col = _row > 0 ? 1 : 0
      // return {
      //   rowspan: _row,  //rowspan:单元格可横跨的行数
      //   colspan: _col,  //colspan:单元格可横跨的列数
      // }
      // } else if (columnIndex === 1) {
      // const _row = this.contentSpanArr[rowIndex]
      // const _col = _row > 0 ? 1 : 0 //等于0表示不合并
      // return {
      //   rowspan: _row,
      //   colspan: _col,
      // }
      // }
    },
    objectSpanMethod2({ row, column, rowIndex, columnIndex }) {
      console.log(this.tableData.length, rowIndex, 'bnnnnnnnnnnngggg')
      if (rowIndex === this.tableData.length - 1) {
        if (columnIndex === 0) {
          return [1, 2];
        } else if (columnIndex === 1) {
          return [0, 0];
        }

        // return [0, 2]; // 合并第一个和第二个单元格
      }
      let _row = 0;
      let _col = 0;
      // 前三列
      // if (columnIndex == 0 || columnIndex == 1 || columnIndex == 2) {
      //   if (rowIndex % 2 == 0) {
      //     _row = 2; // 偶数行 两行
      //     _col = 1;
      //   } else {
      //     _row = 0; // 奇数行 0行
      //     _col = 1;
      //   }
      //   return {
      //     rowspan: _row,
      //     colspan: _col,
      //   };
      // }
      // if (columnIndex == 0) {
      // 第四列
      // if (columnIndex == 7) {
      //   // if (rowIndex % 2 == 0) {
      //     // 偶数行时
      //     _row = 1;
      //     _col = 6; // 跨三列
      //     return {
      //       rowspan: _row,
      //       colspan: _col,
      //     };
      //   // }
      // }
      // 第五、六列
      // if (columnIndex == 4 || columnIndex == 5) {
      //   if (rowIndex % 2 == 0) {
      //     // 偶数行时
      //     _row = 1;
      //     _col = 0; // 跨0列
      //     return {
      //       rowspan: _row,
      //       colspan: _col,
      //     };
      //   }
      // }



      // if (columnIndex === 0) {
      //     if (rowIndex % 2 === 0) {
      //       return {
      //         rowspan: 2,
      //         colspan: 1
      //       };
      //     } else {
      //       return {
      //         rowspan: 0,
      //         colspan: 0
      //       };
      //     }
      //   }

      //row:对象形式，保存了当前行的数据
      //column：对象形式，保存了当前列的参数
      //rowIndex:当前行的行号
      //column：当前列的列号

      // if (rowIndex === 1 || rowIndex === 6) {
      //     return {
      //       rowspan: 2,  //rowspan:单元格可横跨的行数
      //       colspan: 2,  //colspan:单元格可横跨的列数
      //     }

      // }
      //     if(columnIndex === 1||columnIndex === 2||columnIndex === 3||columnIndex === 4||columnIndex === 5||columnIndex === 6){
      //       return {
      //         rowspan: 1,  //rowspan:单元格可横跨的行数
      //         colspan: 6,  //colspan:单元格可横跨的列数
      //       }
      // }else{
      //         return [0, 0];
      //     }

      //  if (rowIndex === 2 || rowIndex === 3) {
      //       let len = Object.keys(row).length
      //       if(columnIndex === 0){
      //         return [1, 1];
      //       }else if(columnIndex === 1){
      //         return [1, 6];
      //       }else{
      //           return [0, 0];
      //       }
      //   }
      // if(columnIndex === 2){
      //       return [2, 10];
      //     }else{
      //         return [0, 0];
      //     }

      // const _row = this.spanArr[rowIndex]
      // const _col = _row > 0 ? 1 : 0
      // return {
      //   rowspan: _row,  //rowspan:单元格可横跨的行数
      //   colspan: _col,  //colspan:单元格可横跨的列数
      // }
      // } else if (columnIndex === 1) {
      // const _row = this.contentSpanArr[rowIndex]
      // const _col = _row > 0 ? 1 : 0 //等于0表示不合并
      // return {
      //   rowspan: _row,
      //   colspan: _col,
      // }
      // }
    },
    queryonetwo() {
      if (this.activeNameMain == 'first') {
        this.getList()
      } else {
        this.getListtwo()
      }
    },
    raidchange(row) {
      // 获取选中数据 row表示选中这一行的数据，可以从里面提取所需要的值
      this.multipleSelection = row


    },
    pageSizeChange(pageSize) {
      this.tablePage.pageSize = pageSize;
      this.getList();
    },
    pageSizeChangetwo(pageSize) {
      this.tablePagetwo.pageSize = pageSize;
      this.getListtwo();
    },
    currentChange(currentPage) {

      // this.visible = true;
      this.tablePage.currentPage = currentPage;
      this.getList();
    },
    currentChangetwo(currentPage) {
      // this.visible = true;
      this.tablePagetwo.currentPage = currentPage;
      this.getListtwo();
    },
    // 表格双击事件
    mfBxHanddle(row, column, event) {
      this.edit = row;
      this.editId = row.id;
      this.$router.push({
        path: '/mes/nonconform/detail',
        query: {
          tyNo: row.lsNo
        }
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // mfBxHanddleone(row, column, event) {
    //   if(this.multipleSelection){
    //     localStorage.setItem('checkdata',JSON.stringify(row))
    //     this.$router.push({
    //       path: '/mes/nonconform/detail',
    //       query: {
    //         id: row.tyNo
    //       }
    //     });
    //   }
    // },
    handleClick(val) {


      if (val.name == 'first') {
        this.getList()
      } else {
        this.getListtwo()
      }
      this.$nextTick(() => {
        this.$refs.jianyan.focus();
      })
    },
    // onChange (data, dateString) {
    //
    //   this.queryParam.date = dateString

    // },
    onChange(data, dateString) {
      this.queryParam.date = dateString
      this.queryParam.staDd = this.queryParam.date[0]
      this.queryParam.endDd = this.queryParam.date[1]

    },
    // 按钮初始化
    btnInit() {
      this.btn.newOpen = true // 首检开工 null||""
      this.btn.pause = true // 暂停 2
      this.btn.continues = true // 继续 3
      this.btn.inspection = true // 首检 1
    },
    // getListtwo() {
    //   this.loading = true
    //  lsqcyps(
    //     Object.assign({
    //         total: this.tablePagetwo.currentPage,
    //       // current: this.tablePage.currentPage,
    //       size: this.tablePagetwo.pageSize,
    //       staDd:this.queryParam.staDd,
    //       endDd:this.queryParam.endDd,
    //       tyNo:this.queryParam.tyNo,
    //       zcNo:this.queryParam.zcNo,
    //       moNo:this.queryParam.moNo,
    //       prdNo:this.queryParam.prdNo,
    //       prdName:this.queryParam.prdName,
    //       // bilNo:this.queryParam.bilNo,
    //       // pgNo: this.queryParam.pgNo
    //     })
    //   ).then(response => {
    //     this.loading = false
    //     this.tableDatatwo = response.data.records
    //     this.totalCount = response.data.total
    //     // this.tablePage.currentPage = response.data.current
    //   }).catch(e => {
    //     this.loading = false
    //   })
    // },
    // 获取列表数据

    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    ClickEvent({ row }) {
      this.btnInit()
      this.rowIndex = row
      getItem(
        Object.assign({
          pgNo: row.pgNo,
          itm: row.itm,
          tiNo: row.tiNo,
          prdNo: row.prdNo
        })
      ).then(response => {
        this.row = response.data
        this.btnState(this.row)
      }).catch(err => this.requestFailed(err))
    },
    // 按钮状态
    btnState(row) {
      // 首检 ：3 暂停：2  继续：1
      if (row.state === '-1') {
        this.btn.newOpen = false
      }
      if (row.state === '0' || row.state === '3' || row.state === '1') {
        this.btn.pause = false // 暂停
        this.btn.inspection = false // 首检
      }
      if (row.state === '2') {
        this.btn.continues = false // 继续 3
      }
    },
    getBtnValue(state) {
      // 获取对应按钮提示
      let value = ''
      switch (state) {
        case '0':
          value = this.$t('quality.newOpen')
          break
        case '3':
          value = this.$t('quality.inspection')
          break
        case '2':
          value = this.$t('quality.pause')
          break
        case '1':
          value = this.$t('quality.continues')
          break
        case '4':
          value = this.$t('quality.task')
          break
      }
      return value
    },
    // 按钮
    onSubmit(state) {
      switch (state) {
        case '0':
        case '1':
        case '2':
          this.loading = true
          let btnMes = this.getBtnValue(state)
          start({
            state: state,
            pgNo: this.row.pgNo,
            itm: this.row.itm,
            id: this.row.id
          }).then((res) => {

            if (res.code === 0) {
              if (res.msg === 'fail') {
                btnMes = res.data
              } else {
                btnMes = btnMes + this.$t('public.success')
                const row = this.row
                this.ClickEvent({ row })
              }
            }
            this.$notification.success({
              message: this.$t('public.tip'),
              description: btnMes
            })
            this.loading = false
          }).catch(err => this.requestFailed(err))

          break
        case '3':
          const row = this.rowIndex
          this.$refs.a.create(row)
          break
      }
    },
    reset() {
      this.queryParam = {}
      // this.queryParam.staDd = moment().subtract(30, "days").format('YYYY-MM-DD')
      // this.queryParam.endDd = moment(new Date()).format('YYYY-MM-DD')
      // this.valueone = [this.queryParam.staDd,this.queryParam.endDd]
    }
  }
}
</script>

<style>
.el-table .el-table__cell {
  padding: 6px 0;
}

.ant-input-wrapper .ant-input-group-addon .ant-btn {
  height: 28px;
}

.ant-input-affix-wrapper.ant-input-affix-wrapper-input-with-clear-btn .ant-input:not(:last-child) {
  padding-right: 0
}

/* .ant-tabs-nav-container{font-size:16px}
::v-deep .el-radio__label{
display:none
} */
.dianjian .cell {
  padding-left: 0px;
  padding-right: 0px;
}

/* .twocell td{padding: 6px 0;} */
.multi-row-cell {
  display: flex;
  flex-direction: column;
  line-height: 1.5;
  /* 行高 */
  height: 3em;
  /* 单元格高度 */
}

.multi-row-cell div {
  flex: 1;
}
</style>