<template>
<div class="consrm">
<div  style="text-align:right;margin-right:16px;position: absolute;top: 90px;right:0px;z-index:999">       
  <a-button
      style="margin-left: 8px"
      type="primary"
      @click="newlyadd"
      size="small"
    >新增</a-button>
    <!--
    <a-button
      style="margin-left: 8px"
      type="primary"
      @click="check"
      size="small"
    >检验</a-button> -->
  </div>
<el-tabs type="border-card"  v-model="activeNameMain" @tab-click="handleClick">
  <div class="table-page-search-wrapper">
            <a-form layout="inline">
              <a-row :gutter="48">
                <a-col
                  :md="6"
                  :sm="24"
                >
                  <a-form-item label="开始日期">
                    <a-date-picker
                        style="width:100%"
                        format="YYYY-MM-DD"
                        valueFormat="YYYY-MM-DD"
                        v-model="queryParam.staDd"
                        placeholder="请输入开始日期"
                      />
                      <!-- <a-range-picker v-model="valueone" @change="onChange" format="YYYY-MM-DD"/> -->
                  </a-form-item>
                </a-col>
                <a-col
                  :md="6"
                  :sm="24"
                >
                  <a-form-item label="结束日期">
                    <a-date-picker
                        style="width:100%"
                        format="YYYY-MM-DD"
                        valueFormat="YYYY-MM-DD"
                        v-model="queryParam.endDd"
                        placeholder="请输入结束日期"
                      />
                  </a-form-item>
                </a-col>
                
                <a-col
                  :md="6"
                  :sm="24"
                   v-if="activeNameMain == 'second'"
                >
                  <a-form-item :label="$t('设备')">
                    <a-input
                      v-model="queryParam.tyNo"
                      :placeholder="$t('设备')"
                      ref="submissiontwo"
                      @keyup.enter.native="queryonetwo"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="6"
                  :sm="24"
                  v-if="activeNameMain == 'second'"
                >
                  <a-form-item :label="$t('制单人')">
                    <a-input
                      v-model="queryParam.usr"
                      :placeholder="$t('制单人')"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="6"
                  :sm="24"
                >
                  <a-form-item :label="$t('送检单号')">
                    <a-input
                      v-model="queryParam.tiNo"
                      :placeholder="$t('送检单号')"
                      ref="submission"
                      @keyup.enter.native="queryonetwo"
                    />
                  </a-form-item>
                </a-col>
                
                <a-col
                  :md="6"
                  :sm="24"
                >
                  <a-form-item :label="$t('制程')">
                    <a-input
                      v-model="queryParam.zcNo"
                      :placeholder="$t('制程')"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="6"
                  :sm="24"
                >
                  <a-form-item :label="$t('制令单号')">
                    <a-input
                      v-model="queryParam.moNo"
                      :placeholder="$t('制令单号')"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="6"
                  :sm="24"
                  v-if="activeNameMain == 'second'"
                >
                  <a-form-item :label="$t('通知单号')">
                    <a-input
                      v-model="queryParam.tzNo"
                      :placeholder="$t('通知单号')"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="6"
                  :sm="24"
                  v-else
                >
                  <a-form-item :label="$t('通知单号')">
                    <a-input
                      v-model="queryParam.bilNo"
                      :placeholder="$t('通知单号')"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :md="6"
                  :sm="24"
                >
                  <a-form-item :label="$t('品号')">
                    <a-input
                      v-model="queryParam.prdNo"
                      :placeholder="$t('品号')"
                    />
                  </a-form-item>
                </a-col>

                <a-col
                  :md="6"
                  :sm="24"
                >
                  <a-form-item :label="$t('品名')">
                    <a-input
                      v-model="queryParam.prdName"
                      :placeholder="$t('品名')"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="2">
                    <a-form-item label="复检:" prop="chkKnd">
                      <a-checkbox
                        v-model="queryParam.chkKnd"
                        @change="onChangeclosecase"
                      ></a-checkbox>
                    </a-form-item>
                  </a-col>
                  <a-col :span="3">
                    <a-form-item label="实验室检验:" prop="sys">
                      <a-checkbox
                        v-model="queryParam.sys"
                      ></a-checkbox>
                    </a-form-item>
                  </a-col>
                <!-- <a-col
                  :md="6"
                  :sm="24"
                >
                  <a-form-item>
                    <div class="salesNo">
                      <a-input
                        v-model="queryParam.pgNo"
                        :placeholder="$t('work.pgNolist')"
                      ></a-input>
                    </div>
                  </a-form-item>
                </a-col> -->
              </a-row>
              
              <a-row :gutter="48">
                <a-col
                  :md="6"
                  :sm="24"
                >
                  <span class="table-page-search-submitButtons">
                    <a-button
                      style="margin-left: 8px"
                      type="primary"
                      size="small"
                      @click="queryonetwo"
                    >{{ $t('public.query') }}</a-button>
                    <a-button
                      size="small"
                       style="margin-left: 8px;    color: #909399;background: #f4f4f5;border-color: #d3d4d6;"
                      @click="reset"
                    >{{ $t('public.reset') }}</a-button>
                  </span>
                </a-col>
              </a-row>
            </a-form>
          </div>

      <el-tab-pane label="设备信息" name="first" >
  <!-- <a-card :bordered="false"> -->
    <!-- <a-row :gutter="20">
      <a-form-item label="检验日期">
      <a-col :span="6" :md="6" :sm="24">
        
          <a-range-picker @change="onChange" format="YYYY-MM-DD"/>
      </a-col>
      </a-form-item>
    </a-row> -->

    <a-row :gutter="8">
      <a-col :span="24">
        <a-row>
          <!-- <a-tabs v-model="activeName" type="card" @change="handleTabClick" style="font-size:16px"> -->
            <!-- 
              size="mini"
                max-height="620"
              height="64vh"
       :cell-style="{ verticalAlign: 'top' }"
      -->
      <!-- font-size: 14px;
    color: #606266; -->
            <!-- <a-tab-pane key="1" tab="已检验"> -->
              <el-table
                stripe
                :data="tableData"
                highlight-current-row
                :cell-style="{ verticalAlign: 'top' }"
                :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                style="width: 100%;"
                @selection-change="handleSelectionChange"
               height="580px"
                @row-dblclick="mfBxHanddle"
              >
                <!-- :header-cell-style="{fontSize:'14px',color: '#606266', verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '700' }" -->
               <!-- @sort-change="sortChange" -->
                <!-- <el-table-column type="selection" align="center" width="50"></el-table-column> -->
                <!-- <el-table-column
                  width="60px"
                  label="选择"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-radio
                      v-model="radio"
                      :label="scope.$index"
                      class="radio"
                      @change.native="raidchange(scope.row)"
                    >&nbsp;</el-radio>
                  </template>
                </el-table-column> -->
                <el-table-column prop="tiDd" width="110" align="left" label="设备代号"></el-table-column>
                <el-table-column prop="tiNo" width="140" align="left" label="设备名称"></el-table-column>
                <el-table-column prop="prdNo" width="120" align="left" label="单位"></el-table-column>
                <el-table-column prop="prdName" width="120" align="left" label="设备型号"></el-table-column>
                <el-table-column prop="qty" width="100" align="left" label="固定资料代号"></el-table-column>
                <el-table-column prop="zcName" width="110" align="left" label="部门">
                  <template slot-scope="scope">
                    {{scope.row.zcNo}}/{{scope.row.zcName}}
                  </template>
                </el-table-column>
                
                <el-table-column prop="moNo" width="130" align="left" label="设备厂商"></el-table-column>
                <el-table-column prop="qtyRtn" width="110" align="left" label="供应商"></el-table-column>
                <el-table-column prop="rem" width="110" align="left" label="设备位置"></el-table-column>
                <el-table-column prop="applyDepName" width="170" align="left" label="入厂时间"></el-table-column>
                <el-table-column prop="faultRem" width="200" align="left" label="备注"></el-table-column>
                <!-- <el-table-column prop="bxDd" label="申请日期" align="left" width="140">
                  <template slot-scope="scope">{{ scope.row.bxDd | formatDate}}</template>
                </el-table-column>
                
                <el-table-column prop="cntName" width="80" align="left" label="联系人"></el-table-column>
                <el-table-column prop="cntTel" width="110" align="left" label="联系人电话"></el-table-column>
                <el-table-column prop="cntAdr" width="400" align="left" label="联系人地址"></el-table-column> -->
              </el-table>
              
              <div style="display: flex;justify-content: space-between;margin: 2px">
                <el-pagination
                  background
                  :page-sizes="[5, 10,20, 30, 50]"
                  :page-size="20"
                  :pager-count="5"
                  @size-change="pageSizeChange"
                  :current-page="tablePage.currentPage"
                  @current-change="currentChange"
                  layout="total,sizes,prev, pager, next"
                  :total="totalCount"
                ></el-pagination>
              </div>

            <!-- </a-tab-pane> -->
            <!-- <a-tab-pane key="2" tab="已检验">
              
            </a-tab-pane> -->
          <!-- </a-tabs> -->
          <!-- <vxe-table
            border
            resizable
            stripe
            highlight-current-row
            show-overflow
            highlight-hover-row
            export-config
            ref="xTable"
            :loading="loading"
            :data="tableData"
            @cell-click="ClickEvent"
            :keyboard-config="{ isArrow: true }"
            :edit-config="{ trigger: 'click', mode: 'row' }"
          >
            <vxe-table-column
              field="tiNo"
              fixed="left"
              title="quality.tiNo"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="prdNo"
              title="quality.prdNo"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="prdtName"
              title="quality.prdtName"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="pgNo"
              title="quality.pgNo"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="zcName"
              title="quality.zcName"
              align="center"
            ></vxe-table-column>
            <vxe-table-column
              field="prdMark"
              title="quality.prdMark"
              align="center"
            ></vxe-table-column>
          </vxe-table>
          <vxe-pager
            :loading="loading"
            :current-page="tablePage.currentPage"
            :page-size="tablePage.pageSize"
            :total="tablePage.total"
            :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
            @page-change="handlePageChange"
          >
          </vxe-pager> -->
        </a-row>
        <a-row>
          <!-- <a-col :span="24">
            <a-button
              :disabled="btn.newOpen"
              style="margin-left: 8px"
              type="primary"
              @click="onSubmit('0')"
              v-permission="mes_quality_newOpen"
            >{{ $t('quality.newOpen') }}</a-button>
            <a-button
              :disabled="btn.inspection"
              style="margin-left: 8px"
              type="primary"
              v-permission="mes_quality_inspection"
              @click="onSubmit('3')"
            >{{ $t('quality.inspection') }}</a-button>
            <a-button
              :disabled="btn.pause"
              style="margin-left: 8px"
              type="primary"
              v-permission="mes_quality_pause"
              @click="onSubmit('2')"
            >{{ $t('quality.pause') }}</a-button>
            <a-button
              :disabled="btn.continues"
              style="margin-left: 8px"
              type="primary"
              v-permission="mes_quality_continues"
              @click="onSubmit('1')"
            >{{ $t('quality.continues') }}</a-button>
            <a-button
              style="margin-left: 8px"
              type="primary"
              v-permission="mes_quality_task"
            >{{ $t('work.task') }}</a-button>
           
          </a-col> -->
          <!-- <a-col>
            <formList :row="row"></formList>
          </a-col> -->

        </a-row>
      </a-col>
      <!-- <inspection
        :row="row"
        ref="a"
        @getList="getList"
      /> -->
    </a-row>
  <!-- </a-card> -->
    </el-tab-pane>
    <!-- <el-tab-pane label="已检验" name="second">
      <el-table
                :data="tableDatatwo"
            
                 stripe
                highlight-current-row
                :cell-style="{ verticalAlign: 'top' }"
                :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                style="width: 100%;"
                @row-dblclick="mfBxHanddle"
                height="580px"
              >
                 <el-table-column prop="tyDd" width="110" align="left" label="检验日期">
                  <template slot-scope="scope">
                      {{scope.row.tyDd | formatDate}}
                  </template>
                 </el-table-column>
                <el-table-column prop="tyNo" width="130" align="left" label="检验单号"></el-table-column>
                 <el-table-column prop="prdNo" width="110" align="left" label="品号"></el-table-column>
                <el-table-column prop="prdName" width="110" align="left" label="品名"></el-table-column>
                <el-table-column prop="qty" width="100" align="left" label="检验数量"></el-table-column>
                <el-table-column prop="qtyOk" width="100" align="left" label="合格量"></el-table-column>
                <el-table-column prop="qtyLost" width="100" align="left" label="不合格量"></el-table-column>
                <el-table-column prop="zcName" width="120" align="left" label="制程">
                  <template slot-scope="scope">
                    {{scope.row.zcNo}}/{{scope.row.zcName}}
                  </template>
                </el-table-column>
                <el-table-column prop="bilNo" width="140" align="left" label="送检单号"></el-table-column>
                <el-table-column prop="tzNo" width="120" align="left" label="通知单号"></el-table-column>
                <el-table-column prop="moNo" width="130" align="left" label="制令单号"></el-table-column>
                <el-table-column prop="usr" width="130" align="left" label="制单人"></el-table-column>
               <el-table-column prop="rem" width="120" align="left" label="备注"></el-table-column>
              </el-table>
              
              <div style="display: flex;justify-content: space-between;margin: 2px">
                <el-pagination
                  background
                  :page-sizes="[5, 10,20, 30, 50]"
                  :page-size="20"
                  @size-change="pageSizeChangetwo"
                  :current-page="tablePagetwo.currentPage"
                  @current-change="currentChangetwo"
                  layout="total,sizes,prev, pager, next"
                  :total="totalCount"
                ></el-pagination>
              </div>
    </el-tab-pane> -->
</el-tabs>
</div>
</template>
<script>
import {
  fetchList, getItem, start,completinspected,completverified
} from '@/api/mes/quality'
import { mapGetters, mapState } from 'vuex'
import moment from 'moment'
import { first } from 'xe-utils/methods'
// import formList from './formList'
// import inspection from './inspection'

export default {
  // components: {
  //   formList, inspection
  // },
  data () {
    return {
      mes_quality_query: 'mes_quality_query',
      mes_quality_reset: 'mes_quality_reset',
      mes_quality_newOpen: 'mes_quality_newOpen',
      mes_quality_inspection: 'mes_quality_inspection',
      mes_quality_pause: 'mes_quality_inspection',
      mes_quality_continues: 'mes_quality_continues',
      mes_quality_task: 'mes_quality_task',
      queryParam: {},
      tableData: [],
      tableDatatwo: [],
      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      tablePagetwo: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      row: {},
      rowIndex: {},
      // 按钮控制
      btn: {
        newOpen: true, // 首检开工
        open: true, // 开始
        pause: true, // 暂停
        continues: true, // 继续
        report: true, // 报工
        exceptional: true, // 异常
        inspection: true, // 送检
        introspecting: true // 自检
      },
      queryParam: {
        tyNo:'',
        tiNo:'',
        zcNo:'',
        moNo:'',
        prdName:'',
        prdNo:'',
        staDd:'',
        endDd:'',
         chkKnd:false,
        sys:false,
      },
      activeName: '1',
      valueone: [],
      multipleSelection: [],
      activeNameMain: "first",
      radioSelection:null,
      radio: '',
       totalCount: -1,
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    }),
    ...mapGetters(['permissions'])
  },
  created(){
    this.$nextTick(() => {
      this.$refs.submission.focus()
    })
    if(this.$route.query.tab){
     this.activeNameMain =this.$route.query.tab
    }
    this.queryParam.staDd = moment().subtract(7, "days").format('YYYY-MM-DD')
    this.queryParam.endDd = moment(new Date()).format('YYYY-MM-DD')
   
    this.valueone = [this.queryParam.staDd,this.queryParam.endDd]

    //     this.startTime = this.$moment().subtract(30, "days").format('YYYY-MM-DD');
    // this.endTime = this.$moment(new Date()).format('YYYY-MM-DD');
    if(this.$route.query.tab){
      if(this.$route.query.tab == 'second'){
       this.getListtwo()
      }else{
        this.getList()
      }
    }
    this.getList()
  },
  methods: {
    newlyadd(){
      this.$router.push({name:'devicedetail', params:{
          tyNo: -1
        }})
    },
    queryonetwo(){
      if(this.activeNameMain == 'first'){
         this.tablePage.currentPage = 1
        this.getList()
      }else{
        this.tablePagetwo.currentPage = 1
        this.getListtwo()
      }
    },
     raidchange(row){
    // 获取选中数据 row表示选中这一行的数据，可以从里面提取所需要的值
      this.multipleSelection = row


    },
     pageSizeChange(pageSize) {
      this.tablePage.pageSize = pageSize;
      this.getList();
    },
    pageSizeChangetwo(pageSize) {
      this.tablePagetwo.pageSize = pageSize;
      this.getListtwo();
    },
    currentChange(currentPage) {

      // this.visible = true;
      this.tablePage.currentPage = currentPage;
      this.getList();
    },
    currentChangetwo(currentPage) {
      // this.visible = true;
      this.tablePagetwo.currentPage = currentPage;
      this.getListtwo();
    },
    // 表格双击事件
    mfBxHanddle(row, column, event) {
      this.edit = row;
      this.editId = row.id;
      // this.$router.push({
      //   path: '/mes/completiontwo/detail',
      //   query: {
      //     tyNo: row.tyNo
      //   }
      // });
        this.$router.push({name:'completiontwodetail', params:{
          tyNo: row.tyNo
        }})
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    mfBxHanddleone(row, column, event) {
      if(this.multipleSelection){
        localStorage.setItem('checkdata',JSON.stringify(row))
        // this.$router.push({
        //   path: '/mes/completiontwo/detail',
        //   query: {
        //     id: row.tiNo
        //   }
        // });
        this.$router.push({name:'completiontwodetail', params:{
          id: row.tiNo
        }})
      }
    },
    handleClick(val) {
      

      if (val.name == 'first') {
       this.getList()
       this.$nextTick(() => {
        this.$refs.submission.focus()
      })
      }else{
        this.getListtwo()
        this.$nextTick(() => {
        this.$refs.submissiontwo.focus()
      })
      }
      
    },
    // onChange (data, dateString) {
    //
    //   this.queryParam.date = dateString

    // },
    onChange (data, dateString) {
      this.queryParam.date = dateString
      this.queryParam.staDd = this.queryParam.date[0]
      this.queryParam.endDd = this.queryParam.date[1]
   
    },
    // 按钮初始化
    btnInit () {
      this.btn.newOpen = true // 首检开工 null||""
      this.btn.pause = true // 暂停 2
      this.btn.continues = true // 继续 3
      this.btn.inspection = true // 首检 1
    },
    getListtwo() {
      this.loading = true
     completverified(
        Object.assign({
            current: this.tablePagetwo.currentPage,
          // current: this.tablePage.currentPage,
          size: this.tablePagetwo.pageSize,
          staDd:this.queryParam.staDd,
          endDd:this.queryParam.endDd,
          tyNo:this.queryParam.tyNo,
          zcNo:this.queryParam.zcNo,
          moNo:this.queryParam.moNo,
          prdNo:this.queryParam.prdNo,
          prdName:this.queryParam.prdName,
          tiNo:this.queryParam.tiNo,
          sys: this.queryParam.sys ? 'T' : 'F',
          chkKnd: this.queryParam.chkKnd ? 'T' : 'F',
          tzNo:this.queryParam.tzNo,
          usr:this.queryParam.usr
          // bilNo:this.queryParam.bilNo,
          // pgNo: this.queryParam.pgNo
        })
      ).then(response => {
        this.loading = false
        this.tableDatatwo = response.data.records
        this.totalCount = response.data.total
        // this.tablePage.currentPage = response.data.current
      }).catch(err => this.requestFailed(err))
      .finally(() => {
        this.loading = false
      })
    },
    // 获取列表数据
    getList () {
      this.loading = true
     completinspected(
        Object.assign({
            current: this.tablePage.currentPage,
          // current: this.tablePage.currentPage,
          size: this.tablePage.pageSize,
            staDd:this.queryParam.staDd,
          endDd:this.queryParam.endDd,
          tiNo:this.queryParam.tiNo,
          zcNo:this.queryParam.zcNo,
          moNo:this.queryParam.moNo,
          prdNo:this.queryParam.prdNo,
          prdName:this.queryParam.prdName,
          sys: this.queryParam.sys ? 'T' : 'F',
          chkKnd: this.queryParam.chkKnd ? 'T' : 'F',
          bilNo:this.queryParam.bilNo,
          // pgNo: this.queryParam.pgNo
        })
      ).then(response => {
        this.loading = false
        this.tableData = response.data.records
        this.tableData.forEach((i,index) => {
          i.addid = index + 1
        })
        this.totalCount = response.data.total
        // this.tablePage.currentPage = response.data.current
      }).catch(err => this.requestFailed(err))
      .finally(() => {
        this.loading = false
      })
    },
    // 分页
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    ClickEvent ({ row }) {
      this.btnInit()
      this.rowIndex = row
      getItem(
        Object.assign({
          pgNo: row.pgNo,
          itm: row.itm,
          tiNo: row.tiNo,
          prdNo: row.prdNo
        })
      ).then(response => {
        this.row = response.data
        this.btnState(this.row)
      }).catch(err => this.requestFailed(err))
    },
    // 按钮状态
    btnState (row) {
      // 首检 ：3 暂停：2  继续：1
      if (row.state === '-1') {
        this.btn.newOpen = false
      }
      if (row.state === '0' || row.state === '3' || row.state === '1') {
        this.btn.pause = false // 暂停
        this.btn.inspection = false // 首检
      }
      if (row.state === '2') {
        this.btn.continues = false // 继续 3
      }
    },
    getBtnValue (state) {
      // 获取对应按钮提示
      let value = ''
      switch (state) {
        case '0':
          value = this.$t('quality.newOpen')
          break
        case '3':
          value = this.$t('quality.inspection')
          break
        case '2':
          value = this.$t('quality.pause')
          break
        case '1':
          value = this.$t('quality.continues')
          break
        case '4':
          value = this.$t('quality.task')
          break
      }
      return value
    },
    // 按钮
    onSubmit (state) {
      switch (state) {
        case '0':
        case '1':
        case '2':
          this.loading = true
          let btnMes = this.getBtnValue(state)
          start({
            state: state,
            pgNo: this.row.pgNo,
            itm: this.row.itm,
            id: this.row.id
          }).then((res) => {
      
            if (res.code === 0) {
              if (res.msg === 'fail') {
                btnMes = res.data
              } else {
                btnMes = btnMes + this.$t('public.success')
                const row = this.row
                this.ClickEvent({ row })
              }
            }
            this.$notification.success({
              message: this.$t('public.tip'),
              description: btnMes
            })
            this.loading = false
          }).catch(err => this.requestFailed(err))

          break
        case '3':
          const row = this.rowIndex
          this.$refs.a.create(row)
          break
      }
    },
    reset () {
      this.queryParam= {
        tyNo:'',
        tiNo:'',
        zcNo:'',
        moNo:'',
        prdName:'',
        prdNo:'',
        staDd:'',
        endDd:'',
      },
      this.queryParam.staDd = moment().subtract(7, "days").format('YYYY-MM-DD')
      this.queryParam.endDd = moment(new Date()).format('YYYY-MM-DD')
     
    }
  }
}
</script>

<style>
.el-table .el-table__cell{
  padding: 6px 0;
}
.ant-input{
  height:28px;
}
/* .ant-tabs-nav-container{font-size:16px}
::v-deep .el-radio__label{
  display:none
} */
</style>
