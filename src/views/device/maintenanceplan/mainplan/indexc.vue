<template>
  <div class="consrm riqi" style="position:relative">
    <div style="position: absolute;top: 6px;left:125px;z-index:1">

      <a-button icon="plus" style="margin-left: 8px" type="primary" @click="newlyadd" size="small">新增</a-button>
      <a-button icon="search" style="margin-left: 8px" type="primary" size="small" @click="search">{{ $t('public.query')
        }}</a-button>
      <a-button size="small" icon="reload"
                style="margin-left: 8px;    color: #909399;background: #f4f4f5;border-color: #d3d4d6;" @click="reset">{{
          $t('public.reset') }}</a-button>
      <a-button icon="save" style="margin-left: 8px" type="primary" @click="savesure" size="small">保存</a-button>
      <a-button style="margin-left: 8px" type="primary" @click="quickset" size="small">快速设定</a-button>
      <a-button style="margin-left: 8px" type="primary" @click="deldata" size="small"
                :disabled="queryParam.byState == 2 || queryParam.byState == 3"
                :style="queryParam.byState == 2 || queryParam.byState == 3 ? 'background-color: #909399;border-color: #909399;color:#fff;' : 'background-color: #f56c6c;border-color: #f56c6c;color:#fff;'">删除</a-button>
      <a-button icon="download" style="margin-left: 8px" type="primary" @click="dropdownMenuEvent"
                size="small">导出</a-button>

    </div>

    <a-modal title="新增" destroyOnClose width="40%" :visible.sync="newvisible" @cancel="handleCancelnew"
             @ok="handleSubmitnew">
      <div>
        <span style="color: #f00;font-size:12px;margin-left:100px;font-size:10px;">保养周期和计划保养开工完工日二选一</span>
        <el-form label-width="110px" :model="entity" :rules="exitRules" ref="addEntityForm">
          <el-row :gutter="0" style="margin-bottom:10px;">
            <el-col :span="24">
              <el-form-item label="年份:" prop="typeNo">

              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="设备型号:" prop="typeNo">
                <my-selectListwotype url="ems/sbtype/queryPage" :read-only="true" :tableColumn="$Column.devicetypeNo"
                                     :form="$Form.devicesalmDep" :datatype="datatypeNo" name="typeNo" @choosetype="choosetypeNo($event)"
                                     allowClear ref="selectListtypeNo"
                                     v-decorator="['typeNo', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                                     :placeholder="$t('salm.placeholder.depName')"></my-selectListwotype>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="设备代号:" class="sbNo">
                <my-selectListwo url="ems/sbtz/queryPage" :read-only="true" :tableColumn="$Column.devicesbNo"
                                 :form="$Form.devicesalmDep" :data="datatsbNo" name="sbNo" @choose="choosesbNo($event)" allowClear
                                 ref="selectListsbNo"
                                 v-decorator="['sbNo', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                                 :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
              </el-form-item>

            </el-col>
            <el-col :span="24">
              <el-form-item label="保养等级:" prop="bydjNo">
                <my-selectListwo url="ems/bydj/queryPage" :read-only="true" :tableColumn="$Column.devicebydjNo"
                                 :form="$Form.devicesalmDep" :data="databydjNo" name="bydjNo" @choose="choosebydjNo($event)" allowClear
                                 ref="selectList"
                                 v-decorator="['bydjNo', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                                 :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$t('部门')" prop="dep">
                <my-selectListwo url="ems/dept/getDeptPage" :read-only="true" :tableColumn="$Column.devicesalmDep"
                                 :form="$Form.devicesalmDep" :data="data" name="dep" allowClear @choose="choosetwo($event)"
                                 ref="selectList"
                                 v-decorator="['dep', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                                 :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$t('保养周期')" prop="dep">
                <el-select v-model="entity.byzq" placeholder="请选择保养周期" size="mini" style="width:100%;" clearable
                           @change='selectChange'>
                  <el-option v-for="item in planzhouqi" :key="item.value" :label="item.label"
                             :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$t('计划保养开工日')" prop="dep">
                <a-date-picker style="width:100%" @change='selectChangekai' format="YYYY-MM-DD HH:mm:ss"
                               valueFormat="YYYY-MM-DD HH:mm:ss" v-model="entity.startDd" placeholder="请输入计划保养开工日" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$t('计划保养完工日')" prop="dep">
                <a-date-picker style="width:100%" @change='selectChangewan' format="YYYY-MM-DD HH:mm:ss"
                               valueFormat="YYYY-MM-DD HH:mm:ss" v-model="entity.endDd" placeholder="请输入计划保养完工日" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- <el-table
                stripe
                :data="tableDatanew"
                highlight-current-row
                :cell-style="{ verticalAlign: 'top' }"
                :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                style="width: 100%;"
                @selection-change="handleSelectionChangenew"
               height="580px"
                @row-dblclick="mfBxHanddle"
              >
              <el-table-column type="selection" width="48"></el-table-column>
                  <el-table-column prop="year" width="110" align="left" label="年"></el-table-column>
                  <el-table-column prop="sbNo" width="180" align="left" label="设备代号">
                    <template slot-scope="scope">
                      <my-selectListwo
                      url="ems/sbtz/queryPage"
                      :read-only="true"
                      :tableColumn="$Column.devicesbNo"
                      :form="$Form.devicesalmDep"
                      :data="scope.row.sbNo + ' ' + scope.row.sbNm"
                      name="sbNo"
                      @choose="choosesbNotable($event)"
                      allowClear
                      ref="selectListsbNo"
                      v-decorator="['sbNo', { rules: [{ required: true, message:'请选择设备代号' } ] }]"
                      placeholder="请选择设备代号"
                      ></my-selectListwo>
                    </template>
                  </el-table-column>
                <el-table-column prop="typeNm" width="180" align="left" label="设备型号">
                  <template slot-scope="scope">
                    <my-selectListwotype
                    url="ems/sbtype/queryPage"
                    :read-only="true"
                    :tableColumn="$Column.devicetypeNo"
                    :form="$Form.devicesalmDep"
                    :datatype="scope.row.typeNo + ' ' + scope.row.typeNm"
                    name="typeNo"
                    allowClear
                    @choosetype="choosetypeNotable($event)"
                    ref="selectListtypeNo"
                    v-decorator="['typeNo', { rules: [{ required: true, message:'请选择设备型号' } ] }]"
                    placeholder="请选择设备型号"
                  ></my-selectListwotype>
                  </template>
                </el-table-column>
                <el-table-column prop="sbNm" width="120" align="left" label="设备名称"></el-table-column>
                <el-table-column prop="bydjNm" width="180" align="left" label="保养等级">
                  <template slot-scope="scope">
                    <my-selectListwo
                      url="ems/bydj/queryPage"
                      :read-only="true"
                      :tableColumn="$Column.devicebydjNo"
                      :form="$Form.devicesalmDep"
                      :data="scope.row.bydjNo + ' ' + scope.row.bydjNm"
                      name="bydjNo"
                      @choose="choosebydjNotable($event)"
                      allowClear
                      ref="selectList"
                      v-decorator="['bydjNo', { rules: [{ required: true, message:'请选择保养等级' } ] }]"
                      placeholder="请选择保养等级"
                    ></my-selectListwo>
                  </template>
                </el-table-column>
                <el-table-column prop="bybwNm" width="180" align="left" label="保养部位">
                  <template slot-scope="scope">
                    <my-selectListwo
                        url="ems/bybw/queryPage"
                        :read-only="true"
                        :tableColumn="$Column.devicebybwNo"
                        :form="$Form.devicesalmDep"
                        :data="scope.row.bybwNo + ' ' + scope.row.bybwNm"
                        name="bybwNo"
                        @choose="choosebybwNo($event,scope)"
                        allowClear
                        ref="selectList"
                        v-decorator="['bybwNo', { rules: [{ required: true, message:'请输入保养部位' } ] }]"
                        placeholder="请输入保养部位"
                    ></my-selectListwo>
                  </template>
                </el-table-column>
                <el-table-column prop="byzq" width="120" align="left" label="保养周期">
                  <template slot-scope="scope">
                    <div v-if="scope.row.byzq == 1">月</div>
                    <div v-if="scope.row.byzq == 2">季</div>
                    <div v-if="scope.row.byzq == 3">半年</div>
                    <div v-if="scope.row.byzq == 4">年</div>
                    <div v-if="scope.row.byzq == 5">天数</div>
                  </template>
                </el-table-column>
                <el-table-column prop="startDd" width="120" align="left" label="计划保养开工日">
                  <template slot-scope="scope">
                    {{scope.row.startDd | formatDate}}
                  </template>
                </el-table-column>
                <el-table-column prop="endDd" width="120" align="left" label="计划保养完工日">
                  <template slot-scope="scope">
                    {{scope.row.endDd | formatDate}}
                  </template>
                </el-table-column>
                <el-table-column prop="byState" width="120" align="left" label="保养状态">
                  <template slot-scope="scope">
                    <div v-if="scope.row.byState == 1">待保养</div>
                    <div v-if="scope.row.byState == 2">保养中</div>
                    <div v-if="scope.row.byState == 3">已保养</div>
                  </template>
                </el-table-column>
                <el-table-column prop="depName" width="120" align="left" label="部门"></el-table-column>
                <el-table-column prop="byDd" width="100" align="left" label="保养完成时间">
                  <template slot-scope="scope">
                    {{scope.row.byDd | formatDate}}
                  </template>
                </el-table-column>
                <el-table-column prop="byNo" width="110" align="left" label="保养单号"></el-table-column>
                <el-table-column prop="rem" width="200" align="left" label="备注"></el-table-column>
              </el-table>
          </div> -->
      <!-- <a-row :gutter="16">
            <a-button type="primary"  @click="newsure">确定</a-button>
            <a-button @click="newvisible = !newvisible">取消</a-button>
        </a-row> -->
    </a-modal>
    <a-modal title="快速设定" destroyOnClose width="40%" :visible.sync="visiblequickset" @ok="hadeOk"
             @cancel="handleCancel">
      <div style="text-align: right;">
        <div>
          <el-form label-width="90px" :model="entity" :rules="exitRules" ref="addEntityForm">
            <el-row :gutter="0" style="margin-bottom:10px;">
              <el-col :span="24">
                <el-form-item label="年份:" prop="year">
                  <a-date-picker mode="year" placeholder="请选择年份" format="YYYY" valueFormat="YYYY" v-model="entity.year"
                                 style="width:100%" :allowClear="false" :open="yearShowOnetwo" @openChange="openChangeOnetwo"
                                 @panelChange="panelChangeOnetwo" />

                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="设备型号:" prop="typeNo">
                  <my-selectListwotype url="ems/sbtype/queryPage" :read-only="true" :tableColumn="$Column.devicetypeNo"
                                       :form="$Form.devicesalmDep" :datatype="datatypeNo" name="typeNo" @choosetype="choosetypeNo($event)"
                                       allowClear ref="selectListtypeNo"
                                       v-decorator="['typeNo', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                                       :placeholder="$t('salm.placeholder.depName')"></my-selectListwotype>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="设备代号:" prop="sbNo">
                  <my-selectListwo url="ems/sbtz/queryPage" :read-only="true" :tableColumn="$Column.devicesbNo"
                                   :form="$Form.devicesalmDep" :data="datatsbNo" name="sbNo" @choose="choosesbNo($event)"
                                   ref="selectListsbNo"
                                   v-decorator="['sbNo', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                                   :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
                </el-form-item>

              </el-col>
              <el-col :span="24">
                <el-form-item label="保养等级:" prop="bydjNo">
                  <my-selectListwo url="ems/bydj/queryPage" :read-only="true" :tableColumn="$Column.devicebydjNo"
                                   :form="$Form.devicesalmDep" :data="databydjNo" name="bydjNo" @choose="choosebydjNo($event)"
                                   allowClear ref="selectList"
                                   v-decorator="['bydjNo', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                                   :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t('部门')" prop="dep">
                  <my-selectListwo url="ems/dept/getDeptPage" :read-only="true" :tableColumn="$Column.devicesalmDep"
                                   :form="$Form.devicesalmDep" :data="data" name="dep" allowClear @choose="choosetwo($event)"
                                   ref="selectList"
                                   v-decorator="['dep', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                                   :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

      </div>
    </a-modal>

    <el-tabs type="border-card" v-model="activeNameMain">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('年份')">


                <a-date-picker mode="year" placeholder="请选择年份" format="YYYY" valueFormat="YYYY"
                               v-model="queryParam.year" style="width:100%" allowClear :open="yearShowOne"
                               @openChange="openChangeOne" @panelChange="panelChangeOne" />

              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('设备代号')">
                <a-input v-model="queryParam.sbNo" :placeholder="$t('设备代号')" ref="submissiontwo" />

              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('设备型号')">
                <a-input v-model="queryParam.typeNo" :placeholder="$t('设备型号')" ref="submissiontwo" />

              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item :label="$t('部门')" prop="dep">
                <my-selectListwo url="ems/dept/getDeptPage" :read-only="true" :tableColumn="$Column.devicesalmDep"
                                 :form="$Form.devicesalmDep" :data="datatwo" name="dep" allowClear @choose="choose($event)"
                                 ref="selectListquery"
                                 v-decorator="['dep', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                                 :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item :label="$t('保养状态')">
                <el-select v-model="queryParam.byState" placeholder="请选择保养状态" size="mini" style="width:100%;">
                  <el-option v-for="item in mainstatus" :key="item.value" :label="item.label"
                             :value="item.value"></el-option>
                </el-select>
              </a-form-item>
            </a-col>
            <a-col :md="5" :sm="24">
              <span class="table-page-search-submitButtons" style="margin-top:4px;">

              </span>
            </a-col>

          </a-row>


        </a-form>
      </div>
      <el-tab-pane label="保养计划" name="first">



        <a-row :gutter="8">
          <a-col :span="24">
            <a-row>
              <el-button size="mini" icon="el-icon-plus" id="add_table" type="primary"
                         @click="addTableRowtwo()"></el-button>
              <el-button size="mini" icon="el-icon-minus" id="add_table" type="primary"
                         :disabled="queryParam.byState == 2 || queryParam.byState == 3"
                         :style="queryParam.byState == 2 || queryParam.byState == 3 ? 'background-color: #909399;border-color: #909399;color:#fff;' : 'background-color: #f56c6c;border-color: #f56c6c;color:#fff;'"
                         @click="locaDelTableRowtwo()"></el-button>
              <vxe-toolbar custom>
                <template v-slot:buttons>
                </template>
              </vxe-toolbar>
              <vxe-table size='small' border="inner" stripe highlight-current-row show-overflow highlight-hover-row
                         export-config :loading="loading" :data="tableData" :keyboard-config="{ isArrow: true }"
                         :edit-config="{ trigger: 'click', mode: 'row' }"
                         :header-cell-style="{ backgroundColor: '#F4F5F9', color: '#7A8085', fontWeight: '400', fontSize: '12px' }"
                         :cell-style="{ fontSize: '12px' }" height="580px" ref="tableone" style="width:100%">

                <vxe-table-column type="checkbox" align="center" :width="50" fixed="left"></vxe-table-column>
                <vxe-table-column field="year" width="110" align="left" title="年">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">{{scope.row.year}}</div>
                    <div v-else>
                      <a-date-picker mode="year" placeholder="请选择年份" format="YYYY" valueFormat="YYYY"
                                     v-model="scope.row.year" style="width:100%" :open="yearShowOnethree"
                                     @openChange="openChangeOnethree" @panelChange="panelChangeOnetable($event,scope)" />
                    </div>
                  </template>
                </vxe-table-column>
                <vxe-table-column field="sbNo" width="210" align="left" title="设备代号">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">{{scope.row.sbNo}}</div>
                    <div v-else>
                      <my-selectListwo url="ems/sbtz/queryPage" :read-only="true" :tableColumn="$Column.devicesbNo"
                                       :form="$Form.devicesalmDep" :data="scope.row.sbNo + ' ' + scope.row.sbNm" name="sbNo"
                                       @choose="choosesbNotable($event,scope)" allowClear ref="selectListsbNo"
                                       v-decorator="['sbNo', { rules: [{ required: true, message:'请选择设备代号' } ] }]"
                                       placeholder="请选择设备代号"></my-selectListwo>
                    </div>
                  </template>
                </vxe-table-column>
                <vxe-table-column field="typeNm" width="210" align="left" title="设备型号">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">{{scope.row.typeNo}} / {{scope.row.typeNm}}</div>
                    <div v-else>
                      <my-selectListwotype url="ems/sbtype/queryPage" :read-only="true"
                                           :tableColumn="$Column.devicetypeNo" :form="$Form.devicesalmDep"
                                           :datatype="scope.row.typeNo + ' ' + scope.row.typeNm" name="typeNo" allowClear
                                           @choosetype="choosetypeNotable($event,scope)" ref="selectListtypeNo"
                                           v-decorator="['typeNo', { rules: [{ required: true, message:'请选择设备型号' } ] }]"
                                           placeholder="请选择设备型号"></my-selectListwotype>
                    </div>
                  </template>
                </vxe-table-column>
                <vxe-table-column field="sbNm" width="120" align="left" title="设备名称"></vxe-table-column>
                <vxe-table-column field="bydjNm" width="210" align="left" title="保养等级">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">{{scope.row.bydjNm}}</div>
                    <div v-else>
                      <my-selectListwo url="ems/bydj/queryPage" :read-only="true" :tableColumn="$Column.devicebydjNo"
                                       :form="$Form.devicesalmDep" :data="scope.row.bydjNo + ' ' + scope.row.bydjNm" name="bydjNo"
                                       @choose="choosebydjNotable($event,scope)" allowClear ref="selectList"
                                       v-decorator="['bydjNo', { rules: [{ required: true, message:'请选择保养等级' } ] }]"
                                       placeholder="请选择保养等级"></my-selectListwo>
                    </div>
                  </template>
                </vxe-table-column>

                <vxe-table-column field="byzq" width="180" align="left" title="保养周期">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">
                      <div
                        v-if="scope.row.byzq == 1001 ||scope.row.byzq == 1002 || scope.row.byzq == 1003 || scope.row.byzq == 1004 || scope.row.byzq == '' || scope.row.byzq == null">
                        <div v-if="scope.row.byzq == 1001">月</div>
                        <div v-if="scope.row.byzq == 1002">季</div>
                        <div v-if="scope.row.byzq == 1003">半年</div>
                        <div v-if="scope.row.byzq == 1004">年</div>
                      </div>
                      <span v-else>{{scope.row.byzq}}天</span>
                    </div>
                    <div v-else>
                      <el-select v-model="scope.row.byzq" placeholder="请选择保养周期" size="mini"
                                 style="width:45%;display: inline-block;" clearable @change='selectChange'>
                        <el-option v-for="item in planzhouqi" :key="item.value" :label="item.label"
                                   :value="item.value"></el-option>
                      </el-select>
                      <a-input-number v-if="scope.row.byzq == 5" v-model="scope.row.daystian" size="mini"
                                      placeholder="请输入天数" :min="1" :max="365" @wheel.native.prevent="stopScroll($event)"
                                      style="width:50%;display: inline-block;"></a-input-number>
                    </div>

                  </template>
                </vxe-table-column>
                <vxe-table-column field="startDd" width="120" align="left" title="计划保养开工日">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">{{scope.row.startDd | formatDate}}</div>
                    <div v-else>
                      <a-date-picker style="width:100%" @change='selectChangekai' format="YYYY-MM-DD HH:mm:ss"
                                     valueFormat="YYYY-MM-DD HH:mm:ss" v-model="scope.row.startDd" placeholder="请输入计划保养开工日" />
                    </div>
                  </template>
                </vxe-table-column>
                <vxe-table-column field="endDd" width="120" align="left" title="计划保养完工日">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">{{scope.row.endDd | formatDate}}</div>
                    <div v-else>
                      <a-date-picker style="width:100%" @change='selectChangewan' format="YYYY-MM-DD HH:mm:ss"
                                     valueFormat="YYYY-MM-DD HH:mm:ss" v-model="scope.row.endDd" placeholder="请输入计划保养完工日" />
                    </div>
                  </template>
                </vxe-table-column>
                <vxe-table-column field="byState" width="120" align="left" title="保养状态">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">
                      <div v-if="scope.row.byState == 1">待保养</div>
                      <div v-if="scope.row.byState == 2">保养中</div>
                      <div v-if="scope.row.byState == 3">已保养</div>
                    </div>
                    <div v-else>
                      <el-select v-model="scope.row.byState" placeholder="请选择保养状态" size="mini"
                                 style="max-width:200px; width:100%;">
                        <el-option v-for="item in mainstatus" :key="item.value" :label="item.label"
                                   :value="item.value"></el-option>
                      </el-select>
                    </div>

                  </template>
                </vxe-table-column>
                <vxe-table-column field="depName" width="180" align="left" title="部门">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">
                      <div v-if="scope.row.dep">
                        {{scope.row.dep}}/{{scope.row.depName}}
                      </div>
                    </div>
                    <div v-else>
                      <my-selectListwo url="ems/dept/getDeptPage" :read-only="true" :tableColumn="$Column.devicesalmDep"
                                       :form="$Form.devicesalmDep" :data="scope.row.dep + ' ' + scope.row.depName" name="dep"
                                       allowClear @choose="choosetable($event,scope)" ref="selectList"
                                       v-decorator="['dep', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                                       :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
                    </div>
                  </template>
                </vxe-table-column>
                <vxe-table-column field="byDd" width="100" align="left" title="保养完成时间">
                  <template slot-scope="scope">
                    {{scope.row.byDd | formatDate}}
                  </template>
                </vxe-table-column>
                <vxe-table-column field="byNo" width="140" align="left" title="保养单号"></vxe-table-column>
                <vxe-table-column field="rem" width="200" align="left" title="备注">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">{{scope.row.rem}}</div>
                    <div v-else>
                      <el-input v-model="scope.row.rem" size="mini" placeholder="请输入备注"
                                style="max-width: 200px;width:100%;"></el-input>
                    </div>
                  </template>
                </vxe-table-column>

              </vxe-table>
              <vxe-pager :loading="loading" :current-page="tablePage.currentPage" :page-size="tablePage.pageSize"
                         :total="totalCount" :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
                         @page-change="handlePageChange"></vxe-pager>
              <!-- <el-table
                stripe
                :data="tableData"
                highlight-current-row
                :cell-style="{ verticalAlign: 'top' }"
                :header-cell-style="{ verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '400' }"
                style="width: 100%;"
                @selection-change="handleSelectionChangenew"
               height="580px"
              >
                <el-table-column type="selection" width="48"></el-table-column>
                  <el-table-column prop="year" width="110" align="left" label="年">
                    <template slot-scope="scope">
                    <div v-if="scope.row.id">{{scope.row.year}}</div>
                    <div v-else>
                      <a-date-picker
                        mode="year"
                        placeholder="请选择年份"
                        format="YYYY"
                        v-model="scope.row.year"
                        style="width:100%"
                        :open="yearShowOnethree"
                        @openChange="openChangeOnethree"
                        @panelChange="panelChangeOnetable($event,scope)"
                      />
                    </div>
                  </template>
                  </el-table-column>
                  <el-table-column prop="sbNo" width="180" align="left" label="设备代号">
                    <template slot-scope="scope">
                      <div v-if="scope.row.id">{{scope.row.sbNo}}</div>
                      <div v-else>
                      <my-selectListwo
                      url="ems/sbtz/queryPage"
                      :read-only="true"
                      :tableColumn="$Column.devicesbNo"
                      :form="$Form.devicesalmDep"
                      :data="scope.row.sbNo + ' ' + scope.row.sbNm"
                      name="sbNo"
                      @choose="choosesbNotable($event,scope)"
                      allowClear
                      ref="selectListsbNo"
                      v-decorator="['sbNo', { rules: [{ required: true, message:'请选择设备代号' } ] }]"
                      placeholder="请选择设备代号"
                      ></my-selectListwo>
                    </div>
                    </template>
                  </el-table-column>
                <el-table-column prop="typeNm" width="180" align="left" label="设备型号">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">{{scope.row.typeNm}}</div>
                    <div v-else>
                    <my-selectListwotype
                    url="ems/sbtype/queryPage"
                    :read-only="true"
                    :tableColumn="$Column.devicetypeNo"
                    :form="$Form.devicesalmDep"
                    :datatype="scope.row.typeNo + ' ' + scope.row.typeNm"
                    name="typeNo"
                    allowClear
                    @choosetype="choosetypeNotable($event,scope)"
                    ref="selectListtypeNo"
                    v-decorator="['typeNo', { rules: [{ required: true, message:'请选择设备型号' } ] }]"
                    placeholder="请选择设备型号"
                  ></my-selectListwotype>
                  </div>
                  </template>
                </el-table-column>
                <el-table-column prop="sbNm" width="120" align="left" label="设备名称"></el-table-column>
                <el-table-column prop="bydjNm" width="180" align="left" label="保养等级">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">{{scope.row.bydjNm}}</div>
                    <div v-else>
                    <my-selectListwo
                      url="ems/bydj/queryPage"
                      :read-only="true"
                      :tableColumn="$Column.devicebydjNo"
                      :form="$Form.devicesalmDep"
                      :data="scope.row.bydjNo + ' ' + scope.row.bydjNm"
                      name="bydjNo"
                      @choose="choosebydjNotable($event,scope)"
                      allowClear
                      ref="selectList"
                      v-decorator="['bydjNo', { rules: [{ required: true, message:'请选择保养等级' } ] }]"
                      placeholder="请选择保养等级"
                    ></my-selectListwo>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="byzq" width="120" align="left" label="保养周期">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">
                      <div v-if="scope.row.byzq == 1001 ||scope.row.byzq == 1002 || scope.row.byzq == 1003 || scope.row.byzq == 1004 || scope.row.byzq == '' || scope.row.byzq == null" >
                        <div v-if="scope.row.byzq == 1001">月</div>
                        <div v-if="scope.row.byzq == 1002">季</div>
                        <div v-if="scope.row.byzq == 1003">半年</div>
                        <div v-if="scope.row.byzq == 1004">年</div>
                      </div>
                      <span v-else>{{scope.row.byzq}}天</span>
                    </div>
                  <div v-else>
                      <el-select
                        v-model="scope.row.byzq"
                        placeholder="请选择保养周期"
                        size="mini"
                        style="width:100%;display: inline-block;"
                        clearable
                        @change='selectChange'
                      >
                        <el-option
                          v-for="item in planzhouqi"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                        <a-input-number
                        v-if="scope.row.byzq == 5"
                        v-model="scope.row.daystian"
                        size="mini"
                        placeholder="请输入天数"
                        :min="1"
                        :max="365"
                        @wheel.native.prevent="stopScroll($event)"
                        style="width:100%;display: inline-block;"
                        ></a-input-number>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="startDd" width="120" align="left" label="计划保养开工日">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">{{scope.row.startDd | formatDate}}</div>
                    <div v-else>
                      <a-date-picker
                        style="width:100%"
                        @change='selectChangekai'
                        format="YYYY-MM-DD HH:mm:ss"
                        valueFormat="YYYY-MM-DD HH:mm:ss"
                        v-model="scope.row.startDd"
                        placeholder="请输入计划保养开工日"
                      />
                      </div>
                  </template>
                </el-table-column>
                <el-table-column prop="endDd" width="120" align="left" label="计划保养完工日">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">{{scope.row.endDd | formatDate}}</div>
                    <div v-else>
                      <a-date-picker
                      style="width:100%"
                      @change='selectChangewan'
                      format="YYYY-MM-DD HH:mm:ss"
                      valueFormat="YYYY-MM-DD HH:mm:ss"
                      v-model="scope.row.endDd"
                      placeholder="请输入计划保养完工日"
                     />
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="byState" width="120" align="left" label="保养状态">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">
                      <div v-if="scope.row.byState == 1">待保养</div>
                    <div v-if="scope.row.byState == 2">保养中</div>
                    <div v-if="scope.row.byState == 3">已保养</div>
                    </div>
                    <div v-else>
                      <el-select
                        v-model="scope.row.byState"
                        placeholder="请选择保养状态"
                        size="mini"
                        style="max-width:200px; width:100%;"
                      >
                        <el-option
                          v-for="item in mainstatus"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </div>

                  </template>
                </el-table-column>
                <el-table-column prop="depName" width="180" align="left" label="部门">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">{{scope.row.bydjNm}}</div>
                    <div v-else>
                  <my-selectListwo
                        url="ems/dept/getDeptPage"
                        :read-only="true"
                        :tableColumn="$Column.devicesalmDep"
                        :form="$Form.devicesalmDep"
                        :data="scope.row.dep + ' ' + scope.row.depName"
                        name="dep"
                        allowClear
                        @choose="choosetable($event,scope)"
                        ref="selectList"
                        v-decorator="['dep', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                        :placeholder="$t('salm.placeholder.depName')"
                      ></my-selectListwo>
                      </div>
                    </template>
                </el-table-column>
                <el-table-column prop="byDd" width="100" align="left" label="保养完成时间">
                  <template slot-scope="scope">
                    {{scope.row.byDd | formatDate}}
                  </template>
                </el-table-column>
                <el-table-column prop="byNo" width="110" align="left" label="保养单号"></el-table-column>
                <el-table-column prop="rem" width="200" align="left" label="备注">
                  <template slot-scope="scope">
                    <div v-if="scope.row.id">{{scope.row.rem}}</div>
                    <div v-else>
                  <el-input
                     v-model="scope.row.rem"
                     size="mini"
                     placeholder="请输入备注"
                     style="max-width: 200px;width:100%;"
                   ></el-input>
                   </div>
                   </template>
                </el-table-column>
              </el-table>
              <div style="display: flex;justify-content: space-between;margin: 2px">
                <el-pagination
                  background
                  :page-sizes="[5, 10,20, 30, 50]"
                  :page-size="20"
                  :pager-count="5"
                  @size-change="pageSizeChange"
                  :current-page="tablePage.currentPage"
                  @current-change="currentChange"
                  layout="total,sizes,prev, pager, next"
                  :total="totalCount"
                ></el-pagination>
              </div> -->
            </a-row>
          </a-col>
        </a-row>
      </el-tab-pane>
    </el-tabs>
    <Export ref="Export" />
  </div>
</template>
<script>
import { byjhqueryPage, createByjhFast, byjhsaveByHand, byjhdel } from '@/api/device/category'
import { mapGetters, mapState } from 'vuex'
import moment from 'moment'
import MySelectListwo from '@/components/MySelectListwo'
import { first } from 'xe-utils/methods'
import MySelectListwotype from '@/components/MySelectListwotype'
import Export from './export'
export default {
  components: {
    MySelectListwo,
    MySelectListwotype,
    Export
  },
  data() {
    return {
      exitRules: {
        year: [{ required: true, message: "必填:年份", trigger: ["change", "blur"] }],
      },
      mes_quality_query: 'mes_quality_query',
      mes_quality_reset: 'mes_quality_reset',
      mes_quality_newOpen: 'mes_quality_newOpen',
      mes_quality_inspection: 'mes_quality_inspection',
      mes_quality_pause: 'mes_quality_inspection',
      mes_quality_continues: 'mes_quality_continues',
      mes_quality_task: 'mes_quality_task',
      tableData: [],
      tableDatatwo: [],
      loading: false,
      tablePage: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      tablePagetwo: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      row: {},
      rowIndex: {},

      btn: {
        newOpen: true, // 首检开工
        open: true, // 开始
        pause: true, // 暂停
        continues: true, // 继续
        report: true, // 报工
        exceptional: true, // 异常
        inspection: true, // 送检
        introspecting: true // 自检
      },
      queryParam: {
        year: '',
        typeNo: '',
        sbNo: '',
        byState: '',
        dep: '',
        depName: ''
      },
      activeName: '1',
      valueone: [],
      multipleSelection: [],
      activeNameMain: "first",
      radioSelection: null,
      radio: '',
      totalCount: -1,
      checkYear: '',
      yearShowOnetwo: false,
      yearShowOnethree: false,
      //年度计划的打开关闭状态，true为打开，false为关闭
      yearShowOne: false,
      //添加对话框的表单绑定
      // yaerMode:moment(new Date()).format('YYYY')    //开始年限,
      yaerMode: '',
      yaerModetwo: '',
      mainstatus: [
        { value: '1', label: '待保养' },
        { value: '2', label: '保养中' },
        { value: '3', label: '已保养' },
      ],
      visiblequickset: false,
      entity: {
        year: '',
        typeNo: '',
        sbNo: '',
        bydjNm: '',
        bydjNo: '',
      },
      datatsbNo: '',
      datatypeNo: '',
      newvisible: false,
      tableDatanew: [],
      planzhouqi: [
        { value: '1001', label: '月' },
        { value: '1002', label: '季' },
        { value: '1003', label: '半年' },
        { value: '1004', label: '年' },
        { value: '5', label: '天数' },
      ],
      multipleSelectionnew: [],
      datatwo: '',
      data: '',
      multipleSelectionnewid: [],
      databydjNo: ''
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    }),
    ...mapGetters(['permissions'])
  },
  created() {
    this.getList()
  },
  methods: {
    dropdownMenuEvent() {
      if (this.tableData.length === 0) return this.$message.warning('请先查询再导出！')
      let obj = {
        queryParam: this.queryParam,
        total: this.totalCount
      }
      this.$refs.Export.create(obj)
    },
    savesure() {
      if (this.tableData.length) {
        let tableDatafilter
        tableDatafilter = JSON.parse(JSON.stringify(this.tableData)).filter(i => i.id == '')
        // this.multipleSelectionnew=this.multipleSelectionnew.filter(i=>i.byNo==''|| i.byNo==null)
        if (tableDatafilter.length) {
          if (tableDatafilter[0].sbNo || tableDatafilter[0].typeNo) {
          } else {
            this.$message.warning('请选择设备代号或者设备型号其中一个')
            return
          }
          if (tableDatafilter[0].byzq) {
            if (tableDatafilter[0].byzq == 5) {
              if (tableDatafilter[0].daystian) {
              } else {
                this.$message.warning('请选择天数')
                return
              }
            }
          } else {
            this.$message.warning('请选择保养周期')
            return
          }
          if (tableDatafilter[0].startDd || tableDatafilter[0].endDd) {
          } else {
            this.$message.warning('计划保养开工日和完工日必填')
            return
          }
          if (tableDatafilter[0].year) {
          } else {
            this.$message.warning('请选择年')
            return
          }
          if (tableDatafilter[0].daystian) {
            tableDatafilter[0].byzq = tableDatafilter[0].daystian
          }
          byjhsaveByHand(
            tableDatafilter[0]
          ).then(res => {
            if (res.code == 0) {
              if (res.msg == 'success') {
                this.getList()
                this.$message.success('保存成功')
              }
            }
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
        } else {
          this.$message.warning('请勾选要操作的数据!')
        }
      } else {
        this.$message.warning('请勾选要操作的数据!')
      }
    },
    stopScroll(evt) {
      evt = evt || window.event;
      if (evt.preventDefault) {
        // Firefox
        evt.preventDefault();
        evt.stopPropagation();
      } else {
        // IE
        evt.cancelBubble = true;
        evt.returnValue = false;
      }
      return false;
    },
    deldata() {
      this.multipleSelectionnew = this.$refs.tableone.getCheckboxRecords()
      if (this.multipleSelectionnew.length) {
        let filterdata = this.multipleSelectionnew.filter(i => i.byState == 2 || i.byState == 3)
        if (filterdata.length) {
          this.$message.warning('存在已保养或保养中的数据不能删除!')
          this.$refs.tableone.clearCheckboxRow()
          return
        }
      } else {
        this.$message.error("请至少选择一条数据！")
        return false;
      }
      if (this.multipleSelectionnew.length) {
        this.multipleSelectionnew = this.multipleSelectionnew.filter(i => i.byNo == '' || i.byNo == null)
        if (this.multipleSelectionnew.length) {
        } else {
          this.$message.error("有保养单号的不能删除");
          return false;
        }
      } else {
        this.$message.error("请至少选择一条数据！")
        return false;
      }

      if (this.multipleSelectionnew.length) {
        let arr = []
        this.multipleSelectionnew.forEach(i => {
          let arry = { id: i.id }
          arr.push(arry)
        })

        const that = this
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('identifier.delcont'),
          okText: this.$t('public.sure'),
          okType: 'warn',
          cancelText: this.$t('public.cancel'),
          onOk() {
            that.loading = true
            byjhdel(arr).then((res) => {
              if (res.code === 0) {
                if (res.msg === 'success') {
                  that.getList()
                  that.$message.success('删除成功')
                } else {
                  that.$message.error('删除失败')
                }
              }
            }).catch(err => that.requestFailed(err))
              .finally(() => {
                that.loading = false
              })
          },
          onCancel() { }
        })
      }
    },
    selectChangewan(val) {
      if (val && this.entity.startDd) {
        this.entity.byzq = ''
        this.$forceUpdate()
      }
    },
    selectChangekai(val) {
      // if(val && this.entity.endDd){
      //   this.entity.byzq= ''
      //   this.$forceUpdate()
      // }
    },
    selectChange(val) {
      // if(val){
      //   this.entity.startDd = ''
      //   this.entity.endDd = ''
      //   this.$forceUpdate()
      // }
    },
    handleSubmitnew() {
      if (this.entity.sbNo || this.entity.typeNo) {
      } else {
        this.$message.warning('设备代号设备型号必填其中一个')
        return
      }
      if (this.entity.year) {
      } else {
        this.$message.warning('年份必填')
        return
      }

      if (this.entity.byzq || this.entity.startDd && this.entity.endDd) {
      } else {
        this.$message.warning('保养周期和计划开始完工日必填其中一项')
        return
      }
    },
    handleCancelnew() {
      this.newvisible = false

    },
    // 本地删除
    locaDelTableRowtwo() {
      this.multipleSelectionnewid = this.$refs.tableone.getCheckboxRecords()
      if (this.multipleSelectionnewid.length) {
        let filterdata = this.multipleSelectionnewid.filter(i => i.byState == 2 || i.byState == 3)
        if (filterdata.length) {
          this.$message.warning('存在已保养或保养中的数据不能删除!')
          this.$refs.tableone.clearCheckboxRow()
          return
        }
      } else {
        this.$message.error("请至少选择一条数据！")
        return false;
      }
      if (this.multipleSelectionnewid.length) {
        // if(this.multipleSelectionnewid.filter(i=>i.id==''|| i.id==null)){}
        if (this.multipleSelectionnewid.filter(i => i.id)) {
        } else {
          let selectRows = this.multipleSelectionnewid
          selectRows.forEach((item) => {
            this.tableData.splice(this.tableData.indexOf(item), 1)
          });
          return
        }
        this.multipleSelectionnewid = this.multipleSelectionnewid.filter(i => i.byNo == '' || i.byNo == null)
        if (this.multipleSelectionnewid.length) {
        } else {
          this.$message.error("有保养单号的不能删除");
          return false;
        }
        this.multipleSelectionnewid = this.multipleSelectionnewid.filter(i => i.id)
      } else {
        this.$message.error("请至少选择一条数据！")
        return false;
      }

      if (this.multipleSelectionnewid.length) {
        let arr = []
        this.multipleSelectionnewid.forEach(i => {
          let arry = { id: i.id }
          arr.push(arry)
        })

        const that = this
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('identifier.delcont'),
          okText: this.$t('public.sure'),
          okType: 'warn',
          cancelText: this.$t('public.cancel'),
          onOk() {
            that.loading = true
            byjhdel(arr).then((res) => {
              if (res.code === 0) {
                if (res.msg === 'success') {
                  that.getList()
                  that.$message.success('删除成功')
                } else {
                  that.$message.error('删除失败')
                }
              }
            }).catch(err => that.requestFailed(err))
              .finally(() => {
                that.loading = false
              })
          },
          onCancel() { }
        })
      }





      let that = this
      this.multipleSelectionnew = this.$refs.tableone.getCheckboxRecords()
      if (this.multipleSelectionnew.length) {
        this.multipleSelectionnew = this.multipleSelectionnew.filter(i => i.byNo == '' || i.byNo == null)
        if (this.multipleSelectionnew.length) {
        } else {
          this.$message.error("有保养单号的不能删除");
          return false;
        }
      } else {
        this.$message.error("请勾选要操作的数据！");
        return false;
      }
      if (this.multipleSelectionnew.length === 0 || this.multipleSelectionnew === []) {
        this.$message.error("请勾选要操作的数据！");
        return false;
      }
      // this.$confirm({
      //   title: this.$t('public.del.title'),
      //   content: this.$t('public.del.content'),
      //   okText: this.$t('public.sure'),
      //   okType: 'danger',
      //   cancelText: this.$t('public.cancel'),
      //   onOk () {
      let selectRows = that.multipleSelectionnew
      selectRows.forEach((item) => {
        that.tableData.splice(that.tableData.indexOf(item), 1)
      });
      //   },
      //   onCancel () {
      //     that.$message.info("已取消删除");
      //   }
      // })
    },
    addTableRowtwo() {
      let tableDatafilter = JSON.parse(JSON.stringify(this.tableData)).filter(i => i.id == '')
      if (tableDatafilter.length > 0) {
        this.$message.warning('已经有一条新增未保存数据')
        return
      } else {
        this.tableAddtwo()
      }
      //  let number = this.tableData.length - 1
      //   if(this.tableData[number].id){
      //     this.tableAddtwo()
      //   }else{
      //     this.$message.warning('已经有一条新增未保存数据')
      //   }

    },
    tableAddtwo() {
      for (let i = 0; i < 1; i++) {
        let obj = {
          year: moment(new Date()).format('YYYY'),
          sbNo: '',
          sbNm: '',
          typeNo: '',
          typeNm: '',
          sbNm: '',
          bydjNo: '',
          bydjNm: '',
          bybwNm: '',
          bybwNo: '',
          byzq: '',
          startDd: '',
          endDd: '',
          byState: '1',
          depName: '',
          byDd: '',
          byNo: '',
          rem: '',
          dep: '',
          depName: '',
          daystian: '',
          id: '',
          buildFlag: 2
        };
        // this.tableData.push(obj);
        // let arryData = []
        // this.tableData.push(arryData)
        this.tableData.unshift(obj)
        // this.basicList[0].tableData.push(obj)
      }
    },
    choosetypeNotable(obj, scope) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.tableData.forEach((item, index) => {
            if (scope.$rowIndex == index) {
              item.typeNo = ''
              item.typeName = ''
              item.typeId = ''
            }
            this.$set(this.tableData, index, item);
          })
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'typeNo') {
        this.tableData.forEach((item, index) => {
          if (scope.$rowIndex == index) {
            item.typeNo = obj.obj.data.typeNo
            item.typeName = obj.obj.data.typeName
            item.typeId = obj.obj.data.typeId
          }
          this.$set(this.tableData, index, item);
        })
        // this.entity.typeName = obj.obj.data.name
        // this.entity.typeNo = obj.obj.data.typeNo
        // this.entity.cusNo1 = obj.obj.data.cusNo1
        // this.entity.cusNo = obj.obj.data.cusNo
        // map[obj.obj.name] = obj.obj.data.typeNo
        // if(obj.obj.data.sortNo){
        //   this.entity.sortNo = obj.obj.data.sortNo
        //   this.entity.sortName = obj.obj.data.sortName
        //   this.$forceUpdate()
        // }
        // if(obj.obj.data.sortNo){
        //   this.typeupdata(obj.obj.data.sortNo,obj.obj.data.sortName)
        // }
      }
    },
    choosebybwNotable(obj, scope) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.tableData.forEach((item, index) => {
            if (scope.$rowIndex == index) {
              item.bybwNo = ''
              item.bybwNm = ''
            }
            this.$set(this.tableData, index, item);
          })
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'bybwNo') {
        this.tableData.forEach((item, index) => {
          if (scope.$rowIndex == index) {
            item.bybwNo = obj.obj.data.bybwNo
            item.bybwNm = obj.obj.data.bybwNm
          }
          this.$set(this.tableData, index, item);
        })
      }
    },
    hadeOk() {
      if (this.entity.year) {
      } else {
        this.$message.warning('年份必填')
        return
      }
      createByjhFast({
        ...this.entity
      }).then(res => {
        if (res.code == 0) {
          if (res.msg == 'success') {
            this.visiblequickset = false
            this.getList()
            this.newvisible = false
            this.$message.success('快速设定成功')
          }
        }
        this.loading = false

      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })

    },
    handleCancel() {
      this.visiblequickset = false
    },
    choosebydjNotable(obj, scope) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.tableData.forEach((item, index) => {
            if (scope.$rowIndex == index) {
              item.bydjNo = ''
              item.bydjNm = ''
              item.bydjId = ''

            }
            this.$set(this.tableData, index, item)
          })
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'bydjNo') {
        this.tableData.forEach((item, index) => {
          if (scope.$rowIndex == index) {
            item.bydjNo = obj.obj.data.bydjNo
            item.bydjNm = obj.obj.data.bydjNm
            item.bydjId = obj.obj.data.id
          }
          this.$set(this.tableData, index, item)
        })
      }
    },
    choosebydjNo(obj) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.entity.bydjNo = ''
          this.entity.bydjNm = ''
          this.entity.bydjId = ''
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'bydjNo') {
        this.flag = true
        this.entity.bydjNm = obj.obj.data.bydjNm
        this.entity.bydjNo = obj.obj.data.bydjNo
        this.entity.bydjId = obj.obj.data.id
        // this.entity.bydjNm = obj.obj.data.cusNo1
        // this.entity.typeNo = obj.obj.data.typeNo
        map[obj.obj.name] = obj.obj.data.bydjNo
      }
      // this.form.setFieldsValue(map)
    },
    openChangeOnetwo(status) {
      //status是打开或关闭的状态
      this.yearShowOnetwo = !this.yearShowOnetwo
    },
    openChangeOnethree(status) {
      //status是打开或关闭的状态
      this.yearShowOnethree = !this.yearShowOnethree
    },
    // 得到年份选择器的值
    panelChangeOnetwo(value) {
      this.entity.year = moment(value).format('YYYY')
      this.yaerModetwo = value
      this.yearShowOnetwo = !this.yearShowOnetwo
    },
    panelChangeOnetable(value, scope) {
      this.tableData.forEach((item, index) => {
        if (scope.$rowIndex == index) {
          scope.row.year = moment(value).format('YYYY')
        }
        this.$set(this.tableData, index, item)
      })
      // this.tableData
      // this.entity.year=moment(value).format('YYYY')
      // this.yaerMode = value
      this.yearShowOnethree = !this.yearShowOnethree
    },

    quickset() {
      this.visiblequickset = true
      this.entity.year = ''
      this.entity = {}
    },
    choosetable(obj, scope) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.tableData.forEach((item, index) => {
            if (scope.$rowIndex == index) {
              item.dep = ''
              item.depName = ''
            }
            this.$set(this.tableData, index, item);
          })
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'dep') {
        this.tableData.forEach((item, index) => {
          if (scope.$rowIndex == index) {
            item.dep = obj.obj.data.dep
            item.depName = obj.obj.data.depName
          }
          this.$set(this.tableData, index, item);
        })
      }
    },
    choosesbNotable(obj, scope) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.tableData.forEach((item, index) => {
            if (scope.$rowIndex == index) {
              item.sbNo = ''
              item.sbNm = ''
              item.sbId = ''
            }
            this.$set(this.tableData, index, item);
          })
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'sbNo') {
        this.tableData.forEach((item, index) => {
          if (scope.$rowIndex == index) {
            item.sbNo = obj.obj.data.sbNo
            item.sbNm = obj.obj.data.name
            item.sbId = obj.obj.data.id
            item.typeNo = obj.obj.data.typeNo
            item.typeNm = obj.obj.data.typeName
            item.typeId = obj.obj.data.id
            if (obj.obj.data.dep) {
              item.dep = obj.obj.data.dep
              item.depName = obj.obj.data.depName
            }
          }
          this.$set(this.tableData, index, item);
        })
      }
    },
    typeupdata(val1, val2, val3) {
      this.$nextTick(() => {
        this.entity.typeNo = val1
        this.entity.typeNm = val2
        this.entity.typeId = val3
        this.datatypeNo = this.entity.typeNo + '-' + this.entity.typeNm
        this.$forceUpdate()
      })
    },
    choosesbNo(obj) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.entity.sbNo = ''
          this.entity.sbNm = ''
          this.entity.sbId = ''
          this.datatsbNo = ''
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'sbNo') {
        this.flag = true
        this.entity.sbNo = obj.obj.data.sbNo
        this.entity.sbNm = obj.obj.data.name
        this.entity.sbId = obj.obj.data.id
        if (obj.obj.data.typeNo) {
          this.typeupdata(obj.obj.data.typeNo, obj.obj.data.typeName, obj.obj.data.typeId)
          // this.$nextTick(() => {
          // setTimeout(() => {
          //   this.entity.typeNo = obj.obj.data.typeNo
          //   this.entity.typeNm = obj.obj.data.typeName
          //   this.datatypeNo = this.entity.typeNo + '-' + this.entity.typeNm
          //   this.$forceUpdate()
          // }, 100);
          // })

        } else {
          this.entity.typeNo = ''
          this.entity.typeNm = ''
          this.datatypeNo = ''
          this.$refs.selectListtypeNo.clear()
          this.$forceUpdate()

        }
        // map[obj.obj.name] = obj.obj.data.sbNo
      }
    },
    // choosesbNo(obj) {
    //   if(obj.obj.clear){
    //     if(obj.obj.clear == 1){
    //       this.entity.sbNo=''
    //       this.entity.sbNm=''
    //       return
    //     }
    //   }
    //   var map = {}
    //   if (obj.obj.name === 'sbNo') {
    //     this.flag = true
    //     this.entity.sbNo= obj.obj.data.sbNo
    //     this.entity.sbNm= obj.obj.data.name
    //     map[obj.obj.name] = obj.obj.data.sbNo
    //   }
    // },
    choosetypeNo(obj) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.entity.typeNo = ''
          this.entity.typeNm = ''
          this.entity.typeId = ''
          this.datatypeNo = ''
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'typeNo') {
        this.flag = true
        this.entity.typeNo = obj.obj.data.typeNo
        this.entity.typeNm = obj.obj.data.name
        this.entity.typeId = obj.obj.data.id
        // this.entity.sbNo=' '
        // this.entity.sbNm=' '
        //   this.datasbNo = ' '
        //   this.$refs.selectListsbNo.clear()
        // this.choosesbNo({
        //   obj:{
        //     clear:1
        //   }
        // })
        this.$forceUpdate()
      }
    },
    // choosetypeNo(obj) {
    //   if(obj.obj.clear){
    //     if(obj.obj.clear == 1){
    //       this.entity.typeNo=''
    //       this.entity.typeNm=''
    //       return
    //     }
    //   }
    //   var map = {}
    //   if (obj.obj.name === 'typeNo') {
    //     this.flag = true
    //     this.entity.typeNo= obj.obj.data.typeNo
    //     this.entity.typeNm= obj.obj.data.name
    //     map[obj.obj.name] = obj.obj.data.typeNo
    //   }
    // },
    // 弹出日历和关闭日历的回调
    openChangeOne(status) {
      //status是打开或关闭的状态
      this.yearShowOne = !this.yearShowOne
    },
    // 得到年份选择器的值
    panelChangeOne(value) {
      this.queryParam.year = moment(value).format('YYYY')
      this.yaerMode = value
      this.yearShowOne = !this.yearShowOne
    },
    handlePanelChange1(val) {
      // this.mode1 = mode
      // console.log(value);
      // console.log(mode);
      // this.state.time = value
      // this.state.isopen = false
    },
    onYearChange(value) {
    },
    choosetwo(obj) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.entity.dep = ''
          this.entity.depName = ''
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'dep') {
        this.entity.depName = obj.obj.data.depName
        this.entity.dep = obj.obj.data.dep
        map[obj.obj.name] = obj.obj.data.dep
      }
      // this.form.setFieldsValue(map)
    },
    choose(obj) {
      if (obj.obj.clear) {
        if (obj.obj.clear == 1) {
          this.queryParam.dep = ''
          this.queryParam.depName = ''
          return
        }
      }
      var map = {}
      if (obj.obj.name === 'dep') {
        this.queryParam.depName = obj.obj.data.depName
        this.queryParam.dep = obj.obj.data.dep
        map[obj.obj.name] = obj.obj.data.dep
      }
      // this.form.setFieldsValue(map)
    },
    search() {
      this.tablePage.currentPage = 1
      this.getList()
    },
    newlyadd() {
      // this.newvisible = true
      // this.tableDatanew = []
      // this.entity={}
      // this.entity.year = ''
      let tableDatafilter = JSON.parse(JSON.stringify(this.tableData)).filter(i => i.id == '')
      if (tableDatafilter.length > 0) {
        this.$message.warning('已经有一条新增未保存数据')
        return
      } else {
        this.tableAddtwo()
      }




    },
    // queryonetwo(){

    //   // if(this.activeNameMain == 'first'){
    //      this.tablePage.currentPage = 1
    //     this.getList()
    //   // }else{
    //   //   this.tablePagetwo.currentPage = 1
    //   //   this.getListtwo()
    //   // }
    // },
    raidchange(row) {
      // 获取选中数据 row表示选中这一行的数据，可以从里面提取所需要的值
      this.multipleSelection = row


    },
    pageSizeChange(pageSize) {
      this.tablePage.pageSize = pageSize;
      this.getList();
    },
    pageSizeChangetwo(pageSize) {
      this.tablePagetwo.pageSize = pageSize;
      this.getListtwo();
    },
    currentChange(currentPage) {

      // this.visible = true;
      this.tablePage.currentPage = currentPage;
      this.getList();
    },
    currentChangetwo(currentPage) {
      // this.visible = true;
      this.tablePagetwo.currentPage = currentPage;
      this.getListtwo();
    },
    // 表格双击事件
    mfBxHanddle(row, column, event) {
      this.edit = row;
      this.editId = row.id;
      // this.$router.push({
      //   path: '/mes/completiontwo/detail',
      //   query: {
      //     tyNo: row.tyNo
      //   }
      // });
      localStorage.setItem("biaozhun", row.id);
      this.$router.push({
        name: 'mainstandardsdetail', params: {
          id: row.id
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSelectionChangenew(val) {
      this.multipleSelectionnew = val
    },
    mfBxHanddleone(row, column, event) {
      if (this.multipleSelection) {
        localStorage.setItem('checkdata', JSON.stringify(row))
        // this.$router.push({
        //   path: '/mes/completiontwo/detail',
        //   query: {
        //     id: row.tiNo
        //   }
        // });
        this.$router.push({
          name: 'completiontwodetail', params: {
            id: row.tiNo
          }
        })
      }
    },
    // handleClick(val) {


    //   if (val.name == 'first') {
    //    this.getList()
    //    this.$nextTick(() => {
    //     this.$refs.submission.focus()
    //   })
    //   }else{
    //     this.getListtwo()
    //     this.$nextTick(() => {
    //     this.$refs.submissiontwo.focus()
    //   })
    //   }

    // },
    // onChange (data, dateString) {
    //
    //   this.queryParam.date = dateString

    // },
    onChange(data, dateString) {
      this.queryParam.date = dateString
      this.queryParam.staDd = this.queryParam.date[0]
      this.queryParam.endDd = this.queryParam.date[1]

    },
    // 按钮初始化
    btnInit() {
      this.btn.newOpen = true // 首检开工 null||""
      this.btn.pause = true // 暂停 2
      this.btn.continues = true // 继续 3
      this.btn.inspection = true // 首检 1
    },
    getListtwo() {
      this.loading = true
      completverified(
        Object.assign({
          current: this.tablePagetwo.currentPage,
          // current: this.tablePage.currentPage,
          size: this.tablePagetwo.pageSize,
          staDd: this.queryParam.staDd,
          endDd: this.queryParam.endDd,
          tyNo: this.queryParam.tyNo,
          zcNo: this.queryParam.zcNo,
          moNo: this.queryParam.moNo,
          prdNo: this.queryParam.prdNo,
          prdName: this.queryParam.prdName,
          tiNo: this.queryParam.tiNo,
          sys: this.queryParam.sys ? 'T' : 'F',
          chkKnd: this.queryParam.chkKnd ? 'T' : 'F',
          tzNo: this.queryParam.tzNo,
          usr: this.queryParam.usr
          // bilNo:this.queryParam.bilNo,
          // pgNo: this.queryParam.pgNo
        })
      ).then(response => {
        this.loading = false
        this.tableDatatwo = response.data.records
        this.totalCount = response.data.total
        // this.tablePage.currentPage = response.data.current
      }).catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    getList() {

      // this.loading = true
      byjhqueryPage(
        Object.assign(
          {
            ...this.queryParam,
            current: this.tablePage.currentPage,
            size: this.tablePage.pageSize,
          }
        )
      )
        .then(res => {

          this.tableData = res.data.records
          if (this.tableData.length) {
            this.tableData.forEach(item => {
              if (item.byzq == '1001' || item.byzq == '1002' || item.byzq == '1003' || item.byzq == '1004' || item.byzq == '' || item.byzq == null) {
              } else {
                item.daystian = item.byzq
              }
            })
          }
          this.totalCount = res.data.total
          this.tablePage.total = res.data.total
          this.tablePage.currentPage = res.data.current
          this.loading = false
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    // 获取列表数据
    // getList () {
    //   this.loading = true

    //   sbtzqueryPage(
    //     // Object.assign(
    //       {
    //         current: this.tablePage.currentPage,
    //       // current: this.tablePage.currentPage,
    //       size: this.tablePage.pageSize,
    //       // keyword:this.queryParam.keyword
    //       //   staDd:this.queryParam.staDd,
    //       // endDd:this.queryParam.endDd,
    //       // tiNo:this.queryParam.tiNo,
    //       // zcNo:this.queryParam.zcNo,
    //       // moNo:this.queryParam.moNo,
    //       // prdNo:this.queryParam.prdNo,
    //       // prdName:this.queryParam.prdName,
    //       // sys: this.queryParam.sys ? 'T' : 'F',
    //       // chkKnd: this.queryParam.chkKnd ? 'T' : 'F',
    //       // bilNo:this.queryParam.bilNo,
    //       // pgNo: this.queryParam.pgNo
    //     }
    //   ).then(response => {
    //     this.loading = false
    //     this.tableData = response.data.records
    //     this.tableData.forEach((i,index) => {
    //       i.addid = index + 1
    //     })
    //     this.totalCount = response.data.total
    //     // this.tablePage.currentPage = response.data.current
    //   }).catch(err => this.requestFailed(err))
    //   .finally(() => {
    //     this.loading = false
    //   })
    // },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    ClickEvent({ row }) {
      this.btnInit()
      this.rowIndex = row
      getItem(
        Object.assign({
          pgNo: row.pgNo,
          itm: row.itm,
          tiNo: row.tiNo,
          prdNo: row.prdNo
        })
      ).then(response => {
        this.row = response.data
        this.btnState(this.row)
      }).catch(err => this.requestFailed(err))
    },
    // 按钮状态
    btnState(row) {
      // 首检 ：3 暂停：2  继续：1
      if (row.state === '-1') {
        this.btn.newOpen = false
      }
      if (row.state === '0' || row.state === '3' || row.state === '1') {
        this.btn.pause = false // 暂停
        this.btn.inspection = false // 首检
      }
      if (row.state === '2') {
        this.btn.continues = false // 继续 3
      }
    },
    getBtnValue(state) {
      // 获取对应按钮提示
      let value = ''
      switch (state) {
        case '0':
          value = this.$t('quality.newOpen')
          break
        case '3':
          value = this.$t('quality.inspection')
          break
        case '2':
          value = this.$t('quality.pause')
          break
        case '1':
          value = this.$t('quality.continues')
          break
        case '4':
          value = this.$t('quality.task')
          break
      }
      return value
    },
    // 按钮
    onSubmit(state) {
      switch (state) {
        case '0':
        case '1':
        case '2':
          this.loading = true
          let btnMes = this.getBtnValue(state)
          start({
            state: state,
            pgNo: this.row.pgNo,
            itm: this.row.itm,
            id: this.row.id
          }).then((res) => {

            if (res.code === 0) {
              if (res.msg === 'fail') {
                btnMes = res.data
              } else {
                btnMes = btnMes + this.$t('public.success')
                const row = this.row
                this.ClickEvent({ row })
              }
            }
            this.$notification.success({
              message: this.$t('public.tip'),
              description: btnMes
            })
            this.loading = false
          }).catch(err => this.requestFailed(err))

          break
        case '3':
          const row = this.rowIndex
          this.$refs.a.create(row)
          break
      }
    },
    reset() {
      this.queryParam = {
        tyNo: '',
        tiNo: '',
        zcNo: '',
        moNo: '',
        prdName: '',
        prdNo: '',
        staDd: '',
        endDd: '',
        dep: '',
        depName: ''
      },
        this.datatwo = ''
      this.$refs.selectListquery.clear()
      this.queryParam.staDd = moment().subtract(7, "days").format('YYYY-MM-DD')
      this.queryParam.endDd = moment(new Date()).format('YYYY-MM-DD')

    }
  }
}
</script>

<style>
.el-table .el-table__cell {
  padding: 6px 0;
}

.ant-input {
  height: 28px;
}

/* .ant-tabs-nav-container{font-size:16px}
::v-deep .el-radio__label{
display:none
} */
</style>