<template>
  <div class="consrm compoct">
    <div style="text-align:right;margin-right:16px;position:absolute;top: 90px;right:0px;z-index:999">
      <!-- <a-button
      style="margin-left: 8px"
      type="primary"
      @click="reset"
      v-if="$route.query.tyNo"
    >新增</a-button> -->
      <!-- <a-button
      style="margin-left: 8px"
      type="primary"
       :disabled="isClaim"
      :style="isClaim ? 'background-color: #909399;border-color: #909399;' : ''"
      @click="editclick"
    >编辑</a-button> -->

      <!-- :disabled="!this.entity.tyNo" -->
      <a-button style="margin-left: 8px" type="primary" @click="savezan" :disabled="yibaoyang || nocanDel || sbwxz"
        :style="isClaim || nocanDel || sbwxz ? 'background-color: #909399;border-color: #909399;color:#fff;' : ''">暂存</a-button>
      <a-button style="margin-left: 8px" type="primary" @click="save" :disabled="isClaim || nocanDel || sbwxz"
        :style="isClaim || nocanDel || sbwxz ? 'background-color: #909399;border-color: #909399;color:#fff;' : ''">保存</a-button>
      <a-button style="margin-left: 8px" type="primary" @click="deldata"
        :disabled="isClaim || nocanDel || sbwxz || dbybyzvisible"
        :style="isClaim || nocanDel || sbwxz || dbybyzvisible ? 'background-color: #909399;border-color: #909399;color:#fff;' : 'background-color: #f56c6c;border-color: #f56c6c;color:#fff;'">删除</a-button>


      <a-button style="margin-left: 8px;    color: #909399;
    background: #f4f4f5;
    border-color: #d3d4d6;" type="primary" @click="cosetiao">关闭</a-button>

      <!-- 新增	编辑	删除	保存	关闭 -->
    </div>
    <el-tabs type="border-card">
      <el-tab-pane label="设备保养详情">
        <el-row :gutter="10">
          <el-col :span="17" :xs="24">
            <div class="sup_info_basics_header">
              <div style="float: left;margin-left: 8px;">基础信息</div>
            </div>
            <div ref="leftHeight" style="border:1px solid #e5e5e5;margin:0 auto;padding: 10px;padding-top:15px;padding-bottom:15px;
            background-color:#fff;border-radius:10px;">
              <el-form label-width="100px" :model="entity" :rules="exitRules" ref="addEntityForm">
                <div style="border-bottom: 1px solid #E5E5E5;padding-bottom:10px;">
                  <el-row :gutter="0" style="margin-bottom:10px;">
                    <!-- <el-col :span="8" >             
                    <el-form-item label="设备型号:" prop="typeNo">
                      <my-selectListwotype
                url="ems/sbtype/queryPage"
                :read-only="true"
                :tableColumn="$Column.devicetypeNo"
                :form="$Form.devicesalmDep"
                :datatype="datatypeNo"
                name="typeNo"
                v-if="isEdit"
                @choosetype="choosetypeNo($event)"
                allowClear
                ref="selectListtypeNo"
                v-decorator="['typeNo', { rules: [{ required: true, message:'请选择设备型号' } ] }]"
                placeholder="请选择设备型号"
              ></my-selectListwotype>
                      <span v-else>{{ entity.typeNo }} / {{ entity.typeNm}}</span>
                    </el-form-item>
                  </el-col> -->

                    <el-col :span="8">
                      <el-form-item label="设备代号:" class="sbNo">
                        <!-- <my-selectListwo
                url="ems/sbtz/queryPage"
                :read-only="true"
                :tableColumn="$Column.devicesbNo"
                :form="$Form.devicesalmDep"
                :data="datasbNo"
                name="sbNo"
                v-if="isEdit"
                @choose="choosesbNo($event)"
                allowClear
                ref="selectListsbNo"
                v-decorator="['sbNo', { rules: [{ required: true, message:'请选择设备代号' } ] }]"
                placeholder="请选择设备代号"
              ></my-selectListwo> -->
                        <span>{{ entity.sbNo }} / {{ entity.name}}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="设备名称:" class="name">
                        <el-input v-model="entity.name" v-if="isEdit" size="mini" disabled
                          style="max-width: 200px;width:100%;" placeholder="设备名称"></el-input>
                        <span v-else>{{ entity.name }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="保养人员:" prop="">
                        <!-- | formatDate -->
                        <div style="font-size:12px;">{{entity.byYgName}}</div>
                      </el-form-item>
                    </el-col>


                  </el-row>
                  <el-row :gutter="0" style="margin-bottom:15px;">
                    <el-col :span="8">
                      <el-form-item label="开始时间:" prop="startDd">
                        <!-- | formatDate -->
                        <!-- <a-date-picker
                        style="width:100%"
                        v-if="isEdit"
                        v-model="entity.startDd"
                        valueFormat="YYYY-MM-DD HH:mm:ss"
                        placeholder="请输入开始时间"
                      /> -->
                        <a-date-picker format="YYYY-MM-DD HH:mm:ss" valueFormat="YYYY-MM-DD HH:mm:ss"
                          v-model="entity.startDd" v-if="isEdit || nocanDel" style="width:100%" placeholder="请输入开始时间" />
                        <!-- v-decorator="['stopDd', { rules: [{ required: false, message:'请输入停用日期' }] }]"                    -->

                        <!-- format="YYYY-MM-DD HH:mm:ss" -->
                        <span v-else>{{ entity.startDd }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="结束时间:" prop="endDd">
                        <!-- | formatDate -->
                        <a-date-picker style="width:100%" v-if="isEdit || nocanDel" format="YYYY-MM-DD HH:mm:ss"
                          valueFormat="YYYY-MM-DD HH:mm:ss" v-model="entity.endDd" placeholder="请输入结束时间" />
                        <span v-else>{{ entity.endDd }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="保养用时:">
                        <!-- <el-input
                      v-model="entity.timeStr"
                      v-if="isEdit"
                      size="mini"
                      style="max-width: 200px;width:100%;"
                      placeholder="保养用时"
                    ></el-input> -->
                        <span>{{ entity.timeStr }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="0" style="margin-bottom:10px;">
                    <el-col :span="8">
                      <!-- <my-selectListwo
                        url="mes/basicData/depPage"
                        :read-only="true"
                        :tableColumn="$Column.salmDep"
                        :form="$Form.salmDep"
                        :data="scope.row.dep"
                        name="dep"
                        @choose="choosedan($event,scope)"
                        allowClear
                        ref="selectList"
                        v-decorator="['dep', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                        :placeholder="$t('salm.placeholder.depName')"
                      ></my-selectListwo> -->
                      <el-form-item label="保养等级:" prop="bydjNo">
                        <!-- url="mes/basicData/depPage"   :tableColumn="$Column.salmDep"   :form="$Form.salmDep"-->
                        <my-selectListwo url="ems/bydj/queryPage" :read-only="true" :tableColumn="$Column.devicebydjNo"
                          :form="$Form.devicesalmDep" :data="databydjNo" name="bydjNo" v-if="isEdit || nocanDel"
                          @choose="choosebydjNo($event)" allowClear ref="selectList"
                          v-decorator="['bydjNo', { rules: [{ required: true, message:'请选择保养等级' } ] }]"
                          placeholder="请选择保养等级"></my-selectListwo>
                        <span v-else>{{ entity.bydjNo }} / {{ entity.bydjNm}}</span>
                      </el-form-item>
                    </el-col>

                    <el-col :span="8">
                      <el-form-item label="保养单号:" prop="byNo">
                        <!-- <el-input
                      v-model="entity.byNo"
                      v-if="isEdit"
                      size="mini"
                      style="max-width: 200px;width:100%;"
                      placeholder="请输入保养单号"
                    ></el-input> -->
                        <span>{{ entity.byNo }}</span>
                      </el-form-item>
                    </el-col>

                    <el-col :span="8">
                      <el-form-item label="是否更换备件:">
                        <el-select v-model="entity.bgpjId" placeholder="请选择是否更换备件" size="mini" v-if="isEdit || nocanDel"
                          style="max-width:200px; width:100%;">
                          <el-option v-for="item in huanbeijian" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                        </el-select>
                        <span v-else>{{ entity.bgpjId }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="0" style="margin-bottom:15px;">
                    <el-col :span="8">
                      <el-form-item label="是否停机:">
                        <el-select v-model="entity.stopId" placeholder="请选择是否停机" size="mini" v-if="isEdit || nocanDel"
                          style="max-width:200px; width:100%;">
                          <el-option v-for="item in baodan" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                        </el-select>
                        <span v-else>{{ entity.stopId }}</span>
                      </el-form-item>
                    </el-col>

                    <el-col :span="8">
                      <el-form-item :label="$t('部门:')" prop="dep">
                        <!-- <my-selectListwo
                      v-if="isEdit"
                        url="ems/dept/getDeptPage"
                        :read-only="true"
                        :tableColumn="$Column.devicesalmDep"
                        :form="$Form.devicesalmDep"
                        :data="data"
                        name="dep"
                        allowClear
                        @choose="choose($event)"
                        ref="selectList"
                        v-decorator="['dep', { rules: [{ required: true, message:$t('salm.placeholder.depName') } ] }]"
                        :placeholder="$t('salm.placeholder.depName')"
                      ></my-selectListwo> -->
                        <span>{{ entity.dep }} / {{ entity.depName}}</span>
                        <!-- <span>{{ entity.dep }}</span> -->
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="保养描述:" prop="byNo">
                        <el-input v-model="entity.remBy" v-if="isEdit || nocanDel" size="mini"
                          style="max-width: 200px;width:100%;" placeholder="请输入保养描述"></el-input>
                        <span v-else>{{ entity.remBy }}</span>
                      </el-form-item>
                    </el-col>
                    <!-- <el-col :span="8">
                    <el-form-item label="部门:" prop="dep">
                      <el-select
                      v-if="isEdit"
                        v-model="entity.dep"
                        :disabled="isShowDisables"
                        placeholder="请选择部门"
                        size="mini"
                        style="max-width:200px; width:100%;"
                      >
                        <el-option
                          v-for="item in options"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                      <span v-else>{{ entity.dep }}</span>
                    </el-form-item>
                  </el-col> -->


                  </el-row>
                </div>
              </el-form>
            </div>
          </el-col>
          <el-col :span="7" :xs="24">
            <!-- 附件 -->
            <!-- <a-col :span="8">
                    <el-form-item label="附件:">
                      <div style="position: relative;">
                        <a-input
                          :readOnly="true"
                          :placeholder="$t('附件')"
                        />
                        <div style="position:absolute;top:0px;right:65px;">
                          <a-button
                            :loading="loading"
                            type="primary"
                            size='small'
                            @click="seepic()"
                          >查看
                          </a-button>
                        </div>
                        <div style="position:absolute;top:0px;right:2px;">
                          <a-button
                            :loading="loading"
                            type="primary"
                            size='small'
                            @click="uploadpic()"
                          >上传
                          </a-button>
                        </div>
                      </div>
                    </el-form-item>
                  </a-col> -->
            <!-- 附件 -->
            <!-- 开始 -->
            <div class="sup_info_basics_header">
              <div style="float: left;margin-left: 8px;">附件</div>
            </div>
            <div class="sup_info_basics_container" :style="{ height: rightHeight }">


              <div class="image-view">
                <div class="addbox">
                  <input type="file" @change="convertToBase64" />
                  <!-- <el-button  type="success" class="addbtn">添加</el-button> -->
                  <!-- <el-button  type="success" size="mini" class="addbtn">上传图片</el-button> -->
                  <div class="addbtn">上传图片</div>
                </div>
                <div class="view" style="height:150px;overflow:scroll;  margin-bottom:10px;">
                  <span class="item" v-for="(item, index) in imageBase64" :key="item.id" style="display: inline-block;">
                    <span class="cancel-btn" @click="turnUpDelImg(index)">x</span>
                    <img :src="item" @dblclick="turnUpBigImg(index)" />
                  </span>
                </div>
              </div>

              <!-- <input type="file" @change="convertToBase64" />
                            <img v-if="imageBase64" :src="imageBase64" alt="Image preview" /> -->

              <!-- <el-table v-loading="loading" stripe :data="tableData" highlight-current-row 
                            :header-cell-style="{ backgroundColor: '#F4F5F9', fontWeight: '400' }" 
                            style="width: 100%;margin-top:10px;"
                            :height="tablerightHeight"
                            >
                              <el-table-column label="附件">
                                <template v-slot="{row}">
                                  <span class="file-name" @click="handleDown(item)">{{ row.original }}</span>
                                </template>
                              </el-table-column>
                              <el-table-column prop="materialName" label="操作">
                                <template slot-scope="scope">
                                  <el-button
                                    :loading="loading"
                                    type="primary"
                                    size='mini'
                                    @click="seepic(scope.$index)"
                                  >查看
                                  </el-button>
                                    <el-button size='mini' type="primary" @click="handleDelVersion(scope.row)">删除</el-button>
                                </template>
                              </el-table-column>
                            </el-table> -->
              <!-- <div style="margin-top: 20px;">
                              <el-pagination
                                background
                                :current-page="tablePage.currentPage"
                                :page-sizes="[5, 10, 50, 100]"
                                :page-size="100"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="tablePage.total"
                                @size-change="handleSizeChange"
                                @current-change="handleCurrentChange"
                              />
                            </div> -->
            </div>
            <!-- 结束 -->


          </el-col>
        </el-row>
        <div style="padding-top:15px;">
          <!-- <el-button
        size="mini"
        icon="el-icon-plus"
        id="add_table"
       type="primary"
        @click="addTableRowtwo()"
        ></el-button>
        <el-button
      
          size="mini"
          icon="el-icon-minus"
          id="add_table"
           type="primary"
:style="'background-color: #f56c6c;border-color: #f56c6c;color:#fff;'"
          @click="locaDelTableRowtwo()"
        ></el-button> -->
          <a-button style="float:right" type="primary" @click="weixiu" :disabled="!ybyjin || nocanDel"
            :style="!ybyjin || nocanDel ? 'background-color: #909399;border-color: #909399;color:#fff;' : ''">维修申请</a-button>
        </div>

        <el-table stripe :data="standardstable" highlight-current-row style="width: 100%;margin-top:10px;"
          @selection-change="handleSelectionChange" height="400px">
          <!--     @row-click="mfBxHanddle" :row-style="selectedstyle" :row-class-name="tableRowClassName" -->
          <!-- @row-dblclick="mfBxHanddle" -->
          <!-- :header-cell-style="{fontSize:'14px',color: '#606266', verticalAlign: 'top',backgroundColor: '#F4F5F9', fontWeight: '700' }" -->
          <!-- @sort-change="sortChange" -->
          <!-- <el-table-column type="selection" align="center" width="50"></el-table-column> -->
          <!-- <el-table-column
                  width="60px"
                  label="选择"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-radio
                      v-model="radio"
                      :label="scope.$index"
                      class="radio"
                      @change.native="raidchange(scope.row)"
                    >&nbsp;</el-radio>
                  </template>
                </el-table-column> -->
          <el-table-column type="selection" width="48"></el-table-column>
          <el-table-column label="序号" width="48">
            <template slot-scope="scope">
              {{scope.$index}}
            </template>
          </el-table-column>
          <el-table-column label="保养项目" width="210" prop="byxmNo">
            <template slot-scope="scope">
              <my-selectListwo url="ems/byxm/queryPage" :read-only="true" :tableColumn="$Column.devicebyxmNo"
                :form="$Form.devicesalmDep" :data="scope.row.byxmNo + ' ' + scope.row.byxmNm" name="byxmNo"
                @choose="choosebyxmNo($event,scope)" allowClear :disabled="!!scope.row.id" ref="selectList"
                v-decorator="['byxmNo', { rules: [{ required: true, message:'请选择保养项目' } ] }]"
                placeholder="请选择保养项目"></my-selectListwo>
              <!-- <el-input
                      @focus="focus(scope.$index,scope.row, $event)"
                      @input="inputNameInput"
                      ref="prdt3Input"
                      readonly
                      v-model="scope.row.qcItm"
                    ></el-input> -->
              <!-- style="border: none;font-size:12px;" -->
              <!-- @blur="blurNameInput" -->
            </template>
          </el-table-column>
          <el-table-column prop="byxmNr" width="280" label="保养内容">
            <template slot-scope="scope">
              <el-input size="mini" type="textarea" style="width:100%" :disabled="!!scope.row.id" placeholder="请输入保养内容"
                v-model="scope.row.byxmNr" :autosize="{ minRows: 1, maxRows: 3}"></el-input>
            </template>
            <!-- @focus="focus(scope.$index,scope.row, $event)"
                @input="inputNameInput" -->
          </el-table-column>
          <el-table-column label="保养部位" width="210" prop="bybwNo">
            <template slot-scope="scope">
              <my-selectListwo url="ems/bybw/queryPage" :read-only="true" :tableColumn="$Column.devicebybwNo"
                :form="$Form.devicesalmDep" :data="scope.row.bybwNo + ' ' + scope.row.bybwNm" name="bybwNo"
                :disabled="!!scope.row.id" @choose="choosebybwNo($event,scope)" allowClear ref="selectList"
                v-decorator="['bybwNo', { rules: [{ required: true, message:'请输入保养部位' } ] }]"
                placeholder="请输入保养部位"></my-selectListwo>
            </template>
          </el-table-column>

          <!-- <el-table-column prop="qcsjZjId" width="50" align="left" label="首检自检">
                  <template slot-scope="scope">
                    <a-checkbox
                    :disabled="!scope.row.qcsjId"
                      v-model="scope.row.qcsjZjId"
                      @change="onChangeselftwo(scope)"
                    ></a-checkbox>
                  </template>
                </el-table-column>
                <el-table-column prop="qcmjZjId" width="50" align="left" label="末检自检">
                  <template slot-scope="scope">
                    <a-checkbox
                    :disabled="!scope.row.qcmjId"
                      v-model="scope.row.qcmjZjId"
                      @change="onChangemoselftwo(scope)"
                    ></a-checkbox>
                  </template>
                </el-table-column> -->
          <!-- 保养内容	保养部位	保养方法	确认方式	符号一	值一	符号二	值二 -->
          <el-table-column prop="byffNo" width="210" label="保养方法">
            <template slot-scope="scope">
              <my-selectListwo url="ems/byff/queryPage" :read-only="true" :tableColumn="$Column.devicebyffNo"
                :form="$Form.devicesalmDep" :data="scope.row.byffNo + ' ' + scope.row.byffNm" name="byffNo"
                @choose="choosebyffNo($event,scope)" allowClear :disabled="!!scope.row.id" ref="selectList"
                v-decorator="['byffNo', { rules: [{ required: true, message:'保养方法' } ] }]"
                placeholder="请输入保养方法"></my-selectListwo>
            </template>
          </el-table-column>
          <el-table-column prop="confirm" width="100" align="left" label="确认方式">
            <template slot-scope="scope">
              <el-select @input="surehandle(scope)" v-model="scope.row.confirm" placeholder="请选择确认方式" size="mini"
                :disabled="!!scope.row.id" style="max-width:200px; width:100%;">
                <el-option v-for="item in surestyle" :key="item.value" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="value" width="150" align="left" label="值">
            <template slot-scope="scope">
              <!-- <el-input
                       v-model="scope.row.value"
                       size="mini"
                       placeholder="请输入值"
                       style="max-width: 200px;width:100%;"
                     ></el-input> -->
              <el-input v-model="scope.row.value" @input="handlezhi(scope)" type="number"
                :disabled="scope.row.confirm != 2 && scope.row.confirm != 4" @wheel.native.prevent="stopScroll($event)"
                size="mini" placeholder="请输入值" />
            </template>
          </el-table-column>
          <el-table-column prop="symbol1" width="100" align="left" label="符号一">
            <template slot-scope="scope">
              <el-select v-model="scope.row.symbol1" placeholder="请选择符号一" size="mini" @input="handlezhi(scope)"
                :disabled="(scope.row.confirm != 2 && scope.row.confirm != 4)||scope.row.id" clearable
                style="max-width:200px; width:100%;">
                <el-option v-for="item in stdValueLIst" :key="item.value" :label="item.value"
                  :value="item.value"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="value1" width="100" align="left" label="值一">
            <template slot-scope="scope">
              <el-input v-model="scope.row.value1" size="mini" type="number" @input="handlezhi(scope)"
                placeholder="请输入值一"
                :disabled="(scope.row.confirm != 2 && scope.row.confirm != 4) || !scope.row.symbol1 || scope.row.id"
                style="max-width: 200px;width:100%;" @wheel.native.prevent="stopScroll($event)"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="symbol2" width="100" align="left" label="符号二">
            <template slot-scope="scope">
              <el-select v-model="scope.row.symbol2" placeholder="请选择符号二" size="mini" @input="handlezhi(scope)"
                :disabled="scope.row.confirm != 2 && scope.row.confirm != 4 || scope.row.id" clearable
                style="max-width:200px; width:100%;">
                <el-option v-for="item in stdValueLIsttwo" :key="item.value" :label="item.value"
                  :value="item.value"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="value2" width="100" align="left" label="值二">
            <template slot-scope="scope">
              <el-input v-model="scope.row.value2" size="mini" type="number" @input="handlezhi(scope)"
                placeholder="请输入值二"
                :disabled="(scope.row.confirm != 2 && scope.row.confirm != 4) || !scope.row.symbol2 || scope.row.id"
                style="max-width: 200px;width:100%;" @wheel.native.prevent="stopScroll($event)"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="rem" width="150" align="left" label="OK或者NG">
            <template slot-scope="scope">
              <el-select v-model="scope.row.okId" placeholder="请选择" size="mini"
                v-if="scope.row.confirm != 2 && scope.row.confirm != 4" style="max-width:200px; width:100%;">
                <el-option v-for="item in okng" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
              <div v-else>
                <div v-if="scope.row.okId ==1">OK</div>
                <div v-if="scope.row.okId ==2">NG</div>
              </div>
              <!-- <el-input
                   v-model="scope.row.rem"
                   size="mini"
                   placeholder="请输入OK"
                   style="max-width: 200px;width:100%;"
                 ></el-input> -->
            </template>
          </el-table-column>
          <el-table-column prop="rem" width="150" align="left" label="备注">
            <template slot-scope="scope">
              <el-input v-model="scope.row.rem" size="mini" placeholder="请输入备注"
                style="max-width: 200px;width:100%;"></el-input>
            </template>
          </el-table-column>

        </el-table>


        <a-card :bordered="false">
          <!-- <a-row :gutter="20">
      <a-form-item label="检验日期">
      <a-col :span="6" :md="6" :sm="24">
        
          <a-range-picker @change="onChange" format="YYYY-MM-DD"/>
      </a-col>
      </a-form-item>
    </a-row> -->
          <a-row :gutter="8">
            <a-col :span="24">
              <a-row>

                <el-dialog title="维修申请" :visible.sync="weixiuvisible" width="50%" style="height:100%">
                  <div class="table-page-search-wrapper" style="padding-bottom:40px;">
                    <el-form label-width="100px">
                      <el-row :gutter="0" style="margin-bottom:15px;">
                        <el-col :span="12">
                          <el-form-item label="是否停机:">
                            <el-select v-model="stopId" placeholder="请选择是否停机" size="mini"
                              style="max-width:200px; width:100%;">
                              <el-option v-for="item in baodan" :key="item.value" :label="item.label"
                                :value="item.value"></el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="故障描述:" prop="faultRem">
                            <el-input v-model="faultRem" size="mini" style="max-width: 200px;width:100%;"
                              placeholder="请输入保养单号"></el-input>
                          </el-form-item>
                        </el-col>

                      </el-row>
                    </el-form>
                    <div slot="footer" style="float: right;margin-top:20px;">
                      <el-button size="mini" @click="weixiuvisible = !weixiuvisible">取 消</el-button>
                      <el-button size="mini" type="primary" @click="otngetData">确 定</el-button>
                    </div>
                  </div>
                </el-dialog>
                <uploadFile ref="uploadFile" @seeFile="seeFile" />
              </a-row>
            </a-col>
            <!-- <inspection
        :row="row"
        ref="a"
        @getList="getList"
      /> -->
          </a-row>
        </a-card>
        <!-- <ModalPic ref="ModalPic" /> -->
        <!-- 不合格原因 -->
        <!-- <el-button
              icon="fa fa-plus"
              @click="insertEvent"
            >新增</el-button>
            <el-button
              @click="removeEvent"
            >删除选中</el-button>
            <el-table
                :data="tableDatatwo"
                v-loading="tableLoading"
                border
                stripe
                @row-dblclick="mfBxHanddle"
                  @selection-change="handleSelectionChangetwo"
                style
                 :header-cell-style="{fontSize:'14px',color: '#606266',backgroundColor: '#F4F5F9', fontWeight: '700' }"
              >
                <el-table-column type="selection" align="center" width="35"></el-table-column>
                <el-table-column prop="qty" width="110" align="left" label="原因代号"></el-table-column>
                <el-table-column prop="up" width="120" align="left" label="不合格原因"></el-table-column>
                <el-table-column prop="up" width="120" align="left" label="不合格量">
                  <template v-slot="scope">
                      <el-input v-model="scope.row.up"></el-input>
                  </template>
                </el-table-column>
            </el-table>

<vxe-toolbar>
          <template #buttons>
            <vxe-button
             
              icon="fa fa-plus"
              @click="insertEvent"
            >新增</vxe-button>
            <vxe-button
              
              @click="removeEvent"
            >删除选中</vxe-button>
          </template>
        </vxe-toolbar>
        <vxe-table
          border
          resizable
          stripe
          id="imp"
          size="mini"
          max-height="450px"
          highlight-current-row
          show-overflow
          highlight-hover-row
          ref="xTable2"
          :checkbox-config="{ strict: true }"
          :loading="loading"
          :data="tableDatatwo"
          @checkbox-change="selectChangeEvent"
          @checkbox-all="selectChangeAll"
          :keyboard-config="{ isArrow: true }"
          :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true}"
        >
          <vxe-table-column
            type="checkbox"
            width="60"
          ></vxe-table-column>
          <vxe-table-column
            field="qty"
            title="原因代号"
            align="center"
            :edit-render="{ name: 'input', attrs: {disabled: nameDisabled }}"
            :width="150"
          ></vxe-table-column>
          <vxe-table-column
            field="unit"
            title="不合格原因"
            align="center"
            :edit-render="{ name: 'input', attrs: {disabled: nameDisabled }}"
            :width="150"
          ></vxe-table-column>
          <vxe-table-column
            field="up"
            title="不合格量"
            align="center"
            :edit-render="{ name: 'input', attrs: {disabled: nameDisabled }}"
            :width="150"
          ></vxe-table-column>
          <vxe-table-column
            field="bDd"
            title="有效开始日期"
            align="center"
            :edit-render="{name: '$input', props: {type: 'date'}}"
            :width="150"
          ></vxe-table-column>
          <vxe-table-column
            field="eDd"
            title="有效结束日期"
            align="center"
            :edit-render="{name: '$input', props: {type: 'date'}}"
            :width="150"
          ></vxe-table-column>
          <vxe-table-column
            field="qcNo"
            title="inquiryoffer.qcno"
            align="center"
            :edit-render="{ name: 'input', attrs: {disabled: nameDisabled }}"
            :width="150"
          ></vxe-table-column>
          <vxe-table-column
            field="rem"
            title="inquirycompri.remarks"
            align="center"
            :edit-render="{ name: 'input', attrs: {disabled: nameDisabled }}"
            :width="150"
          ></vxe-table-column>
        </vxe-table> -->
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
  import {
    datadepPage, qcwgyjInfo, sysfiledel
  } from '@/api/mes/quality'
  import { byffqueryPage, bybzHqueryById, bybzHsaveOrUpdate, bybzHdel, byqueryById, onBytempSave, bysubmit, aldBydel, bxsubmit } from '@/api/device/category'
  import { sbtypequeryPage } from '@/api/device/category'
  import { mapGetters, mapState } from 'vuex'
  import uploadFile from '../../information/deviceinform/uploadFile'

  import moment from 'moment'
  import MySelectListwo from '@/components/MySelectListwo'

  export default {
    components: {
      uploadFile,
      MySelectListwo,
    },
    data() {
      return {
        exitRules: {
          // dep:[{ required: true, message: "必填:部门", trigger: ["change", "blur"] }],
          startDd: [{ required: true, message: "必填:开始日期", trigger: ["change", "blur"] }],
          endDd: [{ required: true, message: "必填:结束日期", trigger: ["change", "blur"] }],
        },
        isEdit: false,
        mes_quality_query: 'mes_quality_query',
        mes_quality_reset: 'mes_quality_reset',
        mes_quality_newOpen: 'mes_quality_newOpen',
        mes_quality_inspection: 'mes_quality_inspection',
        mes_quality_pause: 'mes_quality_inspection',
        mes_quality_continues: 'mes_quality_continues',
        mes_quality_task: 'mes_quality_task',
        queryParam: {},
        tableData: [],
        loading: false,
        tablePage: {
          currentPage: 1,
          pageSize: 5,
          total: 0
        },
        row: {},
        rowIndex: {},
        // 按钮控制
        btn: {
          newOpen: true, // 首检开工
          open: true, // 开始
          pause: true, // 暂停
          continues: true, // 继续
          report: true, // 报工
          exceptional: true, // 异常
          inspection: true, // 送检
          introspecting: true // 自检
        },
        queryParam: {
          date: [],
        },
        activeName: '1',
        visible: false,
        reasondata: [],
        multiplereason: [],
        entity: {
          // tyDd:'',
          // chkKnd:'',
          // qcId:'',
          // sys:'',
          // chktyId:'',
          // tiNo:'',
          // tyNo:'',
          // prdt3Name: "",
          // way: 2,
          // auxId: "",
          // otRemark: "",
          // bxId: "",
          // qaId: "",
          // otId: "",
          // bxNo: "",
          // bxDd: "",
          // status: "",
          // statusMsg: "",
          // kndId: 3,
          // applyDepId: null,
          // applyDepNo: "",
          // applyDepName: "",
          // applyUserId: "",
          // applyUserNo: "",
          // applyUserName: "",
          // prdId: "",
          // prdNo: "",
          // prdName: "",
          //  prdt3Id: "",
          // prdSpc: "",
          // faultRem: "",
          // cntId: "",
          // cntName: "",
          // cntTel: "",
          // cntAdr: "",
          // dcId: "",
          // dcName: "",
          // dcLmt: "",
          // urgent: 0,
          // finId: "",
          // finName: "",
          // finLmt: "",
          // bxPic1: "",
          // bxPic2: "",
          // bxPic3: "",
          // serverDeptId: "",
          // otUserId: "",
          // qty: null,
          // serviceCode: "",
          // coDd: null,
          // prdUt: "",
          // bxType: '1',
          // initialTime: '',
          // completeTime: ''
        },
        inspectiontype: [
          { value: '1', label: '完工检验' },
          { value: '2', label: '首检检验' },
          { value: '3', label: '托工检验' },
        ],
        multipleSelectiontwo: [],
        sysFiles: [],
        colData: [
          { title: "原因代号", istrue: true },
          { title: "不合格原因", istrue: true },
          { title: "不合格量", istrue: true },
          { title: "货品名称", istrue: true },
          { title: "货品代号", istrue: true },
          { title: "规格", istrue: true },
          { title: "现有库存", istrue: true },
          { title: "借出量", istrue: true },
          { title: "单位", istrue: true },
          { title: "单价", istrue: true },
          { title: "数量", istrue: true },
          { title: "已还数量", istrue: true },
          { title: "税率%", istrue: true },
          { title: "未税金额", istrue: true },
          { title: "税额", istrue: true },
          { title: "金额", istrue: true },
        ],
        objStyle: {
          top: "433px",
          left: "",
        },
        pickerList: [],
        testData: [],
        detailIds: [],
        multipleSelection: [],
        pickerIndex: 0,
        isShowPopVel: false,
        rightHeight: '',
        tablerightHeight: '',
        tyNotwo: '',
        tyDdtwo: '',
        flag: false,
        ccc: true,
        isClaim: false,
        data: '',
        tableLoading: false,
        bottomWidth: '',
        typeName: '',
        datatypeNo: '',
        databydjNo: '',
        baodan: [
          { label: '否', value: '0' },
          { label: '是', value: '1' },
        ],
        huanbeijian: [
          { label: '否', value: '0' },
          { label: '是', value: '1' },
        ],
        devicestatus: [
          { label: '在用', value: '1' },
          { label: '在修', value: '2' },
          { label: '停机', value: '3' },
          { label: '报废', value: '4' },
        ],
        surestyle: [
          { label: '判定', value: 1 },
          { label: '测量', value: 2 },
          { label: '判定+拍照', value: 3 },
          { label: '测量+拍照', value: 4 },
        ],
        personnel: [
          { label: '作业人员', value: 1 },
          { label: '维修工', value: 2 },
        ],
        standardstable: [],
        stdValueLIst: [
          { value: '>', label: '>' },
          { value: '>=', label: '>=' },
          { value: '=', label: '=' },
        ],
        stdValueLIsttwo: [
          { value: '<', label: '<' },
          { value: '<=', label: '<=' },
        ],
        okng: [
          { label: 'OK', value: 1 },
          { label: 'NG', value: 2 }
        ],
        datasbNo: '',
        imageBase64: [],
        turnUpImgBase64Url: [],
        dialogImageUrl: "",
        showBigImg: false,
        weixiuvisible: false,
        faultRem: '',
        stopId: '0',
        entitybodyVoList: false,
        yibaoyang: false,
        nocanDel: false,
        ybyjin: false,
        sbwxz: false,
        dbybyzvisible: false
      }
    },
    computed: {
      ...mapState({
        userInfo: state => state.user.userInfo
      }),
      ...mapGetters(['permissions'])
    },
    created() {
      this.sysFiles = []
      this.addTableRowtwo()
      if (localStorage.getItem('sebaoyangone')) {
        this.ybyjin = false
        this.dbybyzvisible = true
        this.entity.id = localStorage.getItem('sebaoyangone')
        this.getListtreat()
        this.isEdit = true
        this.yibaoyang = false
      } else if (localStorage.getItem('sebaoyangtwo')) {
        this.ybyjin = false
        this.dbybyzvisible = true
        this.entity.id = localStorage.getItem('sebaoyangtwo')
        this.getListtreat()
        this.isEdit = true
        this.yibaoyang = false
        // this.isEdit = true sebaoyangthree
        // moment(new Date()).format('YYYY-MM-DD HH:MM:SS'),
        // this.entity.startDd = this.$moment(new Date()).format('YYYY-MM-DD');
      } else if (localStorage.getItem('sebaoyangthree')) {
        this.ybyjin = true
        this.entity.id = localStorage.getItem('sebaoyangthree')
        this.getListtreat()
        this.yibaoyang = true
        this.isEdit = true
      }

      //     this.startTime = this.$moment().subtract(30, "days").format('YYYY-MM-DD');
    },
    mounted() {
      // this.pickerSearch()
      this.$nextTick(() => {
        this.rightHeight = this.$refs.leftHeight.offsetHeight + 'px'
        this.bottomWidth = this.$refs.leftHeight.offsetWidth + 6 + 'px'

        // :height="tablerightHeight"
        this.tablerightHeight = this.$refs.leftHeight.offsetHeight - 65 + 'px'

      })
    },
    methods: {
      turnUpBigImg(index) {
        this.dialogImageUrl = "";
        this.dialogImageUrl = this.turnUpImgBase64Url[index];
        this.showBigImg = true;
      },
      //  图片删除
      turnUpDelImg(index) {
        this.imageBase64.splice(index, 1);
        this.turnUpImgBase64Url.splice(index, 1);
      },
      convertToBase64(event) {
        let that = this
        const file = event.target.files[0];
        if (file) {

          const reader = new FileReader();
          reader.onload = (e) => {
            that.imageBase64.push(e.target.result)
            console.log(this.imageBase64, 'ttteeeeeeeeeee')
            // this.imageBase64 = e.target.result;
          };
          reader.readAsDataURL(file);

        }
      },
      //     symbol2
      // symbol1
      // value1
      surehandle(scope) {
        scope.row.okId = ''
      },
      handlezhi(scope) {
        // value=scope.row.............replace(/[^0-9.]/g,'')
        if (scope.row.symbol2 && scope.row.value2 && scope.row.symbol1 && scope.row.value1) {
          if (scope.row.symbol2 == '<' && scope.row.symbol1 == '>') {
            if (scope.row.value < scope.row.value2 && scope.row.value > scope.row.value1) {
              scope.row.okId = 1
            } else {
              scope.row.okId = 2
            }
          } else if (scope.row.symbol2 == '<' && scope.row.symbol1 == '>=') {
            if (scope.row.value < scope.row.value2 && scope.row.value >= scope.row.value1) {
              scope.row.okId = 1
            } else {
              scope.row.okId = 2
            }
          } else if (scope.row.symbol2 == '<' && scope.row.symbol1 == '=') {
            // if(scope.row.value<scope.row.value2 & scope.row.value>=scope.row.value1){
            scope.row.okId = 2
            // }
          } else if (scope.row.symbol2 == '<=' && scope.row.symbol1 == '>') {
            if (scope.row.value <= scope.row.value2 && scope.row.value > scope.row.value1) {
              scope.row.okId = 1
            } else {
              scope.row.okId = 2
            }
          } else if (scope.row.symbol2 == '<=' && scope.row.symbol1 == '>=') {
            if (scope.row.value <= scope.row.value2 && scope.row.value >= scope.row.value1) {
              scope.row.okId = 1
            } else {
              scope.row.okId = 2
            }
          } else if (scope.row.symbol2 == '<=' && scope.row.symbol1 == '=') {
            if (scope.row.value <= scope.row.value2 && scope.row.value == scope.row.value1) {
              scope.row.okId = 1
            } else {
              scope.row.okId = 2
            }
          }
          this.standardstable.forEach((item, index) => {
            this.$set(this.standardstable, index, item);
          })
          this.$forceUpdate()
        } else if (scope.row.symbol2 && scope.row.value2 && (!scope.row.symbol1 || !scope.row.value1)) {
          if (scope.row.symbol2 == '<=') {
            if (scope.row.value <= scope.row.value2) {
              scope.row.okId = 1
            } else {
              scope.row.okId = 2
            }
          } else if (scope.row.symbol2 == '<') {
            if (scope.row.value < scope.row.value2) {
              scope.row.okId = 1
            } else {
              scope.row.okId = 2
            }
          }
          this.standardstable.forEach((item, index) => {
            this.$set(this.standardstable, index, item);
          })
          this.$forceUpdate()
        } else if ((!scope.row.symbol2 || !scope.row.value2) && scope.row.symbol1 && scope.row.value1) {

          if (scope.row.symbol1 == '>') {
            if (scope.row.value > scope.row.value1) {
              scope.row.okId = 1
            } else {
              scope.row.okId = 2
            }
          } else if (scope.row.symbol1 == '>=') {
            if (scope.row.value >= scope.row.value1) {
              scope.row.okId = 1
            } else {
              scope.row.okId = 2
            }
          } else if (scope.row.symbol1 == '=') {
            if (scope.row.value == scope.row.value1) {
              scope.row.okId = 1
            } else {
              scope.row.okId = 2
            }
          }
        }
        this.standardstable.forEach((item, index) => {
          this.$set(this.standardstable, index, item);
        })
      },
      selectday() {
      },
      // 本地删除
      locaDelTableRowtwo() {
        let that = this
        if (this.multipleSelection.length === 0 || this.multipleSelection === []) {
          this.$message.error("请勾选要操作的数据！");
          return false;
        }
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('public.del.content'),
          okText: this.$t('public.sure'),
          okType: 'danger',
          cancelText: this.$t('public.cancel'),
          onOk() {

            let selectRows = that.multipleSelection
            // selectRows.forEach((item) => {
            //   if (item.id === null || item.id === "" || item.id === 0) {
            //   } else {
            //     that.detailIds.push(
            //       Object.assign(
            //         {},
            //         {
            //           id: item.id,
            //           prdNo: item.prdNo,
            //           itm: item.itm,
            //           lastQty: item.lastQty,
            //           prdtId: item.prdtId,
            //         }
            //       )
            //     );
            //   }
            // });
            selectRows.forEach((item) => {
              // that.tableData.splice(that.tableData.indexOf(item), 1);
              that.standardstable.splice(that.standardstable.indexOf(item), 1)
            });
            // selectRows.forEach((item) => {
            //   that.tableData.splice(that.tableData.indexOf(item), 1);
            // });
          },
          onCancel() {
            // that.loading = false
            that.$message.info("已取消删除");
          }
        })

        // this.$confirm("此操作将删除, 是否继续?", "提示", {
        //   confirmButtonText: "确定",
        //   cancelButtonText: "取消",
        //   type: "warning",
        // })
        // .then(() => {
        // })
        // .catch(() => {
        //   this.$refs.multipleTable.clearSelection();
        //   this.$message.info("已取消删除");
        // });
      },
      addTableRowtwo() {

        this.tableAddtwo();
      },
      tableAddtwo() {
        for (let i = 0; i < 3; i++) {
          let obj = {
            okId: 1,
            byxmNo: '',
            byxmNm: '',
            bybwNo: '',
            bybwNm: '',
            byffNo: '',
            byffNm: '',
            confirm: '',
            byxmNr: '',
            symbol1: '',
            value1: '',
            symbol2: '',
            value2: '',
            // day:'',
            // dayByry:'',
            // month:'',
            // monthByry:'',
            // season:'',
            // seasonByry:'',
            // year:'',
            // yearByry:'',
            // days:'',
            // daysByry:'',
            rem: '',
            id: null,
            // halfYear:'',
            // halfYearByry:'',
            // qcItm:'',
            // name:'',
            // qcType:'2',
            // chkId:'',
            // qcTool:'',
            // qcToolName:'',
            // qcsysId:false,
            // qtyQcsys:'', 
          };
          // this.tableData.push(obj);
          // let arryData = []
          // this.tableData.push(arryData)
          this.standardstable.push(obj)
          // this.basicList[0].tableData.push(obj)
        }
      },
      // gettype () {
      //   this.loading = true
      //    sbtypequeryPage(
      //     Object.assign(
      //       {
      //         typeNo: this.queryParam.typeNo,
      //         name: this.queryParam.name,
      //         // salNo: this.queryParam.salNo,
      //         // dep: this.queryParam.dep,
      //         current: this.tablePage.currentPage,
      //         size: this.tablePage.pageSize,
      //       }
      //     )
      //   )
      //     .then(res => {
      //       this.tableData = res.data.records
      //       this.tablePage.total = res.data.total
      //       this.tablePage.currentPage = res.data.current
      //       this.loading = false
      //     })
      //     .catch(err => this.requestFailed(err))
      //     .finally(() => {
      //       this.loading = false
      //     })
      // },
      jumpnonconform() {
        this.$router.push({
          path: '/mes/nonconform/detail',
          query: {
            lsNo: this.entity.lsNo
          }
        })
      },
      jumpcomplet() {
        this.$router.push({
          path: '/mes/completinspect/check',
          query: {
            tiNo: this.entity.tiNo
          }
        })
      },
      stopScroll(evt) {
        evt = evt || window.event;
        if (evt.preventDefault) {
          // Firefox
          evt.preventDefault();
          evt.stopPropagation();
        } else {
          // IE
          evt.cancelBubble = true;
          evt.returnValue = false;
        }
        return false;
      },
      cosetiao() {

        let tab = ''
        if (localStorage.getItem('sebaoyangone')) {
          tab = '1'
        } else if (localStorage.getItem('sebaoyangtwo')) {
          tab = '2'
        } else if (localStorage.getItem('sebaoyangthree')) {
          tab = '3'
        }
        this.$router.push({
          path: '/device/equipmentby/devicemaintain/index',
          query: {
            tab: tab
          }
        })
      },
      handleDelVersion(row) {

        var list = []
        //  { id, fileName,bucketName }
        if (row.distinguish) {
          this.tableData = this.tableData.filter(i => i.id !== row.id)

        } else {
          sysfiledel(
            {
              id: row.id,
              // fileName:row.fileName,
              // bucketName:row.bucketName,
              // idList:list
            }
          )
            .then(res => {
              if (res.msg == 'success') {
                this.tableData = this.tableData.filter(i => i.id !== row.id)
                this.$message.success('删除成功')
              } else {
                this.$message.success('删除失败')
              }
              this.loading = false
              // this.getList()
            })
            .catch(err => {
              this.loading = false
              this.requestFailed(err)
            })

        }
        //  this.tableData.forEach((item, index) => {
        //     if(item.id === id){
        //       item.contraFileUrlList.forEach((item2, index2) => {
        //       list.push(item2.id)
        //       })
        //     }
        //  })
      },
      handleAddVersion() {
        this.$refs.uploadFile.create({ title: '上传' }, 'oooooooooo')
        // this.$refs.AddVersion.create(true, true)
      },
      deldata() {
        const that = this
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('identifier.delcont'),
          okText: this.$t('public.sure'),
          okType: 'warn',
          cancelText: this.$t('public.cancel'),
          onOk() {
            // that.loading = true
            aldBydel({
              id: that.entity.id,
              byId: that.entity.byId
            }).then((res) => {
              if (res.code === 0) {

                if (res.data == true) {
                  that.cosetiao()
                  that.$message.success('删除成功')
                } else {
                  that.$message.error('删除失败')
                }
              }
            }).catch(err => that.requestFailed(err))
              .finally(() => {
                that.loading = false
              })
          },
          onCancel() { }
        })


      },
      // 渲染组件
      choose(obj) {
        if (obj.obj.clear) {
          if (obj.obj.clear == 1) {
            this.entity.dep = ''
            this.entity.depName = ''
            return
          }
        }
        var map = {}
        // if (obj.obj.name === 'upSalNo') {
        //   this.salName = obj.obj.data.name
        //   map[obj.obj.name] = obj.obj.data.salNo
        // }
        // if (obj.obj.name === 'salNo') {
        //   this.salName = obj.obj.data.name
        //   map[obj.obj.name] = obj.obj.data.salNo
        // }
        if (obj.obj.name === 'dep') {

          this.flag = true
          this.entity.depName = obj.obj.data.depName
          this.entity.dep = obj.obj.data.dep
          map[obj.obj.name] = obj.obj.data.dep
        }
        // this.form.setFieldsValue(map)
      },
      choosebybwNo(obj, scope) {
        console.log(obj, scope, 'ggggggggggggg')
        if (obj.obj.clear) {
          if (obj.obj.clear == 1) {
            this.standardstable.forEach((item, index) => {
              if (scope.$index == index) {
                item.bybwNo = ''
                item.bybwNm = ''
                item.bybwId = ''

              }
              this.$set(this.standardstable, index, item);
            })
            return
          }
        }
        var map = {}
        if (obj.obj.name === 'bybwNo') {
          this.standardstable.forEach((item, index) => {
            if (scope.$index == index) {
              item.bybwNo = obj.obj.data.bybwNo
              item.bybwNm = obj.obj.data.bybwNm
              item.bybwId = obj.obj.data.id
            }
            this.$set(this.standardstable, index, item);
          })
          // this.entity.byxmNo= obj.obj.data.byxmNo
          // this.entity.byxmNm= obj.obj.data.byxmNm
          // map[obj.obj.name] = obj.obj.data.byxmNo
        }
      },
      choosebyxmNo(obj, scope) {
        console.log(obj, scope, 'ggggggggggggg')
        if (obj.obj.clear) {
          if (obj.obj.clear == 1) {
            this.standardstable.forEach((item, index) => {
              if (scope.$index == index) {
                item.byxmNo = ''
                item.byxmNm = ''
                item.byxmId = ''
              }
              this.$set(this.standardstable, index, item);
            })
            return
          }
        }
        var map = {}
        if (obj.obj.name === 'byxmNo') {
          this.standardstable.forEach((item, index) => {
            if (scope.$index == index) {
              item.byxmNo = obj.obj.data.byxmNo
              item.byxmNm = obj.obj.data.byxmNm
              item.byxmId = obj.obj.data.id
            }
            this.$set(this.standardstable, index, item);
          })

          this.flag = true
          // this.entity.byxmNo= obj.obj.data.byxmNo
          // this.entity.byxmNm= obj.obj.data.byxmNm
          // map[obj.obj.name] = obj.obj.data.byxmNo
        }
      },
      chooseygNotian(obj, scope) {
        if (obj.obj.clear) {
          if (obj.obj.clear == 1) {
            this.standardstable.forEach((item, index) => {
              if (scope.$index == index) {
                item.ygNo = ''
                item.ygName = ''
                item.daysByry = ''
              }
              this.$set(this.standardstable, index, item);
            })
            return
          }
        }
        var map = {}
        if (obj.obj.name === 'ygNotwo') {
          this.standardstable.forEach((item, index) => {
            if (scope.$index == index) {
              item.ygNo = obj.obj.data.ygNo
              item.ygName = obj.obj.data.ygName
              item.daysByry = obj.obj.data.ygName
            }
            this.$set(this.standardstable, index, item);
          })
          this.flag = true
          this.entity.ygNo = obj.obj.data.ygNo
          this.entity.ygName = obj.obj.data.ygName
          map[obj.obj.name] = obj.obj.data.ygNo
        }
      },

      chooseygNonian(obj, scope) {
        if (obj.obj.clear) {
          if (obj.obj.clear == 1) {
            this.standardstable.forEach((item, index) => {
              if (scope.$index == index) {
                item.ygNo = ''
                item.ygName = ''
                item.yearByry = ''
              }
              this.$set(this.standardstable, index, item);
            })
            return
          }
        }
        var map = {}
        if (obj.obj.name === 'ygNotwo') {
          this.standardstable.forEach((item, index) => {
            if (scope.$index == index) {
              item.ygNo = obj.obj.data.ygNo
              item.ygName = obj.obj.data.ygName
              item.yearByry = obj.obj.data.ygName
            }
            this.$set(this.standardstable, index, item);
          })
          this.flag = true
          this.entity.ygNo = obj.obj.data.ygNo
          this.entity.ygName = obj.obj.data.ygName
          map[obj.obj.name] = obj.obj.data.ygNo
        }
      },

      chooseygNoban(obj, scope) {
        if (obj.obj.clear) {
          if (obj.obj.clear == 1) {
            this.standardstable.forEach((item, index) => {
              if (scope.$index == index) {
                item.ygNo = ''
                item.ygName = ''
                item.halfYearByry = ''
              }
              this.$set(this.standardstable, index, item);
            })
            return
          }
        }
        var map = {}
        if (obj.obj.name === 'ygNotwo') {
          this.standardstable.forEach((item, index) => {
            if (scope.$index == index) {
              item.ygNo = obj.obj.data.ygNo
              item.ygName = obj.obj.data.ygName
              item.halfYearByry = obj.obj.data.ygName
            }
            this.$set(this.standardstable, index, item);
          })
          this.flag = true
          this.entity.ygNo = obj.obj.data.ygNo
          this.entity.ygName = obj.obj.data.ygName
          map[obj.obj.name] = obj.obj.data.ygNo
        }
      },
      chooseygNoji(obj, scope) {
        if (obj.obj.clear) {
          if (obj.obj.clear == 1) {
            this.standardstable.forEach((item, index) => {
              if (scope.$index == index) {
                item.ygNo = ''
                item.ygName = ''
                item.seasonByry = ''
              }
              this.$set(this.standardstable, index, item);
            })
            return
          }
        }
        var map = {}
        if (obj.obj.name === 'ygNotwo') {
          this.standardstable.forEach((item, index) => {
            if (scope.$index == index) {
              item.ygNo = obj.obj.data.ygNo
              item.ygName = obj.obj.data.ygName
              item.seasonByry = obj.obj.data.ygName
            }
            this.$set(this.standardstable, index, item);
          })
          this.flag = true
          this.entity.ygNo = obj.obj.data.ygNo
          this.entity.ygName = obj.obj.data.ygName
          map[obj.obj.name] = obj.obj.data.ygNo
        }
      },
      chooseygNoyue(obj, scope) {
        if (obj.obj.clear) {
          if (obj.obj.clear == 1) {
            this.standardstable.forEach((item, index) => {
              if (scope.$index == index) {
                item.ygNo = ''
                item.ygName = ''
                item.monthByry = ''
              }
              this.$set(this.standardstable, index, item);
            })
            return
          }
        }
        var map = {}
        if (obj.obj.name === 'ygNotwo') {
          this.standardstable.forEach((item, index) => {
            if (scope.$index == index) {
              item.ygNo = obj.obj.data.ygNo
              item.ygName = obj.obj.data.ygName
              item.monthByry = obj.obj.data.ygName
            }
            this.$set(this.standardstable, index, item);
          })
          this.flag = true
          this.entity.ygNo = obj.obj.data.ygNo
          this.entity.ygName = obj.obj.data.ygName
          map[obj.obj.name] = obj.obj.data.ygNo
        }
      },
      chooseygNori(obj, scope) {
        if (obj.obj.clear) {
          if (obj.obj.clear == 1) {
            this.standardstable.forEach((item, index) => {
              if (scope.$index == index) {
                item.ygNo = ''
                item.ygName = ''
                item.dayByry = ''
              }
              this.$set(this.standardstable, index, item);
            })
            return
          }
        }
        var map = {}
        if (obj.obj.name === 'ygNotwo') {
          this.standardstable.forEach((item, index) => {
            if (scope.$index == index) {
              item.ygNo = obj.obj.data.ygNo
              item.ygName = obj.obj.data.ygName
              item.dayByry = obj.obj.data.ygName
            }
            this.$set(this.standardstable, index, item);
          })
          this.flag = true
          this.entity.ygNo = obj.obj.data.ygNo
          this.entity.ygName = obj.obj.data.ygName
          map[obj.obj.name] = obj.obj.data.ygNo
        }
      },
      // chooseygNotwo(obj,scope) {
      //   if(obj.obj.clear){
      //     if(obj.obj.clear == 1){
      //       this.standardstable.forEach((item,index)=>{
      //         if(scope.$index == index){
      //             item.ygNo = ''
      //               item.ygName =''
      //         }
      //         this.$set(this.standardstable, index, item);
      //       })
      //       return
      //     }
      //   }
      //   var map = {}
      //   if (obj.obj.name === 'ygNotwo') {
      //     this.standardstable.forEach((item,index)=>{
      //      if(scope.$index == index){
      //          item.ygNo = obj.obj.data.ygNo
      //            item.ygName = obj.obj.data.ygName
      //       }
      //       this.$set(this.standardstable, index, item);
      //     })
      //     this.flag = true
      //     this.entity.ygNo= obj.obj.data.ygNo
      //     this.entity.ygName= obj.obj.data.ygName
      //     map[obj.obj.name] = obj.obj.data.ygNo
      //   }
      // },
      choosebyffNo(obj, scope) {
        console.log(this.standardstable, scope)
        if (obj.obj.clear) {
          if (obj.obj.clear == 1) {
            this.standardstable.forEach((item, index) => {
              if (scope.$index == index) {
                item.byffNo = ''
                item.byffNm = ''
                item.byffId = ''
              }
              this.$set(this.standardstable, index, item);
            })
            return
          }
        }
        var map = {}
        if (obj.obj.name === 'byffNo') {
          this.standardstable.forEach((item, index) => {
            if (scope.$index == index) {
              item.byffNo = obj.obj.data.byffNo
              item.byffNm = obj.obj.data.byffNm
              item.byffId = obj.obj.data.id
            }
            this.$set(this.standardstable, index, item);
          })
          this.flag = true
          this.entity.byffNo = obj.obj.data.byffNo
          this.entity.byffNm = obj.obj.data.byffNm
          map[obj.obj.name] = obj.obj.data.byffNo
        }
      },
      typeupdata(val1, val2) {
        this.$nextTick(() => {

          this.entity.typeNo = val1
          this.entity.typeNm = val2
          this.datatypeNo = this.entity.typeNo + '-' + this.entity.typeNm
          this.$forceUpdate()
        })
      },
      choosesbNo(obj) {
        if (obj.obj.clear) {
          if (obj.obj.clear == 1) {
            this.entity.sbNo = ''
            this.entity.sbNm = ''
            this.entity.name = ''
            return
          }
        }
        var map = {}
        if (obj.obj.name === 'sbNo') {
          this.flag = true
          this.entity.sbNo = obj.obj.data.sbNo
          this.entity.name = obj.obj.data.name
          this.entity.sbNm = obj.obj.data.name
          if (obj.obj.data.typeNo) {
            this.typeupdata(obj.obj.data.typeNo, obj.obj.data.typeName)
            // this.$nextTick(() => {
            // setTimeout(() => {
            //   this.entity.typeNo = obj.obj.data.typeNo
            //   this.entity.typeNm = obj.obj.data.typeName
            //   this.datatypeNo = this.entity.typeNo + '-' + this.entity.typeNm
            //   this.$forceUpdate()
            // }, 100);
            // })

          } else {
            this.entity.typeNo = ''
            this.entity.typeNm = ''
            this.datatypeNo = ''
            this.$forceUpdate()
            this.$refs.selectListtypeNo.clear()
          }
          // map[obj.obj.name] = obj.obj.data.sbNo
        }
      },
      choosetypeNo(obj) {
        if (obj.obj.clear) {
          if (obj.obj.clear == 1) {
            this.entity.typeNo = ''
            this.entity.typeNm = ''
            return
          }
        }
        var map = {}
        if (obj.obj.name === 'typeNo') {
          this.flag = true
          this.entity.typeNo = obj.obj.data.typeNo
          this.entity.typeNm = obj.obj.data.name

          console.log(this.datasbNo, this.entity.sbNo, 'whhhhhhhhhhhhhhhh')
          // map[obj.obj.name] = obj.obj.data.typeNo
          this.entity.sbNo = ' '
          this.entity.sbNm = ' '
          this.entity.name = ' '
          this.datasbNo = ' '
          this.$refs.selectListsbNo.clear()
          // this.choosesbNo({
          //   obj:{
          //     clear:1
          //   }
          // })
          this.$forceUpdate()
        }
      },
      choosebydjNo(obj) {
        if (obj.obj.clear) {
          if (obj.obj.clear == 1) {
            this.entity.bydjNo = ''
            this.entity.bydjNm = ''
            this.entity.bydjId = ''
            return
          }
        }
        var map = {}
        // if (obj.obj.name === 'upSalNo') {
        //   this.salName = obj.obj.data.name
        //   map[obj.obj.name] = obj.obj.data.salNo
        // }
        // if (obj.obj.name === 'salNo') {
        //   this.salName = obj.obj.data.name
        //   map[obj.obj.name] = obj.obj.data.salNo
        // }
        if (obj.obj.name === 'bydjNo') {
          this.flag = true
          this.entity.bydjNm = obj.obj.data.bydjNm
          this.entity.bydjNo = obj.obj.data.bydjNo
          this.entity.bydjId = obj.obj.data.bydjId
          // this.entity.bydjNm = obj.obj.data.cusNo1
          // this.entity.typeNo = obj.obj.data.typeNo
          map[obj.obj.name] = obj.obj.data.bydjNo
        }
        // this.form.setFieldsValue(map)
      },

      changeqtyLost(val) {
        if (val.row.qtyLost < 0) {
          this.$message.warning('不合格量不能为负')
          val.row.qtyLost = 0
        }
        this.entity.qtyLost = 0

        let getQtys = 0;
        if (!this.entity.qtyLost) {
          this.entity.qtyLost = 0
        }
        this.testData.forEach(i => {
          if (i.spcNo) {
            this.entity.qtyLost += +i.qtyLost;
          }
        })
        if (this.entity.qtyLost > 0) {
          this.entity.qcId = 'F'
          if (+this.entity.qty - +this.entity.qtyLost < 0) {

            this.$message.info('不合格量超出')
            val.row.qtyLost = ''
            this.entity.qtyLost = 0
            this.testData.forEach(i => {
              if (i.spcNo) {
                this.entity.qtyLost += +i.qtyLost;
              }
            })
          } else {
            this.entity.qtyOk = +this.entity.qty - +this.entity.qtyLost
          }
        } else {
          this.entity.qcId = 'T'
        }


        //  if(this.entity.qtyOk < 0){
        //   this.entity.qtyOk = 0
        //  }

        // this.entity.qtyLost += +val;
        this.$forceUpdate()

        // this.testData.forEach((item) => {
        //   if (item.qty) {
        //     getQtys += +item.qty;

      },
      // 货品关键字搜索回显数据
      getGoodsPopover({
        spcNo,
        name,
        prd3No,
        spc,
        qtyNow,
        qtyLrn,
        ut,
        price,
        qty,
        rtoTax,
        amtnNet,
        tax,
        amt,
        itm,
        id,
      }) {
        let newRow = {
          spcNo,
          spcName: name,
          prdNo: prd3No,
          spc,
          qtyNow,
          qtyLrn,
          ut,
          price,
          qty: "1",
          lastQty: 0,
          rtoTax: rtoTax,
          amtnNet: "",
          tax: "",
          amt: "",
          itm: -1,
          prdtId: id,
          id: null,
          qtyRtn: null,
        };
        newRow.show = false;
        this.$set(this.testData, this.pickerIndex, newRow);
        this.isShowPopVel = false;
        // let getQtys = 0;
        // this.testData.forEach((item) => {
        //   if (item.qty) {
        //     getQtys += +item.qty;
        //     this.qtys = getQtys;
        //   }
        // });
        this.visible = false;
        for (let i = 0; i < this.testData.length; i++) {//for循环遍历每个元素，每遍历一个元素就是arr[i]
          let num = 0//定义一个num统计arr[i]出现的次数，
          for (let j = 0; j < this.testData.length; j++) { //根据这个arr[i]做一次循环遍历这个数组，
            if (this.testData[i].spcNo) {
              if (this.testData[i].spcNo == this.testData[j].spcNo) {//arr[i]出现一次就会+1
                num++
              }
            }
          }
          if (num > 1) {
            this.testData[this.pickerIndex].spcNo = ''
            this.testData[this.pickerIndex].spcName = ''
            this.$message.warning('原因代号不能重复')
            this.$forceUpdate()
          }
        }
      },
      prdt3leave() {
        setTimeout(() => {
          this.visible = false;
        }, 100);
      },
      pickerSearch() {
        // {
        //     ...this.entity,
        //     sysFiles:this.sysFiles
        //   }
        spcLstpage(
          {
            spcNo: this.queryKeyword,
            name: this.queryKeyword
          }

        ).then((res) => {
          if (res.code === 0) {

            this.pickerList = res.data.records
          }
        }).catch(err => this.requestFailed(err))
        //    .finally(() => {
        //         this.loading = false
        // })
      },
      // input聚焦
      focus(index, row, e) {
        this.pickerSearch();
        this.visible = true;
        // this.isSaveColor = "danger";
        this.pickerIndex = index;
        let getTop = e.target.getBoundingClientRect().top + 35;

        if (e.target.getBoundingClientRect().top - 380 < 0) {
          this.objStyle.top = getTop.toString() + "px";
        } else {
          this.objStyle.top = (e.target.getBoundingClientRect().top - 390).toString() + "px";
        }
        this.objStyle.left = e.target.getBoundingClientRect().left + "px";



        // let getTop = e.target.getBoundingClientRect().top + 35;
        // this.objStyle.left = e.target.getBoundingClientRect().left + "px";
        // this.objStyle.top = getTop.toString() + "px";


      },
      blurNameInput() {
        this.visible = false;
        setTimeout(() => {
          this.isShowPopVel = false;
        }, 100);
      },
      inputNameInput(val) {
        if (this.timer) {
          clearTimeout(this.timer);
        }
        this.timer = setTimeout(() => {
          this.queryKeyword = val;
          this.goods_currentPage = 1;
          this.pickerSearch();
        }, 500);
      },
      handleSelectionChangereason(rows) {
        this.multipleSelection = rows;
      },
      handleSelectionChange(rows) {
        this.multipleSelection = rows;
      },
      // 添加行
      addTableRow() {
        this.tableAdd();
      },
      // 本地删除
      locaDelTableRow() {
        let that = this

        if (this.multipleSelection.length === 0 || this.multipleSelection === []) {
          this.$message.error("请勾选要操作的数据！");
          return false;
        }
        this.$confirm({
          title: this.$t('public.del.title'),
          content: this.$t('public.del.content'),
          okText: this.$t('public.sure'),
          okType: 'danger',
          cancelText: this.$t('public.cancel'),
          onOk() {


            let selectRows = that.multipleSelection
            selectRows.forEach((item) => {
              if (item.id === null || item.id === "" || item.id === 0) {
              } else {
                that.detailIds.push(
                  Object.assign(
                    {},
                    {
                      id: item.id,
                      prdNo: item.prdNo,
                      itm: item.itm,
                      lastQty: item.lastQty,
                      prdtId: item.prdtId,
                    }
                  )
                );
              }
            });
            that.entity.qtyLost = 0
            selectRows.forEach((item) => {
              that.testData.splice(that.testData.indexOf(item), 1);
            });
            that.testData.forEach(i => {
              that.entity.qtyLost += +i.qtyLost;
            })
            if (that.entity.qtyLost > 0) {
              that.entity.qcId = 'F'
            } else {
              that.entity.qcId = 'T'
            }
            that.entity.qtyOk = +that.entity.qty - +that.entity.qtyLost
          },
          onCancel() {

            // that.loading = false
            that.$refs.multipleTable.clearSelection();
            that.$message.info("已取消删除");
          }
        })

        // this.$confirm("此操作将删除, 是否继续?", "提示", {
        //   confirmButtonText: "确定",
        //   cancelButtonText: "取消",
        //   type: "warning",
        // })
        // .then(() => {
        // })
        // .catch(() => {
        //   this.$refs.multipleTable.clearSelection();
        //   this.$message.info("已取消删除");
        // });
      },
      // 表格初始化，往数组里面添加50个对象
      tableAdd() {
        for (let i = 0; i < 3; i++) {
          let obj = {
            spcNo: "",
            spcName: "",
            qtyLost: "",
            name: "", // 品名
            id: null,
          };
          this.testData.push(obj);
        }
      },
      //行点击
      handRowClick(row, column, event) {
        var index;
        this.testData.map((c, i) => {
          if (c == row) {
            index = i;
          }
        });
        this.$set(this.testData, index, row);
        event.stopPropagation();
      },
      // this.entity.chktyId: this.entity.chktyId ? 'T' : 'F'
      onChangeinspect(e) {

        // this.checked=e
        //  this.entity.qcId = e.target.checked
      },
      onChange(checked) {
        this.checked = checked;

      },
      onChangeclosecase(e) {


        this.entity.chkKnd = e.target.checked
        this.$forceUpdate()
      },
      onChangeLaboratory(e) {
        this.entity.sys = e.target.checked
      },
      seeFile(obj, formD) {

        this.sysFiles = obj
        console.log(this.sysFiles, '666666666666')
        this.sysFiles.forEach((item, index) => {
          this.tableData.push(item)
        })
        console.log(this.sysFiles, this.tableData, '223344ghhhhhhhhhhhhhh')
        // this.objpic = obj
        // this.formD = formD
        // this.tableData.forEach((item, index) => {
        //   if (this.formD.wyID === item.wyID) {
        //     if (item.imageUrlList == null) {
        //       item.imageUrlList = []
        //       this.objpic.forEach((item2, index2) => {
        //         item.imageUrlList.push(item2)
        //       })
        //     } else {
        //       this.objpic.forEach((item2, index2) => {
        //         item.imageUrlList.push(item2)
        //       })
        //     }
        //   }
        // })
      },
      weixiu() {
        this.weixiuvisible = true
      },
      otngetData() {
        bxsubmit({
          stopId: this.stopId,
          faultRem: this.faultRem,
          id: null,
          sourceType: 2,
          sourceId: this.entity.byId,
          sourceNo: this.entity.byNo,
          sbNo: this.entity.sbNo,
          sbId: this.entity.sbId,
          name: this.entity.name,
          gzfsDd: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
          byYgNo: this.entity.byYgNo,
          byYgName: this.entity.byYgName,
          ygName: this.$store.state.user.info.name,
          ygNo: this.$store.state.user.info.username
        }).then((res) => {
          if (res.code == 0) {
            if (localStorage.getItem('sebaoyangone')) {
              this.ybyjin = false
              this.entity.id = localStorage.getItem('sebaoyangone')
              this.getListtreat()
              this.isEdit = true
              this.yibaoyang = false
            } else if (localStorage.getItem('sebaoyangtwo')) {
              this.ybyjin = false
              this.entity.id = localStorage.getItem('sebaoyangtwo')
              this.getListtreat()
              this.isEdit = true
              this.yibaoyang = false
              // this.isEdit = true sebaoyangthree
              // moment(new Date()).format('YYYY-MM-DD HH:MM:SS'),
              // this.entity.startDd = this.$moment(new Date()).format('YYYY-MM-DD');
            } else if (localStorage.getItem('sebaoyangthree')) {
              this.ybyjin = true
              this.entity.id = localStorage.getItem('sebaoyangthree')
              this.getListtreat()
              this.yibaoyang = true
              this.isEdit = true
            }
            this.$message.success(res.msg)

            // if (res.msg === 'success') {
            // this.entity.id = res.data.id
            // this.entity.byNo = res.data.byNo
            // this.entity.byId = res.data.byId

            // } else {
            //   this.$message.error('保存失败')
            //   return
            // }
          }
        }).catch(err => {

          this.requestFailed(err)
        })
          .finally(() => {

            this.loading = false
          })

        if (this.multiplereason.length) {
          this.reasondata = this.multiplereason
        }
        this.weixiuvisible = false
      },
      savezan() {
        // var startTime = new Date(this.entity.startDd);
        // var endTime = new Date(this.entity.endDd);
        // if(this.entity.startDd&&this.entity.endDd){
        //   if (startTime > endTime) {
        //     this.$message.warning('开始时间不能大于结束时间！');
        //     return
        //   }
        // }
        // this.$refs.addEntityForm.validate(valid => {
        //   if (!valid) {
        //     return
        //   }else{
        let symflag = true
        let symflag2 = true
        this.standardstable = this.standardstable.filter(i => i.byxmNo !== '' & i.byxmNo !== null)
        // scope.row.confirm != 2 & scope.row.confirm != 4
        for (let i = 0; i < this.standardstable.length; i++) {
          if (this.standardstable[i].confirm == 2 || this.standardstable[i].confirm == 4) {
            if (this.standardstable[i].symbol1) {
              if (this.standardstable[i].value1) {
              } else {
                symflag = false
                break
              }
            }
            if (this.standardstable[i].symbol2) {
              if (this.standardstable[i].value2) {
              } else {
                symflag = false
                break
              }
            }
            if (this.standardstable[i].value) {
            } else {
              symflag = false
            }
          } else {
            if (this.standardstable[i].okId) { } else {
              symflag2 = false
            }
          }
        }
        if (this.standardstable.length) {
          // if(symflag && symflag2){
          let arrystandardstable = JSON.parse(JSON.stringify(this.standardstable))
          let validflag
          // this.$refs.addEntityForm.validate(valid => {
          //   validflag = valid
          //     if (!valid) {
          //       this.$message.warning('请选择部门')
          //       return
          //     }
          // })
          // if(validflag){
          //  this.entity.chkKnd = this.entity.chkKnd ? 'T' : 'F'
          //  this.entity.sys = this. entity.sys ? 'T' : 'F'
          // this.tableData.forEach(item=>{
          //   if(item.distinguish){
          //     delete item.distinguish
          //   }
          // })

          onBytempSave({
            ...this.entity,
            optType: 2,
            bodyVoList: arrystandardstable,
            picBs64List: this.imageBase64,
          }).then((res) => {

            if (res.code == 0) {

              // if (res.msg === 'success') {
              this.entity.id = res.data.id
              this.entity.byNo = res.data.byNo
              this.entity.byId = res.data.byId
              this.$router.push({
                path: '/device/equipmentby/devicemaintain/index',
                query: {
                  tab: '2'
                }
              })
              // this.$router.push({name:'devicemaintain', params:{tab: '2'}}) 
              this.$message.success(res.msg)
              // } else {
              //   this.$message.error('保存失败')
              //   return
              // }
            }
          }).catch(err => {

            this.requestFailed(err)
          })
            .finally(() => {

              this.loading = false
            })

          // }else{
          //   if(!symflag){
          //     this.$message.warning('请填写表格中值')
          //   }else if(!symflag2){
          //     this.$message.warning('请选择表格OK或者NG')
          //   }
          // }
        } else {
          this.$message.warning('请填写表身')
        }

        //    }


        // })
      },
      save() {
        var startTime
        var endTime
        if (this.entity.startDd) {
          startTime = new Date(this.entity.startDd);
        }
        if (this.entity.endDd) {
          endTime = new Date(this.entity.endDd)
        }
        if (this.entity.startDd && this.entity.endDd) {
          if (startTime > endTime) {
            this.$message.warning('开始时间不能大于结束时间！');
            return
          }
        }
        this.$refs.addEntityForm.validate(valid => {
          if (this.entity.sbNo) {
          } else {
            this.$message.warning('请选择设备代号')
            return
          }
          if (!valid) {
            return
          } else {

            let symflag = true
            let symflag2 = true
            console.log(this.standardstable, 'gggggggggggggg')
            // scope.row.confirm != 2 & scope.row.confirm != 4
            for (let i = 0; i < this.standardstable.length; i++) {
              if (this.standardstable[i].confirm == 2 || this.standardstable[i].confirm == 4) {
                if (this.standardstable[i].symbol1) {
                  if (this.standardstable[i].value1 === null || this.standardstable[i].value1 === '') {
                    symflag = false
                    break
                  }
                }
                if (this.standardstable[i].symbol2) {
                  if (this.standardstable[i].value2 === null || this.standardstable[i].value2 === '') {
                    symflag = false
                    break
                  }
                }
                if (this.standardstable[i].value === null || this.standardstable[i].value === '') {
                  symflag = false
                  break
                }
              } else {
                if (this.standardstable[i].okId) { } else {
                  symflag2 = false
                }
              }
            }

            this.standardstable = this.standardstable.filter(i => i.byxmNo !== '' & i.byxmNo !== null)
            if (this.standardstable.length) {
              if (symflag && symflag2) {
                let arrystandardstable = JSON.parse(JSON.stringify(this.standardstable))
                let validflag
                // this.$refs.addEntityForm.validate(valid => {
                //   validflag = valid
                //     if (!valid) {
                //       this.$message.warning('请选择部门')
                //       return
                //     }
                // })
                // if(validflag){
                //  this.entity.chkKnd = this.entity.chkKnd ? 'T' : 'F'
                //  this.entity.sys = this. entity.sys ? 'T' : 'F'
                this.tableData.forEach(item => {
                  if (item.distinguish) {
                    delete item.distinguish
                  }
                })
                bysubmit({
                  ...this.entity,
                  optType: 2,
                  //   sys: this.entity.sys ? 'T' : 'F',
                  // chkKnd:this.entity.chkKnd ? 'T' : 'F',
                  bodyVoList: arrystandardstable,
                  picBs64List: this.imageBase64,
                }).then((res) => {
                  if (res.code === 0) {
                    // if (res.msg == '提交成功!') {
                    this.entity.id = res.data.id
                    this.entity.byNo = res.data.byNo
                    this.entity.byId = res.data.byId
                    this.$router.push({
                      path: '/device/equipmentby/devicemaintain/index',
                      query: {
                        tab: '3'
                      }
                    })
                    this.$message.success(res.msg)

                  }
                }).catch(err => this.requestFailed(err))
                  .finally(() => {
                    this.loading = false
                  })

              } else {
                if (!symflag) {
                  this.$message.warning('请填写表格中值')
                } else if (!symflag2) {
                  this.$message.warning('请选择表格OK或者NG')
                }
              }
            } else {
              this.$message.warning('请填写表身')
            }

          }


        })
      },
      selectChangeEvent({ checked, records, reserves, row, rowIndex }) {

        this.multipleSelectiontwo = records
      },
      selectChangeAll({ records, checked }) {

        this.multipleSelectiontwo = records
      },
      editclick() {
        this.isEdit = true
      },
      closeclick() {
        this.isEdit = false
      },
      reset() {
        this.entity = {}
      },
      // seepic (index) {
      //   let arry = []
      //   arry.push(this.tableData[index])

      //   this.$refs.ModalPic.create({ title: '查看' }, arry)
      // },
      uploadpic(row) {
        this.$refs.uploadFile.create({ title: '上传' }, row)
        // this.$refs.Upload.create(
        //   {
        //     title: '上传'
        //   },
        // )
        // this.$refs.Upload.create({ title: '上传' }, row)
      },
      // check(){
      //   this.$router.push('')
      // },
      handleChangereson(val) {

        this.multiplereason = val
      },
      cancelGetData() {
        this.visible = false
      },

      reasonclick(val) {
        this.visible = true
      },
      handleSelectionChange(val) {
        this.multipleSelection = val
      },
      handleSelectionChangetwo(val) {
        this.multipleSelectiontwo = val
      },
      handleTabClick(key) {
        if (key == '1') {
          this.getList()
        } else if (key == '2') {
          this.getListtwo()
        }
      },
      onChange(data, dateString) {

        this.queryParam.date = dateString

      },
      depme(row) {
        datadepPage(
          Object.assign({
            current: 1,
            size: 10,
            deptCode: row
          })
        ).then(response => {
          this.entity.depName = response.data.records[0].name
          this.data = this.entity.dep + '-' + this.entity.depName
        }).catch(err => this.requestFailed(err))
      },
      getListtreat() {
        this.sbwxz = false
        this.loading = true
        let queryTab = ''
        if (localStorage.getItem('sebaoyangone')) {
          queryTab = 1
        } else if (localStorage.getItem('sebaoyangthree')) {
          queryTab = 3
        } else if (localStorage.getItem('sebaoyangtwo')) {
          queryTab = 2
        }
        byqueryById(
          Object.assign({
            // total: this.tablePage.currentPage,
            // current: this.tablePage.currentPage,
            // size: this.tablePage.pageSize,
            // staDd:this.queryParam.staDd,
            // endDd:this.queryParam.endDd,
            queryTab: queryTab,
            id: this.entity.id,
          })
        ).then(response => {
          this.loading = false
          if (response.data) {
            this.entity = response.data
            if (this.entity.byYgName) {
            } else {
              this.entity.byYgName = this.$store.state.user.info.name
              this.entity.byYgNo = this.$store.state.user.info.username
            }
            if (this.entity.startDd) {
            } else {
              this.entity.startDd = ''
              this.entity.startDd = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              // moment(new Date()).format('YYYY-MM-DD HH:MM:SS')
              console.log(this.entity.startDd, '11gggggggggggg')
            }
            if (this.entity.bodyVoList.length) {
              if (this.entity.bodyVoList[0].confirm == 1 || this.entity.bodyVoList[0].confirm == 2) {
                this.entitybodyVoList = true
              } else {
                this.entitybodyVoList = true
              }
            } else {
              this.entitybodyVoList = false
            }
            if (this.entity.stopId) {
              this.entity.stopId = this.entity.stopId + ''
            }
            if (this.entity.bgpjId) {
              this.entity.bgpjId = this.entity.bgpjId + ''
            }
            if (this.entity.stopId == 0) {
              this.entity.stopId = this.entity.stopId + ''
            }
            if (this.entity.bgpjId == 0) {
              this.entity.bgpjId = this.entity.bgpjId + ''
            }
            if (this.yibaoyang) {
              if (this.entity.canDel == 0) {
                this.nocanDel = true
              } else {
                this.nocanDel = false
              }
            }
            // if(this.entity.stopId !=null && this.entity.stopId !=''){
            //   this.entity.stopId = this.entity.stopId + ''
            // }
            // if(this.entity.bgpjId !=null && this.entity.bgpjId !=''){
            //   this.entity.bgpjId = this.entity.bgpjId + ''
            // }
            this.entity.ygNo = this.$store.state.user.info.username
            this.entity.ygName = this.$store.state.user.info.name
            if (this.entity.byNo) { } else {
              this.entity.stopId = '0'
              this.entity.bgpjId = '0'
            }
            this.entity.bodyVoList.forEach(item => {
              if (item.confirm == 1 || item.confirm == 3) {
                if (item.okId == -1) {
                  item.okId = 1
                }
              }
            })

            if (this.entity.bydjNo) {
              this.databydjNo = this.entity.bydjNo + '-' + this.entity.bydjNm
            }
            if (this.entity.typeNo) {
              this.datatypeNo = this.entity.typeNo + '-' + this.entity.typeNm
            }
            if (this.entity.sbNo) {
              this.datasbNo = this.entity.sbNo + '-' + this.entity.name
            }
            if (this.entity.dep) {
              this.data = this.entity.dep + '-' + this.entity.depName
            }


            // if(this.entity.dep){
            // this.depme(this.entity.dep)
            // }
            // if(this.entity.bydjNo){
            //   this.entity.databydjNo = this.entity.bydjNo + this.entity.bydjNm
            // }
            if (response.data.bodyVoList.length) {
              this.standardstable = response.data.bodyVoList
            } else {
              this.standardstable = []
            }
            if (response.data.picBs64List) {
              if (response.data.picBs64List.length) {
                this.imageBase64 = response.data.picBs64List
              } else {
                this.tableData = []
              }
            }
          } else {
            this.sbwxz = true
          }
          // this.tablePage.total = response.data.total
          // this.tablePage.currentPage = response.data.current
        }).catch(err => {
          if (err == '设备在修中, 暂不能保养!') {
            this.sbwxz = true
          }
          this.requestFailed(err)
        })
          .finally(() => {
            this.loading = false
          })
      },
      getList() {
        this.loading = true
        qcwgyjInfo(
          Object.assign({
            // total: this.tablePage.currentPage,
            // current: this.tablePage.currentPage,
            // size: this.tablePage.pageSize,
            // staDd:this.queryParam.staDd,
            // endDd:this.queryParam.endDd,
            tyNo: this.entity.tyNo,
            // zcNo:this.queryParam.zcNo,
            // moNo:this.queryParam.moNo,
            // prdNo:this.queryParam.prdNo,
            // prdName:this.queryParam.prdName,
            // bilNo:this.queryParam.bilNo
          })
        ).then(response => {
          this.loading = false
          this.entity = response.data
          if (this.entity.lsNo) {
            this.isClaim = true
          } else {
            this.isClaim = false
          }
          if (this.entity.qtyLost) {
          } else {
            this.entity.qtyLost = 0
          }
          if (this.entity.dep) {
            this.depme(this.entity.dep)
          }
          this.testData = response.data.mesQcTy1s
          // this.data = this.entity.dep + '-' + this.entity.depName
          if (response.data.sbtzFileList) {
            this.tableData = response.data.sbtzFileList
          } else {
            this.tableData = []
          }
          // this.tablePage.total = response.data.total
          // this.tablePage.currentPage = response.data.current
        }).catch(err => this.requestFailed(err))
        // .catch(err => this.requestFailed(err))
        //       .finally(() => {
        //         this.loading = false
        // })
      },
      // 获取列表数据
      // getList () {
      //   this.loading = true
      //   fetchList(
      //     Object.assign({
      //       current: this.tablePage.currentPage,
      //       size: this.tablePage.pageSize,
      //       pgNo: this.queryParam.pgNo
      //     })
      //   ).then(response => {
      //     this.loading = false
      //     this.tableData = response.data.records

      //     this.tablePage.total = response.data.total
      //     this.tablePage.currentPage = response.data.current
      //   }).catch(e => {
      //     this.loading = false
      //   })
      // },
    },
  }
</script>

<style lang='scss' scoped>
  .ant-input-wrapper .ant-input-group-addon .ant-btn {
    height: 28px;
  }

  .sup_info_item {
    padding: 10px 0;
  }





  .el-form-item--mini.el-form-item,
  .el-form-item--small.el-form-item {
    margin-bottom: 0px
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    -o-appearance: none !important;
    -ms-appearance: none !important;
    appearance: none !important;
    margin: 0;
  }

  input[type="number"] {
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    -o-appearance: textfield;
    -ms-appearance: textfield;
    appearance: textfield;
  }

  .sup_info_basics_container {
    border: 1px solid #E5E5E5;
    border-radius: 10px;
    padding: 0px 20px 0px 20px;
  }

  .sup_info_basics_header {
    font-size: 15px;
    color: #1F2A3F;
    height: 41px;
    font-weight: 500;
    line-height: 41px;
    overflow: hidden;
  }

  .el-table__fixed,
  .el-table__fixed-right {
    height: 100% !important;
  }

  .ant-input {
    height: 28px;
  }

  .bordervisib .el-input__inner {
    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 0px;
    width: 100%;
    /* height:20px; */
  }

  /* .el-table tbody tr:hover > td {
    background-color: #fff!important;
} */
  .compoct .el-form-item--small .el-form-item__content,
  .el-form-item--small .el-form-item__label {
    line-height: 24px;
  }

  /* .bordervisib .el-input__inner{
  height:20px;
} */
  .tableheight .el-table__cell {
    padding: 1px;
  }

  .addbox {
    float: left;
    position: relative;
    height: 20px;
    width: 20px;
    margin-bottom: 20px;
    text-align: center;
  }

  .addbox input {
    position: absolute;
    left: 0;
    height: 20px;
    width: 20px;
    height: 26px;
    width: 50px;
    opacity: 0;
    cursor: pointer;
    margin-top: 9px;
  }

  .addbox .addbtn {
    background-color: #67C23A;
    border-color: #67C23A;
    height: 26px;
    line-height: 26px;
    width: 50px;
    border-radius: 5px;
    float: left;
    cursor: pointer;
    /*
  height: 22px;
  width: 31px;
  line-height: 24px; */
    color: #fff;
    font-size: 10px;
    /* background: #ccc; */
    margin-top: 9px;
  }

  .item {
    position: relative;
    /* 
  float: left; */
    height: 50px;
    width: 50px;
    margin: 5px 5px 0 0;
  }

  .item .cancel-btn {
    position: absolute;
    right: 0;
    top: 0;
    display: block;
    width: 20px;
    height: 20px;
    color: #fff;
    line-height: 20px;
    text-align: center;
    background: red;
    border-radius: 10px;
    cursor: pointer;
  }

  .item img {
    width: 100%;
    height: 100%;
    height: 50px;
    width: 50px;
  }

  .view {
    clear: both;
  }
</style>