<template>
  <div>
    <a-drawer :title="title" placement="right" :closable="false" @close="onClose" :visible="visible"
      :destroyOnClose="true" width="70%">
      <a-form :form="form" ref="form">
        <a-row :gutter="16">
          <!-- <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              :label="$t('salm.salNo')"
              v-bind="formItemLayout"
            >
              <a-input
                :disabled="formIndex"
                v-decorator="['salNo', { rules: [{ required: true, message:$t('salm.placeholder.salNo') }
                                                 
                ] }]"
                :placeholder="$t('salm.placeholder.salNo')"
              />
            </a-form-item>
          </a-col> -->
          <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
            <a-form-item label="代号" v-bind="formItemLayout">
              <a-input :disabled="formIndex" v-decorator="['byffNo', { rules: [{ required: true, message:'代号' }
                                                 
                ] }]" placeholder="代号" />
            </a-form-item>
          </a-col>

          <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
            <a-form-item label="保养方法" v-bind="formItemLayout">
              <a-input :disabled="formStatus" v-decorator="['byffNm', { rules: [{ required: true, message:'保养项目' }
                                                
                ] }]" placeholder="请输入保养方法" />
            </a-form-item>
          </a-col>

          <!-- <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              :label="$t('员工')"
              v-bind="formItemLayout"
            >
              <my-selectListwo
                url="/basic/salm/page"
                :tableColumn="$Column.salm"
                :form="$Form.salm"
                :read-only="true"
                :data="Updata"
                name="upSalNo"
                :disabled="UpStatus"
                @choose="choose($event)"
                ref="selectList"
                v-decorator="['upSalNo', { rules: [{ required: true, message:$t('salm.placeholder.staff') }] }]"
                :placeholder="$t('salm.placeholder.staff')"
              ></my-selectListwo>
            </a-form-item>
          </a-col> -->

          <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
            <a-form-item :label="$t('部门')" v-bind="formItemLayout">
              <!-- url="mes/basicData/depPage"   :tableColumn="$Column.salmDep"   :form="$Form.salmDep"-->
              <my-selectListwo url="ems/dept/getDeptPage" :read-only="true" :tableColumn="$Column.devicesalmDep"
                :form="$Form.devicesalmDep" :data="data" name="dep" :disabled="formStatus" @choose="choose($event)"
                ref="selectList"
                v-decorator="['dep', { rules: [{ required: false, message:$t('salm.placeholder.depName') } ] }]"
                :placeholder="$t('salm.placeholder.depName')"></my-selectListwo>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
            <a-form-item label="备注" v-bind="formItemLayout">
              <a-input :disabled="formStatus" v-decorator="['rem', { rules: [{ required: false, message:'备注' }
                                                 
              ] }]" placeholder="请输入备注" />
            </a-form-item>
          </a-col>
          <!-- upSalNo -->
          <!-- <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              :label="$t('salm.salNo')"
              v-bind="formItemLayout"
            >
              <my-selectListwo
                url="/basic/salm/page"
                :tableColumn="$Column.salm"
                :form="$Form.salm"
                :read-only="true"
                :data="Updata"
                name="upSalNo"
                :disabled="UpStatus"
                @choose="choose($event)"
                ref="selectList"
                v-decorator="['upSalNo', { rules: [{ required: false, message:$t('salm.placeholder.upSalName') }] }]"
                :placeholder="$t('salm.placeholder.upSalName')"
              ></my-selectListwo>
            </a-form-item>
          </a-col> -->
          <!-- <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              :label="$t('员工名称')"
              v-bind="formItemLayout"
            >
              <a-input
                :disabled="formStatus"
                v-decorator="['salName', { rules: [{ required: true, message:$t('salm.placeholder.name') }
                                                
                ] }]"
                :placeholder="$t('salm.placeholder.name')"
              />
            </a-form-item>
          </a-col> -->
          <!-- <a-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <a-form-item
              :label="$t('salm.department')"
              v-bind="formItemLayout"
            >
              <my-selectListwo
                url="/admin/dept/page"
                :read-only="true"
                :tableColumn="$Column.sebeiDep"
                :form="$Form.sebeiDep"
                :data="data"
                name="dep"
                :disabled="formStatus"
                @choose="choose($event)"
                ref="selectList"
                v-decorator="['dep', { rules: [{ required: false, message:$t('salm.placeholder.depName') } ] }]"
                :placeholder="$t('salm.placeholder.depName')"
              ></my-selectListwo>
            </a-form-item>
          </a-col> -->
        </a-row>
      </a-form>
      <a-row :gutter="16">
        <a-col class="gutter-row" :span="12" style="text-align:right">
          <a-button type="primary" v-if="modeType==='0'" :loading="loading" @click="handleOK()">{{ $t('public.save')
            }}</a-button>
          <a-button type="primary" v-if="modeType==='1'" @click="handleMenuClick()">{{ $t('public.edit') }}</a-button>
          <a-button type="primary" v-if="modeType==='2'" :loading="loading" @click="handleEdit()">{{ $t('public.save')
            }}</a-button>
        </a-col>
        <a-col class="gutter-row" :span="12" style="text-align:left">
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script>

  import { byffsaveOrUpdate, bydjsaveOrUpdate } from '@/api/device/category'
  import { add, edit, getTogto } from '@/api/salm'
  import { addSebMang, updateSebMang } from '@/api/barcode/basicdata/management'
  import MySelectList from '@/components/MySelectList'
  import MySelectListwo from '@/components/MySelectListwo'
  import moment from 'moment'
  import { mapGetters } from 'vuex'
  export default {
    name: 'SalmModal',
    components: {
      MySelectList,
      MySelectListwo
    },
    data() {
      return {
        title: '',
        loading: false,
        visible: false,
        formStatus: false,
        formIndex: false,
        UpStatus: false,
        data: '',
        Updata: '',
        row: {},
        modeType: '',
        form: this.$form.createForm(this),
        formItemLayout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 7 },
            md: { span: 8 },
            lg: { span: 7 }
          },
          wrapperCol: {
            xs: { span: 5 },
            sm: { span: 16 },
            md: { span: 17 },
            lg: { span: 16 }
          }
        },
        depName: null,
        salName: null,
        dName: null,
        flag: false
      }
    },
    computed: {
      ...mapGetters(['permissions'])
    },
    created() {

    },
    methods: {
      disabledDate(current) {
        return current && current < moment().endOf('day')
      },
      // 渲染组件
      // choose (obj) {
      //   var map = {}
      //   if (obj.obj.name === 'upSalNo') {
      //     map[obj.obj.name] = obj.obj.data.salNo
      //   }
      //   if (obj.obj.name === 'dep') {
      //     map[obj.obj.name] = obj.obj.data.dep
      //   }
      //   this.form.setFieldsValue(map)
      // },

      // 渲染组件
      choose(obj) {
        var map = {}
        if (obj.obj.name === 'upSalNo') {
          this.salName = obj.obj.data.name
          map[obj.obj.name] = obj.obj.data.salNo
        }
        // if (obj.obj.name === 'salNo') {
        //   this.salName = obj.obj.data.name
        //   map[obj.obj.name] = obj.obj.data.salNo
        // }
        if (obj.obj.name === 'dep') {
          this.flag = true
          this.depName = obj.obj.data.depName
          map[obj.obj.name] = obj.obj.data.dep
        }
        this.form.setFieldsValue(map)
      },
      // 取消关闭
      onClose() {
        this.form.resetFields()
        this.loading = false
        this.visible = false
        this.keyStatus = ''
        this.data = ''
        this.Updata = ''
        this.modeType = '0'
        this.row = {}
      },
      create(model, obj) {
        this.formIndex = false
        this.title = model.title
        this.modeType = '0'
        this.visible = true
        this.formStatus = false
        this.UpStatus = false
        setTimeout(() => {
          if (obj.name && obj.id !== '') {
            this.data = obj.name
            this.form.setFieldsValue({
              'dep': obj.id,
            })
          }
          // this.form.setFieldsValue({
          //   'logon': 'F'
          // })
        }, 1)
      },
      // 点击编辑按钮
      handleMenuClick() {
        this.modeType = '2'
        this.title = this.$t('public.edit')
        this.formIndex = true
        this.UpStatus = true
        this.formStatus = false
      },
      // 双击弹出框
      edit(model, row) {
        if (row.salNo) {
          this.Updata = row.salNo + '-' + row.salName
        }
        if (row.dep) {
          this.data = row.dep + '-' + row.depName
        }
        this.title = model.title
        this.modeType = '1'
        this.row = row
        this.formStatus = true
        this.UpStatus = true
        this.formIndex = true
        this.visible = true
        if (this.flag) {
        } else {
          this.dName = row.depName
        }
        this.salName = row.salName
        //  localStorage.setItem('keyrecords', JSON.stringify(row.id));
        localStorage.setItem('rowId', row.id);
        setTimeout(() => {
          this.form.setFieldsValue({
            'dep': row.dep,
            byffNo: row.byffNo,
            byffNm: row.byffNm,
            'name': row.name,
            rem: row.rem,
          })
        }, 1)
      },

      // 添加确认
      handleOK() {
        this.form.validateFields((err, values) => {

          console.log(values, 'ggggggggggggg')
          const Params = {
            ...values,
            // salName: this.salName,
            depName: this.depName,
            // salNo: values.upSalNo,
            // dep: "030101"
          }
          // if (Params.salNo === Params.upSalNo) {
          //   this.$notification.error({
          //     message: this.$t('public.errorInfo'),
          //     description: this.$t('public.upLeavel')
          //   })
          //   err = true
          // }
          if (!err) {
            this.loading = true
            byffsaveOrUpdate(Params)
              .then((res) => {
                if (res !== null) {
                  if (res.msg == 'fail') {
                    this.$message.error(this.$t(res.data))
                  }
                  if (res.msg == 'success') {
                    this.$emit('onOk')
                    this.visible = false
                    this.$message.success(this.$t('public.success'))
                  }
                  this.loading = false
                }
              })
              .catch(err => this.requestFailed(err))
              .finally(() => {
                this.loading = false
              })
          }
        })
      },
      // 确认编辑
      handleEdit() {
        if (this.flag) {
          this.dName = this.depName
        }
        this.form.validateFields((err, values) => {

          console.log(values, 'rrrrrrrrrrrrr')
          const Params = {
            ...values,
            // salName: this.salName,
            depName: this.dName,
            // salNo: values.upSalNo,
            id: localStorage.getItem('rowId')
          }
          // if (Params.salNo === Params.upSalNo) {
          //   this.$notification.error({
          //     message: this.$t('public.errorInfo'),
          //     description: this.$t('public.upLeavel')
          //   })
          //   err = true
          // }
          if (!err) {
            this.loading = true
            byffsaveOrUpdate(Params)
              .then((res) => {
                if (res !== null) {
                  if (res.msg == 'fail') {
                    this.$message.error(this.$t(res.data))
                  }
                  if (res.msg == 'success') {
                    this.$emit('onOk')
                    this.visible = false
                    this.$message.success(this.$t('public.success'))
                  }
                  this.loading = false
                  this.data = ''
                  this.Updata = ''
                }
              })
              .catch(err => this.requestFailed(err))
              .finally(() => {
                this.loading = false
              })
          }
        })
      }

    }
  }
</script>