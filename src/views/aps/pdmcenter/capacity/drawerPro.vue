<template>
  <div>
    <a-drawer
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :destroyOnClose="true"
      width="70%"
    >
      <template slot="title">
        <span class="title-name">{{ title }}</span>
        <span
          v-if="this.modeType!=='0'"
          class="title-age"
        >
          <a-dropdown>
            <a-button class="ant-dropdown-link">
              {{ $t('public.action') }}
              <a-icon type="down" />
            </a-button>
            <a-menu slot="overlay">
              <a-menu-item v-permission="syspdmcentercapacity_del">
                <a @click="a()">{{ $t('public.delete') }}</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </template>
      <a-form-model
        layout="horizontal"
        ref="ruleForm"
        :rules="rules"
        :model="form"
      >
        <a-row>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('capacity.prd')"
              prop="prdNo"
              ref="prdNo"
            >
              <my-selectList
                :disabled="disabled"
                url="/basic/prdt/page"
                :tableColumn="$Column.apsPrd"
                :form="$Form.apsPrdknd"
                :data="list.prdNo"
                name="prdNo"
                @choose="choose($event)"
                ref="selectList"
                v-model="form.prdNo"
                :placeholder="$t('capacity.placeholder.prdNo')"
              ></my-selectList>
            </a-form-model-item>
          </a-col>
          <!-- <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('capacity.sebNo')"
              prop="sebNo"
            >
              <my-selectList
                url="/basic/sebei/page"
                :disabled="disabled"
                multiple
                :tableColumn="$Column.sebNo"
                :form="$Form.sebNo"
                :data="list.sebNo"
                name="sebNo"
                @choose="choose($event)"
                ref="selectList"
                v-model="form.sebNo"
                :placeholder="$t('base.placeholder.sebNo')"
              >
              </my-selectList>
            </a-form-model-item>
          </a-col> -->
          <!-- <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('capacity.moldName')"
            >
              <my-selectList
                url="/basic/moldwh/page"
                :disabled="disabled"
                :tableColumn="$Column.moldNo"
                :form="$Form.moldNo"
                :data="list.moldNo"
                name="moldNo"
                @choose="choose($event)"
                ref="selectList"
                v-model="form.modNo"
                :placeholder="$t('capacity.placeholder.moldName')"
              >
              </my-selectList>
            </a-form-model-item>
          </a-col> -->
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('capacity.qtyTime')"
              prop="qtyTime"
            >
              <a-input
                :disabled="formStatus"
                v-model="form.qtyTime"
                :placeholder="$t('capacity.placeholder.qtyTime')"
                type="number"
                min="1"
              >
                <a-select
                  slot="addonAfter"
                  v-model="form.unit"
                  style="width: 52px"
                >
                  <a-select-option :value="1">
                    时
                  </a-select-option>
                  <a-select-option :value="2">
                    分
                  </a-select-option>
                  <a-select-option :value="3">
                    秒
                  </a-select-option>
                </a-select>
              </a-input>
              <!-- <a-input
                :disabled="formStatus"
                style="width:100%"
                v-model="form.qtyTime"
                :placeholder="$t('capacity.placeholder.qtyTime')"
                type="number"
                min="0.00"
                :suffix="qtyUnit"
              /> -->
              <!-- @change="onQtyChange" -->
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('capacity.qty')"
              prop="qty"
            >
              <a-input
                style="width:100%"
                v-model="form.qty"
                :disabled="formStatus"
                :placeholder="$t('capacity.placeholder.qty')"
                type="number"
                min="1"
              />
              <!-- @change="onTimeChange" -->
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('capacity.turnDownTime')"
              prop="turnDownTime"
            >
              <a-input
                :disabled="formStatus"
                v-model="form.turnDownTime"
                :placeholder="$t('capacity.placeholder.turnDownTime')"
                type="number"
                min="1"
              >
                <a-select
                  slot="addonAfter"
                  v-model="form.turnDownUnit"
                  style="width: 52px"
                >
                  <a-select-option :value="1">
                    时
                  </a-select-option>
                  <a-select-option :value="2">
                    分
                  </a-select-option>
                  <a-select-option :value="3">
                    秒
                  </a-select-option>
                </a-select>
              </a-input>
              <!-- @change="onQtyChange" -->
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('capacity.turnDownNum')"
              prop="turnDownNum"
            >
              <a-input
                style="width:100%"
                v-model="form.turnDownNum"
                :disabled="formStatus"
                :placeholder="$t('capacity.placeholder.turnDownNum')"
                type="number"
                min="0"
              />
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item
              v-bind="formItemLayout"
              :label="$t('capacity.rem')"
            >
              <a-input
                :max-length="100"
                type="textarea"
                :disabled="formStatus"
                v-model="form.rem"
                :placeholder="$t('capacity.placeholder.rem')"
              />
            </a-form-model-item>
          </a-col>

        </a-row>
      </a-form-model>
      <a-row :gutter="16">
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:right"
        >
          <a-button
            type="primary"
            :loading="loading"
            v-if="modeType==='0'"
            @click="handleOK()"
          >{{ $t('public.save') }}</a-button>
          <a-button
            type="primary"
            v-permission="syspdmcentercapacity_edit"
            v-if="modeType==='1'"
            @click="handleMenuClick()"
          >{{ $t('public.edit') }}</a-button>
          <a-button
            type="primary"
            v-permission="syspdmcentercapacity_edit"
            :loading="loading"
            v-if="modeType==='2'"
            @click="handleEdit()"
          >{{ $t('public.save') }}</a-button>
        </a-col>
        <a-col
          class="gutter-row"
          :span="12"
          style="text-align:left"
        >
          <a-button @click="onClose">{{ $t('public.cancel') }}</a-button>
        </a-col>
      </a-row>
    </a-drawer>
  </div>
</template>

<script>
import { addPro, editPro, delPro, getTime } from '@/api/basic/pdmcenter/capacity'
import MySelectList from '@/components/MySelectList'
export default {
  components: {
    MySelectList
  },
  data () {
    return {
      syspdmcentercapacity_del: 'syspdmcentercapacity_del',
      syspdmcentercapacity_edit: 'syspdmcentercapacity_edit',
      title: '',
      visible: false,
      formStatus: false,
      disabled: false,
      loading: false,
      modeType: '',
      row: {},
      form: {
        qty: 1,
        unit: 1,
        turnDownUnit: 1,
        turnDownNum: 0,
      },
      data: {},
      url: '',
      seb: '',
      qtyUnit: '',
      unit: '',
      list: {
        prdNo: '',
        sebNo: '',
        modNo: ''
      },
      rules: {
        prdNo: [
          { required: true, validator: this.handlePass, trigger: 'change' }
        ],
        // sebNo: [
        //   { required: true, validator: this.handlePassSeb, trigger: 'change' }
        // ],
        qtyTime: [
          { required: true, message: this.$t('capacity.placeholder.qtyTime'), trigger: 'blur' }
        ],
        qty: [
          { required: true, message: this.$t('capacity.placeholder.qty'), trigger: 'blur' }
        ],
        turnDownTime: [
          { required: true, message: this.$t('capacity.placeholder.turnDownTime'), trigger: 'blur' }
        ],
        turnDownNum: [
          { required: true, message: this.$t('capacity.placeholder.turnDownNum'), trigger: 'blur' }
        ]
      },
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        }
      }
    }
  },
  created () {
    // this.getTime()
  },
  // watch: {
  //   bomNo (val) {
  //     this.url = `/basic/mfzc/page?bomNo=${val}`
  //   }
  // },
  methods: {
    getTime () {
      getTime()
        .then(res => {
          this.form.unit = +res.timeType
          this.form.turnDownUnit = +res.timeType
        })
        .catch(err => this.requestFailed(err))
        .finally(() => {
          this.loading = false
        })
    },
    onTimeChange (e) {
      let unit = this.form.unit
      let num = this.form.qty
      if (unit) {
        if (unit === '1') {
          // 小时
          this.form.qtyTime = (num / 3600).toFixed(2)
        }
        if (unit === '2') {
          // 分钟
          this.form.qtyTime = (num / 60).toFixed(2)
        }
        if (unit === '3') {
          // 秒
          this.form.qtyTime = num.toFixed(2)
        }
      }
    },
    onQtyChange (e) {
      let unit = this.form.unit
      let num = this.form.qtyTime
      if (unit) {
        if (unit === '1') {
          // 小时
          this.form.qty = 3600 * num
        }
        if (unit === '2') {
          // 分钟
          this.form.qty = 60 * num
        }
        if (unit === '3') {
          // 秒
          this.form.qty = num
        }
      }
    },

    // 校验
    handlePass (rule, value, callback) {
      value = this.form.prdNo
      if (value) {
        callback()
      } else {
        callback(new Error(this.$t('capacity.placeholder.prdNo')))
      }
    },
    handlePassSeb (rule, value, callback) {
      value = this.seb
      if (value) {
        callback()
      } else {
        callback(new Error(this.$t('capacity.placeholder.sebNo')))
      }
    },
    choose (obj) {
      if (obj.obj.name === 'moldNo') {
        this.form.modNo = obj.obj.data.modNo
        this.form.moldItm = obj.obj.data.modItm
        this.form.moldName = obj.obj.value
      }
      if (obj.obj.name === 'prdNo') {
        this.form.prdNo = obj.obj.data.prdNo
        this.form.prdName = obj.obj.value

      }
      if (obj.obj.name === 'sebNo') {
        let arr = obj.obj.data
        let car = []
        arr.forEach(i => {
          car.push(i.sebNo)
        })
        this.seb = car.join(',')
        this.form[obj.obj.name] = car.join(',')
        this.form.sebName = obj.obj.value
      }
    },
    // 取消
    onClose () {
      this.loading = false
      this.visible = false
      this.form = {
        qty: 1,
        unit: 1,
        turnDownUnit: 1,
        turnDownNum: 1,
      }
      this.list = {}
    },
    create (model, data) {
      this.getTime()
      // 特殊符号转译。货品查询
      // this.url = '/basic/mfzc/page?bomNo=' + encodeURIComponent(data.bomNo)
      this.data = data
      this.form = {
        zcNo: data.zcNo,
        centerId: data.centerId,
        itm: data.total + 1,
        qty: 1,
        qtyTime: data.qtyTime,
        unit: data.unit,
        turnDownUnit: data.turnDownUnit,
        turnDownNum: data.turnDownNum,
        turnDownTime: data.turnDownTime
      }
      this.title = model.title
      this.modeType = '0'
      this.visible = true
      this.disabled = false
      this.formIndex = false
      this.formStatus = false
    },
    // 点击编辑按钮
    handleMenuClick () {
      this.modeType = '2'
      this.title = this.$t('public.edit')
      this.formIndex = true
      this.disabled = false
      this.formStatus = false
    },
    edit (model, row) {
      this.title = model.title
      this.modeType = '1'
      this.row = row
      this.formStatus = true
      this.visible = true
      this.disabled = true
      this.dep = row.dep
      this.form = {
        prdNo: row.prdNo,
        sebNo: row.sebNo,
        rem: row.rem,
        qty: row.qty,
        qtyTime: row.qtyTime,
        zcNo: row.zcNo,
        unit: row.unit,
        centerId: this.data.centerId,
        version: row.version,
        turnDownUnit: row.turnDownUnit,
        turnDownNum: row.turnDownNum,
        turnDownTime: row.turnDownTime
      }
      this.list = {
        prdNo: row.prdName,
        sebNo: row.sebName,
        moldNo: row.moldName
      }
    },
    // 添加确认
    handleOK () {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          // if (this.form.modNo || this.form.sebNo) {
          this.loading = true
          addPro(this.form).then((res) => {
            this.loading = false
            this.onClose()
            this.$emit('getProList')
            this.$message.success(this.$t('public.success'))
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
          // } else {
          //   this.loading = false
          //   this.$message.error('请选择设备或者模具至少一个！')
          // }

        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
          return false
        }
      })
    },
    // 确认编辑
    handleEdit () {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          // if (this.form.modNo || this.form.sebNo) {
          this.loading = true
          this.form.id = this.row.id
          this.form.version = this.row.version
          editPro(this.form).then((res) => {
            this.loading = false
            this.onClose()
            this.$emit('getProList')
            this.$message.success(this.$t('public.success'))
          }).catch(err => this.requestFailed(err))
            .finally(() => {
              this.loading = false
            })
          // } else {
          //   this.$message.error(this.$t('base.choose'))
          //   this.loading = false
          // }
        } else {
          this.loading = false
          this.$message.error(this.$t('public.error'))
          return false
        }
      })
    },
    a () {
      const that = this
      this.$confirm({
        title: this.$t('public.del.title'),
        content: this.$t('public.del.content'),
        okText: this.$t('public.sure'),
        okType: 'danger',
        cancelText: this.$t('public.cancel'),
        onOk () {
          that.loading = true
          const row = that.row
          delPro(row)
            .then(() => {
              that.loading = false
              that.$emit('getProList')
              that.onClose()
              that.$message.success(that.$t('public.success'))
            })
            .catch(err => that.requestFailed(err))
            .finally(() => {
              that.loading = false
            })
        },
        onCancel () {
          that.loading = false
        }
      })
    }
  }
}
</script>
