<template>
  <div>
    <div class="tabs">
      <!-- <div v-for="(item, index) in loginType" :key="item" class="tabs-item" :class="[ index === 1 ? 'tabs-item2': '', index === active ? 'is-active' : '' ]" @click="handleSelect(index)">{{ item }}</div> -->
      <div class="tabs-item" :class="[ 0 === active ? 'is-active' : '', language === 'en' ? 'tabs-item-en' : '' ]" @click="handleSelect(0)">手机登录</div>
      <div class="tabs-item tabs-item2" :class="[ 1 === active ? 'is-active' : '' , language === 'en' ? 'tabs-item-en' : '' ]" @click="handleSelect(1)">邮箱登录</div>

    </div>
    <div class="border_top" :class="{'border_top_en' : language === 'en'}" />

    <MobileLogin  v-show="showMobile"/>
    <EmailLogin v-show="!showMobile" />
    <LogOn />
  </div>
</template>

<script>
import MobileLogin from './mobileLogin.vue'
import EmailLogin from './emailLogin.vue'
import LogOn from './weixinLogon.vue'
// import { mapGetters } from 'vuex'
export default {
  name: 'LoginForm',
  components: {
    MobileLogin,
    EmailLogin,
    LogOn
  },
  data() {
    return {
      showMobile: true,
      active: 0,
      language:'zh'

    }
  },
  computed: {
    // ...mapGetters(['language'])
  },
  methods: {
    handleSelect(key) {
      this.active = key
      // eslint-disable-next-line
      if (key === 0) return this.showMobile = true
      this.showMobile = false
    }
  }
}
</script>

<style scoped>
  ::v-deep .el-select .el-input {
    width: 80px;
  }
  ::v-deep .input-with-select .el-input-group__prepend {
    background-color: #fff;
  }
</style>
