export const arcCH = {
  arcTitleName:'套版设计详情',
  topDialogTitle:'选择导入套版',
  orderTypeName0:'页面报表',
  orderTypeName1:'RDL报表',
  topMsgSuccess:'导入数据源成功',
  topMsgError:'导入数据源失败',
  fileTypeError: '文件类型错误',
  saveReportTopMsg:'保存报表成功！',
  index: {
    dataSource:'数据源',
    sqlParameter: 'SQL参数值',
    sqlDataSourceList: 'SQL数据源列表',
    sqlDataSource: 'SQL数据源',
    rulesDataSourceName: '请输入接口key',
    rulesName: '请输入接口名称',
    rulesConnectString: '请输入接口地址',
    rulesDatasetType: '请选择数据集类型',
    errorMsgSingleItem: '至少选择一条数据',
    errorMsgSelectTemplate: '请选择打印套版',
    errorMsgSelectPrinter: '请先在打印机设置中配置默认打印机',
    UnPrinterPDF: '批量打印不支持打印PDF格式',
    errorMsgSelectPrinterLanguage: '请先在打印机设置中配置默认打印机语言',
    errorRequest:'打印代理服务是否已启动',
    successMsgPrint:'打印以完成',
  },
  interfaceList: {
    titleName:'接口列表',
    templateType:'套版类型:',
    bill_type:'套版类型:',
    interfaceName:'接口名称',
    name:'接口名称',
    interfaceAddress:'接口地址',
    interfaceType:'接口类型',
    isPublic:'是否公共'
  },
  newList: {
    titleName:'接口列表详情',
    menuNo: '菜单号',
    datasetType: '数据集类型',
    switchDatasource:'切换数据源',
    reportFiled:'报表字段',
    filedName:'字段名',
    name:'名称',
    filedType:'字段类型',
    singleItem:'单条',
    list:'列表',
    errorMsgFiledType:'请选择字段类型',
    errorMsgFiledName:'请填写字段名',
    errorMsgFiledName1:'请填写字段显示名',
    errorMsgSave:'请先保存表头信息',

    sqlTest:'SQL测试',
    templateType:'套版类型:',
    isPublic:'是否公共',
    filterFields:'过滤字段:',
    interfaceKey:'接口key:',
    interfaceName:'接口名称:',
    select:'请选择',
    haveParameter:'有参',
    noParameter:'无参',
    customSql:'自定义SQL',
    sqlStatement:'SQL语句',
    pleaseEnterSQL:'请输入SQL',
    iconTooltip0:'单据打印时，查询的数据集为非系统表时需选择此选项',
    iconTooltip1:'报表打印需要选择此选项，然后在下方表格设置字段信息',
  },
  overprint: {
    placeholder:'套版搜索',
    title: '创建新套版',
    page:'页面:',
    language:'语言:',
    templateNO:'模版代号:',
    templateNOPlaceholder:'请输入模版代号',
    templateName:'模版名称:',
    templateNamePlaceholder:'套版名称...',
    labelName:'报表模版:',
    FPL:'页面报表:',
    CPL:'RDL报表:',
    defaultTemplate:'默认套版:',
    is_angles:'套版旋转',
    angles:'旋转角度',
    anglesPlaceholder:'请输入旋转角度',
    width:'标签宽度',
    widthPlaceholder:'请输入标签纸宽度,单位(mm)',
    height:'标签高度',
    heightPlaceholder:'请输入标签纸高度,单位(mm)',
    table: {
      menuName:'菜单名',
      page:'页面',
      templateNO:'套版代号',
      templateName:'套版名称',
      default:'是否默认',
      Y:'是',
      N:'否',
      operation:'操作',
      design:'套版设计'
    },
    treeLabel:'打印模板',
    reportPrint:'报表打印',
    labelPrint:'标签打印',
    error:'错误',
    errorMsg0:'模版代号不能为空',
    errorMsg1:'菜单号不能为空',
    errorMsg2:'模版名称不能为空',
    errorMsg3:'套版旋转角度、宽度、高度不能为空',
    printTemplate: {
      title: '快速过滤',
      select: '选择',
      menuLabel:'菜单代号',
      menuLabel1:'名称',
      conditionLabel: '等于',
      conditionLabel1: '类似',
      table: {
        title: '操作',
        item: '项次',
        menuNo: '菜单代号',
        name: '名称',
      },
      idError:'请正确输入查询条件'
    }
  },
  componentsPrintDialog: {
    index: {
      title: '打印',
      printTemplate: '打印套版',
      startPlaceholder:'开始日期',
      endPlaceholder:'结束日期',
      item:'项次',
      mergePrinting:'合并打印',
      closeESC:'关闭(ESC)',
      preview:'预览(F6)',
      print:'打印(F7)',
      dialogTitlePrintPreview:'打印预览',
      select:'请选择',
      templateEdit:'套版编辑',
      close:'关闭',
    },
  },
  printSettings: {
    name:'默认打印机设置',
    lage:'打印机语言设置',
    dpi:'打印机分辨率',
    btnDwn:'批量打印插件下载',
    tipsMsg0:'打印插件是否启动？请检查',
    tipsMsg1:'打印插件是否启动？请检查',
    tipsMsg2:'请选择打印机',
    tipsMsg3:'请选择打印机语言',
    tipsMsg4:'打印成功',
    tipsMsg5:'打印失败',
    tipsMsg6:'获取报表失败',
  }
}

export const arcTW = {
  arcTitleName:'套版設計詳情',
  topDialogTitle:'選擇導入套版',
  orderTypeName0:'頁面報表',
  orderTypeName1:'RDL報表',
  topMsgSuccess:'導入數據源成功',
  topMsgError:'導入數據源失敗',
  fileTypeError: '文件類型錯誤',
  saveReportTopMsg:'保存報表成功！',
  index: {
    dataSource:'數據源',
    sqlParameter: 'SQL參數值',
    sqlDataSourceList: 'SQL數據源列表',
    sqlDataSource: 'SQL數據源',
    rulesDataSourceName: '請輸入接口key',
    rulesName: '請輸入接口名稱',
    rulesConnectString: '請輸入接口地址',
    rulesDatasetType: '請選擇數據集類型',
    errorMsgSingleItem: '至少選擇一條數據',
    errorMsgSelectTemplate: '請選擇打印套版',
    errorMsgSelectPrinter: '請先在打印機設定中配置默認打印機',
    UnPrinterPDF: '批量打印不支持打印PDF格式',
    errorMsgSelectPrinterLanguage: '請先在打印機設定中配置默認打印機語言',
    errorRequest:'打印代理服務是否已啟動',
    successMsgPrint:'打印以完成',
  },
  interfaceList: {
    titleName:'接口列表',
    templateType:'套版類型',
    bill_type:'套版類型',
    interfaceName:'接口名稱',
    name:'接口名稱',
    interfaceAddress:'接口地址',
    interfaceType:'接口類型',
    isPublic:'是否公共'
  },
  newList: {
    titleName:'接口列表詳情',
    menuNo: '菜單號',
    datasetType: '數據集類型',
    switchDatasource:'切換數據源',
    reportFiled:'報表字段',
    filedName:'字段名',
    name:'名稱',
    filedType:'字段類型',
    singleItem:'單條',
    list:'列表',
    errorMsgFiledType:'請選擇字段類型',
    errorMsgFiledName:'請填寫字段名',
    errorMsgFiledName1:'請填寫字段显示名',
    errorMsgSave:'請先保存表頭信息',

    sqlTest:'SQL 測試',
    templateType:'套版類型',
    isPublic:'是否公共:',
    filterFields:'過濾字段',
    interfaceKey:'接口key',
    interfaceName:'接口名稱:',
    select:'請選擇',
    haveParameter:'有參',
    noParameter:'無参',
    customSql:'自定義SQL',
    sqlStatement:'SQL語句',
    pleaseEnterSQL:'請輸入SQL',
    iconTooltip0:'單據打印時，查詢的數據集為非系統表時需選擇此選項',
    iconTooltip1:'報表打印需要選擇此選項，然後在下方表格設置字段信息',
  },
  overprint: {
    placeholder:'套版搜索',
    title: '創建新套版',
    page:'頁面:',
    language:'語言:',
    templateNO:'模版代號:',
    templateNOPlaceholder:'請輸入區域名稱',
    templateName:'模版名稱:',
    templateNamePlaceholder:'套版名稱...',
    labelName:'模版名稱:',
    FPL:'頁面報表:',
    CPL:'RDL報表:',
    defaultTemplate:'默認套版:',
    is_angles:'套版旋轉:',
    angles:'旋轉角度:',
    anglesPlaceholder:'請輸入套版旋轉角度',
    width:'標籤寬度',
    widthPlaceholder:'請輸入標籤寬度,單位(mm)',
    height:'標籤高度',
    heightPlaceholder:'請輸入標籤高度,單位(mm)',
    table: {
      menuName:'菜單名',
      page:'頁面',
      templateNO:'模版代號',
      templateName:'套版名稱',
      default:'是否默認',
      Y:'是',
      N:'否',
      operation:'操作',
      design:'套版設計'
    },
    treeLabel:'打印模板',
    reportPrint:'報表打印',
    labelPrint:'標籤打印',
    error:'錯誤',
    errorMsg0:'模版代號不能為空',
    errorMsg1:'菜單號不能為空',
    errorMsg2:'模版名稱不能為空',
    errorMsg3:'套版旋轉角度、寬度、高度不能為空',
    printTemplate: {
      title: '快速過濾',
      select: '選擇',
      menuLabel:'菜單代號',
      menuLabel1:'名稱',
      conditionLabel: '等於',
      conditionLabel1: '類似',
      table: {
        title: '操作',
        item: '項次',
        menuNo: '菜單代號',
        name: '名稱',
      },
      idError:'請正确输入查询条件'
    }
  },
  componentsPrintDialog: {
    index: {
      title: '打印',
      printTemplate: '打印套版',
      startPlaceholder:'開始日期',
      endPlaceholder:'結束日期',
      item:'項次',
      mergePrinting:'合併打印',
      closeESC:'關閉(ESC)',
      preview:'預覽(F6)',
      print:'打印(F7)',
      dialogTitlePrintPreview:'打印預覽',
      select:'請選擇',
      templateEdit:'套版編輯',
      close:'關閉',
    }
  },
  printSettings: {
    name:'默认打印機設置',
    lage:'打印機語言設置',
    dpi:'打印機分辨率',
    btnDwn:'批量打印插件下载',
    tipsMsg0:'打印插件是否启动？请检查',
    tipsMsg1:'打印插件是否启动？请检查',
    tipsMsg2:'请选择打印机',
    tipsMsg3:'请选择打印机语言',
    tipsMsg4:'打印成功',
    tipsMsg5:'打印失败',
    tipsMsg6:'获取报表失败',
  }
}

export const arcUS = {
  arcTitleName:'Design details',
  topDialogTitle:'Select the imported template',
  orderTypeName0:'Page report',
  orderTypeName1:'RDL report',
  topMsgSuccess:'Import data source successful',
  topMsgError:'Import data source failed',
  fileTypeError: 'File type error',
  saveReportTopMsg:'Save report successfully!',
  index: {
    dataSource:'Data source',
    sqlParameter: 'SQL parameter',
    sqlDataSourceList: 'SQL data source list',
    sqlDataSource: 'SQL data source',
    rulesDataSourceName: 'Please enter the interface key',
    rulesName: 'Please enter the interface name',
    rulesConnectString: 'Please enter the interface address',
    rulesDatasetType: 'Please enter the dataset type',
    errorMsgSingleItem: 'Select at least one piece of data',
    errorMsgSelectTemplate: 'Please select the printing template',
    errorMsgSelectPrinter: 'Please configure the default printer in the printer setting first',
    UnPrinterPDF: 'Batch printing does not support printing PDF format',
    errorMsgSelectPrinterLanguage: 'Please configure the default printer language in the printer setting first',
    errorRequest:'Is the printing proxy service started? Please check',
    successMsgPrint:'Printing completed',
  },
  interfaceList: {
    titleName:'Interface list',
    templateType:'Template type',
    bill_type:'Template type',
    interfaceName:'Interface name',
    name:'Interface name',
    interfaceAddress:'Interface address',
    interfaceType:'Interface type',
    isPublic:'IS public'
  },
  newList: {
    titleName:'Interface list details',
    menuNo: 'Menu number',
    datasetType: 'Dataset type',
    switchDatasource:'Switch data source',
    reportFiled:'Report field',
    filedName:'Field name',
    name:'name',
    filedType:'Filed type',
    singleItem:'Single item',
    list:'list',
    errorMsgFiledType:'Please select field type',
    errorMsgFiledName:'Please fill in the field name',
    errorMsgFiledName1:'Please fill in the field display name',
    errorMsgSave:'Please save the header information first',

    sqlTest:'SQL Test',
    templateType:'Template type',
    isPublic:'IS public',
    filterFields:'Filter fields',
    interfaceKey:'Interface key',
    interfaceName:'Interface name',
    select:'Please choose',
    haveParameter:'Have parameter',
    noParameter:'No parameter',
    customSql:'Custom SQL',
    sqlStatement:'SQL statement',
    pleaseEnterSQL:'Please enter SQL',
    iconTooltip0:'When printing documents, if the queried dataset is a non system table, this option should be selected',
    iconTooltip1:'To print the report, select this option and then set the field information in the table below',
  },
  overprint: {
    placeholder:'Template search',
    title: 'Create a new version',
    page:'page:',
    language:'language:',
    templateNO:'Template code:',
    templateNOPlaceholder:'Please enter the region name',
    templateName:'Template Name:',
    templateNamePlaceholder:'Template Name...',
    labelName:'Report template:',
    FPL:'Page report:',
    CPL:'RDL report:',
    defaultTemplate:'Default template:',
    is_angles:'Template angles:',
    angles:'Rotate angles',
    anglesPlaceholder:'Please select whether to rotate the template',
    width:'Width',
    widthPlaceholder:'Please enter the template width, unit(mm)',
    height:'Height',
    heightPlaceholder:'Please enter the template height, unit(mm)',
    table: {
      menuName:'Menu name',
      page:'Page',
      templateNO:'Template code',
      templateName:'Template Name',
      default:'Is default',
      Y:'Yes',
      N:'No',
      operation:'Operation',
      design:'Template design'
    },
    treeLabel:'Print template',
    reportPrint:'Report printing',
    labelPrint:'Label printing',
    error:'Error',
    errorMsg0:'Template code cannot be empty',
    errorMsg1:'The menu number cannot be empty',
    errorMsg2:'Template name cannot be empty',
    errorMsg3:'Template code already exists',
    printTemplate: {
      title: 'Quick filtering',
      select: 'Select',
      menuLabel:'Menu code',
      menuLabel1:'Name',
      conditionLabel: 'Be equal to',
      conditionLabel1: 'Similar',
      table: {
        title: 'Operation',
        item: 'Item',
        menuNo: 'Menu code',
        name: 'Name',
      },
      idError:'Please enter the correct query conditions'
    }
  },
  componentsPrintDialog: {
    index: {
      title: 'Print',
      printTemplate: 'Print template',
      startPlaceholder:'Start date',
      endPlaceholder:'End Date',
      item:'Item',
      mergePrinting:'Merge Printing',
      closeESC:'Close(ESC)',
      preview:'Preview(F6)',
      print:'Printing(F7)',
      dialogTitlePrintPreview:'Print Preview',
      select:'Please choose',
      templateEdit:'Template Editor',
      close:'Close',
    }
  },
  printSettings: {
    name:'Default printer settings',
    lage:'Printer language settings',
    dpi:'Printer resolution',
    btnDwn:'Batch printing plug-in download',
    tipsMsg0:'Is the printing plug-in started? Please check',
    tipsMsg1:'Is the printing plug-in started? Please check',
    tipsMsg2:'Please select a printer',
    tipsMsg3:'Please select a printer language',
    tipsMsg4:'Printing successful',
    tipsMsg5:'Printing failed',
    tipsMsg6:'Getting report failed',
  }
}