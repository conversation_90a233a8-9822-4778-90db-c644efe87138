<!DOCTYPE html>
<html lang="zh-cmn-Hans">

<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="format-detection" content="telephone=no">
  <!--避免微信管理防盗链机制-->
  <meta name="referrer" content="no-referrer" />
  <meta http-equiv="X-UA-Compatible" content="chrome=1" />
  <link rel="icon"  href="<%= BASE_URL %>./logon.png"/>
<!--  <link rel="stylesheet" href="<%= BASE_URL %>cdn/css/style.css">-->
<!--  <script src="http://pv.sohu.com/cityjson?ie=utf-8"></script>-->
  <script src="<%= BASE_URL %>cdn/moment.min.js"></script>
  <script src="<%= BASE_URL %>cdn/locale/zh-cn.js"></script>
  <script src="<%= BASE_URL %>cdn/xlsx.full.min.js"></script>
<!--  <script src="<%= BASE_URL %>static/js/web-designer.js"></script>-->
  <!-- <script src="<%= BASE_URL %>cdn/axios.min.js" charset="utf-8"></script> -->
  <!-- <script src="https://cdn.bootcdn.net/ajax/libs/vue/2.6.10/vue.js"></script> -->
  <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.22.2/moment.min.js"></script> -->
  <!-- <script src="https://gw.alipayobjects.com/os/lib/antv/g2/4.1.2/dist/g2.min.js"></script> -->
  <!-- <script src="https://unpkg.com/@antv/data-set"></script> -->
  <title>JUST MAKE</title>
  <style>
    #loading-mask {
      position: fixed;
      left: 0;
      top: 0;
      height: 100%;
      width: 100%;
      background: #fff;
      user-select: none;
      z-index: 9999;
      overflow: hidden;
    }

    .loading-wrapper {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -100%);
    }

    .loading-dot {
      animation: antRotate 1.2s infinite linear;
      transform: rotate(45deg);
      position: relative;
      display: inline-block;
      font-size: 64px;
      width: 64px;
      height: 64px;
      box-sizing: border-box;
    }

    .loading-dot i {
      width: 22px;
      height: 22px;
      position: absolute;
      display: block;
      background-color: #1890ff;
      border-radius: 100%;
      transform: scale(0.75);
      transform-origin: 50% 50%;
      opacity: 0.3;
      animation: antSpinMove 1s infinite linear alternate;
    }

    .loading-dot i:nth-child(1) {
      top: 0;
      left: 0;
    }

    .loading-dot i:nth-child(2) {
      top: 0;
      right: 0;
      -webkit-animation-delay: 0.4s;
      animation-delay: 0.4s;
    }

    .loading-dot i:nth-child(3) {
      right: 0;
      bottom: 0;
      -webkit-animation-delay: 0.8s;
      animation-delay: 0.8s;
    }

    .loading-dot i:nth-child(4) {
      bottom: 0;
      left: 0;
      -webkit-animation-delay: 1.2s;
      animation-delay: 1.2s;
    }

    @keyframes antRotate {
      to {
        -webkit-transform: rotate(405deg);
        transform: rotate(405deg);
      }
    }

    @-webkit-keyframes antRotate {
      to {
        -webkit-transform: rotate(405deg);
        transform: rotate(405deg);
      }
    }

    @keyframes antSpinMove {
      to {
        opacity: 1;
      }
    }

    @-webkit-keyframes antSpinMove {
      to {
        opacity: 1;
      }
    }
  </style>
  <!-- require cdn assets css -->

  <!-- <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.css) { %>
  <link rel="stylesheet" href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" />
  <% } %> -->
</head>
<body >
  <noscript>
    <strong>We're sorry but vue-antd-pro doesn't work properly without JavaScript enabled. Please enable it to
      continue.</strong>
  </noscript>
  <div id="app">
    <div id="loading-mask">
      <div class="loading-wrapper">
        <span class="loading-dot loading-dot-spin"><i></i><i></i><i></i><i></i></span>
      </div>
    </div>
  </div>
  <!-- require cdn assets js -->
  <!-- <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.js) { %>
  <script type="text/javascript" src="<%= htmlWebpackPlugin.options.cdn.js[i] %>"></script>
  <% } %> -->
  <!-- built files will be auto injected -->
</body>

</html>